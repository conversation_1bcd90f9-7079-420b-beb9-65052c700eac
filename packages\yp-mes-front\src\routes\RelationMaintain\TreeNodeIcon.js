/**
 * SelectTree - 组织关系树ICON
 * @date: 2021-1-13
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React from 'react';
import { connect } from 'dva';
import { Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import iconAdd from '@/assets/icon_add.svg';
import iconCopy from '@/assets/icon_copy.svg';
import iconPaste from '@/assets/icon_paste.svg';
import iconDelete from '@/assets/icon_delete.svg';

function treeNodeIcon(props) {
  // 企业 站点 区域 生产线 工作单元
  const organizationTypeMap = {
    ENTERPRISE: ['add'],
    SITE: ['add', 'paste', 'delete'],
    AREA: ['add', 'paste', 'delete'],
    PROD_LINE: ['add', 'copy', 'paste', 'delete'],
    WORKCELL: ['add', 'copy', 'paste', 'delete'],
    LOCATOR: ['add', 'copy', 'paste', 'delete'],
  };

  // 第一级为tree node 节点, 第二级为拖拽节点能否释放
  const dragDisabledMap = {
    SITE: ['LOCATOR'],
    AREA: ['PROD_LINE', 'LOCATOR'],
    PROD_LINE: ['WORKCELL', 'LOCATOR'],
    WORKCELL: ['WORKCELL', 'LOCATOR'],
    LOCATOR: ['LOCATOR'],
  };

  const { callback, item, iconText } = props;

  const hasIcon = type => {
    return (organizationTypeMap[item.organizationType] || []).indexOf(type) > -1;
  };

  const checkPaste = () => {
    const rule =
      item.enableFlag !== 'Y' ||
      !props.orgNodeCopy.organizationId ||
      (props.orgNodeCopy.organizationId &&
        ((dragDisabledMap[item.organizationType] || []).indexOf(
          props.orgNodeCopy.organizationType,
        ) === -1 ||
          item.organizationId === props.orgNodeCopy.organizationId ||
          item.organizationId === props.orgNodeCopy.parentOrganizationId ||
          (props.orgNodeCopy.detailType &&
            item.parentOrganizationId.detailType === props.orgNodeCopy.detailType)));
    return rule;
  };

  const iconEventClick = (e, type) => {
    e.stopPropagation();
    callback(type, item);
  };

  return (
    <span
      key={`${item.organizationRelId}icon`}
      className="tree-node-icon"
      onClick={e => {
        e.stopPropagation();
      }}
    >
      {hasIcon('add') && (
        <span className="tree-node-icon-item">
          <PermissionButton type="text" disabled={item.enableFlag !== 'Y'}>
            <img
              className={
                item.enableFlag !== 'Y' ? 'available-img un-available-img' : 'available-img'
              }
              src={iconAdd}
              alt=""
            />
          </PermissionButton>
          {item.enableFlag === 'Y' && (
            <div className="node-add-change">
              <div className="node-add-change-inner">
                {item.organizationType !== 'LOCATOR' && (
                  <>
                    <div
                      className="node-add-change-row"
                      onClick={e => {
                        iconEventClick(e, 'addRelation');
                      }}
                    >
                      {iconText.createRelation}
                    </div>
                    <div
                      className="node-add-change-row"
                      onClick={e => {
                        iconEventClick(e, 'addSub');
                      }}
                    >
                      {iconText.createSubordinateAndRelation}
                    </div>
                  </>
                )}
                {item.organizationType !== 'ENTERPRISE' && (
                  <div
                    className="node-add-change-row"
                    onClick={e => {
                      iconEventClick(e, 'addLocator');
                    }}
                  >
                    {iconText.createRelationLocator}
                  </div>
                )}
              </div>
            </div>
          )}
          {/* {item.enableFlag === 'Y' && item.organizationType === 'AREA' && (
          )} */}
        </span>
      )}
      {hasIcon('copy') && (
        <span className="tree-node-icon-item">
          <PermissionButton type="text" disabled={item.enableFlag !== 'Y'}>
            <img
              className={
                item.enableFlag !== 'Y' ? 'available-img un-available-img' : 'available-img'
              }
              src={iconCopy}
              alt=""
              onClick={e => {
                iconEventClick(e, 'copy');
              }}
            />
          </PermissionButton>
          {item.enableFlag === 'Y' && <div className="node-info">{iconText.copy}</div>}
        </span>
      )}
      {hasIcon('paste') && (
        <span className="tree-node-icon-item">
          <PermissionButton type="text" disabled={checkPaste()}>
            <img
              className={checkPaste() ? 'available-img un-available-img' : 'available-img'}
              src={iconPaste}
              alt=""
              onClick={e => {
                iconEventClick(e, 'paste');
              }}
            />
          </PermissionButton>
          {item.enableFlag === 'Y' && <div className="node-info">{iconText.paste}</div>}
        </span>
      )}
      {hasIcon('delete') && (
        <span className="tree-node-icon-item">
          <PermissionButton type="text" disabled={item.enableFlag !== 'Y'}>
            <Popconfirm
              placement="left"
              title={iconText.confirmDelete}
              okText={iconText.confirm}
              cancelText={iconText.cancel}
              onConfirm={e => {
                iconEventClick(e, 'delete');
              }}
            >
              <img
                className={
                  item.enableFlag !== 'Y' ? 'available-img un-available-img' : 'available-img'
                }
                src={iconDelete}
                alt=""
              />
            </Popconfirm>
          </PermissionButton>
          {item.enableFlag === 'Y' && <div className="node-info">{iconText.delete}</div>}
        </span>
      )}
    </span>
  );
}

export default connect(({ relationMaintain }) => {
  return relationMaintain;
})(treeNodeIcon);
