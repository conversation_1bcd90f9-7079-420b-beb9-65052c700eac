/**
 * @Description: 物料防呆防错配置-详情页
 * @Author: <EMAIL>
 * @Date: 2023-03-14 10:19:56
 */

import React, { useState, useMemo, useEffect } from 'react';
import { Button, Form, TextField, DataSet, Switch, Select, Spin, Modal } from 'choerodon-ui/pro';
import { Tabs, Collapse } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';
import { detailDS, dataItemDS, assObjectsDS, copyDS } from '../stores/MaterialPreventErrorDS';
import DataItemInfoTab from './DataItemInfoTab';
import AssociatedObjectsTab from './AssociatedObjectsTab';
import CopyDrawer from './CopyDrawer';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.materialPreventError';
const tenantId = getCurrentOrganizationId();

const { TabPane } = Tabs;

const MaterialPreventErrorDetail = props => {
  const { history } = props;
  const { id } = props.match.params;
  const [canEdit, setCanEdit] = useState(false);
  const [tableHeight, setTableHeight] = useState(300);
  const [activeKey, setActiveKey] = useState('dataItemInfo');
  const [detailData, setDetailData] = useState({});
  const [agingDemension, setAgingDemension] = useState([]); // 时效维度值集
  const [statusDemension, setStatusDemension] = useState([]); // 状态维度值集
  const dataItemDs1 = useMemo(() => new DataSet(dataItemDS()), []); // 生效时长
  const dataItemDs2 = useMemo(() => new DataSet(dataItemDS()), []); // 预处理前静置时间
  const dataItemDs3 = useMemo(() => new DataSet(dataItemDS()), []); // 预处理时间
  const dataItemDs4 = useMemo(() => new DataSet(dataItemDS()), []); // 预处理后静置时间
  const dataItemDs5 = useMemo(() => new DataSet(dataItemDS()), []); // 状态维度

  const copyDs = useMemo(() => new DataSet(copyDS()), []); // 复制ds
  const assObjectsDs = useMemo(() => new DataSet(assObjectsDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  let _copyDrawer;

  useEffect(() => {
    // eslint-disable-next-line no-console
    console.log('测试发布11');
    setTableHeight(window.innerHeight - 470 > 300 ? window.innerHeight - 470 : 300);
    if (props.match.params.id === 'create') {
      setCanEdit(true);
      return;
    }
    queryDetail(props.match.params.id);
  }, [props.match.params.id]);

  // 查询详情
  const queryDetail = async kid => {
    detailDs.setQueryParameter('strategyId', kid);
    const res = await detailDs.query();
    if (res && !res.failed) {
      setDetailData(res);
      const { itemList, objectList } = res;
      // 配置项塞值
      const newAgingDemension = [];
      // eslint-disable-next-line no-unused-expressions
      itemList &&
        itemList.forEach(item => {
          if (item.strategyItem === '生效时长') {
            newAgingDemension.push('生效时长');
            dataItemDs1.loadData([{ ...item, ...item.detailList[0] }]);
          }
          if (item.strategyItem === '预处理前静置时间') {
            newAgingDemension.push('预处理前静置时间');
            dataItemDs2.loadData([{ ...item, ...item.detailList[0] }]);
          }
          if (item.strategyItem === '预处理时间') {
            newAgingDemension.push('预处理时间');
            dataItemDs3.loadData([{ ...item, ...item.detailList[0] }]);
          }
          if (item.strategyItem === '预处理后静置时间') {
            newAgingDemension.push('预处理后静置时间');
            dataItemDs4.loadData([{ ...item, ...item.detailList[0] }]);
          }
          if (item.strategyItem === '预处理状态') {
            setStatusDemension(['预处理状态']);
            dataItemDs5.loadData([
              {
                ...item,
                ...item.detailList[0],
                pretreatmentStatus: item.detailList[0] && item.detailList[0].detailValue,
              },
            ]);
          }
        });
      setAgingDemension(newAgingDemension);
      // 关联对象塞值
      const newObjectList = [];
      objectList.forEach((item, index) => {
        newObjectList.push({
          ...item,
          lineNumber: index + 1,
        });
      });
      assObjectsDs.loadData(newObjectList);
    } else {
      notification.error({ message: res.message });
    }
  };

  // 取消
  const handleCancel = () => {
    if (id === 'create') {
      history.push('/hmes/material-prevent-error/list');
      return;
    }
    queryDetail(id);
    setCanEdit(prev => !prev);
  };

  // 保存
  const handleSave = async () => {
    const validate = await detailDs.validate();
    const validate1 = await dataItemDs1.validate();
    const validate2 = await dataItemDs2.validate();
    const validate3 = await dataItemDs3.validate();
    const validate4 = await dataItemDs4.validate();
    if (!(validate && validate1 && validate2 && validate3 && validate4)) {
      return;
    }
    detailDs.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验
    // 关联对象参数
    const tagGroupObjectList = assObjectsDs.toData().map(item => {
      const obj = {
        ...item,
        tagGroupObjectId: item.tagGroupObjectId,
      };
      (item.objectShowList || []).forEach(it => {
        if (it.objectType === 'MATERIAL') {
          obj.materialId = it.objectId;
          obj.materialCode = it.objectCode;
          obj.materialName = it.objectDecs;
        } else if (it.objectType === 'OPERATION') {
          obj.operationId = it.objectId;
          obj.operationName = it.objectCode;
          obj.operationDesc = it.objectDecs;
          obj.revisionCode = it.objectRevision;
        } else if (it.objectType === 'WORKCELL') {
          obj.workcellId = it.objectId;
          obj.workcellCode = it.objectCode;
          obj.workcellName = it.objectDecs;
        } else if (it.objectType === 'AREA') {
          obj.areaId = it.objectId;
          obj.areaCode = it.objectCode;
          obj.areaName = it.objectDecs;
        } else if (it.objectType === 'PROD_LINE') {
          obj.prodLineId = it.objectId;
          obj.prodLineCode = it.objectCode;
          obj.prodLineName = it.objectDecs;
        } else if (it.objectType === 'MATERIAL_CATEGORY') {
          obj.materialCategoryId = it.objectId;
          obj.categoryCode = it.objectCode;
          obj.description = it.objectDecs;
        } else if (it.objectType === 'COM_MATERIAL_CATEGORY') {
          obj.comMaterialCategoryId = it.objectId;
          obj.comCategoryCode = it.objectCode;
          // obj.description = it.objectDecs;
        } else if (it.objectType === 'COM_MATERIAL') {
          obj.comMaterialId = it.objectId;
          obj.comMaterialCode = it.objectCode;
          obj.comMaterialName = it.objectDecs;
        }
      });
      return obj;
    });
    // 配置项参数
    const newItemList = [];
    agingDemension.forEach(item => {
      if (item === '生效时长' && dataItemDs1.toData()[0]?.detailValue) {
        newItemList.push({
          ...dataItemDs1.toData()[0],
          dimension: 'AGING_DIMENSION',
          // strategyItem: '生效时长',
          strategyItem: 'TIME_IN_FORCE',
          detailList: [
            {
              ...(dataItemDs1.toData()[0]?.detailList && dataItemDs1.toData()[0]?.detailList[0]),
              uomId: dataItemDs1.toData()[0]?.uomId,
              detailValue: dataItemDs1.toData()[0]?.detailValue,
            },
          ],
        });
      }
      if (item === '预处理前静置时间' && dataItemDs2.toData()[0]?.detailValue) {
        newItemList.push({
          ...dataItemDs2.toData()[0],
          dimension: 'AGING_DIMENSION',
          // strategyItem: '预处理前静置时间',
          strategyItem: 'PRE_TREATMENT_TIME',
          detailList: [
            {
              ...(dataItemDs2.toData()[0]?.detailList && dataItemDs2.toData()[0]?.detailList[0]),
              uomId: dataItemDs2.toData()[0]?.uomId,
              detailValue: dataItemDs2.toData()[0]?.detailValue,
            },
          ],
        });
      }
      if (item === '预处理时间' && dataItemDs3.toData()[0]?.detailValue) {
        newItemList.push({
          ...dataItemDs3.toData()[0],
          dimension: 'AGING_DIMENSION',
          // strategyItem: '预处理时间',
          strategyItem: 'TREATMENT_TIME',
          detailList: [
            {
              ...(dataItemDs3.toData()[0]?.detailList && dataItemDs3.toData()[0]?.detailList[0]),
              uomId: dataItemDs3.toData()[0]?.uomId,
              detailValue: dataItemDs3.toData()[0]?.detailValue,
            },
          ],
        });
      }
      if (item === '预处理后静置时间' && dataItemDs4.toData()[0]?.detailValue) {
        newItemList.push({
          ...dataItemDs4.toData()[0],
          dimension: 'AGING_DIMENSION',
          // strategyItem: '预处理后静置时间',
          strategyItem: 'AFTER_TREATMENT_TIME',
          detailList: [
            {
              ...(dataItemDs4.toData()[0]?.detailList && dataItemDs4.toData()[0]?.detailList[0]),
              uomId: dataItemDs4.toData()[0]?.uomId,
              detailValue: dataItemDs4.toData()[0]?.detailValue,
            },
          ],
        });
      }
    });
    if (statusDemension[0] && statusDemension[0] === '预处理状态') {
      newItemList.push({
        pretreatmentStatus: dataItemDs5.toData()[0]?.pretreatmentStatus || 'N',
        dimension: 'STATUS_DIMENSION',
        //  strategyItem: '预处理状态',
        strategyItem: 'TREATMENT_STATUS',
        detailList: [
          {
            ...(dataItemDs5.toData()[0]?.detailList && dataItemDs5.toData()[0]?.detailList[0]),
            detailValue: dataItemDs5.toData()[0]?.pretreatmentStatus || 'N',
          },
        ],
      });
    }
    // eslint-disable-next-line no-console
    console.log(detailDs.toData()[0]);
    // 保存参数
    const requestData = {
      ...detailDs.toData()[0],
      siteId: detailDs.toData()[0].siteId || detailDs.toData()[0].siteCode,
      itemList: newItemList,
      objectList: tagGroupObjectList,
    };
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-err-proofing-strategys/strategy/update/ui`,
      {
        method: 'POST',
        body: requestData,
      },
    );
    if (getResponse(res)) {
      notification.success();
      const newKid = res;
      setCanEdit(prev => !prev);
      if (id === 'create') {
        history.push(`/hmes/material-prevent-error/detail/${newKid}`);
        return;
      }
      queryDetail(id);
    }
  };

  // 更改tab页
  const handleChangeTab = newActiveKey => {
    setActiveKey(newActiveKey);
  };

  // 确认复制
  const copyTagGroup = async () => {
    const validate = await copyDs.validate();
    if (!validate) {
      return;
    }
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-err-proofing-strategys/strategy/update/ui`,
      {
        method: 'POST',
        body: { ...copyDs.toData()[0], ...detailData },
      },
    );
    if (getResponse(res)) {
      const newKid = res;
      _copyDrawer.close();
      history.push(`/hmes/material-prevent-error/detail/${newKid}`);
      queryDetail(newKid);
    }
  };

  // 打开复制弹框
  const handleCopy = () => {
    copyDs.create({ strategyId: id });
    _copyDrawer = Modal.open({
      closable: true,
      key: Modal.key(),
      title: intl.get('tarzan.common.button.copy').d('复制'),
      drawer: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal',
      children: <CopyDrawer record={copyDs.current} />,
      footer: (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              copyDs.remove(copyDs.current);
              _copyDrawer.close();
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            type="submit"
            onClick={() => {
              copyTagGroup();
            }}
            color={ButtonColor.primary}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  const childProps = {
    canEdit,
    tableHeight,
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.hmes.materialPreventError.collectionGroupMaintenance')
          .d('物料防呆防错配置')}
        backPath="/hmes/material-prevent-error/list"
      >
        {canEdit && (
          <>
            <Button
              style={{ marginLeft: '8px' }}
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button style={{ marginLeft: '8px' }} icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <Button
            style={{ marginLeft: '8px' }}
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        )}
        <Button
          style={{ marginLeft: '8px' }}
          icon="content_copy-o"
          disabled={id === 'create' || canEdit}
          onClick={handleCopy}
        >
          {intl.get('tarzan.common.button.copy').d('复制')}
        </Button>
      </Header>
      <Content>
        <Spin dataSet={detailDs}>
          <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
            <Panel
              header={intl.get(`${modelPrompt}.baseInfo`).d('基础信息')}
              key="basicInfo"
              dataSet={detailDs}
            >
              <Form disabled={!canEdit} dataSet={detailDs} columns={3}>
                <TextField name="strategyCode" />
                <TextField name="strategyDesc" />
                <TextField name="remark" />
                <Select name="siteId" />
                <Switch name="enableFlag" />
                <Select name="businessType" />
              </Form>
            </Panel>
          </Collapse>
          <Tabs onChange={handleChangeTab} activeKey={activeKey}>
            <TabPane
              tab={intl.get(`${modelPrompt}.dataItemInfo`).d('配置项')}
              key="dataItemInfo"
              forceRender
            >
              <DataItemInfoTab
                {...childProps}
                dataItemDs1={dataItemDs1}
                dataItemDs2={dataItemDs2}
                dataItemDs3={dataItemDs3}
                dataItemDs4={dataItemDs4}
                dataItemDs5={dataItemDs5}
                agingDemension={agingDemension} // 时效维度
                setAgingDemension={setAgingDemension}
                statusDemension={statusDemension} // 状态维度
                setStatusDemension={setStatusDemension}
              />
            </TabPane>
            <TabPane
              tab={intl.get(`${modelPrompt}.assObjects`).d('关联对象')}
              key="assObjects"
              forceRender
            >
              <AssociatedObjectsTab
                {...childProps}
                assObjectsDs={assObjectsDs}
                detailDs={detailDs}
              />
            </TabPane>
          </Tabs>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.materialPreventError', 'tarzan.common'],
})(MaterialPreventErrorDetail);
