/**
 * @Description: 容器管理平台-历史查询抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-04-07 10:46:26
 * @LastEditTime: 2023-05-18 16:08:12
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { Badge } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import { Table } from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';

export default ({ ds, customizeTable }) => {
  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'eventId',
        width: 120,
      },
      {
        name: 'eventTypeCode',
        width: 140,
      },
      {
        name: 'eventTypeDesc',
        width: 140,
      },
      {
        name: 'eventRequestId',
        width: 150,
      },
      {
        name: 'requestTypeCode',
        width: 150,
      },
      {
        name: 'requestTypeDesc',
        width: 140,
      },
      {
        name: 'eventBy',
        width: 120,
      },
      {
        name: 'eventTime',
        width: 180,
        align: ColumnAlign.center,
      },
      {
        name: 'identification',
        width: 240,
      },
      {
        name: 'containerCode',
        width: 240,
      },
      {
        name: 'containerClassificationDesc',
        width: 120,
        align: ColumnAlign.center,
      },
      {
        name: 'statusDesc',
        width: 120,
        align: ColumnAlign.center,
      },
      {
        name: 'siteCode',
        width: 180,
      },
      {
        name: 'locatorCode',
        width: 180,
      },
      {
        name: 'lastLoadTime',
        width: 180,
        align: ColumnAlign.center,
      },
      {
        name: 'lastUnloadTime',
        width: 180,
        align: ColumnAlign.center,
      },
      {
        name: 'ownerTypeDesc',
        width: 120,
      },
      {
        name: 'ownerCode',
        width: 180,
      },
      {
        name: 'reservedFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      {
        name: 'reservedObjectTypeDesc',
        width: 120,
      },
      {
        name: 'reservedObjectCode',
        width: 180,
      },
      {
        name: 'currentContainerCode',
        width: 180,
      },
      {
        name: 'topContainerCode',
        width: 180,
      },
      {
        name: 'printTimes',
        width: 180,
      },
      {
        name: 'creationDate',
        width: 180,
      },
      {
        name: 'createdByName',
        width: 180,
      },
      {
        name: 'lastUpdateDate',
        width: 180,
      },
      {
        name: 'lastUpdatedByName',
        width: 180,
      },
      {
        name: 'containerName',
        width: 180,
      },
      {
        name: 'description',
        width: 180,
      },
    ];
  }, []);

  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.HISTORY`,
    },
    <Table dataSet={ds} columns={columns} />,
  )
};
