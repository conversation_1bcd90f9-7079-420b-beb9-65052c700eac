/**
 * @Description: 消息处理查询-services
 * @Author: <<EMAIL>>
 * @Date: 2022-02-08 14:41:36
 * @LastEditTime: 2023-03-06 14:46:24
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

// 消息处理查询-消息记录重新执行发送
export function ResendMessage(servicesCode) {
  return {
    url: `/${servicesCode}/v1/${tenantId}/mt-message-send-records/resend`,
    method: 'POST',
  };
}

// 消息处理查询-获取消息内容
export function FetchMessageContent(servicesCode, messageType) {
  if (messageType === 'send') {
    return {
      url: `/${servicesCode}/v1/${tenantId}/mt-message-send-records/detail/ui`,
      method: 'POST',
    };
  }
  return {
    url: `/${servicesCode}/v1/${tenantId}/mt-message-receive-records/detail/ui`,
    method: 'POST',
  };
}

// 异常处理
export function KafkaMsg(servicesCode) {
  return {
    url: `/${servicesCode}/v1/${tenantId}/kafka/redeal`,
    method: 'POST',
  };
}
