/**
 * 报检请求管理平台-入口文件
 */
import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import {
  Button,
  DataSet,
  Modal,
  Table,
  Form,
  TextField,
  Lov,
  Select,
  NumberField,
} from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import myInstance from '@utils/myAxios';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from 'hcm-components-front/lib/utils/config';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { headerTableDS, formDS } from './stores/ListDS';
import styles from './index.less';
import CreateMaterial from './CreateMaterialDrawer';

const modelPrompt = 'tarzan.hmes.inspection.inspection-management';
const tenantId = getCurrentOrganizationId();

let modalMaterial;
// let createDrawer;

const Order = observer(props => {
  const {
    headerTableDs,
    formDs,
    match: { path },
    location: { state },
  } = props;
  // 头列表配置
  const tableColumns = [
    { name: 'inspectRequestCode', width: 150, lock: ColumnLock.left },
    { name: 'siteCode', width: 220 },
    // { name: 'businessTypeDesc', width: 120 },
    { name: 'inspectBusinessTypeDesc', width: 120 },
    {
      name: 'inspectRequestStatusDesc',
      width: 120,
      renderer: ({ record }) => {
        if (record!.get('inspectRequestStatus') === 'NEW') {
          return <Tag color="blue">{intl.get(`${modelPrompt}.new`).d('新建')}</Tag>;
        }
        if (record!.get('inspectRequestStatus') === 'CANCEL') {
          return <Tag color="red">{intl.get(`${modelPrompt}.cancel`).d('取消')}</Tag>;
        }

        if (record!.get('inspectRequestStatus') === 'COMPLETED') {
          return <Tag color="green">{intl.get(`${modelPrompt}.complete`).d('完成')}</Tag>;
        }
        if (record!.get('inspectRequestStatus') === 'FIRST_COMPLETED') {
          return (
            <Tag color="yellow">{intl.get(`${modelPrompt}.firstComplete`).d('已初检完成')}</Tag>
          );
        }
        if (record!.get('inspectRequestStatus') === 'INSPECTING') {
          return <Tag color="geekblue">{intl.get(`${modelPrompt}.inspecting`).d('检验中')}</Tag>;
        }
        if (record!.get('inspectRequestStatus') === 'REINSPECTING') {
          return <Tag color="orange">{intl.get(`${modelPrompt}.reinspecting`).d('复检中')}</Tag>;
        }
        if (record!.get('inspectRequestStatus') === 'LAST_COMPLETED') {
          return <Tag color="gray">{intl.get(`${modelPrompt}.lastCompleted`).d('检验完成')}</Tag>;
        }
        return <Tag color="pink">{record!.get('inspectRequestStatusDesc')}</Tag>;
      },
    },
    // {
    //   name: 'urgentFlag',
    //   align: ColumnAlign.center,
    //   renderer: ({ record }) => (
    //     <Badge
    //       status={record?.get('urgentFlag') === 'Y' ? 'success' : 'error'}
    //       text={
    //         record?.get('urgentFlag') === 'Y'
    //           ? intl.get(`tarzan.common.label.yes`).d('是')
    //           : intl.get(`tarzan.common.label.no`).d('否')
    //       }
    //     />
    //   ),
    // },
    { name: 'quantity', align: ColumnAlign.right, width: 120 },
    { name: 'uomName', width: 120 },
    { name: 'materialCode', width: 120 },
    { name: 'materialName', width: 180 },
    { name: 'revisionCode', width: 120 },
    { name: 'equipmentCode', width: 120 },
    { name: 'prodLineCode', width: 120 },
    // { name: 'sourceObjectTypeDesc', width: 130 },
    // { name: 'sourceNumber', width: 200, align: ColumnAlign.left,
    //   renderer: ({ record }) => (
    //     <>
    //       {record.get('sourceObjectCode')}{record.get('sourceObjectLineCode') && '#'}{record.get('sourceObjectLineCode')}
    //     </>
    //   ),
    // },
    // {
    //   name: 'inspectDocNum',
    //   width: 200,
    //   renderer: ({ record }) => (
    //     <a onClick={() => handleToInspectDetail(record)}>{record!.get('inspectDocNum')}</a>
    //   ),
    // },
    // { name: 'locatorCode', width: 150 },
    // { name: 'locatorName', width: 150 },
    // { name: 'lot', width: 150 },
    // { name: 'supplierLot', width: 150 },
    { name: 'inspectReqUserName', width: 150 },
    { name: 'inspectReqCreationDate', width: 150, align: ColumnAlign.center },
    { name: 'okQty', width: 150, align: ColumnAlign.right },
    { name: 'ngQty', width: 150, align: ColumnAlign.right },
    // { name: 'scrapQty', width: 150, align: ColumnAlign.right },
    // { name: 'inspectorName', width: 150 },
    // { name: 'inspectReqCompleteUserName', width: 150 },
    // { name: 'inspectReqCompleteDate', width: 150, align: ColumnAlign.center },
    // { name: 'inspectReqCancelUserName', width: 150 },
    // { name: 'inspectReqCancelDate', width: 150, align: ColumnAlign.center },
    {
      title: intl.get(`tarzan.common.label.action`).d('操作'),
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <PermissionButton
          type="text"
          permissionList={[
            {
              code: `list.table.inspectDetail`,
              type: 'button',
              meaning: '列表页-报检明细按钮',
            },
          ]}
          onClick={() => handleOpenDetailModal(record)}
        >
          {intl.get(`${modelPrompt}.detail`).d('报检明细')}
        </PermissionButton>
      ),
    },
  ];

  useEffect(() => {
    if (headerTableDs) {
      if (state && state.data) {
        headerTableDs.queryDataSet.create({
          identification: state.data.identification,
        });
      }
      headerTableDs.query()
    }
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 查询条件更新时操作
      handler.call(headerTableDs, 'load', handleDataSetSelectUpdate);
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'select', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'unSelect', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'selectAll', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'unSelectAll', handleDataSetSelectUpdate);
    }
  };

  const handleDataSetSelectUpdate = () => {
    const _inspectRequestIdList: string[] = [];
    headerTableDs.selected.forEach(item => {
      const { inspectRequestId } = item.toData();
      _inspectRequestIdList.push(inspectRequestId);
    });
    // setInspectRequestIds(_inspectRequestIdList);
  };

  const handleOpenDetailModal = record => {
    modalMaterial = Modal.open({
      title: intl.get(`${modelPrompt}.detail`).d('报检明细'),
      className: 'hmes-style-modal',
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      onClose: () => {
        modalMaterial.close();
      },
      style: {
        width: 1080,
      },
      children: (
        <CreateMaterial
          inspectRequestId={record.data.inspectRequestId}
          siteId={record.data.siteId}
        />
      ),
      footer: (
        <Button onClick={() => modalMaterial.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // const handleToInspectDetail = record => {
  //   props.history.push(`/hwms/inspect-doc-maintain/dist/${record.get('inspectDocId')}`);
  // };

  // const disposal = () => {
  //   const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inspect-requests/disposal/execute/ui`;
  //   myInstance
  //     .post(url, {
  //       inspectRequestIdList: inspectRequestIds,
  //     })
  //     .then(res => {
  //       if (res.data.success) {
  //         notification.success({});
  //         headerTableDs.query();
  //       } else if (res.data.message) {
  //         notification.error({
  //           description: res.data.message,
  //         });
  //       }
  //     });
  // };

  // // lov变化事件
  // const changeObject = (lovRecords, record) => {
  //   if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
  //     record.getField('revisionCode').set('disabled', false);
  //     record.set('revisionFlag', 'Y');
  //     record.set('revisionCode', lovRecords.revisionCode);
  //   } else {
  //     record.getField('revisionCode').set('disabled', true);
  //     record.set('revisionCode', '');
  //     record.set('revisionFlag', 'N');
  //   }
  // };

  // // 版本下拉框变化事件
  // const changeVersion = record => {
  //   if (record.data.revisionFlag === 'Y') {
  //     record.getField('revisionCode').set('disabled', false);
  //   }
  // };

  const create = () => {
    formDs.create({});
    const modalKey = Modal.key();
    Modal.open({
      key: modalKey,
      drawer: true,
      width: 400,
      title: intl.get(`${modelPrompt}.inspectCreate`).d('新建'),
      closable: true,
      children: (
        <Form dataSet={formDs} columns={1}>
          <TextField name="inspectRequestCode" />
          <Lov name="siteLov" />
          <Select name="inspectBusinessType" />
          <Lov name="material" />
          <Select name="revisionCode" />
          <Lov name="equipObj" onChange={handleEquip} />
          <TextField name="prodLineCode" />
          <NumberField name="quantity" />
        </Form>
      ),
      onOk: () => createLine(),
      onCancel: () => cancelLine(),
    });
  };

  const cancelLine = () => {
    formDs.reset();
  };

  const createLine = async () => {
    const validate = await formDs.validate();
    if (!validate) {
      return false;
    }
    const data = formDs.toData();
    const result = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inspect-requests/save/ui`, {
      // const result = await request(`/yp-mes-38510/v1/${tenantId}/mt-inspect-requests/save/ui`, {
      method: 'POST',
      body: { ...data[0] },
    });
    if (result.success) {
      notification.success({});
      formDs.reset();
      headerTableDs.query();
    } else {
      notification.error({
        description: result.message,
      });
      return false;
    }
  };

  const handleEquip = async record => {
    if (record && record.equipmentCode) {
      const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inspect-requests/get/prod?equipmentCode=${record.equipmentCode}`;
      const res = await myInstance.get(url);
      if (res && !res.data.failed) {
        formDs.current.set('prodLineId', res.data.organizationId);
        formDs.current.set('prodLineCode', res.data.organizationCode);
      } else {
        notification.error({ message: res.data.message });
        formDs.current.set('prodLineId', null);
        formDs.current.set('prodLineCode', '');
      }
    } else {
      formDs.current.set('prodLineId', null);
      formDs.current.set('prodLineCode', '');
    }
  };

  // @ts-ignore
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('报检平台')}>
        {/* <PermissionButton
          type="c7n-pro"
          icon="cached"
          disabled={!inspectRequestIds.length}
          onClick={() => disposal()}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.dispose`).d('处置')}
        </PermissionButton> */}
        <PermissionButton
          type="c7n-pro"
          icon="add"
          color={ButtonColor.primary}
          onClick={() => create()}
          permissionList={[
            {
              code: `${path}.button.create`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.create`).d('新建')}
        </PermissionButton>
      </Header>
      <Content className={styles['deliver-content']}>
        <Table
          searchCode="InspectionManagement"
          dataSet={headerTableDs}
          columns={tableColumns as any}
          highLightRow
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
        />
        ,
      </Content>
    </div>
  );
});

export default flow(
  formatterCollections({ code: ['tarzan.hmes.inspection.inspection-management', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const formDs = new DataSet({ ...formDS() });
      return {
        headerTableDs,
        formDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(Order);
