import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.cassetteMaterial';

const productHistoryFactory = () =>
  new DataSet({
    primaryKey: 'hisId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'identification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.identification`).d('条码号'),
      },
      {
        name: 'markingCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.markingCode`).d('标记编码'),
      },
      {
        name: 'statusMeaning',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.statusMeaning`).d('标记状态'),
      },
      {
        name: 'markingLotStatusMeaning',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.markingLotStatusMeaning`).d('条码标记绑定状态'),
      },
      {
        name: 'sourceWayMeaning',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.sourceWayMeaning`).d('标记来源'),
      },
      {
        name: 'markingCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.markingCode`).d('标记编码'),
      },
      {
        name: 'sourceIdentification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.sourceIdentification`).d('来源条码号'),
      },
      {
        name: 'originalIdentification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.originalIdentification`).d('原始条码标识'),
      },
      {
        name: 'typeMeaning',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.typeMeaning`).d('标记类型'),
      },
      {
        name: 'markingContentMeaning',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.markingContentMeaning`).d('标记内容'),
      },
      {
        name: 'enableFlag',
        lookupCode: 'MT.YES_NO',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.enableFlag`).d('有效性'),
      },
      {
        name: 'description',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.description`).d('拦截工艺'),
      },
      {
        name: 'realName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.realName`).d('操作人'),
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'POST',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-identification-markings/his/query`,
        };
      },
    },
  });

export default productHistoryFactory;
