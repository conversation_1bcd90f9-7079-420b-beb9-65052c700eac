import React, { useMemo } from 'react';
import {
  DataSet,
  Table,
  Modal,
  Button,
  Form,
  TextField,
  Lov,
  Select,
  NumberField,
  Switch,
} from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
import intl from 'utils/intl';
import { Record } from 'choerodon-ui/dataset';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { drawerPropsC7n } from '@components/tarzan-ui';
// import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import { lineTableDS } from '../stores/detailPageDS';

const modelPrompt = 'tarzan.hmes.equipmentPointMaintenanceList';

const EditTableInModal = ({ ds, canEdit, siteId }) => {
  const modalDs = useMemo(() => new DataSet(lineTableDS()), []);
  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        header: () => (
          <Button
            icon="add"
            disabled={!canEdit}
            funcType={FuncType.flat}
            onClick={() => handleEdit(null, true)}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        width: 60,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              ds.remove(record);
            }}
          >
            <Button icon="remove" disabled={!canEdit} funcType={FuncType.flat} size={Size.small} />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'materialCode',
        renderer: ({ value, record }) => (<a onClick={() => handleEdit(record, false)}>{value}</a>),
      },
      // 物料描述
      {
        name: 'materialName',
      },
      // 单位
      {
        name: 'uomName',
      },
      // 物料版本
      {
        name: 'revisionCode',
      },
      // 最大装载量
      {
        name: 'maxQty',
      },
      // 有效性
      {
        name: 'enableFlag',
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
    ];
  }, [canEdit, siteId]);

  const handleEdit = (record?: Record | null, isNew?: boolean) => {
    let createFlag = true;
    if (!record) {
      modalDs.loadData([{ siteId, enableFlag: 'Y' }]);
    } else {
      modalDs.loadData([record.toData()]);
      createFlag = false;
    }
    Modal.open({
      ...drawerPropsC7n({ canEdit, modalDs, isNew }),
      title: createFlag
        ? intl.get(`${modelPrompt}.createContainer`).d('新建物料信息')
        : intl.get(`${modelPrompt}.handleEdithandleEdit`).d('编辑物料信息'),
      style: {
        width: 720,
      },
      children: (
        <Form labelWidth={112} dataSet={modalDs} columns={1} disabled={!canEdit}>
          <Lov name="materialLov" disabled={!isNew} />
          <TextField name="materialName" disabled />
          <TextField name="uomName" disabled />
          <Select name="revisionCode" />
          <NumberField name="maxQty" />
          <Switch name="enableFlag" />
        </Form>
      ),
      afterClose: () => {
        modalDs.loadData([]);
      },
      onOk: () =>handleDrawerConfirm(isNew),
    });
  };

  const handleDrawerConfirm = async (isNew) => {
    const validate = await modalDs.validate();
    if (!validate) {
      return false;
    }
    const temp = modalDs.current!.toData();
    if(isNew){
      if(ds.find((record) => record.get('materialId') === temp.materialId)){
        notification.warning({
          message: intl.get(`${modelPrompt}.message.duplicateMaterialCode`).d('物料编码重复'),
        });
        return false;
      }
      ds.create(modalDs.current!.toJSONData(), 0);
    }else{
      ds.find((record) => record.get('materialId') === temp.materialId)?.set(temp);
    }
  };
  return (
    <Table
      dataSet={ds}
      columns={columns}
      filter={record => {
        return record.status !== 'delete';
      }}
    />
  );
};

export default EditTableInModal;

