/**
 * @feature PageHeaderWrapper
 */
import React, { useEffect, useState } from 'react';
import {
  DataSet,
  Tree,
  Button,
  Row,
  Col,
  TextField,
  Form,
  Table,
  Select,
  Output,
} from 'choerodon-ui/pro';
import { Tabs, Tag } from 'choerodon-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import request from 'utils/request';
import { TARZAN_REPORT } from '@/utils/config';
import XLSX from 'xlsx';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import { observer } from 'mobx-react';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import {
  initialDs,
  formDS,
  productHistoryDS,
  rawMaterialBarcodeDS,
  productParameterDS,
  ncRecordQueryDS,
  equipmentParamCollectDS,
  inspectReportDS,
} from './stories/InitialDs';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

const modelPrompt = 'tarzan.inventory.initial.model';

const TabPane = Tabs.TabPane;
const tenantId = getCurrentOrganizationId();

/**
 * 头行结构的表单示例
 */
const Initial = observer(props => {
  const {
    dataSet,
    formDs,
    productHistoryDs,
    rawMaterialBarcodeDs,
    productParameterDs,
    ncRecordQueryDs,
    equipmentParamCollectDs,
    inspectReportDs,
    match
  } = props;
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [selectedRow, setSelectedRow] = useState([]);
  const [tabKey, setTabKey] = useState('1');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [disabled, setDisabled] = useState(true);
  const [formData, setFormData] = useState({});

  useEffect(() => {
    if (match.params && match.params.code && dataSet?.queryDataSet) {
      dataSet?.queryDataSet.loadData([{ processBarcodeListStr: match.params.code,orderType:'positive' }])
    }
  }, [match,])

  const handleSearch = async () => {
    const { processBarcodeListStr, orderType } = dataSet?.queryDataSet?.toData()[0];
    if (!processBarcodeListStr || !orderType) {
      notification.error({
        message: intl.get(`${modelPrompt}.pleaseEnterQueryCriteria`).d('请输入查询条件'),
      });
      return;
    }
    dataSet?.queryDataSet.current.set('processBarcodeList', [processBarcodeListStr]);
    dataSet.query();
    setSelectedKeys([]);
    formDs.create({});
    productHistoryDs.loadData([]);
    rawMaterialBarcodeDs.loadData([]);
    productParameterDs.loadData([]);
    ncRecordQueryDs.loadData([]);
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
    }
  };

  const nodeRenderer = ({ record }) => {
    return `${record.get('traceLevelMeaning')}-${record.get('identification')}`;
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: dataSet,
    onOpenInputModal,
  };

  const productHistoryColumns = [
    {
      name: 'identification',
      lock: 'left',
      width: 150,
    },
    {
      name: 'eoQty',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'workOrderCode',
    },
    {
      name: 'sequence',
    },
    {
      name: 'operationName',
    },
    {
      name: 'operationDesc',
    },
    {
      name: 'wipStatusDesc',
    },
    {
      name: 'qty',
    },
    {
      name: 'equipmentCode',
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'workcellCode',
    },
    {
      name: 'workcellName',
    },
    {
      name: 'reworkFlag',
    },
    {
      name: 'shiftCodeDesc',
    },
    {
      name: 'shiftDate',
    },
    {
      name: 'createdByRealName',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'prodLineCode',
    },
    {
      name: 'prodLineName',
    },
  ];

  const rawMaterialBarcodeColumns = [
    {
      name: 'parentIdentification',
      width: 200,
      lock: 'left',
    },
    {
      name: 'parentMaterialCode',
      width: 150,
    },
    {
      name: 'parentMaterialName',
      width: 150,
    },
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'materialLotIdentification',
      width: 150,
    },
    {
      name: 'materialLotSupplier',
      width: 150,
    },
    {
      name: 'assembleQty',
      width: 150,
    },
    {
      name: 'operationName',
      width: 150,
    },
    {
      name: 'description',
      width: 150,
    },
    {
      name: 'workcellCode',
      width: 150,
    },
    {
      name: 'workcellName',
      width: 150,
    },
    {
      name: 'loginName',
    },
    {
      name: 'creationDate',
    },
  ];

  const productParameterColumns = [
    {
      name: 'identification',
      lock: 'left',
      width: 200,
    },
    {
      name: 'equipmentCode',
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'tagCode',
    },
    {
      name: 'tagDescription',
    },
    {
      name: 'tagValue',
    },
    {
      name: 'tagCalculateResultMeaning',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'createByName',
    },
    {
      name: 'tagGroupCode',
    },
    {
      name: 'tagGroupDescription',
    },
    {
      name: 'operationName',
    },
    {
      name: 'operationDescription',
    },
    {
      name: 'workCellCode',
    },
    {
      name: 'workCellName',
    },
    {
      name: 'recordRemark',
    },
  ];

  const ncRecordQueryColumns = [
    {
      name: 'siteCode',
      width: 200,
    },
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'eoNum',
      width: 150,
    },
    {
      name: 'materialLotCode',
      width: 150,
    },
    {
      name: 'ncRecodeTypeDesc',
      width: 150,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'ncCode',
      width: 150,
    },
    {
      name: 'ncCodeName',
      width: 150,
    },
    {
      name: 'ncCodeStatusDesc',
      width: 150,
    },
    {
      name: 'workcellName',
      width: 150,
    },
    {
      name: 'equipmentName',
      width: 150,
    },
    {
      name: 'operationDesc',
      width: 150,
    },
    {
      name: 'ncRecordTime',
      width: 150,
    },
    {
      name: 'ncRecordUserIdRealName',
      width: 150,
    },
    {
      name: 'ncRecordClosedTime',
      width: 150,
    },
    {
      name: 'ncRecordClosedRealName',
      width: 150,
    },
  ];

  const equipmentParamCollectColumns = [
    {
      name: 'equipmentCode',
      lock: 'left',
      width: 200,
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'tagCode',
    },
    {
      name: 'tagDescription',
    },
    {
      name: 'tagValue',
    },
    {
      name: 'tagCalculateResultMeaning',
    },
    {
      name: 'trueValue',
      width: 200,
      renderer: ({ value, record }) => {
        if (record.get('valueType') === 'VALUE') {
          if (record.get('trueValueList') && record.get('trueValueList').length) {
            const temp = record.get('trueValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue && <Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {
      name: 'falseValue',
      width: 200,
      renderer: ({ value, record }) => {
        if (record.get('valueType') === 'VALUE') {
          if (record.get('falseValueList') && record.get('falseValueList').length) {
            const temp = record.get('falseValueList')
            return <>
              {
                temp.map(item => {
                  return (item.dataValue && <Tag color="geekblue">{item.dataValue}</Tag>)
                })
              }</>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {
      name: 'recordDate',
    },
    {
      name: 'createByName',
    },
    {
      name: 'identification',
    },
    {
      name: 'tagGroupCode',
    },
    {
      name: 'tagGroupDescription',
    },
    {
      name: 'uomCode',
    },
    {
      name: 'uomName',
    },
    {
      name: 'creationDate',
      width: 150,
      align: 'center',
    },
    {
      name: 'lastUpdateDate',
      width: 150,
      align: 'center',
    },
  ];

  const renderValueList = (name, record) => {
    const value = record.get(name);
    switch (record.get('dataType')) {
      case 'VALUE':
      case 'VALUE_LIST':
      case 'CALCULATE_FORMULA':
        return (
          value?.length &&
          value[0].dataValue &&
          value.map(item => (
            <Tag className="hcm-tag-blue" key={item.dataValue}>
              {item.dataValue}
            </Tag>
          ))
        );
      default:
        return value?.length && value[0].dataValue;
    }
  };

  const inspectReportColumns = [
    { name: 'materialCode' },
    { name: 'materialName', width: 150 },
    { name: 'sourceObjectTypeDesc', width: 150 },
    { name: 'sourceObjectCode', width: 150 },
    { name: 'operationName', width: 150 },
    { name: 'supplierName', width: 150 },
    { name: 'prodLineName', width: 150 },
    { name: 'processWorkcellName', width: 150 },
    { name: 'equipmentName', width: 150 },
    { name: 'creationDate', width: 150, align: 'center' },
    { name: 'lineInspectResultDesc', width: 150, align: 'center' },
    { name: 'dtlInspectResultDesc', width: 150, align: 'center' },
    { name: 'inspectorName' },
    { name: 'inspectValue' },
    { name: 'inspectDocNum', width: 180 },
    { name: 'inspectBusinessTypeDesc', width: 150 },
    { name: 'inspectItemCode', width: 150 },
    { name: 'inspectItemDesc', width: 150 },
    { name: 'inspectItemType', width: 150 },
    { name: 'subItemType', width: 150 },
    { name: 'featureType' },
    { name: 'qualityCharacteristicDesc' },
    { name: 'inspectToolDesc' },
    { name: 'inspectMethodDesc' },
    { name: 'dataTypeDesc' },
    {
      name: 'trueValueList',
      width: 200,
      renderer: ({ record, name }) => renderValueList(name, record),
    },
    {
      name: 'falseValueList',
      width: 200,
      renderer: ({ record, name }) => renderValueList(name, record),
    },
    {
      name: 'warningValueList',
      width: 200,
      renderer: ({ record, name }) => renderValueList(name, record),
    },
    {
      name: 'inspectFrequencyDesc',
      align: 'center',
      width: 120,
      renderer: ({ value, record }) => {
        let inspectFrequencyShow = record?.get('inspectFrequencyDesc');
        if (inspectFrequencyShow) {
          inspectFrequencyShow = inspectFrequencyShow.replace('M', record?.get('m') || 'M');
          inspectFrequencyShow = inspectFrequencyShow.replace('N', record?.get('n') || 'N');
          return inspectFrequencyShow;
        }
        return value;
      },
    },
  ];

  const onSelect = async selectedKeys => {
    setSelectedKeys([selectedKeys[selectedKeys.length - 1]]);
    const dataList = dataSet.toData();
    const currentRow = dataList.filter(
      item => Number(item.traceRelId) === Number(selectedKeys[selectedKeys.length - 1]),
    );
    setSelectedRow(currentRow);

    const { orderType } = dataSet?.queryDataSet?.toJSONData()[0];
    const result = await request(
      `${TARZAN_REPORT}/v1/${tenantId}/hme-trace-rel-priors/one/query/ui`,
      {
        method: 'POST',
        body: {
          identification: currentRow[0].identification,
          orderType,
          level: currentRow[0].level,
          traceLevel: currentRow[0].traceLevel,
        },
      },
    );
    if (result) {
      if (result.rows) {
        formDs.create({ ...result.rows, identification: currentRow[0].identification });
        setDisabled(false);
        setFormData(result.rows)
      } else {
        setDisabled(true);
      }
    }
    queryTabs(tabKey, currentRow);
  };

  const handleTabs = key => {
    setTabKey(key);
    if (selectedKeys.length) {
      const row = selectedRow;
      queryTabs(key, row);
    }
  };

  const queryTabs = (key, currentRow) => {
    if (key === '1') {
      productHistoryDs.setQueryParameter('identifications', currentRow[0].identification);
      productHistoryDs.query();
    }
    if (key === '2') {
      rawMaterialBarcodeDs.setQueryParameter('parentIdentifications', currentRow[0].identification);
      rawMaterialBarcodeDs.setQueryParameter('parentTraceLevels', [currentRow[0].traceLevel]);
      rawMaterialBarcodeDs.query();
    }
    if (key === '3') {
      productParameterDs.setQueryParameter('identifications', currentRow[0].identification);
      productParameterDs.query();
    }
    if (key === '4') {
      ncRecordQueryDs.setQueryParameter('identifications', [currentRow[0].identification || '']);
      ncRecordQueryDs.query();
    }

    if (key === '5') {
      equipmentParamCollectDs.setQueryParameter('identifications', currentRow[0].identification);
      equipmentParamCollectDs.query();
    }

    if (key === '6') {
      inspectReportDs.queryDataSet?.current?.set({
        productCode: currentRow[0].identification,
      });
      inspectReportDs.query();
    }
  };

  const handleExport = async () => {
    const res = await request(`${TARZAN_REPORT}/v1/${tenantId}/hme-trace-rel-priors/one/export/ui`, {
      method: "POST",
      responseType: 'blob',
      body: formData,
    })
    if (res) {
      const file = new Blob([res], { type: 'application/vnd.ms-excel' }); // 解析文件
      const fileURL = URL.createObjectURL(file); // 生成文件地址
      const fileName = '一键追溯报表.xls';
      const elink = document.createElement('a'); // 生成a标签
      elink.download = fileName; // a标签下载名
      elink.style.display = 'none'; // 设置a标签隐藏，不在页面展示
      elink.href = fileURL; // 设置a标签下载地址
      document.body.appendChild(elink); // 在body中添加a标签
      elink.click();  // 手动调用a标签点击下载属性
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink); // 在body中删除a标签
    }
  }

  return (
    <div className="hmes-style">
      <PageHeaderWrapper title={intl.get(`${modelPrompt}.OneTraceReport`).d('一键追溯报表')}>
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Col span={18}>
            <Form columns={4} dataSet={dataSet.queryDataSet} labelWidth={120}>
              <TextField
                name="processBarcodeListStr"
              // suffix={
              //   <div className="c7n-pro-select-suffix">
              //     <Icon
              //       type="search"
              //       onClick={() =>
              //         onOpenInputModal(true, 'processBarcodeListStr', '原材料条码', dataSet.queryDataSet)
              //       }
              //     />
              //   </div>
              // }
              />
              <Select name="orderType" />
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                onClick={() => {
                  dataSet.queryDataSet.current.reset();
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
            </div>
          </Col>
        </Row>
        <div style={{ width: '100%' }}>
          <div style={{ width: '40%', float: 'left', marginRight: '3%', overflow: 'auto' }}>
            <Tree
              showLine={{
                showLeafIcon: false,
              }}
              showIcon={false}
              dataSet={dataSet}
              renderer={nodeRenderer}
              onSelect={onSelect}
              selectedKeys={selectedKeys}
            />
          </div>
          <div style={{ width: '55%', float: 'right' }}>
            <Row>
              <Col span={22}>
                <Form dataSet={formDs} columns={3} labelLayout="horizontal">
                  <Output name="identification" />
                  <Output name="materialCode" />
                  <Output name="materialName" />
                  <Output name="revisionCode" />
                  <Output name="status" />
                  <Output name="workStatus" />
                  <Output name="workOrderNum" />
                  <Output name="prodLine" />
                  <Output name="prodLineName" />
                  <Output name="workDate" />
                </Form>
              </Col>
              <Col span={2}>
                <Button color='primary' icon='export' disabled={disabled} onClick={handleExport}>{intl.get(`${modelPrompt}.export.button`).d('导出')}</Button>
              </Col>
            </Row>
            <Tabs defaultActiveKey="1" onChange={handleTabs}>
              <TabPane tab="加工履历" key="1">
                <Table
                  customizedCode="OneTraceReport"
                  dataSet={productHistoryDs}
                  columns={productHistoryColumns}
                  style={{ height: 400 }}
                />
              </TabPane>
              <TabPane tab="原材料追溯" key="2">
                <Table
                  customizedCode="OneTraceReport"
                  dataSet={rawMaterialBarcodeDs}
                  columns={rawMaterialBarcodeColumns}
                  style={{ height: 400 }}
                />
              </TabPane>
              <TabPane tab="加工参数" key="3">
                <Table
                  customizedCode="OneTraceReport"
                  dataSet={productParameterDs}
                  columns={productParameterColumns}
                  style={{ height: 400 }}
                />
              </TabPane>
              <TabPane tab="不良记录" key="4">
                <Table
                  customizedCode="OneTraceReport"
                  dataSet={ncRecordQueryDs}
                  columns={ncRecordQueryColumns}
                  style={{ height: 400 }}
                />
              </TabPane>
              <TabPane tab="设备加工参数" key="5">
                <Table
                  customizedCode="OneTraceReport"
                  dataSet={equipmentParamCollectDs}
                  columns={equipmentParamCollectColumns}
                  style={{ height: 400 }}
                />
              </TabPane>
              <TabPane tab="检验报表" key="6">
                <Table
                  queryBar="none"
                  customizedCode="InspectReport"
                  dataSet={inspectReportDs}
                  columns={inspectReportColumns}
                  style={{ height: 400 }}
                />
              </TabPane>
            </Tabs>
          </div>
        </div>
        <LovModal {...lovModalProps} />
      </PageHeaderWrapper>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.product.processing.parameters.supplement', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...initialDs(),
      });
      const formDs = new DataSet({
        ...formDS(),
      });
      const productHistoryDs = new DataSet({
        ...productHistoryDS(),
      });
      const rawMaterialBarcodeDs = new DataSet({
        ...rawMaterialBarcodeDS(),
      });
      const productParameterDs = new DataSet({
        ...productParameterDS(),
      });
      const ncRecordQueryDs = new DataSet({
        ...ncRecordQueryDS(),
      });
      const equipmentParamCollectDs = new DataSet({
        ...equipmentParamCollectDS(),
      });
      const inspectReportDs = new DataSet({
        ...inspectReportDS(),
      });
      return {
        dataSet,
        formDs,
        productHistoryDs,
        rawMaterialBarcodeDs,
        productParameterDs,
        ncRecordQueryDs,
        equipmentParamCollectDs,
        inspectReportDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Initial),
);
