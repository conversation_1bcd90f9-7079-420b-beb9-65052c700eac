import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import {FieldType} from "choerodon-ui/pro/lib/data-set/enum";

const modelPrompt = 'tarzan.hmes.ShipmentReportPrintMaintenance';

const tenantId = getCurrentOrganizationId();

// const Host1 = `/key-ne-focus-mes-38510`;

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'shipmentReportTagId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    paging: true,
    autoQuery: false,
    selection: 'single',
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-shipment-report/head/query`,
          method: 'POST',
        };
      },
    },
    queryFields: [
      {
        name: 'materialLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
        labelWidth: 150,
        lovCode: 'MT.MATERIAL',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'materialCode',
        multiple: true,
      },
      {
        name: 'materialIdList',
        bind: 'materialLov.materialId',
      },
      {
        name: 'customerLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.customerLov`).d('客户编码'),
        labelWidth: 150,
        lovCode: 'MT.MODEL.CUSTOMER',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'customerCode',
        multiple: true,
      },
      {
        name: 'customerIdList',
        bind: 'customerLov.customerId',
      },
    ],
    fields: [
      {
        name: 'materialLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
        lovCode: 'MT.MATERIAL',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'materialCode',
        labelWidth: 150,
        required: true,
      },
      {
        name: 'materialCode',
        bind: 'materialLov.materialCode',
      },
      {
        name: 'materialId',
        bind: 'materialLov.materialId',
      },
      {
        name: 'materialSiteId',
        bind: 'materialLov.materialSiteId',
      },
      {
        name: 'revisionFlag',
        bind: 'materialLov.revisionFlag',
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        bind: 'materialLov.revisionCode',
        textField: 'revisionCode',
        valueField: 'revisionCode',
        lookupUrl: `${Host}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
        // lookupAxiosConfig: ({ record }) => {
        //   const _params = record?.toData() || {};
        //   if (_params.siteId && _params.materialSiteId) {
        //     return {
        //       params: {
        //         materialSiteId: _params.materialSiteId,
        //         siteId: _params.siteId,
        //       },
        //       transformResponse(data) {
        //         // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
        //         if (data instanceof Array) {
        //           return data;
        //         }
        //         if (data.failed) {
        //           return [];
        //         }
        //         const rows = JSON.parse(data);
        //         return rows;
        //       },
        //     };
        //   }
        // },
        dynamicProps: {
          disabled: ({ record }) => !(record?.get('materialLov')?.revisionFlag && record?.get('materialLov')?.revisionFlag === 'Y'),
        },
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'materialLov.materialName',
      },
      {
        name: 'item',
        type: 'string',
        label: intl.get(`${modelPrompt}.projectItem`).d('项目'),
        required: true,
      },
      {
        name: 'productShortName',
        type: 'string',
        label: intl.get(`${modelPrompt}.productShortName`).d('产品简称'),
        required: true,
      },
      {
        name: 'productModel',
        type: 'string',
        label: intl.get(`${modelPrompt}.productModel`).d('产品型号'),
        required: true,
      },
      {
        name: 'customerLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.customerLov`).d('客户编码'),
        labelWidth: 150,
        lovCode: 'MT.MODEL.CUSTOMER',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'customerCode',
        required: true,
      },
      {
        name: 'customerId',
        bind: 'customerLov.customerId',
      },
      {
        name: 'customerCode',
        bind: 'customerLov.customerCode',
      },
      {
        name: 'customerName',
        type: 'string',
        label: intl.get(`${modelPrompt}.customerName`).d('客户名称'),
        bind: 'customerLov.customerName',
      },
    ],
  };
};

const assObjectsDS = () => {
  return {
    name: 'assObjectsDS',
    primaryKey: 'shipmentReportTagLineId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    paging: true,
    autoQuery: false,
    selection: false,
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-shipment-report/line/query`,
          method: 'POST',
        };
      },
    },
    queryFields: [
      {
        name: 'tagLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.tagLov`).d('收集项编码'),
        labelWidth: 150,
        lovCode: 'HME_MT_TAG',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'tagCode',
        multiple: true,
      },
      {
        name: 'tagIdList',
        bind: 'tagLov.tagId',
      },
    ],
    fields: [
      {
        name: 'serialNumber',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
        required: true,
      },
      {
        name: 'tagLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.tagLov`).d('收集项编码'),
        labelWidth: 150,
        lovCode: 'HME_MT_TAG',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'tagCode',
        required: true,
      },
      {
        name: 'tagId',
        bind: 'tagLov.tagId',
      },
      {
        name: 'tagCode',
        bind: 'tagLov.tagCode',
      },
      {
        name: 'tagDescription',
        bind: 'tagLov.tagDescription',
        label: intl.get(`${modelPrompt}.tagDescription`).d('收集项名称'),
      },
    ],
  };
};

export { tableDS, assObjectsDS };
