/**
 * @Description: 盘点工作台详情页-盘点明细Tab-条码明细drawer
 * @Author: <<EMAIL>>
 * @Date: 2022-02-14 16:05:10
 * @LastEditTime: 2023-01-10 11:00:49
 * @LastEditors: <<EMAIL>>
 */
import React, {useEffect, useState} from 'react';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExportPro';
import {Button, DataSet, Table} from 'choerodon-ui/pro';
import {observer} from 'mobx-react';
import {useRequest} from '@components/tarzan-hooks';
import {ColumnProps} from 'choerodon-ui/pro/lib/table/Column';
import {ColumnAlign} from 'choerodon-ui/pro/lib/table/enum';
import {Badge, Tag} from 'choerodon-ui';
import {getCurrentOrganizationId, getCurrentUser} from 'utils/utils';
import {Button as PermissionButton} from 'components/Permission';
import expandTableBar from './ExpandTableBar';
import styles from "../index.modules.less";
import {AdjustDiffer, SaveSpotCheck,} from '../../services';
import {API_HOST, BASIC} from "@utils/config";

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';

export default observer(
  ({ handleSearch, ds, id, stocktakeStatus, stocktakeId, stockActualIds, selectedStocktakeBarcodeList, path }) => {
    const {run: adjustDiffer } = useRequest(AdjustDiffer(), { manual: true });
    const {run: saveSpotCheck } = useRequest(SaveSpotCheck(), { manual: true, needPromise: true });
    const [canEdit, setCanEdit] = useState(false)

    const handleAdjustDiffer = async (list: any[]) => {
      await adjustDiffer({
        params: {
          diffAdjustInfoList: list,
          stocktakeId: id,
        },
        onSuccess: () => {
          handleSearch()
        },
      })
    };
    const renderHead = () => {
      let total = 0;
      let adjustNumber = 0; // 差异数绝对值相加
      let differNumber = 0; // 差异数相加
      let adjustByName = '';
      if (ds.selected.length) {
        total = ds.selected.length;
        ds.selected.forEach(records => {
          const item = records.data
          const { firstCountQty, reCountQty, currentQuantity } = item;
          if (!firstCountQty && !reCountQty) {
            // 没有进行初复的数据也是有差异的
            adjustNumber += Math.abs(currentQuantity);
            differNumber += -Math.abs(currentQuantity);
          } else {
            adjustNumber +=
              typeof item.reCountDiffQty === 'number'
                ? Math.abs(item.reCountDiffQty)
                : Math.abs(item.firstCountDiffQty);
            differNumber +=
              typeof item.reCountDiffQty === 'number'
                ? item.reCountDiffQty
                : item.firstCountDiffQty;
          }
        });
        adjustByName = getCurrentUser().realName;
      }
      return (
        <>
          <div className={styles['content-item']}>
            {`${intl.get(`${modelPrompt}.total`).d('合计')}: ${total}条`}
          </div>
          <div className={styles['content-item']}>
            {`${intl.get(`${modelPrompt}.adjustNumber`).d('调整数')}: ${adjustNumber}`}
          </div>
          <div className={styles['content-item']}>
            {`${intl.get(`${modelPrompt}.differNumber`).d('差异数')}: ${differNumber}`}
          </div>
          <div className={styles['content-item']}>
            {`${intl.get(`${modelPrompt}.adjustCountByName`).d('调整人')}: ${adjustByName}`}
          </div>
        </>
      );
    };
    useEffect(() => {
      if (['NEW', 'RELEASED'].includes(stocktakeStatus)) {
        ds.queryDataSet.getField('diffFlag').set('disabled', true);
        ds.queryDataSet.getField('adjustFlag').set('disabled', true);
      }
      const materialList: any = [];
      const locatorList: any = [];
      // 将选中盘点明细行的物料和库位信息取出来，用作查询
      selectedStocktakeBarcodeList.forEach(item => {
        const _materialId = `${item.materialId}`;
        const _locatorId = `${item.locatorId}`;
        if (_materialId && !materialList.some(it => it.materialId === _materialId)) {
          materialList.push({
            materialId: _materialId,
            materialCode: item.materialCode,
          });
        }
        if (_locatorId && !locatorList.some(it => it.locatorId === _locatorId)) {
          locatorList.push({
            locatorId: _locatorId,
            locatorCode: item.locatorCode,
          });
        }
      });
      const materialDs = new DataSet();
      const locatorDs = new DataSet();
      materialDs.loadData(materialList);
      locatorDs.loadData(locatorList);
      ds.queryDataSet.getField('materialIds').set('options', materialDs);
      ds.queryDataSet.getField('locatorIds').set('options', locatorDs);
    }, []);

    const getColumns = () => {
      const columns: ColumnProps[] = [
        {
          name: 'identification',
          width: 120,
        },
        {
          name: 'lot',
          width: 120,
        },
        {
          name: 'materialCode',
          width: 120,
        },
        {
          name: 'materialName',
          width: 120,
        },
        {
          name: 'revisionCode',
          width: 120,
        },
        {
          name: 'locatorCode',
          width: 120,
        },
        {
          name: 'locatorName',
          width: 120,
        },
        {
          name: 'materialLotStatusDesc',
          width: 120,
        },
        {
          name: 'qualityStatusDesc',
          width: 120,
        },
        {
          name: 'currentQuantity',
          width: 120,
        },
        {
          name: 'uomCode',
          width: 120,
        },
      ];
      const dynamicColumns: ColumnProps[] = [
        {
          name: 'diffFlag',
          width: 120,
          align: ColumnAlign.center,
          renderer: ({ value }) => (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          ),
        },
        {
          name: 'adjustFlag',
          width: 120,
          align: ColumnAlign.center,
          renderer: ({ value }) => (
            <Badge
              status={value === 'Y' ? 'success' : 'error'}
              text={
                value === 'Y'
                  ? intl.get('tarzan.common.label.yes').d('是')
                  : intl.get('tarzan.common.label.no').d('否')
              }
            />
          ),
        },
        {
          name: 'firstCountQty',
          width: 120,
        },
        {
          name: 'firstCountDiffQty',
          width: 120,
          renderer: ({ value }) => {
            return value && <Tag color={value < 0 ? 'red' : 'green'}>{value}</Tag>;
          },
        },
        {
          name: 'reCountQty',
          width: 120,
          editor: record => record.getState('editing'),
        },
        {
          name: 'reCountDiffQty',
          width: 120,
          renderer: ({ value }) => {
            return value && <Tag color={value < 0 ? 'red' : 'green'}>{value}</Tag>;
          },
        },
        {
          name: 'firstLocatorCode',
          width: 120,
        },
        {
          name: 'firstLocatorName',
          width: 120,
        },
        {
          name: 'reLocatorCodeLov',
          width: 120,
          editor: record => record.getState('editing'),
        },
        {
          name: 'reLocatorName',
          width: 120,
        },
        {
          name: 'firstCountRemark',
          width: 120,
        },
        {
          name: 'firstCountByName',
          width: 120,
        },
        {
          name: 'firstCountDate',
          width: 120,
        },
        {
          name: 'reCountRemark',
          width: 120,
        },
        {
          name: 'reCountByName',
          width: 120,
        },
        {
          name: 'reCountDate',
          width: 120,
        },
        {
          name: 'adjustQty',
          width: 120,
        },
        {
          name: 'adjustCountByName',
          width: 120,
        },
      ];
      if (['NEW', 'RELEASED'].includes(stocktakeStatus)) {
        return columns;
      }
      return columns.concat(dynamicColumns);
    };

    const [currentColumns, setCurrentColumns] = useState(getColumns(false)); // 实际columns
    const changeColumn = val => setCurrentColumns(val);
    const handleSpotCheck = () => {
      setCanEdit(true);
      ds.selected.forEach(record => {
        record.setState('editing', true);
      })
    }
    const handleCancel = () => {
      setCanEdit(false);
      ds.selected.forEach(record => {
        record.reset();
        record.setState('editing', false);
      })
    }
    const handleSave = async () => {
      if(await ds.validate(false, true)){
        await saveSpotCheck({
          params: ds.toJSONData(),
        }).then(res => {
          if(res && res.success) {
            setCanEdit(false);
            setCurrentColumns(getColumns(false))
            handleSearch()
          }
        })
      }
    }

    const getExportQueryParams = () => {
      const data = ds.queryDataSet.current.toData();
      return {
        ...data,
        stocktakeStatus,
        stocktakeId,
        stockActualIds
      }
    };
    return (
      <>
        <div className={styles['barcode-drawer-title-wapper']}>
          <div className={styles['barcode-drawer-title']}>
          </div>
          <div className={styles['barcode-drawer-title-content']}>
            {ds.selected.length ? renderHead() : null}
            {/* <PermissionButton
              disabled={!ds.selected.length}
              onClick={() => handleAdjustDiffer(ds.selected.map(item => item.data))}
              type="c7n-pro"

              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.differenceAdjust`).d('差异调整')}
            </PermissionButton> */}
            {!canEdit?(
              <>
                <PermissionButton
                  disabled={stocktakeStatus !== 'COMPLETED' || !ds.selected.length}
                  onClick={() => handleSpotCheck()}
                  type="c7n-pro"
                  permissionList={[
                    {
                      code: `hzero.yp-wms.les.inventory-check.inventory.list.button.spotCheck`,
                      type: 'button',
                      meaning: '抽盘按钮',
                    },
                  ]}
                >
                  {intl.get(`${modelPrompt}.spotCheck`).d('抽盘')}
                </PermissionButton></>
            ):(
              <>
                <Button onClick={handleCancel}>{intl.get(`${modelPrompt}.cancel`).d('取消')}</Button>
                <Button onClick={handleSave}>{intl.get(`${modelPrompt}.save`).d('保存')}</Button>
              </>
            )}
            <ExcelExport
              method="POST"
              allBody
              exportAsync
              requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/bar-code/details/export/ui`}
              queryParams={getExportQueryParams}
              buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
              modalProps={{ drawer: false }}
            />
          </div>
        </div>
        <Table
          key="materialLotId"
          columns={currentColumns}
          dataSet={ds}
          queryBar={expandTableBar({
            keyStr: 'stocktakeBarcodeList', // 全局唯一，后台查询的key
            className: 'stocktakeBarcodeList',
            lockString: '', // 固定列，无法修改
            columnProps: getColumns(canEdit),
            dataSet: ds,
            type: 'pro',
            changeColumn,
          })}
          virtual
          virtualCell
          // style={{
          //   height: 720,
          // }}
        />
      </>
    );
  },
);
