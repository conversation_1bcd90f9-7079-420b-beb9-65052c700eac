/**
 * @Description: 生产指令管理列表页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2021-08-13 18:04:16
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.productionOrderMgt';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/list/ui`,
        method: 'GET',
        data: {
          ...data,
          workOrderType:
            (data.workOrderType && data.workOrderType.length) > 0
              ? data.workOrderType.join(',')
              : undefined,
          status: (data.status && data.status.length) > 0 ? data.status.join(',') : undefined,
        },
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'workOrderId',
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.siteId`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderNum`).d('WO编码'),
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialId`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'workOrderType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderType`).d('WO类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('WO状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=WO_STATUS&type=workOrderStatusOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'productionLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionLineId`).d('生产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'productionLineId',
      type: FieldType.string,
      bind: 'productionLine.prodLineId',
    },
    {
      name: 'planStartTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartTimeFrom`).d('开始时间从'),
      max: 'planStartTimeTo',
    },
    {
      name: 'planStartTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartTimeTo`).d('开始时间至'),
      min: 'planStartTimeFrom',
    },
    {
      name: 'customer',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.customerId`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'customerId',
      type: FieldType.string,
      bind: 'customer.customerId',
    },
    {
      name: 'planEndTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planEndTimeFrom`).d('结束时间从'),
      max: 'planEndTimeTo',
    },
    {
      name: 'planEndTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planEndTimeTo`).d('结束时间至'),
      min: 'planEndTimeFrom',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
  ],
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderNum`).d('WO编码'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.siteName`).d('站点名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialRevision`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialName`).d('物料名称'),
    },
    {
      name: 'workOrderType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderType`).d('WO类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('WO状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=WO_STATUS&type=workOrderStatusOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'productionLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionLineCode`).d('生产线编码'),
    },
    {
      name: 'productionLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionLineName`).d('生产线名称'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.qty`).d('WO数量'),
    },
    {
      name: 'kitQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.kitQty`).d('投料套数'),
    },
    {
      name: 'releasedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.releasedQty`).d('下达数量'),
    },
    {
      name: 'completedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.completedQty`).d('完成数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.scrappedQty`).d('报废数量'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.creationDate`).d('创建时间'),
    },
  ],
});

export { tableDS };
