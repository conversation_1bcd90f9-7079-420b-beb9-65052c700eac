#!/bin/bash
set -e

find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_PATH ${BUILD_BASE_PATH:-/} g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find /usr/share/nginx/html -name '*.html' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find /usr/share/nginx/html -name '*.css' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_API_HOST $BUILD_API_HOST g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_CLIENT_ID $BUILD_CLIENT_ID g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_WEBSOCKET_HOST $BUILD_WEBSOCKET_HOST g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_PLATFORM_VERSION $BUILD_PLATFORM_VERSION g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_IM_ENABLE $BUILD_IM_ENABLE g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_IM_WEBSOCKET_HOST $BUILD_IM_WEBSOCKET_HOST g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TRACE_LOG_ENABLE $BUILD_TRACE_LOG_ENABLE g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_CUSTOMIZE_ICON_NAME $BUILD_CUSTOMIZE_ICON_NAME g"

find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_HMES_BASIC /mes g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_COMMON /tznc g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_HRPT_COMMON /hrpt g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_MODEL /tznm g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_REPORT /tznr g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_METHOD /tznd g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_SAMPLING /tznq g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_HSPC /tzns g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_APS_METHODTZND /tznd g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_APS_COMMON /tznc g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_APS_METHOD /aps g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_POOL_QUERY query g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_SERVERPLAN /aps-mltp g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_SERVERPURCHASE /aps-purchase g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_SERVER /aps g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_CUSZ_CODE_BEFORE MT g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_LOV_CODE_BEFORE YP_MES g"



exec "$@"
