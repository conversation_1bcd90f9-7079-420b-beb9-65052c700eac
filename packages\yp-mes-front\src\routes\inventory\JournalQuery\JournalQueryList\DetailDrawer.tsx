/**
 * @feature 库存日记账报表明细
 * @date 2022-11-30
 * <AUTHOR> <<EMAIL>>
 */
import intl from 'utils/intl';
import React, { useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';

export default ({ ds }) => {
  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'materialLotCode',
        align: ColumnAlign.left,
        width: 150,
      },
      { name: 'materialCode' },
      { name: 'materialName' },
      { name: 'revisionCode' },
      { name: 'uomQty' },
      { name: 'uomCode' },
      { name: 'locatorCode' },
      { name: 'lotCode' },
      { name: 'siteCode' },
      { name: 'qualityStatus' },
      {
        name: 'ownerType',
        renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
      },
      { name: 'ownerCode' },
      { name: 'ownerDesc' },
      { name: 'materialLotStatus' },
      {
        name: 'instructionDocNum',
        width: 130,
      },
      { name: 'instructionNum' },
      { name: 'containerCode' },
      {
        name: 'topContainerCode',
        width: 130,
      },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
    ],
    [],
  );

  return (
    <>
      <Table
        customizedCode="kcrjzwlpmx"
        dataSet={ds}
        queryBar={TableQueryBarType.none}
        columns={columns}
        virtual
        virtualCell
      />
    </>
  );
};
