/**
 * @Description: 寻址策略列表页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-09-08 13:38:56
 * @LastEditTime: 2023-07-19 14:50:14
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.addressing.strategy';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-addressing-strategy/list/ui`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'eoId',
  cacheSelection: true,
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'addressingStrategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyCode`).d('策略编码'),
    },
    {
      name: 'addressingStrategyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyType`).d('策略类型'),
      textField: 'description',
      valueField: 'typeCode',
      // noCache: true,
      lovPara: { tenantId },
      multiple: false,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ADDRESSING_STRATEGY_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'addressingStrategyLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyLevel`).d('策略层级'),
      textField: 'description',
      valueField: 'typeCode',
      // noCache: true,
      multiple: false,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ADDRESSING_STRATEGY_LEVEL`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'descendantStrategy',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.descendantStrategyCode`).d('子策略编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.ADDRESSING_STRATEGY`,
      ignore: 'always',
      lovPara: {
        tenantId,
        addressingStrategyLevel: 'PARTIAL',
      },
    },
    {
      name: 'descendantStrategyId',
      type: FieldType.string,
      bind: 'descendantStrategy.addressingStrategyId',
    },
    {
      name: 'areaAddressingTrigger',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaAddressingTrigger`).d('子策略触发条件'),
      textField: 'description',
      valueField: 'typeCode',
      // noCache: true,
      multiple: false,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=AREA_ADDRESSING_TRIGGER`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.enable`).d('启用') },
          { value: 'N', key: intl.get(`tarzan.common.label.disable`).d('禁用') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  fields: [
    {
      name: 'addressingStrategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addressingStrategyCode`).d('寻址策略编码'),
    },
    {
      name: 'addressingStrategyTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyType`).d('策略类型'),
    },
    {
      name: 'addressingStrategyLevelDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyLevel`).d('策略层级'),
    },
    {
      name: 'addStrategyLocationLimitDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addStrategyLocationLimit`).d('区域限制'),
    },
    {
      name: 'descendantStrategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.descendantStrategy`).d('子策略'),
    },
    {
      name: 'areaAddressingTrigger',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaAddressingTrigger`).d('子策略触发条件'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=AREA_ADDRESSING_TRIGGER`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'addressingRangeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addressingRangeDesc`).d('寻址范围-上层库位编码'),
    },
    {
      name: 'addressingRangeType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lowerLocatorLimitType`).d('寻址范围-下层库位限定类型'),
    },
    {
      name: 'addressingRange',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addressingRange`).d('寻址范围-下层库位限定范围'),
    },
    {
      name: 'screeningConditionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.screeningConditionDesc`).d('筛选条件'),
    },
    {
      name: 'retrievalOrder',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.retrievalOrder`).d('检索次序'),
    },
    {
      name: 'judgmentConditionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.judgmentConditionDesc`).d('判定条件'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

export { tableDS };
