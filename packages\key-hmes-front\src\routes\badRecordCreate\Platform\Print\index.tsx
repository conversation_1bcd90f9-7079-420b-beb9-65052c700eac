/* eslint-disable no-array-constructor */
/* eslint-disable new-cap */
import React, { useEffect, useMemo, useState } from 'react';
import iconv from 'iconv-lite'
import {
  DataSet,
  Form,
  Select,
  Table,
  TextField,
  Output,
  DateTimePicker,
  Button,
  Modal,
} from 'choerodon-ui/pro';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { BASIC } from '@utils/config';
import myInstance from "@utils/myAxios";
import notification from 'utils/notification';
import scanImg from '@/assets/icons/scan-o.svg';
import { queryMapIdpValue } from '@services/api';
import PrintElement from '@components/tarzan-ui/PrintButton/PrintElement';
import { Collapse, Card, Row, Col } from 'choerodon-ui';
import { ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { observer } from 'mobx-react';
// import { getCurrentOrganizationId } from 'utils/utils';
import openBatchBarCodeModal from '../Detail/BatchBarCodeModal';
import { detailDS, detailLineDS } from '../stores/printDS';
import InputLovDS from '../stores/InputLovDS';
import { ScanCodePrint, Cache } from '../services';
import { API_HOST } from '@/utils/constants';
import './index.module.less';
// import { TemplatePrintButton } from './component'

const { Panel } = Collapse;
const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';
// const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const BadRecordPrint = observer(props => {
  const {
    match: { params },
    location: {
      state,
    },
  } = props;
  const { id } = params as any;

  // 不良对象类型
  const [ncRecordType, setNcRecordType] = useState<string>('');
  const [userRoleList, setUserRoleList] = useState<any>([]);
  // const [printData, setPrintData] = useState<any>([]);
  // const [printLoading, setPrintLoading] = useState<boolean>(false);
  // 折叠activeKey
  const [activeKey, setActiveKey] = useState<any>([
    'basicInfo',
    'badRecordDetail',
    'badRecordInfoObject',
  ]);
  // 多输入框
  const inputLovDS = new DataSet({ ...InputLovDS() });

  // 不良记录明细Ds
  const detailLineDs = useMemo(() => new DataSet(detailLineDS()), []);
  // 详情界面Ds
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );

  const { run: scanCodePrint, loading: scanCodePrintLoading } = useRequest(
    ScanCodePrint(),
    {
      manual: true,
    },
  );

  const { run: cache, loading: cacheLoading } = useRequest(
    Cache(),
    {
      manual: true,
      needPromise: true,
    },
  );

  useEffect(() => {
    queryMapIdpValue({
      userList: 'HME.NC_PRINT_GREEN_ROLE',
    }).then(res => {
      if (res && res.userList && res.userList.length > 0) {
        setUserRoleList(res.userList.map(item=>item.value));
      }
    });
    detailDs.create({ codes: state?.list.join(',') });
    if(state?.list.join(',')){
      scanCode()
    }
    if (id !== 'create') {
      detailDs.create({ labelColor: '红', labelType: '不合格' });
    }
  }, [id]);

  const handleChangeNcRecordType = () => {
    detailDs?.current?.init('labelType', '');
    detailDs?.current?.init('codes', '');
    detailLineDs.unSelectAll();
  };

  const detailColumn: any[] = useMemo(
    () => [
      ncRecordType === 'EO_ALL_NC' && {
        name: 'identification',
        lock: ColumnLock.left,
      },
      ncRecordType === 'RM_NC' && {
        name: 'materialLotCode',
        width: 200,
        lock: ColumnLock.left,
      },
      {
        name: 'materialCode',
        lock: ColumnLock.left,
      },
      {
        name: 'revisionCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'qty',
      },
      {
        name: 'uomName',
      },
      {
        name: 'locatorName',
      },
      {
        name: 'containerCode',
      },
      {
        name: 'supplierName',
      },
      ncRecordType === 'EO_ALL_NC' && {
        name: 'eoNum',
        width: 200,
      },
      {
        name: 'prodLineName',
      },
      // {
      //   name: 'routerStepDesc',
      // },
      {
        name: 'routerName',
      },
      ncRecordType === 'EO_ALL_NC' && {
        name: 'operationName',
      },
      {
        name: 'equipmentCode',
      },
      {
        name: 'workcellName',
      },
      {
        name: 'workOrderNum',
      },
      {
        name: 'workOrderQty',
      },
    ],
    [ncRecordType],
  );

  const handleScan = () => {
    scanCode();
  };

  // 去重
  const uniqueArray = (res, tableList, field) => {
    // tableList为原值 res为新增数据 field为去重字段
    const temp: any = [];
    res.forEach(i => {
      if (tableList.every((j: any) => j[field] !== i[field])) {
        temp.push(i);
      }
    });
    return temp;
  };

  const scanCode = () => {
    if(!detailDs.current?.get('codes'))return
    let str = ''
    if(detailLineDs.toData().length){
      str = detailLineDs.toData().map((item: any) => item?.identification).join(',')
    }
    scanCodePrint({
      params: {
        code: `${detailDs.current?.get('codes')},${str}`,
      },
      onSuccess: res => {
        if (res) {
          detailDs.current?.init('codes', null);
          setNcRecordType(res[0].ncRecordType);
          if(res[0].ncRecordType === 'EO_ALL_NC'){
            detailLineDs.loadData(
              detailLineDs.toData().concat(uniqueArray(res, detailLineDs.toData(), 'identification')),
            );
          }else{
            detailLineDs.loadData(
              detailLineDs.toData().concat(uniqueArray(res, detailLineDs.toData(), 'materialLotCode')),
            );
          }
        }
      },
    });
  };

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    openBatchBarCodeModal({
      inputLovDS,
      inputLovFlag,
      inputLovTitle,
      inputLovVisible,
      targetDS: detailDs,
      submit: handleScan,
    });
    inputLovDS.queryDataSet?.current?.set('code', '');
    inputLovDS.data = [];
    inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
  };

  // 渲染卡片
  const renderCard = () => {
    if (detailLineDs.selected.length&&detailDs.current?.get('labelColor')&&detailDs.current?.get('labelType')) {
      return (
        <>
          <div style={{ padding: '30px', height: '26vh', overflow: 'auto' }} className='ncRecord-print'>
            <Row gutter={24}>
              {detailLineDs.selected.map((record) => {
                const item:any = record.data;
                let color = '';
                if (detailDs?.current?.get('labelColor') === '绿') {
                  color = 'card-green';
                }
                if (detailDs?.current?.get('labelColor') === '黄') {
                  color = 'card-yellow';
                }
                if (detailDs?.current?.get('labelColor') === '红') {
                  color = 'card-red';
                }
                return (
                  <div>
                    <Col span={8}>
                      <Card title={detailDs?.current?.get('labelType')} className={color} style={{width: '378px'}}>
                        <Form columns={1} style={{color: '#000'}} record={record}>
                          {detailDs?.current?.get('labelType') === '让步接收' && (
                            <TextField
                              name="reviewNum"
                              defaultValue={item?.reviewNum}
                              label={intl.get(`${modelPrompt}.reviewNum`).d('评审单号')}
                              // onChange={(e, record) => handleCardValue(e, record, 'reviewNum')}
                            />
                          )}
                          {detailDs?.current?.get('labelType') === '降级使用' && (
                            <TextField
                              name="restrictedUse"
                              defaultValue={item?.restrictedUse}
                              label={intl.get(`${modelPrompt}.restrictedUse`).d('限定使用用途')}
                              // onChange={e => handleCardValue(e, item?.id, 'restrictedUse')}
                            />
                          )}
                          <Output
                            name="materialCode"
                            defaultValue={item?.materialCode}
                            label={intl.get(`${modelPrompt}.materialCode`).d('物料号')}
                          />
                          <Output
                            name="materialName"
                            defaultValue={item?.materialName}
                            label={intl.get(`${modelPrompt}.materialName`).d('物料名称')}
                          />
                          <Output name="qty" defaultValue={item?.qty} label="数量" />
                          {detailDs?.current?.get('labelColor') !== '黄'&&<Output
                            name="currentUserName"
                            defaultValue={item?.currentUserName}
                            label={intl.get(`${modelPrompt}.currentDate`).d('检验员')}
                          />}
                          {!['待返工', '待判断', '待挑选'].includes(detailDs?.current?.get('labelType'))&&<Output
                            name="currentDate"
                            defaultValue={item?.currentDate}
                            label={intl.get(`${modelPrompt}.currentDate`).d('检验日期')}
                          />}
                          {['待判断','待挑选'].includes(detailDs?.current?.get('labelType'))&&<Output
                            name="currentDate"
                            defaultValue={item?.currentDate}
                            label={intl.get(`${modelPrompt}.currentDate`).d('日期')}
                          />}
                          {detailDs?.current?.get('labelType') === '待返工' && (
                            <>
                              <TextField
                                name="reworkUserName"
                                defaultValue={item?.reworkUserName}
                                label={intl.get(`${modelPrompt}.reworkUserName`).d('返工人员')}
                                // onChange={e => handleCardValue(e, item?.id, 'reworkUserName')}
                              />
                              <DateTimePicker
                                name="reworkDate"
                                defaultValue={item?.reworkDate}
                                label={intl.get(`${modelPrompt}.reworkDate`).d('返工完成日期')}
                                // onChange={e => handleCardValue(e, item?.id, 'reworkDate')}
                              />
                            </>
                          )}
                          {detailDs?.current?.get('labelType') === '不合格' && (
                            <TextField
                              name="defectDescription"
                              defaultValue={item?.defectDescription}
                              label={intl.get(`${modelPrompt}.defectDescription`).d('缺陷描述')}
                              // onChange={e => handleCardValue(e, item?.id, 'defectDescription')}
                            />
                          )}
                        </Form>
                      </Card>
                    </Col>
                  </div>
                );
              })}
            </Row>
          </div>
        </>
      );
    }
    // }
  };

// // 连接到BLE设备
// navigator.bluetooth.requestDevice({
//   filters: [{
//     services: ['<your_service_uuid>']
//   }]
// })
// .then(device => {
//   console.log('设备已连接', device);
//   return device.gatt.connect();
// })
// .then(server => {
//   console.log('已连接到GATT服务器', server);
//   return server.getPrimaryService('<your_service_uuid>');
// })
// .then(service => {
//   console.log('已获取主要服务', service);
//   return service.getCharacteristic('<your_characteristic_uuid>');
// })
// .then(characteristic => {
//   console.log('已获取特征', characteristic);

//   // 将数据分成较小的块发送
//   const chunkSize = 20;
//   const data = new Uint8Array(1000); // 1000字节的数据，可根据实际需求更改

//   let offset = 0;
//   const sendChunk = () => {
//     const chunk = data.slice(offset, offset + chunkSize);
//     characteristic.writeValue(chunk)
//       .then(() => {
//         offset += chunkSize;
//         if (offset < data.length) {
//           sendChunk();
//         } else {
//           console.log('数据发送完毕');
//         }
//       })
//       .catch(error => {
//         console.log('发送数据时出错', error);
//       });
//   };

//   sendChunk();
// })
// .catch(error => {
//   console.log('发生错误', error);
// });

  const   printAwait = (dkdk, list, index) =>{
    let printList = []
    console.log(list.length)
    if (list.length > (index * 300 + 300)) {
      printList = list.slice(index * 300, index * 300 + 300)
      dkdk
      .writeValueWithoutResponse(printList)
      .then(res => {
        console.log('success in :', index, 'success info: ', res);

        printAwait(dkdk, list, index + 1);
      })

      .catch(err => {
        console.log('err in :', index, 'err info: ', err);
        printAwait(dkdk, list, index + 1);
      });
    }


    // if (index < list.length) {
    //   console.log('print detail:', list[index])
    //   dkdk
    //     .writeValueWithoutResponse(list[index])
    //     .then(res => {
    //       console.log('success in :', index, 'success info: ', res);

    //       printAwait(dkdk, list, index + 1);
    //     })

    //     .catch(err => {
    //       console.log('err in :', index, 'err info: ', err);
    //       printAwait(dkdk, list, index + 1);
    //     });
    // }
  };

  const bluetoothPrint = async() => {
    console.log(233);
    const chunkSize = 20;
    let offset = 0;
    let printList: any = []

    const sendChunk = (_kdkd) => {
      const chunk = printList.slice(offset, offset + chunkSize);
      console.log(chunk)
      _kdkd.writeValue(chunk)
        .then(() => {
          offset += chunkSize;
          if (offset < printList.length) {
            sendChunk(_kdkd);
          } else {
            console.log('数据发送完毕');
          }
        })
        .catch(error => {
          console.log('发送数据时出错', error);
        });
    };

    let aaa = ''


    detailLineDs.selected.forEach(record => {

      let num = 4;
      aaa += `\n ! 0 200 200 416 1`
      aaa += `\n PAGE-WIDTH 785 `
      aaa += `\n SETMAG 2 2`
      aaa += `\n TEXT 55 0 331 ${num} ${detailDs.current?.get('labelType') || ' '}`
      num += 50
      aaa += `\n TEXT 55 2 81 ${num} 物料号 `
      aaa += `\n TEXT 55 2 229 ${num} ${record.get('materialCode') || ' '} `
      num += 50
      aaa += `\n TEXT 55 2 81 ${num} 物料名称 `
      aaa += `\n TEXT 55 2 229 ${num} ${record.get('materialName') || ' '} `
      num += 50
      aaa += `\n TEXT 55 2 81 ${num} 数量 `
      aaa += `\n TEXT 55 2 229 ${num} ${record.get('qty') || ' '} `
      num += 50
      if (detailDs?.current?.get('labelColor') !== '黄') {
        aaa += `\n TEXT 55 2 81 ${num} 检验员 `
        aaa += `\n TEXT 55 2 229 ${num} ${record.get('currentUserName') || ' '} `
        num += 50
      }
      if (!['待返工', '待判断', '待挑选'].includes(detailDs?.current?.get('labelType'))) {
        aaa += `\n TEXT 55 2 81 ${num} 检验日期 `
        aaa += `\n TEXT 55 2 229 ${num} ${record.get('currentDate') || ' '} `
        num += 50
      }
      if (['待判断','待挑选'].includes(detailDs?.current?.get('labelType'))) {
        aaa += `\n TEXT 55 2 81 ${num} 日期 `
        aaa += `\n TEXT 55 2 229 ${num} ${record.get('currentDate') || ' '} `
        num += 50
      }
      if (detailDs?.current?.get('labelType') === '待返工') {
        aaa += `\n TEXT 55 2 81 ${num} 返工人员 `
        aaa += `\n TEXT 55 2 229 ${num} ${record.get('reworkUserName') || ' '} `
        num += 50
        aaa += `\n TEXT 55 2 81 ${num} 返工完成日期 `
        aaa += `\n TEXT 55 2 229 ${num} ${record.get('reworkDate') || ' '} `
        num += 50
      }
      if (detailDs?.current?.get('labelType') === '不合格') {
        aaa += `\n TEXT 55 2 81 ${num} 缺陷描述 `
        aaa += `\n TEXT 55 2 229 ${num} ${record.get('defectDescription') || ' '} `
        num += 50
      }
      if (detailDs?.current?.get('labelType') === '让步接收') {
        aaa += `\n TEXT 55 2 81 ${num} 评审单号 `
        aaa += `\n TEXT 55 2 229 ${num} ${record.get('reviewNum') || ' '} `
        num += 50
      }
      if (detailDs?.current?.get('labelType') === '降级使用') {
        aaa += `\n TEXT 55 2 81 ${num} 评审单号 `
        aaa += `\n TEXT 55 2 229 ${num} ${record.get('reviewNum') || ' '} `
        // num += 50
      }
      aaa += `\n TEXT 55 2 81 400 . `
      // num += 50
      aaa += `\n SETMAG 0 0`
      // aaa = `\n ! 0 200 200 ${num} 1 ${aaa}`
      aaa += `\n PRINT `
      aaa += `\n`
    })

    console.log(aaa)

    const _aaa = iconv.encode(aaa, "gbk")
    console.log('site: ', new Blob([aaa]).size, 'Bytes')
    console.log(_aaa, 'aaa')

    printList = _aaa

    navigator.bluetooth
      .requestDevice({
        acceptAllDevices: true,
        optionalServices: ['0000fff0-0000-1000-8000-00805f9b34fb'],
        // filters: [{
        //   name: 'CC4_3618L'
        // }],
        // optionalServices: ['0000fff0-0000-1000-8000-00805f9b34fb'],
      })
      .then(device => {
        console.log(device);
        device.gatt
          .connect()
          .then(server => {
            console.log(server, 'server');
            server.getPrimaryService('0000fff0-0000-1000-8000-00805f9b34fb').then(_serverRes => {
              console.log(_serverRes, '_serverRes');
              _serverRes
                .getCharacteristic('0000fff2-0000-1000-8000-00805f9b34fb')
                .then(dkdkdk => {

                  // printAwait(dkdkdk, printList, 0);
                  sendChunk(dkdkdk)
                })
                .catch(err2 => {
                  console.log(err2, 'server err2');
                });
            });
          })
          .catch(err => {
            console.log(err, 'server err 1');
          });
      })
      .catch(res => {
        console.log(res, 'device err');
      });
  };

  const stringToUint8Array = str => {
    const arr:any = [];
    for (let i = 0, j = str.length; i < j; ++i) {
      arr.push(str.charCodeAt(i));
    }
    console.log('🚀 ~ stringToUint8Array ~ arr:', arr);
    const tmpUint8Array = new Uint8Array(arr);
    console.log("🚀 ~ stringToUint8Array ~ tmpUint8Array:", tmpUint8Array)
    return tmpUint8Array;
  };

  const printError = message => {
    notification.error({message})
  };

  const print = async () => {
    // @ts-ignore
    const printDomRef = React.createRef(null);
    cache({
      params: detailLineDs.selected?.map(item => item.toJSONData()),
    }).then(async result => {
      let labelTemplateCode = '';
      if(detailDs.current?.get('labelColor') === '红'){
        labelTemplateCode = 'MT.NC_RED_NG';
      }else if(detailDs.current?.get('labelColor') === '黄'){
        if(detailDs.current?.get('labelType') === '待判断'){
          labelTemplateCode = 'MT.NC_Y_NG'
        }else if(detailDs.current?.get('labelType') === '待挑选'){
          labelTemplateCode = 'MT.NC_Y_NG_TX'
        }else{
          labelTemplateCode = 'MT.NC_Y_NG_FG'
        }
      }else if(detailDs.current?.get('labelColor') === '绿'){
        if(detailDs.current?.get('labelType') === '合格'){
          labelTemplateCode = 'MT.NC_G_NG_HG'
        }else if(detailDs.current?.get('labelType') === '已挑选合格'){
          labelTemplateCode = 'MT.NC_G_NG_YTX_HG'
        }else if(detailDs.current?.get('labelType') === '让步接收'){
          labelTemplateCode = 'MT.NC_G_NG_RB_JS'
        }else if(detailDs.current?.get('labelType') === '返工合格'){
          labelTemplateCode = 'MT.NC_G_NG_FG_HG'
        }else if(detailDs.current?.get('labelType') === '降级使用'){
          labelTemplateCode = 'MT.NC_G_NG_JJ'
        }
      }
      const html = getHtml(result.rows, labelTemplateCode);
      // @ts-ignore
      await Modal.open({
        drawer: true,
        maskClosable: true,
        key: 'ModalKey',
        destroyOnClose: true,
        closable: true,
        style: {
          width: 720,
        },
        title: intl.get('tarzan.common.title.preview').d('预览'),
        children: (
          <div id="pdfContainer">
            <div
              id="pdf"
              style={{ paddingBottom: 20, overflow: 'hidden' }}
              // @ts-ignore
              ref={printDomRef}
              // @ts-ignore
              dangerouslySetInnerHTML={{ __html: html }}
            />
          </div>
        ),
        footer: () => (
          <div>
            <Button
              icon="print"
              onClick={() => {
                PrintElement({
                  content: document.getElementById('pdfContainer'),
                });
              }}
            >
              {intl.get('tarzan.common.button.print').d('打印')}
            </Button>
            <Button onClick={bluetoothPrint}>蓝牙打印</Button>
          </div>
        ),
      });
    })
  }

  const getHtml = async (key, labelTemplateCode) => {
    const url = `${API_HOST}${BASIC.HRPT_COMMON}/v1/${getCurrentOrganizationId()}/label-prints/view/html?labelTemplateCode=${labelTemplateCode}&organizationId=${getCurrentOrganizationId()}&printKey=${key}`;
    myInstance.get(url).then(res => {
      if (res.statusText === 'OK' && (res.data || {}).label) {
        // @ts-ignore
        document.getElementById('pdf').innerHTML = res.data.label.replace(/↵/gm, ''); // 去掉回车换行;
      } else {
        notification.error({
          message: res.data.message,
          description: '',
        });
      }
    });
  }
  const handleDelete = () => {
    detailLineDs.remove(detailLineDs.selected)
  }

  const optionsFilter = (record) => {
    if(!userRoleList.includes(userInfo.currentRoleCode)){
      return record.get('meaning')!=='绿'
    }
    return record.get('meaning')==='绿'||record.get('meaning')==='黄'||record.get('meaning')==='红'
  }

  const buttonsObject = [
    <Button
      icon="delete_black-o"
      onClick={handleDelete}
      disabled={detailLineDs?.selected.length === 0}
    >
      {intl.get('tarzan.common.button.delete').d('删除')}
    </Button>,
  ];

  return (
    <div style={{ height: '100%' }} className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={scanCodePrintLoading||cacheLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.print`).d('不良记录打印')}
          backPath="/hmes/bad-record/platform-new/list"
        >
          <Button
            disabled={detailLineDs.selected.length === 0}
            onClick={() => print()}>
            {intl.get(`${modelPrompt}.label.print`).d('标签打印')}
          </Button>
        </Header>
        <Content>
          <Collapse bordered={false} activeKey={activeKey} onChange={value => setActiveKey(value)}>
            <Panel
              header={intl.get(`${modelPrompt}.title.chooseCard`).d('标签选择')}
              key="basicInfo"
            >
              <Form columns={3} labelWidth={112} dataSet={detailDs}>
                <Select
                  name="labelColor"
                  onChange={handleChangeNcRecordType}
                  disabled={id !== 'create'}
                  optionsFilter={optionsFilter}
                />
                <Select name="labelType" disabled={id !== 'create'} />
                <>
                  <TextField
                    newLine
                    name="codes"
                    onEnterDown={scanCode}
                    clearButton={false}
                    disabled={id !== 'create'}
                    suffix={
                      <img
                        alt=""
                        style={{ width: '20px', paddingRight: '5px' }}
                        src={scanImg}
                        onClick={() => onOpenInputModal(true, 'codes', '条码号')}
                      />
                    }
                  />
                </>
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.barCodeInfo`).d('条码信息')}
              key="badRecordInfoObject"
            >
              {ncRecordType&&(
                <Table
                  style={{ height: '30vh' }}
                  buttons={buttonsObject}
                  dataSet={detailLineDs} columns={detailColumn} />)}
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.printDetail`).d('打印明细')}
              key="badRecordDetail"
            >
              {(renderCard())}
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(BadRecordPrint as any);
