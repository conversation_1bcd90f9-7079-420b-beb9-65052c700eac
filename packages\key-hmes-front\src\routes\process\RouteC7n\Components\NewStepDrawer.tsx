/**
 * 组件行新建编辑抽屉
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */
import React, { FC, useEffect, useMemo, useState, forwardRef, useImperativeHandle } from 'react';
import {
  DataSet,
  Lov,
  Select,
  TextField,
  SelectBox,
  Form,
  NumberField,
} from 'choerodon-ui/pro';
import { isNull, cloneDeep } from 'lodash';
import myInstance from '@/utils/myAxios';
import { C7nFormItemSort } from '@/components/tarzan-ui';
import notification from 'utils/notification';
import intl from 'utils/intl';
import { BASIC } from '@/utils/config';
import '../index.module.less';

import {
  routerFormDS,
  groupFormDS,
  operationFormDS,
} from '../stories/NewStepDrawerDS'

const { Option } = SelectBox;

const modelPrompt = 'tarzan.process.routes.model.routes';

export interface NewStepDrawerProps {
  editStatus,
  dataSource, // 当前步骤的对象详情
  stepsList, // 详情页的stepsList，是步骤的集合
  routerLov,
  UpdateStepList,
  operationLov,
  tenantId,
  selectedRouterType,
  methodServeCode,
  customizeForm: any,
  custCode: string,
}

const NewStepDrawer: FC<NewStepDrawerProps> = (
  {
    editStatus,
    dataSource,
    stepsList,
    routerLov,
    UpdateStepList,
    operationLov,
    tenantId,
    selectedRouterType,
    methodServeCode,
    customizeForm,
    custCode,
  }, ref) => {
  const [stateDataSource, setStateDataSource] = useState(dataSource);
  const [groupList, setGroupList] = useState([]);
  const [timeUom, setTimeUom] = useState('m');
  const [currentRadio, setCurrentRadio] = useState('OPERATION');
  const [stepAttr, setStepAttr] = useState(false);
  const [defaultSelect, setDefaultSelect] = useState<any>([]);
  const [selectReturnType, setSelectReturnType] = useState('');
  // 工艺tab
  const operationFormDs = useMemo(() => new DataSet({ ...operationFormDS(operationLov) }), []);
  // 子工艺路线tab
  const routerFormDs = useMemo(() => new DataSet({ ...routerFormDS(routerLov, operationLov) }), []);
  // 步骤组tab
  const groupFormDs = useMemo(() => new DataSet({ ...groupFormDS(operationLov) }), []);

  const [initStepsList, setInitStepsList] = useState([]); // 初始stepsList
  const [stateMtRouterStepGroupDTO, setStateMtRouterStepGroupDTO] = useState({});


  // 通过ref调用的方法
  useImperativeHandle(ref, () => ({
    handleOK,
  }));

  // 初始化数据
  useEffect(() => {
    initHandleData([])
  }, [stepsList.length]);

  // 销毁
  useEffect(() => {
    return () => {
      setStateDataSource(dataSource);
      setGroupList([]);
      setTimeUom('m');
      setCurrentRadio('OPERATION');
      setStepAttr(false);
      setDefaultSelect([]);
      setSelectReturnType('');
    }
  }, [])

  // groupList更新更新步骤组下拉框
  useEffect(() => {
    // @ts-ignore
    operationFormDs.getField('stepGroup').set('options',
      new DataSet({
        data: [...new Set(groupList)],
      }),
    );
    // 存在所属步骤组
    let typeCode = ''
    groupList.forEach((item: any) => {
      if (stateDataSource.routerStepGroupId === item.routerStepGroupId) {
        typeCode = item.typeCode
      }
    })
    if (stateDataSource.routerStepGroupId && stateDataSource.routerStepGroupStepId) {
      operationFormDs.current?.set('stepGroup', typeCode);
    }
    if (typeCode && stateDataSource.routerStepId) {
      setStepAttr(true)
    } else {
      setStepAttr(false)
    }
  }, [groupList.length]);


  // 初始化数据
  const initHandleData = list => {
    const cloneList = cloneDeep(list.length ? list : stepsList);
    const {
      mtRouterStepGroupDTO = {},
      routerStepType,
      routerStepId,
      entryStepFlag = 'N', //  入口步骤
      keyStepFlag = 'N', //  关键步骤
      routerDoneStepFlag = 'N', // 完成步骤
      mtRouterReturnStepDTO,
    } = stateDataSource;
    const newGroupList: any = [];
    cloneList.forEach(item => {
      if (item.routerStepType === 'GROUP') {
        // 工艺ds需要用
        newGroupList.push({
          typeCode: item.routerStepId,
          description: item.description,
          keyStepFlag: item.keyStepFlag,
          routerStepGroupId: item.mtRouterStepGroupDTO?.routerStepGroupId,
        });
      }
    });
    // 记录处理后的数据
    setInitStepsList(cloneList);
    setStateMtRouterStepGroupDTO(mtRouterStepGroupDTO || {});
    setGroupList(newGroupList || []);

    if (routerStepId) {
      // 存在routerStepId ，切换当前的tab
      setCurrentRadio(routerStepType)

      const stepFlag: Array<string> = [];

      if (keyStepFlag === 'Y') {
        stepFlag.push("keyStepFlag");
      }
      if (entryStepFlag === 'Y') {
        stepFlag.push('entryStepFlag');
      }
      if (routerDoneStepFlag === 'Y') {
        stepFlag.push('routerDoneStepFlag');
      }

      // 返回步骤
      if (mtRouterReturnStepDTO?.returnType) {
        stepFlag.push('mtRouterReturnStepDTO');
        setSelectReturnType(mtRouterReturnStepDTO?.returnType);
      }
      setDefaultSelect(stepFlag)

      // loadData
      if (routerStepType === 'OPERATION') {

        operationFormDs.loadData([{
          ...stateDataSource,
          ...stateDataSource.mtRouterOperationDTO,
          timeUom: 'm',
          operationRevision: stateDataSource.mtRouterOperationDTO.revision,
          stepFlag,
          returnType: mtRouterReturnStepDTO?.returnType,
          returnOperationId: mtRouterReturnStepDTO?.operationId,
          returnOperationName: mtRouterReturnStepDTO?.operationName,
        }])
        setTimeUom('m');
      }
      if (routerStepType === 'GROUP') {
        groupFormDs.loadData([{
          ...stateDataSource,
          ...stateDataSource.mtRouterStepGroupDTO,
          stepFlag,
          returnType: mtRouterReturnStepDTO?.returnType,
          returnOperationId: mtRouterReturnStepDTO?.operationId,
          returnOperationName: mtRouterReturnStepDTO?.operationName,
        }])
      }
      if (routerStepType === 'ROUTER') {
        routerFormDs.loadData([{
          ...stateDataSource,
          ...stateDataSource.mtRouterLinkDTO,
          stepFlag,
          returnType: mtRouterReturnStepDTO?.returnType,
          returnOperationId: mtRouterReturnStepDTO?.operationId,
          returnOperationName: mtRouterReturnStepDTO?.operationName,
        }])
      }
    }
  };

  // 切换tab
  const handleChange = (value) => {
    setCurrentRadio(value)
  };

  // 判断入口步骤,结束步骤,返回步骤是不是唯一
  const getOnlyFlag = flag => {
    if (flag === 'mtRouterReturnStepDTO') {
      return initStepsList.some((item: any) => {
        return (
          (item.mtRouterReturnStepDTO || {}).returnType &&
          stateDataSource?.routerStepId !== item.routerStepId
        );
      });
    }
    return initStepsList.some((item: any) => {
      return item[flag] === 'Y' && stateDataSource?.routerStepId !== item.routerStepId;
    });
  };

  // 工艺路线的lov
  const changeOperationId = async value => {
    if (value) {
      const {
        revision,
        description,
        operationId,
        standardReqdTimeInProcess,
        standardSpecialIntroduction,
        operationName,
      } = value;
      const indexArr: any = [];
      let mtRouterSubstepDTO = [];
      const url = `${methodServeCode}/v1/${tenantId}/mt-operation-substep/list/ui?operationId=${operationId}`;
      const res: any = await myInstance.get(url);
      if (res && res.data.rows && res.data.rows.content) {
        mtRouterSubstepDTO = res.data.rows.content;
      }
      initStepsList.forEach((item: any) => {
        if (
          (item.mtRouterOperationDTO || {}).operationId === operationId &&
          item.routerStepId !== stateDataSource?.routerStepId
        ) {
          indexArr.push(item.routerStepId);
        }
        if (((item.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO || []).length > 0) {
          item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.forEach((it: any) => {
            if (
              (it.mtRouterOperationDTO || {}).operationId === operationId &&
              it.routerStepId !== stateDataSource?.routerStepId
            ) {
              indexArr.push(it.routerStepId);
            }
          });
        }
      });
      if (indexArr.length > 0) {
        operationFormDs.current?.set('stepNameRequired', true);
      } else {
        operationFormDs.current?.set('stepNameRequired', false);
      }

      operationFormDs.current?.set('operationRevision', revision);
      operationFormDs.current?.set('description', description || operationName);
      operationFormDs.current?.set('requiredTimeInProcess', standardReqdTimeInProcess);
      operationFormDs.current?.set('specialInstruction', standardSpecialIntroduction);

      operationFormDs.current?.set('mtRouterOperationDTO', {
        operationId,
        operationName,
        revision,
        description: description || operationName,
        requiredTimeInProcess: standardReqdTimeInProcess,
        specialInstruction: standardSpecialIntroduction,
        mtRouterSubstepDTO,
      });
    } else {
      operationFormDs.current?.set('stepNameRequired', false);
      operationFormDs.current?.init('operationRevision');
      operationFormDs.current?.init('description');
      operationFormDs.current?.init('requiredTimeInProcess');
      operationFormDs.current?.init('specialInstruction');
      operationFormDs.current?.set('mtRouterOperationDTO', {});
    }
  };

  // 子工艺路线的工艺change
  const changeRouterId = value => {
    if (value) {
      routerFormDs.current?.set('revision', value.revision);
      routerFormDs.current?.set('description', value.description || value.routerName);
      routerFormDs.current?.set('mtRouterLinkDTO', {
        routerId: value.routerId,
        routerName: value.routerName,
        revision: value.revision,
        description: value.description || value.routerName,
      });
    } else {
      routerFormDs.current?.init('revision');
      routerFormDs.current?.init('description');
      routerFormDs.current?.set('mtRouterLinkDTO', {});
    }
  }

  // 保存接口
  const handleOK = async () => {
    const { routerStepId, routerId } = stateDataSource;
    // 子工艺路线
    if (currentRadio === 'ROUTER') {
      const validate = await routerFormDs.validate()
      if (validate) {
        const data: any = routerFormDs.toData()[0]
        if (!routerStepId) {
          const newData = {
            ...data,
            routerRevision: data.revision,
            _status: 'create',
            keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
            entryStepFlag: data.stepFlag.indexOf('entryStepFlag') > -1 ? 'Y' : '',
            mtRouterReturnStepDTO: data.stepFlag.indexOf('mtRouterReturnStepDTO') > -1 ? data.mtRouterReturnStepDTO : undefined,
            routerDoneStepFlag: data.stepFlag.indexOf('routerDoneStepFlag') > -1 ? 'Y' : '',
            routerStepId: new Date().getTime(),
            routerStepType: 'ROUTER',
            sequence: getSequence(false),
          }
          const newStepList = [newData, ...initStepsList];
          if (validateRepeat(newStepList)) {
            UpdateStepList(newStepList)
            initHandleData(newStepList);
            notification.success({});
            clear();
          }
        } else {
          const newStepList: Array<object> = [];
          initStepsList.forEach((item: any) => {
            if (item.routerStepId === routerStepId) {
              newStepList.push({
                ...item,
                ...data,
                keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
                entryStepFlag: data.stepFlag.indexOf('entryStepFlag') > -1 ? 'Y' : '',
                mtRouterReturnStepDTO: data.stepFlag.indexOf('mtRouterReturnStepDTO') > -1 ? data.mtRouterReturnStepDTO : undefined,
                routerDoneStepFlag: data.stepFlag.indexOf('routerDoneStepFlag') > -1 ? 'Y' : '',
              })
            } else {
              newStepList.push(item)
            }
            return item;
          })
          if (validateRepeat(newStepList)) {
            UpdateStepList(newStepList)
            notification.success({});
          }
        }
      }
    } else if (currentRadio === 'GROUP') {
      const validate = await groupFormDs.validate()
      if (validate) {
        const data: any = groupFormDs.toData()[0]
        if (!routerStepId) {
          const newData = {
            ...data,
            _status: 'create',
            keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
            entryStepFlag: data.stepFlag.indexOf('entryStepFlag') > -1 ? 'Y' : '',
            routerDoneStepFlag: data.stepFlag.indexOf('routerDoneStepFlag') > -1 ? 'Y' : '',
            routerStepId: new Date().getTime(),
            routerStepGroupId: new Date().getTime(),
            routerStepType: 'GROUP',
            sequence: getSequence(false),
          }
          const newStepList = [newData, ...initStepsList];
          if (validateRepeat(newStepList)) {
            UpdateStepList(newStepList);
            initHandleData(newStepList);
            notification.success({});
            clear();
          }
        } else {
          const newStepList: any = [];
          initStepsList.forEach((item: any) => {
            if (item.routerStepId === routerStepId) {
              // 处理步骤组中子工艺的关键步骤（需求：步骤组从非关键变更为关键时，工艺也需要修改
              const operateArr: any = [];
              (item.mtRouterStepGroupDTO?.mtRouterStepGroupStepDTO || []).forEach(i => {
                operateArr.push({
                  ...i,
                  stepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? ['keyStepFlag'] : [],
                  keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
                })
              })
              newStepList.push({
                ...item,
                ...data,
                keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
                entryStepFlag: data.stepFlag.indexOf('entryStepFlag') > -1 ? 'Y' : '',
                mtRouterReturnStepDTO: data.stepFlag.indexOf('mtRouterReturnStepDTO') > -1 ? data.mtRouterReturnStepDTO : undefined,
                routerDoneStepFlag: data.stepFlag.indexOf('routerDoneStepFlag') > -1 ? 'Y' : '',
                mtRouterStepGroupDTO: {
                  ...data.mtRouterStepGroupDTO,
                  mtRouterStepGroupStepDTO: operateArr,
                },
                mtRouterStepGroupStepDTO: operateArr, // 将数据同步到外层
              })
            } else {
              newStepList.push(item);
            }
          })
          if (validateRepeat(newStepList)) {
            UpdateStepList(newStepList);
            setStepAttr(false);
            notification.success({});
          }
        }
      }
    } else if (currentRadio === 'OPERATION') {

      const validate = await operationFormDs.validate()
      if (validate) {
        const data: any = operationFormDs.toData()[0]
        const pertainFlag = ['SPECIAL', 'NC'].includes(selectedRouterType);

        // 做数据校验
        if (
          !pertainFlag &&
          data.mtRouterReturnStepDTO &&
          data.mtRouterReturnStepDTO.returnType
        ) {
          //  当工艺路线类型为'SPECIAL', 'NC'时，才可以选择返回步骤策略
          notification.error({
            message: intl
              .get(`${modelPrompt}.repeatedEntryFlag`)
              .d('工艺路线不为特殊类型或不良代码类型，不允许选择返回步骤策略'),
          });
          return;
        }

        if (pertainFlag && data.stepFlag.indexOf('routerDoneStepFlag') > -1) {
          //  当工艺路线类型为'SPECIAL', 'NC'时，不可以选择完成步骤
          notification.error({
            message: intl
              .get(`${modelPrompt}.repeatedDoneFlag`)
              .d('工艺路线为特殊类型或不良代码类型时，不允许选择完成步骤'),
          });
          return;
        }

        if (data.stepFlag.indexOf('entryStepFlag') > -1) {
          //  判断入口步骤是否唯一
          const findedEntryFlagIndex = initStepsList.findIndex(
            (item: any) => item.entryStepFlag === 'Y' && item.routerStepId !== stateDataSource?.routerStepId,
          );
          if (findedEntryFlagIndex > -1) {
            notification.error({
              message: intl
                .get(`${modelPrompt}.onlyOneEntryFlag`)
                .d('工艺路线只有且仅有一个入口步骤，请修改后再保存'),
            });
            return;
          }
        }

        let newStepList: Array<object> = [];
        const { operationId, requiredTimeInProcess, specialInstruction } = data;
        if (requiredTimeInProcess || operationId || specialInstruction) {
          data.mtRouterOperationDTO = {
            ...data.mtRouterOperationDTO,
            ...data,
            mtRouterOperationComponentDTO: [],
            operationId,
            requiredTimeInProcess: requiredTimeInProcess
              ? timeTransform(requiredTimeInProcess)
              : requiredTimeInProcess,
            specialInstruction,
          };
        }
        data.routerStepType = currentRadio;
        data.keyStepFlag = data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '';

        const initStepsListState = [...initStepsList];

        // 步骤组下的子步骤编辑
        if (stateDataSource?.routerStepGroupId) {
          newStepList = [...initStepsListState];
          newStepList.forEach((its: any) => {
            if (its.mtRouterStepGroupDTO?.routerStepGroupId === data?.routerStepGroupId) {
              (its.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO.forEach((it, index) => {
                if (it.routerStepId === data.routerStepId) {
                  (its.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO[index] = {
                    ...stateDataSource,
                    ...data,
                    routerId,
                  };
                }
              });
            }
          });
          // 工艺类型选择了所属步骤组,
        } else if ((data.mtRouterStepGroup || {}).routerStepId) {
          if (routerStepId) {
            // 编辑,选择了所属步骤组,需要把当前数据塞到步骤组下,并把当前工艺从表格中删除
            initStepsListState.forEach((item: any, index) => {
              if (item.routerStepId === routerStepId) {
                initStepsListState.splice(index, 1);
              }
            });
            /* eslint-disable */
            initStepsListState.forEach((item: any) => {
              if (item.routerStepId === (data.mtRouterStepGroup || {}).routerStepId) {
                item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO
                  ? item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.push({
                    ...stateDataSource,
                    ...data.mtRouterStepGroup,
                    ...data,
                    sequence: getSequence((data.mtRouterStepGroup || {}).routerStepId),
                    routerStepId,
                  })
                  : (item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO = [
                    {
                      ...stateDataSource,
                      ...data.mtRouterStepGroup,
                      ...data,
                      sequence: data.getSequence((data.mtRouterStepGroup || {}).routerStepId),
                      routerStepId,
                      entryStepFlag: data.stepFlag.indexOf('entryStepFlag') > -1 ? 'Y' : '',
                      keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
                      mtRouterReturnStepDTO: data.stepFlag.indexOf('mtRouterReturnStepDTO') > -1 ? data.mtRouterReturnStepDTO : undefined,
                      routerDoneStepFlag: data.stepFlag.indexOf('routerDoneStepFlag') > -1 ? 'Y' : '',
                    },
                  ]);
              }
            });
          } else {
            // 新增,选了所属步骤组,需要把当前数据塞到步骤组下
            initStepsListState.forEach((item: any) => {
              if (item.routerStepId === (data.mtRouterStepGroup || {}).routerStepId) {
                item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO
                  ? item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.push({
                    ...data.mtRouterStepGroup,
                    ...data,
                    routerStepId: new Date().getTime(),
                    routerStepGroupId: item.mtRouterStepGroupDTO.routerStepGroupId,
                    routerStepGroupStepId: new Date().getTime(),
                    _status: 'create',
                    sequence: getSequence((data.mtRouterStepGroup || {}).routerStepId),
                    entryStepFlag: data.stepFlag.indexOf('entryStepFlag') > -1 ? 'Y' : '',
                    keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
                    mtRouterReturnStepDTO: data.stepFlag.indexOf('mtRouterReturnStepDTO') > -1 ? data.mtRouterReturnStepDTO : undefined,
                    routerDoneStepFlag: data.stepFlag.indexOf('routerDoneStepFlag') > -1 ? 'Y' : '',
                  })
                  : (item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO = [
                    {
                      ...data.mtRouterStepGroup,
                      ...data,
                      routerStepGroupId: item.mtRouterStepGroupDTO.routerStepGroupId,
                      routerStepGroupStepId: new Date().getTime(),
                      routerStepId: new Date().getTime(),
                      _status: 'create',
                      sequence: getSequence((data.mtRouterStepGroup || {}).routerStepId),
                      entryStepFlag: data.stepFlag.indexOf('entryStepFlag') > -1 ? 'Y' : '',
                      keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
                      mtRouterReturnStepDTO: data.stepFlag.indexOf('mtRouterReturnStepDTO') > -1 ? data.mtRouterReturnStepDTO : undefined,
                      routerDoneStepFlag: data.stepFlag.indexOf('routerDoneStepFlag') > -1 ? 'Y' : '',
                    },
                  ]);
                // 同步数据到外层
                item.mtRouterStepGroupStepDTO = item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO
              }
            });
          }
          newStepList = [...initStepsListState];
        } else if (stateDataSource?.routerStepId) {
          // @ts-ignore
          newStepList = initStepsListState.map((item: any) => {
            if (item.routerStepId === routerStepId) {
              return {
                ...item,
                ...data,
                routerId,
                mtRouterOperationDTO: {
                  ...data,
                  requiredTimeInProcess: timeTransform(data.requiredTimeInProcess),
                },
                sequence: item.sequence || getSequence(false),
                routerStepId,
                entryStepFlag: data.stepFlag.indexOf('entryStepFlag') > -1 ? 'Y' : '',
                keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
                mtRouterReturnStepDTO: data.stepFlag.indexOf('mtRouterReturnStepDTO') > -1 ? data.mtRouterReturnStepDTO : undefined,
                routerDoneStepFlag: data.stepFlag.indexOf('routerDoneStepFlag') > -1 ? 'Y' : '',
              };
            } else {
              return item;
            }
          });

        } else {
          newStepList = [
            {
              ...data,
              routerId,
              sequence: getSequence(false),
              routerStepId: new Date().getTime(),
              _status: 'create',
              entryStepFlag: data.stepFlag.indexOf('entryStepFlag') > -1 ? 'Y' : '',
              keyStepFlag: data.stepFlag.indexOf('keyStepFlag') > -1 ? 'Y' : '',
              mtRouterReturnStepDTO: data.stepFlag.indexOf('mtRouterReturnStepDTO') > -1 ? data.mtRouterReturnStepDTO : undefined,
              routerDoneStepFlag: data.stepFlag.indexOf('routerDoneStepFlag') > -1 ? 'Y' : '',
            },
            ...initStepsList,
          ];
        }

        if (validateRepeat(newStepList)) {
          newStepList.forEach((i: any) => {
            if (i.mtRouterStepGroupDTO?.routerStepGroupId) {
              i.mtRouterStepGroupStepDTO = i.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO
            }
          });
          UpdateStepList([...newStepList])
          notification.success({});
          if (!routerStepId) {
            initHandleData([...newStepList]);
            clear();
          }
        }
      }
    }
  };

  const validateRepeat = (newStepList) => {
    const indexArr: Array<string> = [];
    newStepList.forEach((item: any) => {
      if (item.routerStepType === 'GROUP') {
        indexArr.push(`${item.description},${item.stepName}`);
        if (((item.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO || []).length > 0) {
          item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.forEach(it => {
            indexArr.push(`${it.description},${it.stepName}`);
          });
        }
      } else {
        indexArr.push(`${item.description},${item.stepName}`);
      }
    });
    const uniqueArr = indexArr.filter(i => indexArr.indexOf(i) !== indexArr.lastIndexOf(i));
    if (uniqueArr.length > 0) {
      const str = uniqueArr[0].split(',');
      const desc = str[0] ? str[0] : '';
      const stepName =
        isNull(str[1]) || str[1] === undefined || str[1] === 'undefined' ? '' : str[1];
      notification.error({
        message: intl
          .get(`tarzan.process.routes.model.routes.sameInfo`, {
            desc,
            stepName,
          })
          .d(`工艺路线步骤描述【${desc}】+识别码【${stepName}】重复，请检查！`),
      });
      return false;
    } else {
      return true;
    }
  }

  const clear = () => {
    setStateDataSource({});
    setStepAttr(false);
    setSelectReturnType('');
    setDefaultSelect([]);
    operationFormDs.reset();
    routerFormDs.reset();
    groupFormDs.reset();
  };

  // 顺序自增加
  const getSequence = routerStepId => {
    const seqArr: any = [];
    if (routerStepId) {
      const arr: any = initStepsList.filter((i: any) => i.routerStepId === routerStepId);
      if (((arr[0].mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO || []).length > 0) {
        arr[0].mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.forEach(i => {
          seqArr.push(i.sequence);
        });
        return parseInt(String(Math.max(...seqArr) / 10), 10) * 10 + 10;
      }
      return 10;

    } if (
      (initStepsList.length === stepsList.length && stepsList.length === 0) ||
      initStepsList.length === 0
    ) {
      return 10;
    }
    initStepsList.forEach((i: any) => {
      seqArr.push(i.sequence);
    });
    return parseInt(String(Math.max(...seqArr) / 10), 10) * 10 + 10;
  };

  /**
   * 步骤组 切换步骤组类型
   */
  const changeRouterStepGroupType = value => {
    const { routerStepId } = stateDataSource;

    groupFormDs.current?.set('mtRouterStepGroupDTO', {
      routerStepGroupId: new Date().getTime(),
      ...stateMtRouterStepGroupDTO,
      routerStepGroupType: value,
      routerStepId: routerStepId || new Date().getTime(),
    });
  };

  /**
   * 切换标准工时时间单位下拉框
   */
  const changeTime = val => {
    setTimeUom(val)
  };

  // 时间转换
  const timeTransform = time => {
    if (timeUom === 's') {
      return time / 60;
    } if (timeUom === 'm') {
      return time;
    } if (timeUom === 'h') {
      return time * 60;
    } if (timeUom === 'd') {
      return time * 60 * 24;
    }
  };

  // 选择所属步骤组
  const changeStepGroup = val => {
    if (!val) {
      setStepAttr(false)
      operationFormDs.current?.set('mtRouterStepGroup', undefined);
      return false;
    }
    setStepAttr(true)
    const arr = groupList.filter((i: any) => i.typeCode === val);
    // @ts-ignore
    const keyStepFlagStatus = (arr[0] || {}).keyStepFlag === "Y"
    const keyStepFlag = keyStepFlagStatus ? ['keyStepFlag'] : [];
    operationFormDs.current?.set('stepFlag', keyStepFlag);
    operationFormDs.current?.set('mtRouterStepGroup', {
      routerStepId: val
    });
  }

  const changeReturnStep = val => {
    setSelectReturnType(val);
    if (currentRadio === 'ROUTER') {
      routerFormDs.current?.set('mtRouterReturnStepDTO', {
        returnType: val,
        routerStepId: new Date().getTime(),
        _status: 'create',
      })

    } else if (currentRadio === 'GROUP') {
      groupFormDs.current?.set('mtRouterReturnStepDTO', {
        returnType: val,
        routerStepId: new Date().getTime(),
        _status: 'create',
      })

    } else if (currentRadio === 'OPERATION') {
      operationFormDs.current?.set('mtRouterReturnStepDTO', {
        returnType: val,
        routerStepId: new Date().getTime(),
        _status: 'create',
      })
    }
  }

  const changeSteFlag = val => {
    setDefaultSelect(val);
  }

  const changeReturnOperation = value => {
    if (value) {
      if (currentRadio === 'ROUTER') {
        routerFormDs.current?.set('mtRouterReturnStepDTO', {
          ...routerFormDs.current?.get('mtRouterReturnStepDTO'),
          operationId: value.operationId,
          operationName: value.operationName
        })
      } else if (currentRadio === 'GROUP') {
        groupFormDs.current?.set('mtRouterReturnStepDTO', {
          ...groupFormDs.current?.get('mtRouterReturnStepDTO'),
          operationId: value.operationId,
          operationName: value.operationName
        })

      } else if (currentRadio === 'OPERATION') {
        operationFormDs.current?.set('mtRouterReturnStepDTO', {
          ...operationFormDs.current?.get('mtRouterReturnStepDTO'),
          operationId: value.operationId,
          operationName: value.operationName
        })
      }
    }
  }

  return (
    <div className="hmes-style">
      <SelectBox
        value={currentRadio}
        disabled={!editStatus || stateDataSource?.routerStepId}
        // @ts-ignore
        mode='button'
        onChange={handleChange}
        style={{ marginBottom: 16 }}
      >
        <Option value="OPERATION">
          {intl.get(`${modelPrompt}.operationRadio`).d('工艺')}
        </Option>
        <Option value="GROUP">
          {intl.get(`${modelPrompt}.groupRadio`).d('步骤组')}
        </Option>
        <Option value="ROUTER">
          {intl.get(`${modelPrompt}.routerRadio`).d('子工艺路线')}
        </Option>
      </SelectBox>
      { /* 工艺 */}
      {
        currentRadio === 'OPERATION' &&
        customizeForm(
          {
            code: `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.STEP_BASIC`,
          },
          <Form columns={2} labelWidth={112} dataSet={operationFormDs} disabled={!editStatus}>
            <C7nFormItemSort name="operationLov" itemWidth={['70%', '30%']}>
              <Lov
                disabled={stateDataSource?.routerStepId}
                name="operationLov"
                onChange={changeOperationId}
              />
              <TextField name="operationRevision" disabled />
            </C7nFormItemSort>
            <TextField name="stepName" />
            <C7nFormItemSort name="requiredTimeInProcess" itemWidth={['70%', '30%']}>
              <NumberField name="requiredTimeInProcess" precision={6} min={0} />
              <Select
                name="timeUom"
                onChange={changeTime}
              />
            </C7nFormItemSort>
            <Select
              disabled={stateDataSource?.routerStepId}
              name="stepGroup"
              onChange={changeStepGroup}
              noCache
            />
            <TextField
              name="specialInstruction"
              colSpan={2}
            />
            <SelectBox
              name="stepFlag"
              disabled={stepAttr}
              colSpan={2}
              onChange={changeSteFlag}
            >
              <Option value="keyStepFlag">
                {intl.get(`${modelPrompt}.keyStep`).d('关键')}
              </Option>
              <Option
                value="entryStepFlag"
                disabled={getOnlyFlag('entryStepFlag')}
              >
                {intl.get(`${modelPrompt}.entryStep`).d('入口')}
              </Option>
              {
                ['SPECIAL', 'NC'].includes(selectedRouterType) ? <Option value="mtRouterReturnStepDTO">
                  {intl.get(`${modelPrompt}.returnStep`).d('返回')}
                </Option> : <Option value="routerDoneStepFlag">
                  {intl.get(`${modelPrompt}.routerDoneStep`).d('完成')}
                </Option>
              }
            </SelectBox>
            {
              ['SPECIAL', 'NC'].includes(selectedRouterType) &&
              defaultSelect.includes('mtRouterReturnStepDTO')
              && <Select
                name="returnType"
                onChange={changeReturnStep}
              />
            }
            {
              selectReturnType === 'DESIGNATED_OPERATION' &&
              <Lov
                name="returnOperationLov"
                onChange={changeReturnOperation}
              />
            }
          </Form>,
        )
      }
      { /* 步骤组 */}
      {
        currentRadio === 'GROUP' &&
        <Form columns={2} labelWidth={112} dataSet={groupFormDs} disabled={!editStatus}>
          <TextField name="description" />
          <Select name="routerStepGroupType" onChange={changeRouterStepGroupType} />
          <SelectBox name="stepFlag" onChange={changeSteFlag}>
            <Option value="keyStepFlag">
              {intl.get(`${modelPrompt}.keyStep`).d('关键')}
            </Option>
            <Option
              value="entryStepFlag"
              disabled={getOnlyFlag('entryStepFlag')}
            >
              {intl.get(`${modelPrompt}.entryStep`).d('入口')}
            </Option>
            {
              ['SPECIAL', 'NC'].includes(selectedRouterType) ? <Option value="mtRouterReturnStepDTO">
                {intl.get(`${modelPrompt}.returnStep`).d('返回')}
              </Option> : <Option value="routerDoneStepFlag">
                {intl.get(`${modelPrompt}.routerDoneStep`).d('完成')}
              </Option>
            }
          </SelectBox>
          {
            ['SPECIAL', 'NC'].includes(selectedRouterType) &&
            defaultSelect.includes('mtRouterReturnStepDTO')
            && <Select
              name="returnType"
              onChange={changeReturnStep}
            />
          }
          {
            selectReturnType === 'DESIGNATED_OPERATION' &&
            <Lov
              name="returnOperationLov"
              onChange={changeReturnOperation}
            />
          }
        </Form>
      }
      { /* 子工艺路线 */}
      {
        currentRadio === 'ROUTER' &&
        <Form columns={2} labelWidth={112} dataSet={routerFormDs} disabled={!editStatus}>
          <C7nFormItemSort name="routerLov" itemWidth={['70%', '30%']}>
            <Lov name="routerLov" onChange={changeRouterId} />
            <TextField name="revision" disabled />
          </C7nFormItemSort>
          <br />
          <SelectBox name="stepFlag" onChange={changeSteFlag}>
            <Option value="keyStepFlag">
              {intl.get(`${modelPrompt}.keyStep`).d('关键')}
            </Option>
            <Option
              value="entryStepFlag"
              disabled={getOnlyFlag('entryStepFlag')}
            >
              {intl.get(`${modelPrompt}.entryStep`).d('入口')}
            </Option>
            {
              ['SPECIAL', 'NC'].includes(selectedRouterType) ? <Option value="mtRouterReturnStepDTO">
                {intl.get(`${modelPrompt}.returnStep`).d('返回')}
              </Option> : <Option value="routerDoneStepFlag">
                {intl.get(`${modelPrompt}.routerDoneStep`).d('完成')}
              </Option>
            }
          </SelectBox>
          {
            ['SPECIAL', 'NC'].includes(selectedRouterType) &&
            defaultSelect.includes('mtRouterReturnStepDTO')
            && <Select
              name="returnType"
              onChange={changeReturnStep}
            />
          }
          {
            selectReturnType === 'DESIGNATED_OPERATION' &&
            <Lov
              name="returnOperationLov"
              onChange={changeReturnOperation}
            />
          }
        </Form>
      }
    </div>
  );
};

// @ts-ignore
export default forwardRef(NewStepDrawer);
