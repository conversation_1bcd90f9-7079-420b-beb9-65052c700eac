/**
 * @feature 班次装配实绩
 * @date 2021-5-11
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';
import moment from 'moment';

const modelPrompt = 'tarzan.workshop.assembleReport.model';
const tenantId = getCurrentOrganizationId();

/**
 * 列表和详情页
 */
const entranceDS = () => ({
  primaryKey: 'tagCode',
  queryUrl: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-shift-assemble-actual/list/ui`,
  autoQuery: true,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  queryFields: [
    {
      name: 'workcell',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcell`).d('工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: 'always',
      noCache: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workcellId',
      bind: 'workcell.workcellId',
    },
    {
      name: 'calendarShiftDateFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.calendarShiftDateFrom`).d('时间从'),
      max: 'calendarShiftDateTo',
    },
    {
      name: 'calendarShiftDateTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.calendarShiftDateTo`).d('时间至'),
      min: 'calendarShiftDateFrom',
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次'),
      textField: 'description',
      valueField: 'description',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-operation-actual/shift-code/box`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          if (rows && rows.length) {
            const returnData = [];
            // eslint-disable-next-line array-callback-return
            rows.map((item, index) => {
              returnData.push({
                typeCode: index,
                description: rows[index],
              });
            });
            return returnData;
          } 
          return [];
          
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('calendarShiftDateFrom') && !record.get('calendarShiftDateTo');
        },
        lovPara: ({ record }) => {
          return {
            calendarShiftDateFrom: record.get('calendarShiftDateFrom')
              ? moment(record.get('calendarShiftDateFrom')).format('YYYY-MM-DD')
              : undefined,
            calendarShiftDateTo: record.get('calendarShiftDateTo')
              ? moment(record.get('calendarShiftDateTo')).format('YYYY-MM-DD')
              : undefined,
          };
        },
      },
    },
  ],
  fields: [
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元'),
    },
    {
      name: 'shiftDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftDate`).d('日期'),
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('实际装配物料'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('实际装配物料版本'),
    },
    {
      name: 'disPatchDemandQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disPatchDemandQty`).d('调用组件需求数量'),
    },
    {
      name: 'assembleQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleQty`).d('装配数量'),
    },
    {
      name: 'removeQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.removeQty`).d('移除数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-shift-assemble-actual/list/ui`,
        data: {
          ...data,
          calendarShiftDateFrom: data.calendarShiftDateFrom
            ? moment(data.calendarShiftDateFrom).format('YYYY-MM-DD')
            : undefined,
          calendarShiftDateTo: data.calendarShiftDateTo
            ? moment(data.calendarShiftDateTo).format('YYYY-MM-DD')
            : undefined,
        },
        method: 'GET',
      };
    },
  },
});

const detailDS = () => ({
  primaryKey: 'tagCode',
  queryUrl: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-shift-assemble-actual/list-detail/ui`,
  autoQuery: true,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  fields: [
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
    },
    {
      name: 'eoMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoMaterialCode`).d('执行作业物料'),
    },
    {
      name: 'eoMaterialRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoMaterialRevisionCode`).d('执行作业物料版本'),
    },
    {
      name: 'componentMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentMaterial`).d('组件物料'),
    },
    {
      name: 'componentRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentRevisionCode`).d('组件物料版本'),
    },
    {
      name: 'assembleExcessFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleExcessFlag`).d('强制装配标识'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'substituteFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.substituteFlag`).d('替代装配标识'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'disPatchDemandQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disPatchDemandQty`).d('调度组件需求数量'),
    },
    {
      name: 'assembleQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleQty`).d('装配数量'),
    },
    {
      name: 'removeQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.removeQty`).d('移除数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-shift-assemble-actual/list-detail/ui`,
        method: 'GET',
      };
    },
  },
});

export { entranceDS, detailDS };
