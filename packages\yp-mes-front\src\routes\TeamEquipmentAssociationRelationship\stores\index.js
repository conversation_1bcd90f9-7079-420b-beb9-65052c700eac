import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;
const modelPrompt = 'tarzan.receive.scrapBarcodeGeneration';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'classEquipmentId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      },
      {
        name: 'equipmentLov',
        type: 'object',
        lovCode: 'MT.MODEL.EQUIPMENT',
        ignore: 'always',
        required: true,
        label: intl.get(`${modelPrompt}.equipmentLov`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        label: intl.get(`${modelPrompt}.equipmentLov`).d('设备名称'),
        bind: 'equipmentLov.equipmentName',
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'equipmentCode',
        bind: 'equipmentLov.equipmentCode',
      },
      {
        name: 'equipmentAbbr',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentAbbr`).d('设备简称'),
      },
      {
        name: 'priority',
        type: 'number',
        min: 0,
        precision: 0,
        required: true,
        label: intl.get(`${modelPrompt}.priority`).d('优先级'),
      },
      {
        name: 'classCode',
        required: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.classCode`).d('班组编码'),
      },
      {
        name: 'className',
        type: 'string',
        required: true,
        label: intl.get(`${modelPrompt}.className`).d('班组名称'),
      },{
        name: 'areaLov',
        type: 'object',
        lovCode: 'MT.MODEL.AREA',
        ignore: 'always',
        required: true,
        label: intl.get(`${modelPrompt}.areaLov`).d('区域编码'),
      },
      {
        name: 'areaName',
        bind: 'areaLov.areaName',
        label: intl.get(`${modelPrompt}.areaLov`).d('区域名称'),
      },
      {
        name: 'areaId',
        bind: 'areaLov.areaId',
      },
      {
        name: 'areaCode',
        bind: 'areaLov.areaCode',
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
    ],
    queryFields: [
      {
        name: 'equipmentLov',
        type: 'object',
        lovCode: 'MT.MODEL.EQUIPMENT',
        label: intl.get(`${modelPrompt}.equipmentLov`).d('设备编码'),
        ignore: 'always',
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'classCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.classCode`).d('班组编码'),
      },
      {
        name: 'className',
        type: 'string',
        label: intl.get(`${modelPrompt}.className`).d('班组名称'),
      },
      {
        name: 'areaLov',
        type: 'object',
        lovCode: 'MT.MODEL.AREA',
        label: intl.get(`${modelPrompt}.areaLov`).d('区域编码'),
        ignore: 'always',
      },
      {
        name: 'areaId',
        bind: 'areaLov.areaId',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-class-equipments/query`,
          method: 'GET',
        };
      },
      submit: ({ data }) => {
        return {
          url: `${Host}/v1/${tenantId}/hme-class-equipments/save`,
          data,
          method: 'POST',
        };
      },
    },
  };
};



export {tableDS};
