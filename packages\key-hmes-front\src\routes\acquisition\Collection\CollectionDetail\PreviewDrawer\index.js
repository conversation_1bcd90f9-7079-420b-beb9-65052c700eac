/**
 * @Description: 数据收集组-数据采集实绩预览
 * @Author: <<EMAIL>>
 * @Date: 2021-04-29 12:51:31
 * @LastEditTime: 2023-01-10 10:58:23
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect } from 'react';
import intl from 'utils/intl';
import request from 'utils/request';
import { Tag, Collapse } from 'choerodon-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { getResponse } from '@utils/utils';
import PreviewList from './PreviewList';
import styles from './index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.acquisition.collection';

function buildValidateInfoItemName(item) {
  return `tag-${item.tagCode}-id-${item.tagId}-validate`;
}

// 所有采集信息值的验证
const valueValidateInfo = {};
// 所有采集信息必输条数的验证
const numberValidateInfo = {};

let resultClassName = 'blue';

const PreviewDrawer = props => {
  const resultType = {
    noValue: intl.get(`${modelPrompt}.noValue`).d('待采集'),
    qualified: intl.get(`${modelPrompt}.qualified`).d('合格'),
    halfQualified: intl.get(`${modelPrompt}.halfQualified`).d('合格（未采集完成）'),
    unqualified: intl.get(`${modelPrompt}.unqualified`).d('不合格'),
  };
  const { dataSource = [], cardDetail = {} } = props;
  const [result, setResult] = useState(resultType.noValue);
  const [tagGroupTypeArr, setTagGroupTypeArr] = useState([]);
  const [businessTypeArr, setBusinessTypeArr] = useState([]);

  useEffect(() => {
    async function fetchData() {
      // 业务类型
      const tagGroupTypeArry = await request(
        `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_GROUP_TYPE`,
        {
          method: 'GET',
        },
      );
      setTagGroupTypeArr((getResponse(tagGroupTypeArry) || {}).rows);
      // 收集组类型
      const businessTypeArry = await request(
        `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=TAG_GROUP_BUSINESS_TYPE`,
        {
          method: 'GET',
        },
      );
      setBusinessTypeArr((getResponse(businessTypeArry) || {}).rows);
    }
    fetchData();
  }, []);

  const getCollectionResult = () => {
    let valueValidate = true;
    let numberValidate = true;
    if (!Object.keys(valueValidateInfo).length) {
      // 待输入
      resultClassName = 'blue';
      setResult(resultType.noValue);
      return;
    }
    Object.keys(valueValidateInfo).forEach(element => {
      valueValidate = valueValidate && valueValidateInfo[element];
    });
    Object.keys(numberValidateInfo).forEach(element => {
      numberValidate = numberValidate && numberValidateInfo[element];
    });
    if (valueValidate) {
      if (numberValidate) {
        resultClassName = 'green';
        setResult(resultType.qualified);
      } else {
        resultClassName = 'green';
        setResult(resultType.halfQualified);
      }
    } else {
      resultClassName = 'red';
      setResult(resultType.unqualified);
    }
  };

  const setItemValidateInfo = (item, value, number) => {
    const id = buildValidateInfoItemName(item);
    if (!value) {
      // 列表没有值，删除操作
      delete valueValidateInfo[id];
      numberValidateInfo[id] = number;
      getCollectionResult();
      return;
    }
    valueValidateInfo[id] = value === 'Y';
    numberValidateInfo[id] = number === 'Y';
    // else if ((valueValidateInfo[id] !== value) || (numberValidateInfo[id] !== number)) {
    //   // 验证信息有变化
    //   valueValidateInfo[id] = value;
    //   numberValidateInfo[id] = number;
    // }
    getCollectionResult();
  };

  const childProps = {
    setItemValidateInfo,
    dataSource,
  };

  return (
    <>
      <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
        <Panel header={intl.get(`${modelPrompt}.tagGroup`).d('数据收集组')} key="basicInfo">
          <ul className={styles['hcm-collection-group-card']}>
            <li>
              <b>{intl.get(`${modelPrompt}.tagGroupCode`).d('收集组编码')}</b>
              <span>{`：${cardDetail.tagGroupCode || ''}`}</span>
            </li>
            <li>
              <b>{intl.get(`${modelPrompt}.tagGroupDescription`).d('收集组描述')}</b>
              <span>{`：${cardDetail.tagGroupDescription || ''}`}</span>
            </li>
            <li>
              <b>{intl.get(`${modelPrompt}.tagGroupType`).d('收集组类型')}</b>
              <span>
                {`：${(
                  tagGroupTypeArr.filter(item => item.typeCode === cardDetail.tagGroupType)[0] || {}
                ).description || ''}`}
              </span>
            </li>
            <li>
              <b>{intl.get(`${modelPrompt}.businessType`).d('业务类型')}</b>
              <span>
                {`：${(
                  businessTypeArr.filter(item => item.typeCode === cardDetail.businessType)[0] || {}
                ).description || ''}`}
              </span>
            </li>
            <li>
              <b>{intl.get(`${modelPrompt}.collectionResult`).d('采集结果')}：</b>
              {/* {resultIcon} */}
              <Tag color={resultClassName}>{result}</Tag>
              {/* <span>{result}</span> */}
            </li>
          </ul>
        </Panel>
        <Panel header={intl.get(`${modelPrompt}.collectionItem`).d('数据收集项')} key="location">
          <PreviewList {...childProps} />
        </Panel>
      </Collapse>
    </>
  );
};

export default PreviewDrawer;
