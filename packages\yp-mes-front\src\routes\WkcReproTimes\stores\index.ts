/*
 * @Description: 复采次数配置-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2024-02-22 09:44:29
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-02-22 11:46:23
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.wkcReproTimes';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellLov`).d('工位编码'),
      lovCode: 'HME.WORKCELL_CODE',
      lovPara: {
        tenantId,
      },
      textField: 'organizationCode',
      ignore: FieldIgnore.always,
    },
    {
      name: 'workcellId',
      bind: 'workcellLov.organizationId',
    },
    {
      name: 'workcellCode',
      bind: 'workcellLov.organizationCode',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工位名称'),
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineLov`).d('产线编码'),
      lovCode: 'MT.MODEL.PRODLINE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
  ],
  fields: [
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellLov`).d('工位编码'),
      lovCode: 'HME.WORKCELL_CODE',
      lovPara: {
        tenantId,
      },
      textField: 'organizationCode',
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'workcellId',
      bind: 'workcellLov.organizationId',
    },
    {
      name: 'workcellCode',
      bind: 'workcellLov.organizationCode',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工位名称'),
      bind: 'workcellLov.organizationName',
    },
    {
      name: 'organizationId',
    },
    {
      name: 'organizationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
    },
    {
      name: 'organizationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
    },
    {
      name: 'reProTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.reproTimes`).d('复采次数'),
      min: 0,
      validator: (value) => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.reproTimes`).d('复采次数') + intl
            .get(`${modelPrompt}.validation.objectQtyMoreThanZero`)
            .d(`必须大于0, 请检查!`);
        }
      },
      precision: 0,
      step: 1,
      dynamicProps: {
        required: ({ record }) => !record?.getState('deleteFlag'),
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wkc-repro-times/query`,
        method: 'POST',
      };
    },
  },
});

export { tableDS };