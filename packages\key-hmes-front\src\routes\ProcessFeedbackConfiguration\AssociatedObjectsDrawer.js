import React, { useState, useMemo, useEffect } from 'react';
import { Table, Button, Lov, Select, Switch, Form, Spin } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
// import notification from 'utils/notification';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hmes.materialPreventError';

export default ({
  singleAssObjectDs,
  enabledDs,
  handleDeleteAssociatDetail,
  loading,
  record: propsRecord,
  handleDrawer,
  associatedObjectsDrawerClose,
  associatedObjectsDrawerSubmit,
}) => {
  const [selected, setSelected] = useState({});
  const selectedProxy = useMemo(() => ({}), []);
  const [canEdit, setCanEdit] = useState(false);
  selectedProxy.selected = selected;
  const typeName = {
    // MATERIAL: intl.get(`${modelPrompt}.MATERIAL`).d('物料'),
    // MATERIAL_CATEGORY: intl.get(`${modelPrompt}.MATERIAL_CATEGORY`).d('物料类别'),
    OPERATION: intl.get(`${modelPrompt}.OPERATION`).d('工艺'),
    // WORKCELL: intl.get(`${modelPrompt}.WORKCELL`).d('工作单元'),
    // EQUIPMENT: intl.get(`${modelPrompt}.AREA`).d('设备'),
    // EQUIPMENT_CATEGORY: intl.get(`${modelPrompt}.PROD_LINE`).d('设备类别'),
  };

  useEffect(() => {
    if (singleAssObjectDs.toData() && singleAssObjectDs.toData().length > 0) {
      const cacheSelected = { OPERATION: true };
      setSelected(cacheSelected);
      const nowData = singleAssObjectDs.toData().map(item => {
        return {
          ...item,
          objectType: 'OPERATION',
        };
      });
      singleAssObjectDs.loadData(nowData);
    } else {
      setSelected([]);
      singleAssObjectDs.loadData([]);
    }
  }, []);

  const selectObjTypeDiv = type => {
    if (!canEdit) {
      return;
    }
    if (selected[type]) {
      // 已选过的类型，再次点击为删除行
      const newList = singleAssObjectDs.filter(record => record.get('objectType') !== type);
      const newSelected = {
        ...selectedProxy.selected,
        [type]: false,
      };
      singleAssObjectDs.loadData(newList);
      setSelected(newSelected);
      handleDeleteAssociatDetail();
    } else {
      // 新增行
      const newRow = { uuid: uuid(), objectType: type };
      singleAssObjectDs.create(newRow);
      setSelected({
        ...selectedProxy.selected,
        [type]: true,
      });
    }
  };

  const changeObject = async (lovRecords, record) => {
    if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
      record.getField('objectRevision').set('required', true);
      record.set('revisionFlag', 'Y');
      record.set('operationRevision', lovRecords.revisionCode);
    } else {
      record.getField('objectRevision').set('required', false);
      record.set('objectRevision', '');
      record.set('revisionFlag', 'N');
    }
  };

  const handleEdit = () => {
    setCanEdit(true);
    handleDrawer(true);
  };

  const handleCancel = () => {
    setCanEdit(false);
    handleDrawer(false);
  };

  const columns = [
    {
      header: null,
      align: 'center',
      width: 60,
      renderer: ({ record }) => {
        const objType = record.get('objectType');
        return (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              selectObjTypeDiv(objType);
            }}
          >
            <Button disabled={!canEdit} funcType="flat" icon="remove" shape="circle" size="small" />
          </Popconfirm>
        );
      },
      lock: 'left',
    },
    {
      name: 'objectType',
      width: 120,
      renderer: ({ record }) => typeName[record.get('objectType')],
    },
    {
      name: 'objectLov',
      width: 180,
      renderer: ({ record }) => record.get('operationName'),
      editor: record => {
        return (
          canEdit && (
            <Lov
              dataSet={singleAssObjectDs}
              name="objectLov"
              onChange={lovRecords => changeObject(lovRecords, record)}
            />
          )
        );
      },
    },
    {
      name: 'operationRevision',
      width: 120,
      renderer: ({ record }) => record.get('operationRevision'),
      editor: record => {
        return (
          canEdit &&
          record.get('revisionFlag') === 'Y' &&
          record.data.objectType === 'MATERIAL' && (
            <Select name="operationRevision" dataSet={singleAssObjectDs} />
          )
        );
      },
    },
    {
      name: 'operationDesc',
    },
  ];

  return (
    <>
      {/* <Header title=''> */}
      {canEdit ? (
        <Button style={{ float: 'right' }} onClick={handleCancel}>
          {intl.get('tarzan.common.button.cancel').d('取消')}
        </Button>
      ) : (
        <Button icon="edit" color="primary" style={{ float: 'right' }} onClick={handleEdit}>
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </Button>
      )}
      {/* </Header> */}
      <div className={styles['card-select-wrapper']}>
        {Object.keys(typeName).map(item => {
          if (selected[item]) {
            return (
              <Popconfirm
                title={intl
                  .get(`${modelPrompt}.message.confirm.delete`, {
                    typeName: typeName[item],
                  })
                  .d(`是否确认删除关联对象类型为“${typeName[item]}”的数据?`)}
                onConfirm={() => {
                  selectObjTypeDiv(item);
                }}
              >
                <div key={item} className={styles['card-select']} data-selected disabled={!canEdit}>
                  {typeName[item]}
                </div>
              </Popconfirm>
            );
          }
          return (
            <div
              key={item}
              onClick={() => {
                selectObjTypeDiv(item);
              }}
              className={styles['card-select']}
              disabled={!canEdit}
            >
              {typeName[item]}
            </div>
          );
        })}
      </div>
      <Spin spinning={loading}>
        <Form dataSet={enabledDs} columns={1}>
          <Switch name="enableFlag" disabled={!canEdit} />
        </Form>
        <Table dataSet={singleAssObjectDs} columns={columns} />
        <div style={{ bottom: 30, position: 'fixed', right: 30 }}>
          <Button
            onClick={() => {
              associatedObjectsDrawerClose();
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          {canEdit && (
            <Button
              type="submit"
              onClick={() => {
                associatedObjectsDrawerSubmit(propsRecord);
              }}
              color={ButtonColor.primary}
            >
              {intl.get('tarzan.common.button.confirm').d('确定')}
            </Button>
          )}
        </div>
      </Spin>
    </>
  );
};
