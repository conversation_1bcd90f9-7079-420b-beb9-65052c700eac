import React, { useMemo, useEffect, useState } from 'react';
import {
  DataSet,
  Table,
  Button,
  Switch,
} from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { tableDS  } from './stores';
import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import { getCurrentOrganizationId } from 'utils/utils';
import { fetchDefaultSite } from '../../services/api';

// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.TeamEquipmentAssociationRelationship';

const RawMaterialBarcodeTraceabilityReport = observer(() => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const [siteInfo, setSiteInfo] = useState({});

  useEffect(() => {
    fetchDefaultSite().then(res => {
      if (res && res.success) {
        setSiteInfo(res.rows);
      }
    });
  }, []);
  const columns = [
    // {
    //   name: 'timeQuantumDesc',
    //   type: 'column',
    //   lock: 'left',
    //   header: () => (
    //     <Button
    //       icon="add"
    //       funcType="flat"
    //       shape="circle"
    //       size="small"
    //       onClick={handleCreate}
    //     />),
    //   renderer: ({record}) => (
    //     <Popconfirm title="是否确认删除？" onConfirm={() => handleDelete(record)} okText="确认" cancelText="取消">
    //       <Button
    //         icon="remove"
    //         funcType="flat"
    //         shape="circle"
    //         size="small"
    //         disabled={!canEdit}
    //       /></Popconfirm>
    //   ),
    // },
    {
      name: 'siteCode',
    },
    {
      name: 'equipmentLov',
      editor: record => record.getState('editing'),
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'equipmentAbbr',
      editor: record => record.getState('editing'),
    },
    {
      name: 'priority',
      editor: record => record.getState('editing'),
    },
    {
      name: 'classCode',
      editor: record => record.getState('editing'),
    },
    {
      name: 'className',
      editor: record => record.getState('editing'),
    },{
      name: 'areaLov',
      editor: record => record.getState('editing'),
    },
    {
      name: 'areaName',
    },
    {
      name: 'enableFlag',
      editor: record => record.getState('editing') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      width: 150,
      align: 'center',
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      renderer: ({ record }) =>
        record.getState('editing') ? (
          <>
            <Button color="primary" funcType="flat" onClick={() => handleEdit(record, false)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button color="primary" funcType="flat" onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        ) : (
          <>
            <Button color="primary"
              disabled={tableDs.records.some(record => record.getState('editing'))}
              funcType="flat" onClick={() => handleEdit(record, true)}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Button color="primary"
              disabled={tableDs.records.some(record => record.getState('editing'))} funcType="flat"
              onClick={() => handleDelete(record, false)}>
              {intl.get('tarzan.common.button.delete').d('删除')}
            </Button></>
        ),
      lock: 'right',
    },
  ];
  const handleDelete = async (record) => {
    await tableDs.delete(record)
  }
  const handleSave = async () => {
    const p = tableDs.records.map(item => item?.validate())
    const resp = await Promise.all(p)
    if(resp.some(item => !item))return
    await tableDs.submit()
    await tableDs.query();
  }

  const handleCreate =() => {
    tableDs.create({
      ...siteInfo,
    }, 0)
    tableDs.current?.setState('editing', true)
  }
  const handleEdit = (record, flag) => {
    record.setState('editing', flag);
    if(!flag){
      if(record.status === 'add'){
        tableDs.remove(record)
      }else{
        record.reset()
      }
    }
  }

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/HME.CLASS_EQUIPMENT`,
      title: intl.get('tarzan.hmes.teamEquipmentAssociationRelationship.import').d('班组设备关联关系导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('班组设备关联关系')}>
        <Button
          type="c7n-pro"
          icon="add"
          color='primary'
          disabled={tableDs.records.some(record => record.getState('editing'))}
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新增')}
        </Button>
        <Button color='primary' icon="daorucanshu" onClick={handleImport}>{intl.get('tarzan.common.button.import').d('导入')}</Button>
      </Header>
      <Content>
        <Table
          searchCode="TeamEquipmentAssociationRelationship"
          customizedCode="TeamEquipmentAssociationRelationship"
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.teamEquipmentAssociationRelationship', 'tarzan.common'],
})(RawMaterialBarcodeTraceabilityReport);
