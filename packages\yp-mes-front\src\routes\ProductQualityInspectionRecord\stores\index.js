import { Host, BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/yp-mes-38510`;
const modelPrompt = 'tarzan.receive.scrapBarcodeGeneration';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'ncRecordId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码标识'),
      },
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      },
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'qualityStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
      },
    ],
    queryFields: [
      {
        name: 'identificationstr',
        required: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.identificationstr`).d('条码号'),
      },
      {
        name: 'materialLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
        lovCode: 'MT.MATERIAL',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'materialCode',
      },
      {
        name: 'materialId',
        bind: 'materialLov.materialId',
      },
      {
        name: 'materialCode',
        bind: 'materialLov.materialCode',
      },
      {
        name: 'qualityStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
        textField: 'description',
        valueField: 'statusCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
        lookupAxiosConfig: {
          transformResponse(data) {
            // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'workOrderNumstr',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNumstr`).d('生产指令'),
      },
      {
        name: 'prodLineLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineName`).d('生产线'),
        lovCode: 'MT.MODEL.PRODLINE',
        lovPara: { tenantId },
        textField: 'prodLineName',
        ignore: 'always',
      },
      {
        name: 'prodLineId',
        bind: 'prodLineLov.prodLineId',
      },
      {
        name: 'prodLineCode',
        bind: 'prodLineLov.prodLineCode',
      },
      // {
      //   name: 'startTime',
      //   type: 'dateTime',
      //   label: intl.get(`${modelPrompt}.startTime`).d('时间开始'),
      // },
      // {
      //   name: 'endTime',
      //   type: 'dateTime',
      //   label: intl.get(`${modelPrompt}.endTime`).d('时间结束'),
      // },
    ],
    transport: {
      read: ({ data }) => {
        const code = data.identificationstr;
        const code2 = data.workOrderNumstr;
        const dataParams = {
          ...data,
          processBarcodes: data.identificationstr ? code.split(',') : null,
          workOrderNums: data.workOrderNumstr ? code2.split(',') : null,
        };
        if (!code) {
          delete dataParams.processBarcodes;
        }
        if (!code2) {
          delete dataParams.workOrderNums;
        }
        return {
          url: `${Host}/v1/${tenantId}/hme-quality-inspect/record/query/ui`,
          data: dataParams,
          method: 'GET',
        };
      },
    },
  };
};

const detailDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'ncRecordId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'ncRecordNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordNum`).d('不良记录编码'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('异常工艺'),
      },
      {
        name: 'workcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellName`).d('异常工作单元'),
      },
      {
        name: 'disposalFunctionDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalFunctionDescription`).d('处置结果'),
      },
      {
        name: 'review',
        type: 'string',
        label: intl.get(`${modelPrompt}.review`).d('评审附加'),
      },
      {
        name: 'disposalTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalTime`).d('处置时间'),
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('处置人'),
      },
      {
        name: 'remark',
        type: 'string',
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-quality-inspect/disposal/detail/query/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const drawerDS = () => {
  return {
    name: 'tableDS',
    // primaryKey: 'traceRelId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      },
      {
        name: 'ncCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('不良工艺'),
      },
      {
        name: 'ncStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncStatusDesc`).d('不良状态'),
      },
      {
        name: 'ncRecordTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordTime`).d('不良记录时间'),
      },
      {
        name: 'ncUserName',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncUserName`).d('记录人'),
      },
      {
        name: 'lastUpdateTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateTime`).d('不良关闭时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-quality-inspect/nc/detail/query/ui`,
          method: 'GET',
        };
      },
    },
  };
};

export { tableDS, drawerDS, detailDS };
