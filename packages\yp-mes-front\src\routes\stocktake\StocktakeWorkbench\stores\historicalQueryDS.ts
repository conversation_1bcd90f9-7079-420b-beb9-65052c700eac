import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.mes.event.creationOfScrapInventoryFormTrial';
const tenantId = getCurrentOrganizationId();
const BASE_SERVER = '/tznr';

export const infoDS: () => DataSetProps = () => ({
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'stocktakeNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeNum`).d('盘点单据编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('盘点单标识'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('盘点单说明'),
    },
    {
      name: 'stocktakeStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeStatus`).d('状态'),
    },
    {
      name: 'openFlagDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.openFlagDesc`).d('是否明盘'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'areaLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('盘点当前区域'),
    },
    {
      name: 'createDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createDate`).d('创建时间'),
    },
    {
      name: 'createByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createByName`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdateByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateByName`).d('最后更新人'),
    },
  ],
  queryFields: [
    {
      name: 'dateObj',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.`).d('创建时间'),
      range: ['start', 'end'],
      ignore: FieldIgnore.always,
    },
    {
      name: 'creationDateFrom',
      bind: 'dateObj.start',
    },
    {
      name: 'creationDateTo',
      bind: 'dateObj.end',
    },
  ],
  transport: {
    read: () => ({
      url: `${BASE_SERVER}/v1/${tenantId}/mt-stocktake-doc/his/list`,
      method: 'POST',
    }),
  },
});
