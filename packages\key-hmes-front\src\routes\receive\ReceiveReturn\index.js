/**
 * @Description:  领退料工作台-入口页
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table, Dropdown, Modal, Button, Row, Col, TextField, Form, Icon } from 'choerodon-ui/pro';
import { Badge, Menu, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import ExcelExportPro from 'components/ExcelExportPro';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import LovModal from '@/components/BatchInput/LovModal';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import { headerTableDS, lineTableDS } from './stores/ListDS';
import styles from './index.module.less';
import MaterialLotDrawer from './MaterialLotDrawer';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.receiveReturn';
let modalAssembly;

const lugeUrl = '';
const HMES_BASIC = BASIC.HMES_BASIC;

const Order = props => {

  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;

  // 判断头搜索条件切换
  const [siteId, setSiteId] = useState();
  const [selectedStatus, setSelectedStatus] = useState(undefined);
  const [printIds, setPrintIds] = useState([]); // 头表格选择的id
  const [loading, setLoading] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);


  // 头选中行instructionDocType
  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    if (props?.history?.action === 'PUSH') {
      headerTableDs.query(props.headerTableDs.currentPage);
      handleLineTableChange({
        dataSet: headerTableDs,
      });
    }
  }, []);

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.queryDataSet.addEventListener
        : headerTableDs.queryDataSet.removeEventListener;
      handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
      handler.call(headerTableDs, 'batchSelect', handleLineTableChange);
      handler.call(headerTableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (data.siteId !== siteId) {
      setSiteId(data.siteId);
      if (siteId) {
        record.set('prodLine', null);
      }
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 列表刷新清除头单选状态
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      // headerRowClick(dataSet?.current);
    } else {
      queryLineTable();
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    const _selectedStatus = [];
    const _printIds = [];
    const completedList = ['1_PROCESSING', '1_COMPLETED', '2_PROCESSING', 'COMPLETED'];
    dataSet.selected.forEach(item => {
      const instructionDocStatus = item?.data?.instructionDocStatus;
      _printIds.push(item?.data?.instructionDocId);
      if (completedList.indexOf(instructionDocStatus) > -1) {
        if (_selectedStatus.indexOf('COMPLETED') === -1) {
          _selectedStatus.push('COMPLETED');
        }
      } else if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    setSelectedStatus(_selectedStatus.length === 1 ? _selectedStatus[0] : undefined);
    setPrintIds(_printIds);
  };

  // 行列表数据查询
  const queryLineTable = data => {
    lineTableDs.setQueryParameter('instructionDocId', data?.instructionDocId);
    lineTableDs.setQueryParameter('instructionDocType', data?.instructionDocType);
    lineTableDs.setQueryParameter('workOrderId', data?.workOrderId);
    lineTableDs.setQueryParameter('prodLineId', data?.prodLineId);
    lineTableDs.setQueryParameter('materialId', data?.materialId);
    lineTableDs.setQueryParameter('revisionCode', data?.revisionCode);
    lineTableDs.query();
  };

  // 操作列渲染
  const optionRender = record => (
    <>
      <a
        style={{ marginRight: '8px' }}
        onClick={() => {
          handleMaterialLotDetail(record);
        }}
      >
        {intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细')}
      </a>
      {/* 限制单据状态为新增时可点击 */}
      <PermissionButton
        type="text"
        disabled={record.get('instructionStatus') !== 'NEW'}
        onClick={() => handleCancel(record)}
        permissionList={[
          {
            code: `${path}.button.edit`,
            type: 'button',
            meaning: '列表页-编辑新建删除复制按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.line.button.cancel`).d('行取消')}
      </PermissionButton>
    </>
  );

  // 组件信息
  const handleMaterialLotDetail = async record => {
    const recordDetail = record?.toData() || {};
    modalAssembly = Modal.open({
      title: intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <MaterialLotDrawer
          record={{
            instructionId: recordDetail.instructionId,
            docTypeTag: headerTableDs.current.get('docTypeTag'),
            instructionDocType: headerTableDs.current.get('instructionDocType'),
            instructionDocLineId: recordDetail.instructionDocLineId,
          }}
          customizeTable={customizeTable}
        />
      ),
      footer: (
        <Button onClick={() => modalAssembly.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 行取消
  const handleCancel = async record => {
    return request(
      `${HMES_BASIC}${lugeUrl}/v1/${tenantId}/receive-return-bill/line/cancel/ui`,
      {
        method: 'POST',
        body: {
          instructionId: record.get('instructionId'),
          businessType: 'CANCEL',
        },
      },
    ).then(res => {
      if (res?.success) {
        lineTableDs.query(props.lineTableDs.currentPage);
        headerTableDs.query(props.headerTableDs.currentPage);
        handleLineTableChange({
          dataSet: headerTableDs,
        });
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'instructionDocNum',
      width: 200,
      renderer: ({ record, value }) => {
        if (record.data.instructionDocStatus === 'RELEASED' || record.data.instructionDocStatus === 'NEW') {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/hmes/receive/receive-return-new/detail/${record.data.instructionDocId}/${record.data.instructionDocType}/${record.data.docTypeTag}`,
                );
              }}
            >
              {value}
            </a>
          );
        }
        return value;

      },
      lock: 'left',
    },
    {
      name: 'instructionDocType',
      // width: 140,
    },
    {
      name: 'instructionDocStatusDesc',
      // width: 140,
    },
    {
      name: 'siteCode',
      // width: 140,
    },
    {
      name: 'demandTime',
      // width: 140,
      align: 'center',
    },
    {
      name: 'remark',
      // width: 140,
    },
    {
      name: 'printTimes',
      width: 100,
    },
    {
      name: 'realName',
      // width: 140,
    },
    {
      name: 'creationDate',
      // width: 140,
      align: 'center',
    },
  ];

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'lineNumber',
      width: 100,
      lock: 'left',
    },
    {
      name: 'materialCode',
      width: 140,
      lock: 'left',
    },
    {
      name: 'revisionCode',
      width: 100,
      lock: 'left',
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'quantity',
      width: 100,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'instructionStatusDesc',
      width: 100,
    },
    {
      name: 'soNum',
      title: intl.get(`${modelPrompt}.soNumSoLineNumber`).d('销单/行号'),
      width: 140,
      renderer: ({ record }) => {
        if (record.data.soNumber) {
          return `${record.data.soNumber || ''}/${record.data.soLineNum || ''}`;
        }
        return '';

      },
    },
    {
      name: 'minPackagingQty',
      width: 150,
    },
    {
      name: 'sourceOrderNum',
      width: 140,
      renderer: ({ record, value }) => (
        <a
          onClick={() => {
            props.history.push(
              `/hmes/workshop/production-order-mgt/detail/${record.data.sourceOrderId}`,
            );
          }}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'productionLineCode',
      width: 140,
    },
    {
      name: 'receivedQty',
      width: 100,
    },
    {
      name: 'signedQty',
      width: 100,
    },
    {
      name: 'returnedQty',
      width: 100,
    },
    {
      name: 'fromIdentifyType',
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'fromLocatorCode',
      width: 140,
    },
    // {
    //   name: 'sourceSubLocatorCode',
    //   width: 140,
    // },
    {
      name: 'toIdentifyType',
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'toLocatorCode',
      width: 140,
    },
    {
      name: 'subLocatorCode',
      width: 140,
    },
    {
      name: 'targetLocation',
      width: 140,
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceTypeDesc',
      width: 140,
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
    },
    {
      name: 'specifiedLot',
      width: 120,
    },
    {
      name: 'specifiedLevel',
      width: 120,
    },
    {
      name: 'option',
      fixed: 'right',
      lock: 'right',
      width: 200,
      align: 'center',
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => optionRender(record),
    },
  ];

  const headerRowClick = record => {
    queryLineTable(record?.toData());
  };

  const getExportQueryParams = () => {
    return {
      ...headerTableDs?.queryDataSet?.toJSONData()[0],
      instructionDocIds: headerTableDs.selected.map(item => item.get('instructionDocId'))
    };
  };

  const clickMenu = async ({ key }) => {
    setLoading(true);
    const docList =
      headerTableDs?.selected?.map(item => {
        return {
          instructionDocId: item?.get('instructionDocId'),
        };
      }) || [];

    return request(
      `${HMES_BASIC}${lugeUrl}/v1/${tenantId}/receive-return-bill/state/change/ui`,
      {
        method: 'POST',
        body: {
          docList,
          operationType: key,
        },
      },
    ).then(res => {
      setLoading(false);
      if (res?.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        headerTableDs.clearCachedSelected();
        setSelectedStatus(undefined);
        setPrintIds([]);
        headerTableDs.query(props.headerTableDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  const createDelivery = () => {
    props.history.push(`/hmes/receive/receive-return-new/detail/create/null/null`);
  };

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
    }
  };

  const toggleForm = () => {
    setExpandForm(!expandForm);
  }

  const handleSearch = async () => {
    headerTableDs.query();
  };

  const renderQueryBar = ({ buttons, queryDataSet, queryFields, dataSet }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              {queryFields.slice(0, 3)}
              {expandForm && (
                <>
                  {queryFields.slice(3, queryFields.length - 2)}
                  <TextField
                    name="materialLotCodes"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          onClick={() => onOpenInputModal(true, 'materialLotCodes', intl.get(`${modelPrompt}.materialLotCodes`).d('物料批'))}
                        />
                      </div>
                    }
                  />
                  <TextField
                    name="containerCodes"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          onClick={() => onOpenInputModal(true, 'containerCodes', intl.get(`${modelPrompt}.containerCodes`).d('容器编码'))}
                        />
                      </div>
                    }
                  />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button funcType={FuncType.link} icon={
                expandForm ? 'expand_less' : 'expand_more'
              } onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button onClick={() => {
                queryDataSet.current.reset();
                dataSet.fireEvent('queryBarReset', {
                  dataSet,
                  queryFields,
                });
              }}>
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button onClick={handleSearch} color={ButtonColor.primary}>
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.receiveReturn`).d('领退料工作台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={createDelivery}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.createDelivery`).d('创建单据')}
        </PermissionButton>
        <Dropdown
          overlay={
            <Menu onClick={clickMenu} className={styles['split-menu']}>
              {selectedStatus === 'RELEASED' && (
                <Menu.Item key="CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
                  </a>
                </Menu.Item>
              )}
              {/* 新增状态，仅选择新建状态的单据可选择 */}
              {selectedStatus === 'NEW' && (
                <Menu.Item key="RELEASED">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.released`).d('下达')}
                  </a>
                </Menu.Item>
              )}
              {selectedStatus === 'NEW' && (
                <Menu.Item key="CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
                  </a>
                </Menu.Item>
              )}
              {(selectedStatus === 'COMPLETED' ||
                selectedStatus === '1_COMPLETED' ||
                selectedStatus === '2_COMPLETED' ||
                selectedStatus === 'PROCESSING' ||
                selectedStatus === '1_PROCESSING' ||
                selectedStatus === '2_PROCESSING')
                && (
                  <Menu.Item key="CLOSED">
                    <a target="_blank" rel="noopener noreferrer">
                      {intl.get(`${modelPrompt}.button.close`).d('关闭')}
                    </a>
                  </Menu.Item>)}
            </Menu>
          }
          trigger={['click']}
          disabled={
            [
              'NEW',
              'RELEASED',
              'COMPLETED',
              '1_COMPLETED',
              '2_COMPLETED',
              'PROCESSING',
              '1_PROCESSING',
              '2_PROCESSING',
            ].indexOf(selectedStatus) === -1
          }
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            loading={loading}
            disabled={
              [
                'NEW',
                'RELEASED',
                'COMPLETED',
                '1_COMPLETED',
                '2_COMPLETED',
                'PROCESSING',
                '1_PROCESSING',
                '2_PROCESSING',
              ].indexOf(selectedStatus) === -1
            }
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <ExcelExportPro
          method="POST"
          exportAsync={false}
          allBody
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/receive-return-bill/receipt/export`}
          queryParams={getExportQueryParams}
          buttonText="导出"
        />
        <FRPrintButton
          kid="PICK_RETURN_WORKBENCH"
          queryParams={printIds}
          disabled={!(printIds.length > 0)}
          printObjectType="INSTRUCTION_DOC"
        />
      </Header>
      <Content>
        {customizeTable(
          {
            // ${BASIC.CUSZ_CODE_BEFORE}
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.HEAD`,
          },
          <Table
            searchCode="ltlgzt1"
            customizedCode="ltlgzt1"
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            queryBarProps={{
              fuzzyQuery: false,
            }}
            queryBar={renderQueryBar}
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            {lineTableDs && (
              customizeTable(
                {
                  // ${BASIC.CUSZ_CODE_BEFORE}
                  code: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.LINE`,
                },
                <Table
                  customizedCode="ltlgzt2"
                  className={styles['expand-table']}
                  dataSet={lineTableDs}
                  highLightRow={false}
                  columns={lineTableColumns}
                />,
              )
            )}

          </Panel>
        </Collapse>
        <LovModal
          inputLovDS={inputLovDS}
          inputLovFlag={inputLovFlag}
          inputLovTitle={inputLovTitle}
          inputLovVisible={inputLovVisible}
          targetDS={headerTableDs}
          onOpenInputModal={onOpenInputModal}
        />
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.receive.receiveReturn', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    //
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_MATERIAL_LOT.QUERY`,
    ],
  }),
)(Order);
