/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-06-23 10:15:52
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-06-24 09:23:27
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\WipBarcodeBatchComplete\stores\listPageDs.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'hmes.wipBarcodeBatchComplete';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'barcode',
    selection: DataSetSelection.multiple,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'processBarcodes',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.processBarcodes`).d('条码号'),
          required: true,
        },
      ],
    }),
    fields: [
      {
        name: 'identification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.identification`).d('条码'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'qualityStatusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      },
      {
        name: 'workOrderNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单号'),
      },
      {
        name: 'woTypeDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.woTypeDesc`).d('工单类型'),
      },
      {
        name: 'currentOperationDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.currentOperationDesc`).d('当前工序'),
      },
      {
        name: 'endOperationDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.endOperationDesc`).d('末工序'),
      },
      {
        name: 'ncCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      },
    ],
    transport: {
      read: ({ data }) => {
        let processBarcodes = data.processBarcodes;
        if (processBarcodes) {
          processBarcodes = processBarcodes.split(/,|\s|\n/).filter(Boolean);
        }
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-working-eo-batch-complete/query`,
          method: 'POST',
          data: processBarcodes,
        };
      },
    },
  });

export default listPageFactory;