import request from 'utils/request';
import { BASIC } from '@utils/config';
import { parseParameters, getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

/**
 * 查询号码段表格数据
 * @async
 * @function fetchMaintainNumberList
 * @param {object} params - 查询条件
 * @param {!number} [params.page = 0] - 数据页码
 * @param {!number} [params.size = 10] - 分页大小
 * @returns {object} fetch Promise
 */
export async function fetchMaintainNumberList(params) {
  const param = parseParameters(params);
  return request(`${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-numrange-rule/numrange-list/ui`, {
    method: 'POST',
    query: { page: param.page, size: param.size },
    body: param,
  });
}

/**
 * 查询号码段详细数据
 * @async
 * @function fetchNumberRangeLineList
 * @param {object} params - 查询条件
 * @returns {object} fetch Promise
 */
export async function fetchMaintainNumberDetail(params) {
  return request(`${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-numrange-rule/detail/ui`, {
    method: 'GET',
    query: params,
  });
}

/**
 * 保存号码段规则详情
 * @async
 * @function fetchNumberRangeLineList
 * @param {object} params - 查询条件
 * @returns {object} fetch Promise
 */
export async function saveMaintainNumber(params) {
  return request(`${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-numrange-rule/update/ui`, {
    method: 'POST',
    body: params,
  });
}

/**
 * 编码对象下拉模糊查询
 * @async
 * @function fetchCodingObject
 * @param {object} params - 查询条件
 * @returns {object} fetch Promise
 */
export async function fetchCodingObject(params) {
  return request(
    `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-numrange-assign/numrange-object/query/ui`,
    {
      method: 'GET',
      query: { ...params, tenantId },
    },
  );
}

/**
 * 编码对象下的编码对象属性查询
 * @async
 * @function fetchObjectColumnCodeList
 * @param {object} params - 查询条件
 * @returns {object} fetch Promise
 */
export async function fetchObjectColumnCodeList(params) {
  return request(
    `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-numrange-object-column/limit-object/column/all/ui`,
    {
      method: 'GET',
      query: params,
    },
  );
}

/**
 * 当前序列号相关数据查询
 * @async
 * @function fetchNumberRangeLineList
 * @param {object} params - 查询条件
 * @returns {object} fetch Promise
 */
export async function fetchNumCurrentInfo(params) {
  return request(
    `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-numrange-object-num/num-current/refresh/ui`,
    {
      method: 'GET',
      query: params,
    },
  );
}

/**
 * 当前序列号相关数据查询
 * @async
 * @function updateNumCurrent
 * @param {object} params - 查询条件
 * @returns {object} fetch Promise
 */
export async function updateNumCurrent(params) {
  return request(
    `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-numrange-object-num/num-current/update/ui`,
    {
      method: 'GET',
      query: params,
    },
  );
}
