import React, { useMemo, useEffect, useState } from 'react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Badge, } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import ExcelExport from 'components/ExcelExport';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { useDataSetEvent } from 'utils/hooks';
import { tableDS } from '../stories';
import { fetchDefaultSite } from '../../../services/api';
import { ButtonColor } from 'choerodon-ui/pro/es/button/enum';
import axios from 'axios';

const modelPrompt = 'tarzan.mes.barcodeWorkstationBinding';

const WorkMobileEventDetailReport = (props) => {
  const { tableDs, history } = props;

  const [cuttingFlag, setCuttingFlag] = useState(true)

  useEffect(() => {
    fetchDefaultSite().then(res => {
      if (res && res.success) {
        tableDs.queryDataSet.getField('assemblePointLov').set('lovPara', { siteId: res.rows.siteId, tenantId: getCurrentOrganizationId() })
      }
    });
  }, []);

  useDataSetEvent(tableDs, 'load', () => {
    eventStatus()
  });

  useDataSetEvent(tableDs, 'select', () => {
    eventStatus()
  });

  useDataSetEvent(tableDs, 'selectAll', () => {
    eventStatus()
  });

  useDataSetEvent(tableDs, 'unSelectAll', () => {
    eventStatus()
  });

  useDataSetEvent(tableDs, 'unselect', () => {
    eventStatus()
  });

  const eventStatus = () => {
    setCuttingFlag(tableDs.selected.length === 0)
  }

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'equipmentCode',
      },
      {
        name: 'equipmentName',
      },
      {
        name: 'workcellCode',
      },
      {
        name: 'workcellName',
      },
      {
        name: 'assemblePointCode',
      },
      {
        name: 'assemblePointName',
      },
      {
        name: 'materialLotCode',
      },
      {
        name: 'materialCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'revisionCode',
      },
      {
        name: 'qty',
      },
      {
        name: 'materialLotSequence',
      },
      {
        name: 'enableFlag',
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      {
        name: 'createdByRealName',
      },
      {
        name: 'creationDate',
      },
      {
        name: 'lastUpdatedByRealName',
      },
      {
        name: 'lastUpdateDate',
      },
    ];
  }, []);

  const handleHistory = () => {
    history.push(`/hmes/barcode-workstation-binding-report/history`)
  }

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const keyIds: any = (tableDs?.selected || []).map((record) => record?.get('keyId'))
    const queryParmas = {
      ...tableDs.queryDataSet.current.toData(),
      keyIds: keyIds.join(','),
    }
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  const handleCutting = async () => {
    const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-workcell-material-lots/unbind`;
    const res: any = await axios.post(url, tableDs.selected.map(item => ({
      assemblePointId: item.get('assemblePointId'),
      workcellId: item.get('workcellId'),
      materialLotList: [
        {
          materialLotId: item.get('materialLotId'),
          materialLotCode: item.get('materialLotCode'),
          qty: item.get('qty'),
          materialLotSequence: item.get('materialLotSequence'),
        }
      ]
    })));
    if (res && res.success) {
      notification.success({})
      tableDs.query()
    } else {
      notification.error({ message: res.message })
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('条码与工位绑定报表')}>
        <Button onClick={handleHistory} color={ButtonColor.primary}>
          {intl.get(`${modelPrompt}.button.history`).d('历史查询')}
        </Button>
        <Button disabled={cuttingFlag} onClick={handleCutting} color={ButtonColor.primary}>
          {intl.get(`${modelPrompt}.button.cutting`).d('下料')}
        </Button>
        <ExcelExport
          exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-workcell-material-lots/export`}
          queryParams={getExportQueryParams}
        >
          {intl.get(`${modelPrompt}.button.export`).d('导出')}
        </ExcelExport>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          style={{ height: '400px' }}
          dataSet={tableDs}
          columns={columns}
          searchCode="barcodeWorkstationBinding"
          customizedCode="barcodeWorkstationBinding"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.mes.barcodeWorkstationBinding', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(WorkMobileEventDetailReport),
);
