/**
 * 主页列表ds
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';
import { routeStatusOptionDS, routeStepOptionDS } from './CommonDS';

const modelPrompt = 'tarzan.process.routes.model.routes';
const tenantId = getCurrentOrganizationId();

const listDS = (typeGroup, serveCode) => ({
  autoQuery: false,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'routerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerName`).d('编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
    },
    {
      name: 'routerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerType`).d('类型'),
      options: new DataSet({ ...routeStepOptionDS(typeGroup) }),
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'routerStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerStatus`).d('状态'),
      options: new DataSet({ ...routeStatusOptionDS() }),
      textField: 'description',
      valueField: 'statusCode',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.isEnable`).d('是否有效'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`${modelPrompt}.ok`).d('是') },
          { value: 'N', key: intl.get(`${modelPrompt}.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  fields: [
    {
      name: 'routerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerName`).d('编码'),
    },
    {
      name: 'routerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerType`).d('类型'),
      options: new DataSet({ ...routeStepOptionDS(typeGroup) }),
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'routerStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerStatus`).d('状态'),
      options: new DataSet({ ...routeStatusOptionDS() }),
      textField: 'description',
      valueField: 'statusCode',
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
    },
    {
      name: 'currentFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
    },
    {
      name: 'dateFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
    },
    {
      name: 'dateTo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
    },
  ],
  transport: {
    read: () => {
      // 查询请求的 axios 配置或 url 字符串
      return {
        url: `${serveCode}/v1/${tenantId}/mt-router/list/ui`,
        method: 'GET',
      };
    },
  },
});

const historyTableDS = (typeGroup) => ({
  name: 'historyTableDS',
  primaryKey: 'routerId',
  paging: true,
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryDataSet: new DataSet({
    fields: [
      {
        name: 'creationDateFrom',
        max: 'creationDateTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.form.creationDateFrom`).d('开始时间'),
      },
      {
        name: 'creationDateTo',
        min: 'creationDateFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.form.creationDateTo`).d('结束时间'),
      },
    ],
  }),
  fields: [
    {
      name: 'routerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerName`).d('编码'),
    },
    {
      name: 'routerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerType`).d('类型'),
      options: new DataSet({ ...routeStepOptionDS(typeGroup) }),
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'routerStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerStatus`).d('状态'),
      options: new DataSet({ ...routeStatusOptionDS() }),
      textField: 'description',
      valueField: 'statusCode',
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
    },
    {
      name: 'currentFlag',
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
    },
    {
      name: 'dateFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
    },
    {
      name: 'dateTo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建日期'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
  ],
  transport: {
    read: config => {
      return {
        ...config,
        url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-router/his/query`,
      };
    },
  },
});

export { listDS, historyTableDS };
