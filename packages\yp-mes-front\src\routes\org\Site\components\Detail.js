/**
 * @Description: 站点维护详情页组件
 * @Author: <<EMAIL>>
 * @Date: 2021-02-03 13:32:42
 * @LastEditTime: 2023-07-26 11:11:59
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useEffect, useImperativeHandle, forwardRef, useState, useRef } from 'react';
import { DataSet, TextField, Form, Switch, Select, IntlField, Tabs } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Collapse } from 'choerodon-ui';
import { isEmpty } from 'lodash';
import notification from 'utils/notification';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { detailDS, siteBasicInfoDS, siteManufacturingDS, siteScheduleDS } from '../stores/SiteDS';
import BasicInfoTab from './BasicInfoTab';
import PlanInfoTab from './PlanInfoTab';
import ProduceInfoTab from './ProduceInfoTab';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const modelPrompt = 'tarzan.model.org.site';

const Detail = (props, ref) => {
  const {
    canEdit,
    kid,
    columns = 1,
    componentType,
    customizeForm,
  } = props;
  const retrievalOrderRef = useRef();
  const [activeKey, setActiveKey] = useState('basic');
  const [dragList, setDragList] = useState([]);
  const siteBasicInfoDs = useMemo(() => new DataSet(siteBasicInfoDS()), []);
  const siteScheduleDs = useMemo(() => new DataSet(siteScheduleDS()), []);
  const siteManufacturingDs = useMemo(() => new DataSet(siteManufacturingDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          site: siteBasicInfoDs,
          siteSchedule: siteScheduleDs,
          siteManufacturing: siteManufacturingDs,
        },
      }),
    [],
  );

  useEffect(() => {
    if (kid !== 'create') {
      detailQuery(kid);
    }
  }, [kid]);

  const detailQuery = id => {
    detailDs.setQueryParameter('siteId', id);
    detailDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.SITE_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.SITE`)
    detailDs.query().then(res => {
      if (res.success) {
        siteBasicInfoDs.loadData([res.rows.site]);
        siteScheduleDs.loadData([{ ...res.rows.siteSchedule, siteType: res.rows.site.siteType }]);
        siteManufacturingDs.loadData([res.rows.siteManufacturing]);
        const _newList = (res.rows.siteManufacturing.compWarehousingPrioStrtySeq || []).map(
          item => {
            return {
              questionTuid: item,
              questionContent: intl.get(`${modelPrompt}.${item}`),
            };
          },
        );
        setDragList(_newList);
      }
    });
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    submit: async () => {
      detailDs.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验
      siteScheduleDs.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验
      let addressingRange;
      const _compWarehousingPrioStrty = {};
      if (retrievalOrderRef && retrievalOrderRef.current) {
        addressingRange = retrievalOrderRef.current.getCoordinateList();
        addressingRange.forEach((item, index) => {
          _compWarehousingPrioStrty[item.questionTuid] = index + 1;
        });
      }
      siteManufacturingDs.current.set('compWarehousingPrioStrty', _compWarehousingPrioStrty);
      const validate = await detailDs.validate();
      if (validate) {
        let success = false;
        let newKid = '';
        await detailDs.submit().then(res => {
          const { rows = [] } = res || {};
          if (!isEmpty(rows) && rows[0].success) {
            notification.success({});
            success = true;
            newKid = res.rows[0].rows;
            detailQuery(newKid);
          }
        });
        return { success, newKid };
      } if (
        siteBasicInfoDs.current.toData().siteType === 'SCHEDULE' &&
        (!siteScheduleDs.current.toData().bomSourceSiteType ||
          !siteScheduleDs.current.toData().routerSourceSiteType)
      ) {
        setActiveKey('plan');
      }
      return { success: false };
    },
    reset: () => {
      detailQuery(kid);
    },
  }));

  const customizeCode = useMemo(() => {
    switch (componentType) {
      case 'SITE':
        return `${BASIC.CUSZ_CODE_BEFORE}.SITE_DETAIL.BASIC`;
      case 'ORG_RELATION':
        return `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.SITE`;
      default:
        console.error('父组件传入类型错误！')
        break;
    }
  }, [componentType]);


  const handleChangeSiteName = value => {
    if (siteScheduleDs.current) {
      siteScheduleDs.current.set('siteType', value);
    } else {
      siteScheduleDs.loadData([{ siteType: value }])
    }
  };

  const handleChangeTab = value => {
    setActiveKey(value);
  };

  const childProps = {
    canEdit,
    columns,
  };

  return (
    <>
      <Collapse bordered={false} defaultActiveKey={['siteInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.siteInfo`).d('站点信息')}
          key="siteInfo"
          dataSet={siteBasicInfoDs}
        >
          {customizeForm(
            {
              code: customizeCode,
            },
            <Form
              disabled={!canEdit}
              dataSet={siteBasicInfoDs}
              columns={columns}
              labelLayout="horizontal"
              labelWidth={112}
            >
              <TextField name="siteCode" />
              <IntlField
                name="siteName"
                modalProps={{
                  title: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
                }}
              />
              <Select name="siteType" onChange={handleChangeSiteName} />
              <Switch name="enableFlag" />
            </Form>,
          )}
        </Panel>
      </Collapse>
      <Tabs activeKey={activeKey} onChange={handleChangeTab}>
        <TabPane
          tab={intl.get(`${modelPrompt}.geographicInfo`).d('地理信息')}
          key="basic"
          forceRender
        >
          <BasicInfoTab ds={siteBasicInfoDs} focus={activeKey !== 'basic'} {...childProps} />
        </TabPane>
        <TabPane tab={intl.get(`${modelPrompt}.planInfo`).d('计划属性')} key="plan" forceRender>
          <PlanInfoTab ds={siteScheduleDs} focus={activeKey !== 'plan'} {...childProps} />
        </TabPane>
        <TabPane
          tab={intl.get(`${modelPrompt}.produceInfo`).d('生产属性')}
          key="produce"
          forceRender
        >
          <ProduceInfoTab
            ds={siteManufacturingDs}
            focus={activeKey !== 'produce'}
            dragList={dragList}
            setDragList={setDragList}
            retrievalOrderRef={retrievalOrderRef}
            {...childProps}
          />
        </TabPane>
      </Tabs>
    </>
  );
};

export default withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.SITE_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.SITE`],
})(forwardRef(Detail));
