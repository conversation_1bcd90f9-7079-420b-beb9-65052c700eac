import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, Dropdown, Menu } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableAutoHeightType, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor, } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification'
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import listPageFactory from '../stores/listPageDs';
import listLineFactory from '../stores/listLinePageDs';
import axios from 'axios';
import { Collapse } from 'choerodon-ui';
import { Action } from 'choerodon-ui/es/trigger/enum';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';

const Panel = Collapse.Panel;
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
  lineDs: DataSet;
}

const modelPrompt = 'tarzan.mes.inventoryTransfer';

const ListPageComponent: FC<ListPageProps> = ({ listDs, lineDs, history, match: { path }, }) => {

  const [submitFlag, setSubmitFlag] = useState(true)

  const [closeFlag, setCloseFlag] = useState(true)

  const [flag, setFlag] = useState(false)

  useEffect(() => {
    listDs.query();
  }, []);

  useDataSetEvent(listDs, 'load', () => {
    if (listDs.toData().length > 0) {
      lineDs.setQueryParameter('invTransferDocId', listDs.toData()[0].invTransferDocId);
      lineDs.query()
    } else {
      lineDs.loadData([])
    }
    if (listDs.selected.length === 0) {
      setSubmitFlag(true)
      setCloseFlag(true)
      setFlag(false)
    }
  });

  useDataSetEvent(listDs, 'select', () => {
    selectStatus()
  });

  useDataSetEvent(listDs, 'selectAll', () => {
    selectStatus()
  });

  useDataSetEvent(listDs, 'unSelectAll', () => {
    selectStatus()
  });

  useDataSetEvent(listDs, 'unselect', () => {
    selectStatus()
  });

  const selectStatus = () => {
    if (listDs.selected.length > 0) {
      const flag = listDs.selected.every(item => ['NEW', 'REJECTED'].includes(item.get('transferDocStatus')))
      const flagClose = listDs.selected.every(item => ['PROCESSING',].includes(item.get('transferDocStatus')))
      setFlag(flag || flagClose)
      setSubmitFlag(!flag)
      setCloseFlag(!flagClose)
    } else {
      setSubmitFlag(true)
      setCloseFlag(true)
      setFlag(false)
    }
  }

  const columns: ColumnProps[] = [
    {
      name: 'invTransferDocNum',
      width: 150,
      renderer: ({ record }) => {
        return <a onClick={() => { onHandleDetail(record?.get('invTransferDocId')) }}>{record?.get('invTransferDocNum')}</a>
      }
    },
    {
      name: 'transferDocStatus',
    },
    {
      name: 'siteCode',
    },
    {
      name: 'createdByRealName',
    },
    {
      name: 'approveRealName',
    },
    {
      name: 'creationDate',
      width: 150
    },
    {
      name: 'lastUpdatedByRealName',
    },
    {
      name: 'lastUpdateDate',
      width: 150
    },
    {
      name: 'remark',
    },
  ]

  const lineColumns: ColumnProps[] = [
    {
      name: 'materialLotCode',
      width: 250
    },
    {
      name: 'materialCode',
      width: 150
    },
    {
      name: 'materialName',
      width: 150
    },
    {
      name: 'transferStatus',
    },
    {
      name: 'qty',
      width: 70
    },
    {
      name: 'uomCode',
      width: 70
    },
    {
      name: 'lot',
    },
    {
      name: 'fromLocatorCode',
    },
    {
      name: 'toLocatorCode',
    },
  ]

  const onHandleDetail = (id) => {
    history.push(`/hmes/inventoryTransfer/detail/${id}`)
  }

  const handleCreate = () => {
    history.push(`/hmes/inventoryTransfer/detail/create`)
  }

  const headerRowClick = (record) => {
    lineDs.setQueryParameter('invTransferDocId', record.get('invTransferDocId'));
    lineDs.query();
  };

  const onHandleSubmit = async () => {
    const list = listDs.selected.map(item => ({ invTransferDocId: item.get('invTransferDocId'), transferDocStatus: item.get('transferDocStatus') }))
    const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-inv-transfer/submit/ui`;
    const res: any = await axios.post(url, list)
    if (res && res.success) {
      listDs.query();
      if (res.message) {
        notification.error({
          message: res.message,
        });
      } else {
        notification.success({});
      }
    } else {
      notification.error({
        message: res.message,
      });
    }
  }

  const onHandleStatus = async ({ key }) => {
    if (key === 'SUBMIT') {
      onHandleSubmit()
    } else {
      const invTransferDocIds = listDs.selected.map(item => item.get('invTransferDocId'))
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-inv-transfer/status/change`
      const res: any = await axios.post(url, {
        invTransferDocIds,
        transferDocStatus: key,
      })
      if (res && res.success) {
        notification.success({})
        listDs.query();
      } else {
        notification.error({
          message: res.message
        });
      }
    }
  }

  const menu = (
    <Menu onClick={onHandleStatus}>
      {!submitFlag && <Menu.Item key="CANCEL" >
        {intl.get(`${modelPrompt}.title.cancel`).d('取消')}
      </Menu.Item>}
      {!closeFlag && <Menu.Item key="CLOSED" >
        {intl.get(`${modelPrompt}.title.close`).d('关闭')}
      </Menu.Item>}
      {!submitFlag && <Menu.Item key="SUBMIT" disabled={submitFlag}>
        {intl.get(`${modelPrompt}.title.submit`).d('提交')}
      </Menu.Item>}
    </Menu>
  );

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.InventoryTransfer`).d('库存调拨平台')}>
        <Dropdown trigger={[Action.click]} overlay={menu} placement={Placements.bottomCenter}>
          <PermissionButton
            type="c7n-pro"
            disabled={!flag}
            permissionList={[
              {
                code: `${path}.button.execute`,
                type: 'button',
                meaning: '网点需求-状态',
              },
            ]} >
            {intl.get(`${modelPrompt}.title.status`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <Button color={ButtonColor.primary} onClick={handleCreate}>{intl.get(`${modelPrompt}.title.create`).d('新建')}</Button>
      </Header>
      <Content style={{ overflowY: 'scroll' }}>
        <Table
          dataSet={listDs}
          columns={columns}
          key="InventoryTransfer"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
          autoHeight={{
            type: TableAutoHeightType.maxHeight,
            diff: 2
          }}
          queryFieldsLimit={4} // 头部显示的查询字段的数量
          searchCode="InventoryTransfer" // 动态筛选条后端接口唯一编码
          customizedCode="InventoryTransfer" // 个性化编码
        />
        <Collapse bordered={false} defaultActiveKey={['1']} >
          <Panel header={intl.get(`${modelPrompt}.title.lineMessage`).d('行信息')} key="1">
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              key="InventoryTransferLine"
              queryBar={TableQueryBarType.none}
              queryFieldsLimit={4} // 头部显示的查询字段的数量
              searchCode="InventoryTransferLine" // 动态筛选条后端接口唯一编码
              customizedCode="InventoryTransferLine" // 个性化编码
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    const lineDs = listLineFactory();

    return {
      listDs,
      lineDs
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.ass.InventoryTransfer'],
})(ListPage);
