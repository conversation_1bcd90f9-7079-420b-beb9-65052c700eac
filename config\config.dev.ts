import { IConfig } from 'umi'; // ref: https://umijs.org/config/

export default {
  define: {
    'process.env': {
      APS_CUSZ_CODE_BEFORE: 'MT_APS',
      API_HOST: 'http://************:30080',
      HMES_BASIC: '/mes',
      TARZAN_COMMON: '/tznc',
      HRPT_COMMON: '/hrpt',
      TARZAN_MODEL: '/tznm',
      TARZAN_REPORT: '/tznr',
      TARZAN_METHOD: '/tznd',
      TARZAN_SAMPLING: '/tznq',
      TARZAN_HSPC: '/tzns',
      TARZAN_MONGO: '/tzng',
      ADDITIONAL: process.env.ADDITIONAL,
      PLATFORM_VERSION: 'SAAS',
      CLIENT_ID: 'localhost',
      BASE_PATH: '/',
      MULTIPLE_SKIN_ENABLE: true,
      REACT_APP_SC_DISABLE_SPEEDY: 'false',
      PACKAGE_PUBLIC_URL: '',
      SKIP_TS_CHECK_IN_START: true,
      // aps
      POOL_QUERY: 'query',
      BASE_SERVER: '/aps',
      BASE_SERVERPURCHASE: '/aps-purchase',
      BASE_SERVERPLAN: '/aps-mltp',
      //后续aps:7.X的包下面三个环境变量可删除
      APS_COMMON: '/tznc',
      APS_METHOD: '/aps',
      APS_METHODTZND: '/tznd',
      CUSZ_CODE_BEFORE: 'MT', // 个性化单元前缀
      LOV_CODE_BEFORE: 'YP_MES', // lov值集前缀

    },
  },
} as IConfig;
