import React, { FC, useEffect } from 'react';
import { Header, Content } from 'components/Page';
import { DataSet, Form, TextField, TextArea } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import listPageFactory from '../stores/listPagePubDs';

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.ass.deviceLock';

const ListPageComponent: FC<ListPageProps> = ({ listDs, match: { params } }) => {

  useEffect(() => {
    if (params.id) {
      listDs.setQueryParameter('equipmentId', params.id)
      listDs.query();
    }
  }, [params]);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.deviceLock`).d('设备锁定管理')} />
      <Content >
        <Form dataSet={listDs} columns={3} labelWidth={121} disabled={true}>
          <TextField name="equipmentCode" />
          <TextField name="equipmentName" />
          <TextField name="workcellCode" />
          <TextField name="lockStatus" />
          <TextField name="lockReasonMeaning" />  
          <TextField name="lockTime" />
          <TextField name="inspectRequestCode" />
          <TextField name="inspectDocCode" />
          <TextField name="inspectBusinessType" />
          <TextField name="inspectReqUserRealName" />
          <TextField name="inspectorRealName" />
          <TextArea name="unlockReason" rowSpan={4} />
          <TextField name="unlockTime" />
          <TextField name="status" />
        </Form>
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.ass.deviceLock'],
})(ListPage);
