import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();


export function QueryDetail() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-point-materials/header/query/ui`,
    method: 'GET',
  };
}

export function DefSite() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-points/get/user/def/site`,
    method: 'GET',
  };
}
export function SaveData() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-points/save/data`,
    method: 'POST',
  };
}

