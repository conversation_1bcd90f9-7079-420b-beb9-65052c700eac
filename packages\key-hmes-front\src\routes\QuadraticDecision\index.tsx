import React, { useMemo, useState } from 'react';
import { Table, DataSet, Button, Modal } from 'choerodon-ui/pro';
import { Tag, Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { isUndefined, isNumber } from 'lodash';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import request from 'utils/request';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { getCurrentOrganizationId } from 'utils/utils';
import { Host } from '@/utils/config';
import { observer } from 'mobx-react';
import { tableDS, numberListDS } from './stores';
import DataItemInfoDrawer from './DataItemInfoDrawer';

const modelPrompt = 'QuadraticDecision';

const QuadraticDecision = observer((props) => {
  const { tableDs, trueNumberDs, falseNumberDs } = props;

  let _dataItemDrawer;

  const [addButton, setAddButton] = useState(false);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'materialLov',
        width:150,
        editor: record => record.getState('editing'),
      },
      {
        name: 'materialName',
        width: 150,
      },
      {
        name: 'tagGroupLov',
        editor: record => record.getState('editing'),
      },
      {
        name: 'tagGroupDescription',
      },
      {
        name: 'equipmentCode',
        width: 150,
      },
      {
        name: 'equipmentName',
        width: 150,
      },
      {
        name: 'tagLov',
        editor: record => record.getState('editing'),
      },
      {
        name: 'enableFlag',
        editor: record => record.getState('editing'),
        renderer: ({ record }) => (
          <Badge
            text={
              record?.get('enableFlag') === 'Y'
                ? intl.get(`${modelPrompt}.enable`).d('启用')
                : intl.get(`${modelPrompt}.forbidden`).d('禁用')
            }
            status={record?.get('enableFlag') === 'Y' ? 'success' : 'error'}
          />
        ),
      },
      {
        name: 'tagDescription',
      },
      {
        name: 'checkPoint',
        editor: record => record.getState('editing'),
      },
      {
        name: 'operationName',
      },
      {
        name: 'valueTypeMeaning',
      },
      {
        name: 'trueValue',
        width: 200,
        renderer: ({ value, record }) => {
          if (record?.get('valueType') === 'VALUE') {
            if (record.get('trueValueList') && record.get('trueValueList').length) {
              const temp = record.get('trueValueList');
              return (
                <>
                  {temp.map(item => {
                    return item.dataValue && <Tag color="geekblue">{item.dataValue}</Tag>;
                  })}
                </>
              );
            }
            return '';
          }
          return <span>{value}</span>;
        },
      },
      {
        name: 'falseValue',
        width: 200,
        renderer: ({ value, record }) => {
          if (record?.get('valueType') === 'VALUE') {
            if (record.get('falseValueList') && record.get('falseValueList').length) {
              const temp = record.get('falseValueList');
              return (
                <>
                  {temp.map(item => {
                    return item.dataValue && <Tag color="geekblue">{item.dataValue}</Tag>;
                  })}
                </>
              );
            }
            return '';
          }
          return <span>{value}</span>;
        },
      },
      {
        name: 'uomCode',
      },
      {
        name: 'valueList',
        renderer: ({ record }) => {
          const valueList = record?.get('valueListDesc') || record?.get('valueList') || [];
          const valueList1 = typeof valueList === 'string' ? valueList.split(',') : valueList;
          if (valueList1.length) {
            return valueList1.map(item => (
              <Tag key={item} color="blue">
                {item}
              </Tag>
            ));
          }
          return '';
        },
      },
      {
        name: 'dateFormat',
      },
      {
        name: 'ncCodeLov',
        width: 120,
        editor: record => record.getState('editing'),
      },
      {
        name: 'lastUpdateByName',
      },
      {
        name: 'lastUpdateDate',
        width:150
      },
      {
        header: intl.get(`${modelPrompt}.operation`).d('操作'),
        lock: ColumnLock.right,
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ record }) => {
          return (
            <span>
              {record?.getState('edit') ? (
                <>
                  <a
                    onClick={() => {
                      record?.setState('edit', false);
                      tableDs.reset();
                      setAddButton(false);
                    }}
                  >
                    {intl.get('tarzan.common.button.cancel').d('取消')}
                  </a>
                  &nbsp;&nbsp;
                  <PermissionButton
                    type="text"
                    onClick={() => {
                      handleSave(record);
                    }}
                  >
                    {intl.get('tarzan.common.button.save').d('保存')}
                  </PermissionButton>
                </>
              ) : (
                <PermissionButton
                  type="text"
                  onClick={() => {
                    handleEditModel(record);
                  }}
                >
                  {intl.get('tarzan.common.button.edit').d('编辑')}
                </PermissionButton>
              )}
            </span>
          );
        },
      },
    ];
  }, []);

  const handleAdd = () => {
    const record = tableDs.create({}, 0);
    record.set('updateFlag', 'N')
    record.setState('editing', true);
    record.setState('edit', true);
    setAddButton(true);
  }

  const handleSave = async(record) => {
    const validate = await record.validate();
    if (!validate) {
      return;
    }
    const data = record.toData();
    data.trueValue=JSON.stringify(data.trueValueList)
    data.falseValue=JSON.stringify(data.falseValueList)
    if (data?.updateFlag !== 'N') {
      data.updateFlag = 'Y';
    }
    const res = await request(
      `${Host}/v1/${getCurrentOrganizationId()}/hme-double-check-tags/save`,
      {
        method: 'POST',
        body: [data],
      },
    );
    if (res?.success) {
      notification.success({})
      tableDs.query();
      setAddButton(false);
    } else {
      notification.error({ message: res?.message});
    }
  }

  const handleEditModel = record => {
    record?.setState('edit', true);
    const ncCode = record.get('ncCode');
    const uomCode = record.get('uomCode');
    record?.set('ncCodes', ncCode );
    record?.set('uomCodes', uomCode);
    if (record) {
      trueNumberDs.loadData(record.toData().trueValueList || []);
      falseNumberDs.loadData(record.toData().falseValueList || []);
    } else {
      trueNumberDs.reset();
      falseNumberDs.reset();
    }
    _dataItemDrawer = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.dataItemEdit`).d('数据项编辑'),
      drawer: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <DataItemInfoDrawer
          edit={false}
          record={record}
          falseNumberDs={falseNumberDs}
          trueNumberDs={trueNumberDs}
          handleChangeLov={handleChangeLov}
        />
      ),
      footer: (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              dataItemDrawerClose(record);
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            onClick={() => {
              dataItemDrawerSubmit(record);
            }}
            color={ButtonColor.primary}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  const handleChangeLov = (data1, data2) => {
    trueNumberDs.loadData(data1);
    falseNumberDs.loadData(data2);
  };

  const dataItemDrawerClose = record => {
    record?.setState('edit', false);
    tableDs.reset();
    setAddButton(false);
    _dataItemDrawer.close();
  };

  const dataItemDrawerSubmit =async record => {
    if (record.get('valueType') === 'VALUE') {
      if (handleIntersection()) {
        return notification.error({
          message: intl
            .get(`${modelPrompt}.error.trueVlueList.falseValueList`)
            .d(`符合值与不符合值存在交集，请检查！`),
        });
      }
      if (!(await falseNumberDs?.validate())) return;
    }
    const validate = await record.validate(true);
    if (!validate) {
      return;
    }
    _dataItemDrawer.close();
    record.set('trueValueList', trueNumberDs.toData());
    record.set('falseValueList', falseNumberDs.toData());
  };

  const handleIntersection = () => {
    let flag = false;
    const trueData = trueNumberDs.toData();
    const falseData = falseNumberDs.toData();
    for (let i = 0; i < falseData.length; i++) {
      const itemF = falseData[i];
      if (itemF.valueType === 'single') {
        if (
          trueData.some(
            x =>
              x.dataValue &&
              x.valueType === 'single' &&
              Number(x.dataValue) === Number(itemF.dataValue),
          )
        ) {
          flag = true;
          break;
        }
        const arr = trueData.filter(m => m.valueType === 'section');
        if (arr.length) {
          const leftTemp = arr.map(m =>
            isUndefined(m.multipleValue.leftValue)
              ? undefined
              : Number(m.multipleValue.leftValue),
          );
          const rightTemp = arr.map(m =>
            isUndefined(m.multipleValue.rightValue)
              ? undefined
              : Number(m.multipleValue.rightValue),
          );
          const leftMin = leftTemp.reduce((x, y) => (x < y ? x : y));
          const rightMax = rightTemp.reduce((x, y) => (x > y ? x : y));
          // 不存在负无穷
          if (leftTemp.every(m => isNumber(m)) && rightTemp.every(m => isNumber(m))) {
            if (itemF.dataValue > leftMin && itemF.dataValue < rightMax) {
              flag = true;
              break;
            }
          }
          // 负无穷
          if (leftTemp.some(m => isUndefined(m))) {
            if (itemF.dataValue < rightMax) {
              flag = true;
              break;
            }
          }
          // 正无穷
          if (rightTemp.some(m => isUndefined(m))) {
            if (itemF.dataValue > leftMin) {
              flag = true;
              break;
            }
          }
        }
      } else if (itemF.valueType === 'section') {
        const leftValue = isUndefined(itemF.multipleValue.leftValue)
          ? undefined
          : Number(itemF.multipleValue.leftValue);
        const rightValue = isUndefined(itemF.multipleValue.rightValue)
          ? undefined
          : Number(itemF.multipleValue.rightValue);
        const arrSingle = trueData.filter(m => m.valueType === 'single' && m.dataValue);
        const arrSection = trueData.filter(m => m.valueType === 'section');
        // 符合值中的左侧值与右侧值
        if (arrSection.length) {
          const leftTemp = arrSection.map(m =>
            isUndefined(m.multipleValue.leftValue)
              ? undefined
              : Number(m.multipleValue.leftValue),
          );
          const rightTemp = arrSection.map(m =>
            isUndefined(m.multipleValue.rightValue)
              ? undefined
              : Number(m.multipleValue.rightValue),
          );
          const leftMin = leftTemp.reduce((x, y) => (x < y ? x : y));
          const rightMax = rightTemp.reduce((x, y) => (x > y ? x : y));
          if (rightValue && leftValue) {
            if (arrSingle.length) {
              if (
                arrSingle.some(
                  x => x.dataValue && x.dataValue > leftValue && x.dataValue < rightValue,
                )
              ) {
                flag = true;
                break;
              }
            }
            if (leftTemp.every(m => isNumber(m)) && rightTemp.every(m => isNumber(m))) {
              if (
                (leftValue > leftMin && leftValue < rightMax) ||
                (rightValue > leftMin && rightValue < rightMax) ||
                (leftValue < leftMin && rightValue > rightMax)
              ) {
                flag = true;
                break;
              }
            }
            // 负无穷
            if (leftTemp.some(m => isUndefined(m))) {
              if (leftValue < rightMax) {
                flag = true;
                break;
              }
            }
            // 正无穷
            if (rightTemp.some(m => isUndefined(m))) {
              if (rightValue > leftMin) {
                flag = true;
                break;
              }
            }
          } else if (isUndefined(rightValue) || isUndefined(leftValue)) {
            if (arrSingle.length) {
              if (
                arrSingle.some(x => x.dataValue && Number(x.dataValue) < rightValue) ||
                arrSingle.some(x => x.dataValue && Number(x.dataValue) > leftValue)
              ) {
                flag = true;
                break;
              }
            }
            if (leftTemp.every(m => isNumber(m)) && rightTemp.every(m => isNumber(m))) {
              if (isUndefined(rightValue) && leftValue < rightMax) {
                flag = true;
                break;
              }
              if (isUndefined(leftValue) && rightValue > leftMin) {
                flag = true;
                break;
              }
            }
            // 负无穷
            if (leftTemp.some(m => isUndefined(m))) {
              if (isUndefined(rightValue) && leftValue < rightMax) {
                flag = true;
                break;
              }
              if (isUndefined(leftValue)) {
                flag = true;
                break;
              }
            }
            // 正无穷
            if (rightTemp.some(m => isUndefined(m))) {
              if (isUndefined(rightValue)) {
                flag = true;
                break;
              }
              if (isUndefined(leftValue) && rightValue > leftMin) {
                flag = true;
                break;
              }
            }
          }
        } else if (arrSingle.length) {
          if (rightValue && leftValue) {
            if (
              arrSingle.some(
                x =>
                  x.dataValue &&
                  Number(x.dataValue) > leftValue &&
                  Number(x.dataValue) < rightValue,
              )
            ) {
              flag = true;
              break;
            }
          } else if (isUndefined(rightValue) || isUndefined(leftValue)) {
            if (arrSingle.length) {
              if (
                arrSingle.some(x => x.dataValue && Number(x.dataValue) > leftValue) ||
                arrSingle.some(x => x.dataValue && Number(x.dataValue) < rightValue)
              ) {
                flag = true;
                break;
              }
            }
          }
        }
      }
    }
    return flag;
  };

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/HME.DOUBLE_CHECK`,
      title: intl.get(`${modelPrompt}.import`).d('二次判定采集项维护导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('二次判定采集项维护')}>
        <>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={handleAdd}
            disabled={addButton}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
          <Button icon="daorucanshu" onClick={handleImport}>
            {intl.get('tarzan.common.button.import').d('导入')}
          </Button>
        </>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="QuadraticDecision"
          customizedCode="QuadraticDecision"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      const trueNumberDs = new DataSet({
        ...numberListDS(),
      });
      const falseNumberDs = new DataSet({
        ...numberListDS(),
      });
      return {
        tableDs,
        trueNumberDs,
        falseNumberDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(QuadraticDecision),
);
