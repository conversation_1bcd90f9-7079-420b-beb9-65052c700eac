/**
 * 工艺组件-抽屉表格中的ds
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';

import { nextStepDecisionOptionDS } from './CommonDS';

const modelPrompt = 'tarzan.process.routes.model.routes';

const pathTableDS = (componentLov, currentBomId, tenantId) => ({
  selection: false,
  paging: false,
  fields: [
    {
      name: 'currentStepDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentStepName`).d('当前步骤'),
    },
    {
      name: 'nextStepDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.nextStepDesc`).d('下一步骤'),
    },
    {
      name: 'sequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentSequence`).d('顺序'),
      required: true,
    },
    {
      name: 'nextDecisionType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.nextDecisionType`).d('下一步骤策略'),
      options: new DataSet({...nextStepDecisionOptionDS()}),
      textField: 'description',
      valueField: 'typeCode',
      required: true,
    },
    {
      name: 'nextDecisionObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.nextDecisionValue`).d('决策值'),
      lovCode: 'MT.ASSEMBLE_GROUP',
      dynamicProps: {
        required: ({ record }) => {
          return record.get('nextDecisionType') !== 'MAIN';
        },
        lovCode: ({ record }) => {
          if (['COPRODUCT', 'NC'].includes(record.get('nextDecisionType'))){
            const coproductFlag = record.get('nextDecisionType') === 'COPRODUCT';
            return coproductFlag ? componentLov : 'MT.NC_CODE';
          }
        },
        lovPara: ({ record }) => {
          if (['COPRODUCT', 'NC'].includes(record.get('nextDecisionType'))){
            const coproductFlag = record.get('nextDecisionType') === 'COPRODUCT';
            const queryParams = coproductFlag
              ? {
                tenantId,
                bomId: currentBomId || 0,
                bomComponentType: 'COPRODUCT',
              }
              : {
                tenantId,
              };
            return {
              ...queryParams,
            }
          }
        },
      },
    },
    {
      name: 'nextDecisionValue',
    },
  ],
});


export { pathTableDS };
