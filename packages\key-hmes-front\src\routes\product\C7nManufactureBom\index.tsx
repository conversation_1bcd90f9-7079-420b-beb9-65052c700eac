/**
 * @Description: 制造装配清单 - 入口页面（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/8/1 11:22
 * @LastEditTime: 2023-05-18 15:13:29
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { AssemblyList, AssemblyListProps } from '@components/C7nAssemblyCommon/AssemblyList';

const C7nManufactureBomList = props => {
  // 装配组件列表页入参
  const assemblyListProps: AssemblyListProps = {
    history: props.history,
    location: props.location,
    featureTitle: intl.get('tarzan.product.bom.title.manufactureBom').d('制造装配清单'), // 列表页标题title
    exportServerCode: BASIC.HMES_BASIC, // 导出接口访问的服务
    uiServerCode: BASIC.HMES_BASIC, // 功能接口（如列表页的查询接口）要访问的服务
    match: props.match,
    typeGroup: 'MES_BOM_TYPE', // 查询条件与详情页中类型的值集code
    searchCode: 'zzzpqd1',
    customizedCode: 'zzzpqd1',
    customizeTable: props.customizeTable,
    custCode: 'MES_BOM_LIST',
  };

  return <AssemblyList {...assemblyListProps} />;
};

export default flow(
  formatterCollections({ code: ['tarzan.product.bom', 'tarzan.common'] }),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.MES_BOM_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.MES_BOM_LIST.LIST`] }),
)(C7nManufactureBomList);
