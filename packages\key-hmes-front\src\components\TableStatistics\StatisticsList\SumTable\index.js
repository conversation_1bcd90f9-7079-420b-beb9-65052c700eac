import React from 'react';
import styles from './index.less';

export default class SumTable extends React.Component {
  constructor(props) {
    super(props);
  }

  /**
   * 子表 展示统计数量
   * @param {*} obj
   */
  getStatisticsTable(obj) {
    const keys = Object.keys(obj);
    const values = Object.values(obj);

    return (
      <div className={styles[('statistical', 'information')]}>
        <table>
          <tr>
            {keys.map(c => {
              return <th>{c}</th>;
            })}
          </tr>
          <tr className={styles.value}>
            {values.map(c => {
              return <th>{c}</th>;
            })}
          </tr>
        </table>
      </div>
    );
  }

  render() {
    const { data } = this.props;
    if (!data || data.length === 0) {
      return null;
    }
    return (
      <div className={styles.statistical}>
        {data &&
          data.map(item => {
            return (
              <table>
                {
                  <>
                    <tr>
                      <th>{item.collectFieldName}</th>
                      <td>{this.getStatisticsTable(item.collectColumnMap)}</td>
                    </tr>
                  </>
                }
              </table>
            );
          })}
      </div>
    );
  }
}
