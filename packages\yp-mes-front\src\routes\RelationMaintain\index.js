/**
 * RelationMaintain - 组织关系维护
 * @date: 2021-1-13
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React, { useEffect } from 'react';
import { connect } from 'dva';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { LocaleProvider } from '@components/tarzan-ui';
import styles from './index.module.less';
import SelectTree from './SelectTree';
import FilterForm from './FilterForm';

const modelPrompt = 'tarzan.model.org.relation';

function LocatorList(props) {
  useEffect(() => {
    // 获取原始组织关系tree 数据
    getAllTreeData();
  }, []);
  const { dispatch } = props;

  const getAllTreeData = () => {
    dispatch({
      type: 'relationMaintain/allTreeData',
    }).then(res => {
      if (res) {
        initTreeData(res);
      }
    });
  };

  const initTreeData = dataList => {
    // 为测试性能处理relkey (需要删除)
    // const treeData = dataList.sort((a, b) => {
    //   return a.sequence - b.sequence;
    // });

    const dataListLoop = list => {
      const addMap = ['ENTERPRISE', 'SITE'];
      let keys = [];
      list.forEach(item => {
        if (item.organizationMessageList && item.organizationMessageList.length > 0) {
          keys = keys.concat(dataListLoop(item.organizationMessageList));
          if (
            item.enableFlag === 'Y' &&
            addMap.indexOf(item.organizationType) > -1 &&
            keys.indexOf(item.organizationRelId) === -1
          ) {
            keys.push(item.organizationRelId);
          }
        }
      });
      return keys;
    };
    const expandedKeys = dataListLoop(dataList);
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        treeData: dataList,
        originData: dataList,
        expandedKeys,
        autoExpandParent: true,
      },
    });
  };

  return (
    <div
      className="hmes-style"
      style={{
        height: '100%',
        flexGrow: '1',
        position: 'relative',
        display: 'flex',
        flexFlow: 'column',
      }}
    >
      <Header title={intl.get(`${modelPrompt}.relationMaintenance`).d('新组织关系维护')} />
      <LocaleProvider>
        <Content
          className={styles['content-inner']}
          wrapperStyle={{
            display: 'flex',
            flexDirection: 'column',
            flexGrow: 1,
            overflow: 'hidden',
          }}
        >
          <FilterForm initTreeData={initTreeData} />
          <SelectTree />
        </Content>
      </LocaleProvider>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.model.org.relation', 'tarzan.common'],
})(
  connect(({ relationMaintain }) => {
    return relationMaintain;
  })(LocatorList),
);
