/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-04-11 14:13:20
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-04-11 14:42:26
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\BackupWorkEoSkips\stories\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import DataSet, { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.mes.backupWorkEoSkips';
const tenantId = getCurrentOrganizationId();

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryDataSet: new DataSet({
    fields: [
      {
        name: 'identifications',
        type: FieldType.string,
        multiple: true,
        label: intl.get(`${modelPrompt}.identifications`).d('条码'),
      },
      {
        name: 'skipOperationLov',
        label: intl.get(`${modelPrompt}.form.skipOperationLov`).d('跳过工序'),
        type: FieldType.object,
        lovCode: 'MT.OPERATION_CONVERSION',
        ignore: FieldIgnore.always,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'skipOperationId',
        bind: 'skipOperationLov.operationId',
      },
      {
        name: 'interceptOperationLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.interceptOperationLov`).d('拦截工序'),
        lovCode: 'MT.OPERATION_CONVERSION',
        ignore: FieldIgnore.always,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'interceptOperationId',
        bind: 'interceptOperationLov.operationId',
      },
      {
        name: 'dateFrom',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.dateFrom`).d('条码跳站开始时间'),
        max: 'dateTo',
      },
      {
        name: 'dateTo',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.dateTo`).d('条码跳站截止时间'),
        min: 'dateFrom',
      },
    ],
  }),
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'skipOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.skipOperationName`).d('跳过工序编码'),
    },
    {
      name: 'skipOperationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.skipOperationDesc`).d('跳过工序名称'),
    },
    {
      name: 'interceptOperationId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.interceptOperationId`).d('拦截工序id'),
    },
    {
      name: 'interceptOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.interceptOperationName`).d('拦截工序编码'),
    },
    {
      name: 'interceptOperationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.interceptOperationDesc`).d('拦截工序名称'),
    },
    {
      name: 'additionalDataFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.additionalDataFlag`).d('是否需要补录数据'),
    },
    {
      name: 'backupWorkStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.backupWorkStatus`).d('是否质量评审'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        data,
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-backup-work-eo-skips/query`,
        method: 'POST',
      };
    },
  },
});
