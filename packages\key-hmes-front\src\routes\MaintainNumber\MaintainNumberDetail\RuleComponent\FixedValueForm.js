import React, { Component } from 'react';
import { Form, Input, Row, Col } from 'hzero-ui';
import {
  FORM_COL_3_LAYOUT,
  SEARCH_FORM_ROW_LAYOUT,
  DRAWER_FORM_ITEM_LAYOUT,
} from '@utils/constants';
import intl from 'utils/intl';
import { connect } from 'dva';

const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';

let timeout;

@connect(({ maintainNumber }) => ({
  maintainNumber,
}))
@Form.create({ fieldNameProp: null })
export default class FixedValueForm extends Component {
  constructor(props) {
    super(props);
    props.onRef(this);
  }

  handleChange = e => {
    const { dataSource, setUsingRuleDetail } = this.props;
    const usingRuleDetail = {
      ...dataSource,
      fixInput: e.target.value,
      numRule: '1',
    };

    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }

    timeout = setTimeout(() => {
      setUsingRuleDetail(usingRuleDetail);
    }, 300);
  };

  render() {
    const {
      form,
      canEdit,
      dataSource,
      maintainNumber: { maintainNumberDetail = {}, userRole = 'N' },
    } = this.props;
    const { initialFlag = 'N' } = maintainNumberDetail;
    const { getFieldDecorator } = form;
    const { fixInput = undefined } = dataSource;
    return (
      <Row {...SEARCH_FORM_ROW_LAYOUT}>
        <Col {...FORM_COL_3_LAYOUT}>
          <Form.Item
            {...DRAWER_FORM_ITEM_LAYOUT}
            label={intl.get(`${modelPrompt}.fixInput`).d('输入值')}
          >
            {getFieldDecorator('fixInput', {
              initialValue: fixInput,
              rules: [
                {
                  required: true,
                  message: intl.get('hzero.common.validation.notNull', {
                    name: intl.get(`${modelPrompt}.fixInput`).d('输入值'),
                  }),
                },
              ],
            })(
              <Input
                dbc2sbc={false}
                onChange={this.handleChange}
                disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                inputChinese={false}
                maxLength={10}
              />,
            )}
          </Form.Item>
        </Col>
      </Row>
    );
  }
}
