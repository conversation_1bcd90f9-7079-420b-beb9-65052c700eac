import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { DataSet, Button, Form, Lov, Switch, TextField, Modal, Table } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import DetailPageTable from './DetailPageTable';
import { detailDS, lineTableDS } from '../stores/detailPageDS';
import materialHistoryFactory from '../stores/materialHistoryDs';
import { useDataSet, } from 'utils/hooks';
import { QueryDetail, DefSite, SaveData } from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.equipmentPointMaintenanceList';

const EquipmentPointMaintenanceDetail = observer(props => {
  const {
    history,
    match: { path, params },
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState(false);
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const lineTableDs = useMemo(() => new DataSet(lineTableDS()), []);

  const materialHistoryDs = useDataSet(materialHistoryFactory, 'equipmentPoint2');

  const { run: queryDetail, loading: queryDetailLoading } = useRequest(QueryDetail(), {
    manual: true,
    needPromise: true,
  });

  const { run: defSite, loading: defSiteLoading } = useRequest(DefSite(), {
    manual: true,
    needPromise: true,
  });
  const { run: saveData, loading: saveDataLoading } = useRequest(SaveData(), {
    manual: true,
    needPromise: true,
  });
  useEffect(() => {
    if (kid === 'create') {
      // 新建时
      queryDefSite();
      setCanEdit(true);
      return;
    }
    // 编辑时
    handleQueryDetail()
  }, [kid]);
  const handleQueryDetail = () => {
    queryDetail({ params: { assemblePointId: kid } }).then(res => {
      if (res && res.success) {
        detailDs.loadData([res.rows]);
      }
    });
    lineTableDs.setQueryParameter('assemblePointId', kid);
    lineTableDs.query()
  }

  const queryDefSite = () => {
    defSite({}).then(res => {
      if (res) {
        detailDs.current?.set('siteLov', res);
        detailDs.current?.set('siteId', res.siteId);
      }
    });
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(() => {
    if (kid === 'create') {
      history.push('/hmes/equipment-point-maintenance/list');
    } else {
      setCanEdit(false);
      handleQueryDetail()
    }
  }, []);

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    saveData({
      params: {
        ...detailDs.current?.toJSONData(),
        pointMaterialVOList: lineTableDs.toJSONData(),
      },
    }).then((res) => {
      if (res && res?.rows) {
        setCanEdit(false);
        if (kid === 'create') {
          history.push(`/hmes/equipment-point-maintenance/detail/${res.rows}`);
        } else {
          setCanEdit(false);
          handleQueryDetail()
        }
        return true;
      }
    })
    return false;
  };

  const columnHistory: ColumnProps[] = [
    {
      name: 'materialCode',
      width:150
    },
    {
      name: 'materialName',
      width:150
    },
    {
      name: 'uomName',
    },
    {
      name: 'maxQty',
    },
    {
      name: 'enableFlag',
    },
    {
      name: 'createdByName',
    },
    {
      name: 'creationDate',
      width: 150,
    },
  ]

  const handleHistory = () => {
    materialHistoryDs.setQueryParameter('assemblyPointId', kid)
    materialHistoryDs.query()
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history`).d('历史查询'),
      okButton:false,
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table
        dataSet={materialHistoryDs}
        columns={columnHistory}
      />,
    });
  }

  return (
    <div className="hmes-style">
      <TarzanSpin
        dataSet={lineTableDs}
        spinning={queryDetailLoading || defSiteLoading || saveDataLoading}
      >
        <Header
          title={
            kid === 'create'
              ? intl.get(`${modelPrompt}.title.create`).d('新建装配点')
              : intl.get(`${modelPrompt}.title.edit`).d('编辑装配点')
          }
          backPath="/hmes/equipment-point-maintenance/list"
        >
          {canEdit ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
          <Button onClick={handleHistory}>{intl.get('tarzan.common.button.history').d('历史查询')}</Button>
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basic', 'basicMaterialInfo']}>
            <Panel key="basic" header={intl.get(`${modelPrompt}.title.basic`).d('基础属性')}>
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <Lov name="siteLov" disabled={kid !== 'create' || lineTableDs.records.length !== 0} />
                <TextField name="assemblePointCode" disabled={kid !== 'create'} />
                <TextField name="description" />
                <Lov name="assemblePointTypeLov" />
                <Switch name="enableFlag" />
              </Form>
            </Panel>
            <Panel
              key="basicMaterialInfo"
              header={intl.get(`${modelPrompt}.title.basicMaterialInfo`).d('物料信息')}
            >
              <DetailPageTable
                canEdit={canEdit}
                ds={lineTableDs}
                siteId={detailDs.current?.get('siteId')}
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(EquipmentPointMaintenanceDetail);
