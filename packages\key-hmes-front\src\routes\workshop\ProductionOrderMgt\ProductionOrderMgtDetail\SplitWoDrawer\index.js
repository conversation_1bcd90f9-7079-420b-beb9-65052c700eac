/**
 * @Description: 生产指令管理拆分抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:40:22
 * @LastEditTime: 2022-11-10 15:55:03
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Form, TextField, NumberField, Icon, Table } from 'choerodon-ui/pro';
import { Tabs } from 'choerodon-ui';
import { isEmpty } from 'lodash';
import { TarzanDrawer } from '@components/tarzan-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';

import styles from '../../index.module.less';
import { splitDS, splitListDS } from '../../stores/ProductionOrderMgtDrawerSplitDS';

const { TabPane } = Tabs;

const modelPrompt = 'tarzan.workshop.productionOrderMgt';

const ProductionOrderMgtSplitWoForm = props => {
  const { workOrderId, visible, handleRefresh = () => {}, handleCancel = () => {}, status } = props;

  const [drawerEdit, setDrawerEdit] = useState(false);
  const [showSplitList, setShowSplitList] = useState([0]);
  const [hideSplitList, setHideSplitList] = useState([1, 2, 3, 4, 5, 6, 7, 8, 9]);

  const splitDs = useMemo(() => {
    return new DataSet(splitDS());
  }, []);

  const splitListDs = useMemo(() => {
    return new DataSet(splitListDS());
  }, []);

  useEffect(() => {
    if (visible) {
      splitQuery(workOrderId);
    }
  }, [visible]);

  const splitQuery = id => {
    splitDs.queryParameter = {
      workOrderId: id,
    };
    splitDs.query().then(() => {
      resetSplits();
    });
    splitListDs.queryParameter = {
      parentWorkOrderId: id,
    };
    splitListDs.query();
  };

  const resetData = () => {
    splitDs.loadData([]);
    setShowSplitList([0]);
    setHideSplitList([1, 2, 3, 4, 5, 6, 7, 8, 9]);
  };

  const resetSplits = () => {
    splitDs.current.init('splitQty0', undefined);
    splitDs.current.init('splitQty1', undefined);
    splitDs.current.init('flagSplitQty1', false);
    splitDs.current.init('splitQty2', undefined);
    splitDs.current.init('flagSplitQty2', false);
    splitDs.current.init('splitQty3', undefined);
    splitDs.current.init('flagSplitQty3', false);
    splitDs.current.init('splitQty4', undefined);
    splitDs.current.init('flagSplitQty4', false);
    splitDs.current.init('splitQty5', undefined);
    splitDs.current.init('flagSplitQty5', false);
    splitDs.current.init('splitQty6', undefined);
    splitDs.current.init('flagSplitQty6', false);
    splitDs.current.init('splitQty7', undefined);
    splitDs.current.init('flagSplitQty7', false);
    splitDs.current.init('splitQty8', undefined);
    splitDs.current.init('flagSplitQty8', false);
    splitDs.current.init('splitQty9', undefined);
    splitDs.current.init('flagSplitQty9', false);
    setDrawerEdit(false);
    setShowSplitList([0]);
    setHideSplitList([1, 2, 3, 4, 5, 6, 7, 8, 9]);
  };

  const handleSave = async () => {
    splitDs.current.set({ nowDate: new Date().getTime() });
    const validate = await splitDs.validate(false, true);
    if (validate) {
      await splitDs.submit().then(res => {
        const { rows = [] } = res || {};
        if (!isEmpty(rows) && rows[0].success) {
          setDrawerEdit(false);
          splitQuery(workOrderId);
          handleRefresh('WOSPLIT');
        }
      });
    }
  };

  const handleClose = async () => {
    setDrawerEdit(false);
    handleCancel();
  };

  const addSplit = () => {
    if (showSplitList.length < 10) {
      const newHideSplitList = [...hideSplitList];
      const newShowSplitList = newHideSplitList.splice(0, 1);
      splitDs.current.init(`splitQty${newShowSplitList[0]}`, undefined);
      splitDs.current.init(`flagSplitQty${newShowSplitList[0]}`, true);
      setShowSplitList([...showSplitList, ...newShowSplitList]);
      setHideSplitList(newHideSplitList);
    }
  };

  const deleteSplit = value => {
    const clickIndex = showSplitList.indexOf(value);
    const newShowSplitList = [...showSplitList];
    const newHideSplitList = newShowSplitList.splice(clickIndex, 1);
    splitDs.current.init(`splitQty${value}`, undefined);
    splitDs.current.init(`flagSplitQty${value}`, false);
    setHideSplitList([...hideSplitList, ...newHideSplitList]);
    setShowSplitList(newShowSplitList);
  };

  const orderDetail = id => {
    resetSplits();
    props.history.push(`/hmes/workshop/production-order-mgt/detail/${id}`);
  };

  const columns = [
    {
      name: 'workOrderNum',
      lock: 'left',
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              orderDetail(record.data.workOrderId);
            }}
          >
            {value}
          </a>
        );
      },
      width: 180,
    },
    {
      name: 'materialCode',
      width: 100,
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'statusDesc',
      width: 70,
    },
    {
      name: 'qty',
      width: 100,
    },
    {
      name: 'creationDate',
      align: 'center',
      width: 160,
    },
  ];

  return (
    <>
      <TarzanDrawer
        visible={visible}
        canEdit={false}
        onOk={handleClose}
        onClose={handleClose}
        width={720}
        title={
          <div className={styles['modal-title-container']}>
            <div>{intl.get(`${modelPrompt}.model.productionOrderMgt.splitWo`).d('WO拆分')}</div>
            <div className={styles['modal-title-btn-box']}>
              {drawerEdit && (
                <>
                  <PermissionButton type="c7n-pro" onClick={resetSplits}>
                    {intl.get('tarzan.common.button.cancel').d('取消')}
                  </PermissionButton>
                  <PermissionButton type="c7n-pro" color={ButtonColor.primary} onClick={handleSave}>
                    {intl.get('tarzan.common.button.save').d('保存')}
                  </PermissionButton>
                </>
              )}
              {!drawerEdit && (
                <PermissionButton
                  type="c7n-pro"
                  // color={ButtonColor.primary}
                  disabled={
                    !status || ['NEW', 'RELEASED', 'EORELEASED', 'HOLD'].indexOf(status) === -1
                  }
                  onClick={() => {
                    setDrawerEdit(prev => !prev);
                  }}
                >
                  {intl.get(`${modelPrompt}.split`).d('拆分')}
                </PermissionButton>
              )}
            </div>
          </div>
        }
      >
        <Form
          disabled={!drawerEdit}
          dataSet={splitDs}
          columns={2}
          labelLayout="horizontal"
          labelWidth={110}
        >
          <TextField name="workOrderNum" />
          <TextField name="qty" />
          {showSplitList.map((item, index) => {
            return (
              <div key={item} name={`splitQty${item}`} className={styles['split-qty-box']}>
                <NumberField name={`splitQty${item}`} />
                {index === 0 && drawerEdit && (
                  <Icon
                    className={styles['split-qty-icon']}
                    type="control_point"
                    style={{
                      color: showSplitList.length < 10 ? '#73D4DF' : 'rgba(0,0,0,0.2)',
                      fontSize: 14,
                      cursor: 'pointer',
                    }}
                    onClick={addSplit}
                  />
                )}
                {index !== 0 && index < 10 && drawerEdit && (
                  <Icon
                    className={styles['split-qty-icon']}
                    type="remove_circle_outline"
                    style={{
                      color: '#F90404',
                      fontSize: 14,
                      cursor: 'pointer',
                    }}
                    onClick={() => {
                      deleteSplit(item);
                    }}
                  />
                )}
              </div>
            );
          })}
        </Form>
        <Tabs>
          <TabPane tab={intl.get(`${modelPrompt}.splitHistory`).d('历史拆分')}>
            <Table dataSet={splitListDs} columns={columns} />
          </TabPane>
        </Tabs>
      </TarzanDrawer>
    </>
  );
};

export default ProductionOrderMgtSplitWoForm;
