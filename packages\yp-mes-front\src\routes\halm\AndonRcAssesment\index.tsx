import React, { useMemo, useState } from 'react';
import { Table, Button, Switch, DataSet } from 'choerodon-ui/pro/lib';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useDataSet } from 'hzero-front/lib/utils/hooks';
// @ts-ignore
import formatterCollections from 'utils/intl/formatterCollections';

import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import { enableRender, operatorRender } from 'utils/renderer';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
// @ts-ignore
import { Host } from '@/utils/config';
// @ts-ignore

import { useKeysExportParams } from 'alm-src/hooks';
// @ts-ignore
import CusExcelExport from 'alm-src/components/CusExcelExport';

import { tableDS  } from './Stores';
import getLang from './Langs';

const tenantId = getCurrentOrganizationId();

const AndonRcAssesment = () => {
  const [isEdit, setIsEdit] = useState(false);
  const tableDs = useDataSet(() => new DataSet(tableDS(setIsEdit)));
  // 导出参数
  const exportParams = useKeysExportParams(tableDs, 'andonRcAssesmentId', 'andonRcAssesmentIds');

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'evalItemCode',
        editor: (record ) => record.status === 'add' || record.getState('editing'),
        width: 180,
      },
      {
        name: 'evalItemDesc',
        editor: (record ) => record.status === 'add' || record.getState('editing'),
        width: 200,
      },
      {
        name: 'assetLov',
        editor: (record ) => record.status === 'add' || record.getState('editing'),
        width: 180,
      },
      {
        name: 'equipmentName',
      },
      {
        name: 'description',
        editor: (record ) => record.status === 'add' || record.getState('editing'),
      },
      {
        name: 'createdByName',
        width: 120,
        // editor: (record ) => record.status === 'add' || record.getState('editing'),
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },

      {
        name: 'lastUpdatedByName',
        width: 80,
      },
      {
        name: 'enabledFlag',
        align: ColumnAlign.left,
        width: 80,
        editor: (record ) => (record.status === 'add' || record.getState('editing')) && <Switch />,
        renderer: ({ value }) => enableRender(value),
      },
      {
        header: getLang('OPTION'),
        width: 80,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          const operators =
            record?.status === 'add'
              ? [
                // 新增状态
                {
                  key: 'save',
                  ele: <a onClick={() => handleSaveLine()}>{getLang('SAVE')}</a>,
                  len: 2,
                  title: getLang('SAVE'),
                },
                {
                  key: 'clean',
                  ele: <a onClick={() => handleCleanLine(record)}>{getLang('CLEAN')}</a>,
                  len: 2,
                  title: getLang('CLEAN'),
                },
              ]
              : record?.getState('editing')
                ? [
                  // 编辑状态
                  {
                    key: 'save',
                    ele: <a onClick={() => handleSaveLine()}>{getLang('SAVE')}</a>,
                    len: 2,
                    title: getLang('SAVE'),
                  },
                  {
                    key: 'cancel',
                    ele: <a onClick={() => handleCancelLine(record)}>{getLang('CANCEL')}</a>,
                    len: 2,
                    title: getLang('CANCEL'),
                  },
                ]
                : [
                  // 非编辑状态
                  {
                    key: 'edit',
                    ele: <a disabled={isEdit && isEdit !== record?.get('lampFailureId')} onClick={() => handleEditLine(record)}>{getLang('EDIT')}</a>,
                    width: 180,
                    title: getLang('EDIT'),
                  },
                ];
          return operatorRender(operators);
        },
      },
    ],
    [isEdit],
  );

  const handleImport = () => {
    // openTab({
    //   key: `/aori/andon-rc-assesment/data-import/FYDS_LIMS_REPORT_CONFIG`,
    //   title: getLang('HEADER_IMPORT'),
    //   search: queryString.stringify({
    //     action: getLang('HEADER_IMPORT'),
    //   }),
    // });
    openTab({
      key: `/himp/commentImport/FYDS_LIMS_REPORT_CONFIG_MES`,
      title: getLang('HEADER_IMPORT'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const handleAddLine = () => {
    const record = tableDs.create({
      createdByName: getCurrentUser().realName,
    }, 0)
    record.setState('editing', true)
    setIsEdit(true);
  }

  const handleEditLine = (record) => {
    record.setState('editing', true)
    setIsEdit(record?.get('lampFailureId'));
  }

  const handleCancelLine = (record) => {
    record.reset();
    record.setState('editing', false)
    setIsEdit(false);
  }

  const handleCleanLine = (record) => {
    tableDs.remove(record)
    setIsEdit(false);
  }

  const handleSaveLine = async () => {
    const validateFlag = await tableDs.validate()
    if (validateFlag) {
      await tableDs.submit();
      setIsEdit(false);
      tableDs.query();
    }
  }

  return (
    <PageHeaderWrapper
      header={
        <>
          <Button icon="add" color={ButtonColor.primary} disabled={isEdit} onClick={() => handleAddLine()}>
            {getLang('CREATE')}
          </Button>
          <CusExcelExport
            requestUrl={`${Host}/v1/${tenantId}/aaccm-lamp-failures//export`}
            queryParams={exportParams}
            showDynamicField
          />
          <Button icon="vertical_align_top" onClick={handleImport}>
            {getLang('IMPORT')}
          </Button>
        </>
      }
      title={getLang('HEADER')}
    >
      <Table
        key="andonRcAssesmentList"
        customizedCode="AORI.ANDON_RC_ASSESMENT.LIST"
        dataSet={tableDs}
        columns={columns}
      />
    </PageHeaderWrapper>
  )
}

export default formatterCollections({
  code: ['alm.common', 'alm.component', 'aori.andonRcAssesment'],
})(AndonRcAssesment);
