import React, { useState, useMemo, useEffect } from 'react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import withProps from 'utils/withProps';
import formatterCollections from 'utils/intl/formatterCollections';
import { FuncType, ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { Header, Content } from 'components/Page';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useRequest } from '@components/tarzan-hooks';
import { openTab } from 'utils/menuTab';
import { compact } from 'lodash';
import { useDataSetEvent } from 'utils/hooks';
import notification from 'utils/notification';
import queryString from 'querystring';
import { tableDS } from './stories';
import { HandleDelete } from './services';

function RollBottomScrappingMaintenance(props) {
  const { tableDs } = props;

  const [disabledFlag, setDisabledFlag] = useState(false);
  const [selectFlag, setSelectFlag] = useState(false);
  const userInfo = getCurrentUser();

  // 删除
  const { run: handleDelete } = useRequest(HandleDelete(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    tableDs.query();
  }, []);

  const selectStatus = () => {
    setSelectFlag(tableDs.selected?.length);
  };

  useDataSetEvent(tableDs, 'select', () => {
    selectStatus();
  });

  useDataSetEvent(tableDs, 'selectAll', () => {
    selectStatus();
  });

  useDataSetEvent(tableDs, 'unSelectAll', () => {
    selectStatus();
  });

  useDataSetEvent(tableDs, 'unselect', () => {
    selectStatus();
  });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'siteLov',
        width: 120,
        editor: record => record.getState('editing'),
      },
      {
        name: 'materialLov',
        width: 120,
        editor: record => record.getState('editing'),
      },
      {
        name: 'materialName',
        width: 120,
      },
      {
        name: 'scrapQty',
        width: 120,
        editor: record => record.getState('editing'),
      },
      {
        name: 'defaultNcCodeLov',
        width: 150,
        editor: record => record.getState('editing'),
      },
      {
        name: 'description',
        width: 150,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'createdName',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 120,
      },
    ];
  }, []);

  // 编辑
  const handelEdit = () => {
    tableDs.records.forEach(item => {
      item.setState('editing', true);
    });
    setDisabledFlag(true);
  };

  // 取消
  const handleCancel = () => {
    tableDs.records.forEach(item => {
      if (item.status === 'add') {
        tableDs.remove(item);
      }
      item.setState('editing', false);
    });
    tableDs.query();
    setDisabledFlag(false);
  };

  // 保存
  const handleSave = async () => {
    const validate = await tableDs.validate();
    if (validate) {
      tableDs.submit().then(res => {
        if (res && res.success) {
          tableDs.query();
          setDisabledFlag(false);
        }
      });
    }
  };

  // 删除
  const handleLineDelete = () => {
    const materialScrapQtyIds = compact(
      tableDs.selected.map(item => item.get('materialScrapqtyId')),
    );
    if (materialScrapQtyIds.length > 0) {
      handleDelete({
        queryParams: { materialScrapQtyIds: `${materialScrapQtyIds}` },
      }).then(res => {
        if (res && res.success) {
          notification.success({
            message: intl.get(`tarzan.common.operation.success`).d('操作成功'),
          });
          tableDs.remove(tableDs.selected);
          tableDs.query();
        }
      });
    } else {
      tableDs.remove(tableDs.selected);
    }
  };

  // 新建
  const handleCreate = () => {
    const record = tableDs.create({ createdName: userInfo.realName }, 0);
    record.setState('editing', true);

    setDisabledFlag(true);
  };

  // 导入
  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MES_MATERIAL_SCRAP`,
      title: intl.get('tarzan.common.title.import').d('导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get('tarzan.mes.rollBottomScrappingMaintenance.title').d('卷底报废数量维护')}
      >
        {!disabledFlag && (
          <>
            <Button
              onClick={handelEdit}
              style={{ marginRight: 15 }}
              icon="edit-o"
              color={ButtonColor.primary}
              disabled={disabledFlag}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Button
              onClick={handleImport}
              style={{ marginRight: 15 }}
              color={ButtonColor.primary}
              icon="daorucanshu"
            >
              {intl.get('tarzan.common.button.repeat').d('导入')}
            </Button>
          </>
        )}
        {disabledFlag && (
          <>
            <Button onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button
              onClick={handleSave}
              style={{ marginRight: 15 }}
              icon="save"
              color={ButtonColor.primary}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        )}
        <Button color={ButtonColor.primary} icon="add" onClick={handleCreate}>
          {intl.get('tarzan.common.button.created').d('新建')}
        </Button>
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleLineDelete()}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button
            icon="delete"
            color={ButtonColor.red}
            funcType={FuncType.flat}
            disabled={!selectFlag}
          >
            {intl.get(`tarzan.common.button.delete`).d('删除')}
          </Button>
        </Popconfirm>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="rollBottomScrappingMaintenance"
          customizedCode="rollBottomScrappingMaintenance"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.common', 'tarzan.model.roll.bottom.scrapping.maintenance'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(RollBottomScrappingMaintenance),
);
