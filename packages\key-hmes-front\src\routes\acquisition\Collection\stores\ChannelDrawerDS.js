/**
 * @Description: radiofly
 * @Author: <<EMAIL>>
 * @Date: 2022-06-27 11:14:55
 * @LastEditTime: 2022-06-29 18:04:38
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.acquisition.collection';
const tenantId = getCurrentOrganizationId();

const tableDS = id => ({
  paging: false,
  selection: 'multiple',
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'tagObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagCode`).d('数据收集项编码'),
      lovCode: 'MT.TAG',
      ignore: 'always',
      multiple: true,
      required: true,
      lovPara: {
        tagGroupId: id,
        tenantId,
      },
    },
    {
      name: 'tagIds',
      type: FieldType.number,
      bind: 'tagObject.tagId',
      multiple: ',',
    },
    {
      name: 'tagCodes',
      type: FieldType.string,
      bind: 'tagObject.tagCode',
      multiple: ',',
    },
    {
      name: 'channelType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.channelType`).d('分发渠道'),
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=CHANNEL_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      name: 'channelObject',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.channelObject`).d('分发渠道对象'),
      required: true,
    },
    {
      name: 'checkType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.checkType`).d('校验类型'),
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=CHECK_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag-assign-channel/tag-assign-channel/ui`,
        method: 'get',
      };
    },
  },
});

export { tableDS };
