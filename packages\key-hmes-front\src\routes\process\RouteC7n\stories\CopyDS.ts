/**
 * 复制抽屉DS
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.process.routes.model.routes';

const copyDrawerDS: (typeGroup: string, routeStepOptionDs: DataSet) => DataSetProps = (
  typeGroup,
  routeStepOptionDs,
) => ({
  autoCreate: true,
  fields: [
    {
      name: 'targetRouterName',
      label: intl.get(`${modelPrompt}.targetRouterName`).d('目标编码'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'targetRevision',
      label: intl.get(`${modelPrompt}.targetRevision`).d('目标版本'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'targetRouterType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetRouterType`).d('目标类型'),
      required: true,
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        options: ({ dataSet }) => {
          const data = routeStepOptionDs.toData();
          let targetRouterTypeList: any = [];
          if (typeGroup === 'MES_ROUTER_TYPE') {
            if (['WO', 'EO'].indexOf(dataSet.getState('sourceRouterType')) > -1) {
              targetRouterTypeList = data.filter((item: any) => ['WO', 'EO'].includes(item.typeCode));
            } else {
              targetRouterTypeList = data.filter((item: any) => ['SPECIAL', 'NC'].includes(item.typeCode))
            }
          } else {
            targetRouterTypeList = data;
          }
          return new DataSet({
            data: [...targetRouterTypeList],
          });
        },
      },
    },
  ],
});

export { copyDrawerDS };
