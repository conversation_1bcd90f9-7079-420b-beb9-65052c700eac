import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor, FuncType, } from 'choerodon-ui/pro/lib/button/enum';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import listPageFactory from '../stores/listPageDs';
import axios from 'axios';
import { Popconfirm } from 'choerodon-ui';
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.teamMaintenanceRadio';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {
  const [edit, setEdit] = useState(false);

  useDataSetEvent(listDs, 'query', () => {
    setEdit(false)
  });

  useEffect(() => {
    listDs.query();
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'areaCode',
      editor: record => record.getState('editing'),
    },
    {
      name: 'areaName',
      editor: record => record.getState('editing'),
    },
    {
      name: 'broadcastFlag',
      editor: record => record.getState('editing'),
    },
    {
      name: 'points',
      width:150,
      editor: record => record.getState('editing'),
    },
    {
      name: 'pointsName',
      width:150,
      editor: record => record.getState('editing'),
    },
    {
      width:70,
      name: 'volume',
      editor: record => record.getState('editing'),
    },
    {
      name: 'repeatTime',
      width:100,
      editor: record => record.getState('editing'),
    },
    {
      name: 'speed',
      width:70,
      editor: record => record.getState('editing'),
    },
    {
      name: 'noRepeatMinutes',
      width:120,
      editor: record => record.getState('editing'),
    },
    {
      name: 'templateName',
      width:200,
      editor: record => record.getState('editing'),
    },
    {
      name: 'messageName',
      width:180,
      editor: record => record.getState('editing'),
    },
    {
      name: 'typeName',
      width:180,
      editor: record => record.getState('editing'),
    },
    {
      name: 'enableFlag',
      editor: record => record.getState('editing'),
    },
    {
      name: 'mediaStreamUrls',
      width:180,
      editor: record => record.getState('editing'),
    },
    {
      width: 150,
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      lock: ColumnLock.right,
      renderer: ({ record }) =>
        record!.getState('editing') ? (
          <>
            <Button color={ButtonColor.primary} funcType={FuncType.flat} onClick={() => handleEdit(record, false)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button color={ButtonColor.primary} funcType={FuncType.flat} onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        ) : (
          <>
            <Button color={ButtonColor.primary}
              disabled={listDs.records.some(record => record.getState('editing'))}
              funcType={FuncType.flat} onClick={() => handleEdit(record, true)}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => handleDelete(record)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <Button color={ButtonColor.primary} funcType={FuncType.flat}
                disabled={listDs.records.some(record => record.getState('editing'))}
              >
                {intl.get('tarzan.common.button.delete').d('删除')}
              </Button>
            </Popconfirm>
          </>
        ),
    },
  ];

  const handleDelete = async (record) => {
    if (record.get('productionAreaId')) {
      onDelete(record)
    } else {
      listDs.remove(record)
    }
  }

  const handleSave = async (record) => {
    const validate = await record.validate()
    if (validate) {
      const params = record.toData()
      if(params.templateName&&params.templateName.templateName){
        params.templateCode=params.templateName.templateCode
        params.templateName=params.templateName.templateName
      }
      if(params.messageName&&params.messageName.messageName){
        params.messageCode=params.messageName.messageCode
        params.messageName=params.messageName.messageName
      }
      if(params.typeName&&params.typeName.typeName){
        params.typeCode=params.typeName.typeCode
        params.typeName=params.typeName.typeName
      }
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-production-areas/save/ui`;
      const res: any = await axios.post(url, params)
      if (res && res.success) {
        await listDs.query();
      } else {
        notification.error({ message: res.message })
      }
    }
  }

  const onDelete = async (record) => {
      const params = record.toData()
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-production-areas/delete`;
      const res: any = await axios.post(url, params)
      if (res && res.success) {
        await listDs.query();
      } else {
        notification.error({ message: res.message })
      }
  }

  const handleCreate = () => {
    listDs.create({ status: 'add' }, 0)
    listDs.current?.setState('editing', true)
    setEdit(true)
  }

  const handleEdit = (record, flag) => {
    record.setState('editing', flag);
    if (!flag) {
      if (record.status === 'add') {
        listDs.remove(record)
      } else {
        record.reset()
      }
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.teamMaintenanceRadio`).d('班组区域维护-广播系统')}>
        <Button
          icon="add"
          color={ButtonColor.primary}
          disabled={edit}
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新增')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="teamMaintenanceRadio"
          rowHeight={35}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={4} // 头部显示的查询字段的数量
          searchCode="teamMaintenanceRadio" // 动态筛选条后端接口唯一编码
          customizedCode="teamMaintenanceRadio" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.teamMaintenanceRadio'],
})(ListPage);
