import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId, } from 'utils/utils';
import {  FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.cassetteMaterial';

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'lampStatusId',
    selection: false,
    paging: true,
    pageSize: 10,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
     
        {
          name: 'boxCodes',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.boxCode`).d('料盒编码'),
        },
        {
          name: 'identifications',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.identification`).d('产品条码'),
        },
        {
          name: 'materialLotCodes',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.materialLotCode`).d('物料批'),
        },
      ],
    }),
    fields: [
      {
        name: 'boxCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.boxCode`).d('料盒编码'),
      },
      {
        name: 'identification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.identification`).d('产品条码'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('产品物料'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialName`).d('产品名称'),
      },
      {
        name: 'bindTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.bindTime`).d('绑定时间'),
      }, 
      {
        name: 'productHistory',
        label: intl.get(`${modelPrompt}.form.productHistory`).d('产品料盒绑定历史'),
      },
      {
        name: 'history',
        label: intl.get(`${modelPrompt}.form.history`).d('料盒物料绑定历史'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method:'POST',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-box-binding/production/box/ui`,
        };
      },
    },
  });

export default listPageFactory;
