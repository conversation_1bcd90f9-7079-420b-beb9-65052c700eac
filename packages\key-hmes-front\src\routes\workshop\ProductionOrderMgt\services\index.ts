/**
 * @Description: 生产指令管理 eo创建 接口
 * @Author: <<EMAIL>>
 * @Date: 2022-10-12 10:29:53
 * @LastEditTime: 2022-10-12 15:25:52
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 获取指定物料wo基础信息
export function getAssembleDetailConfig() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/pre-assemble/upper/ui`,
    method: 'GET',
  };
}

// 获取指定物料wo基础信息
export function getAssembleListConfig() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/pre-material/ui`,
    method: 'GET',
  };
}

// 预装创建eo
export function saveAssembleListConfig() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/pre-assemble/save/ui`,
    method: 'POST',
  };
}



