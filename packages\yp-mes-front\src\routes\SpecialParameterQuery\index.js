import React, { useState, useMemo, useEffect } from 'react';
import {
  DataSet,
  Table,
  Button,
  Row,
  Col,
  TextField,
  Form,
  Icon,
  Lov,
  DateTimePicker,
  Select,
} from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExportPro';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { isNil } from 'lodash';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import moment from 'moment';
import { tableDS } from './stores';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

const tenantId = getCurrentOrganizationId();

// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.specialParameterQuery';

const SpecialParameterQuery = observer(props => {
  const {
    location: { state },
    history,
  } = props;

  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  useEffect(() => {
    if (state?.identifications) {
      tableDs.queryDataSet?.loadData([{ identifications: state?.identifications }]);
      tableDs.query(tableDs.currentPage);      
      history.replace({ ...history.location, state: undefined });
    }
  }, [history.location.state]);

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds

  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'areaLov':
        record.set('prodlineLov', null);
        record.set('equipmentLov', null);
        break;
      case 'prodlineLov':
        record.set('equipmentLov', null);
        break;
      default:
        break;
    }
  });

  const columns = [
    {
      name: 'identification',
      lock: 'left',
      width: 200,
    },
    {
      name: 'equipmentCode',
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'tagCode',
    },
    {
      name: 'tagDescription',
    },
    {
      name: 'tagValue',
    },
    {
      name: 'uomCode',
    },
    {
      name: 'uomName',
    },
    {
      name: 'tagCalculateResultDesc',
    },
    {
      name: 'trueValue',
      renderer: ({ value, record }) => {
        if (record.get('valueType') === 'VALUE') {
          if (record.get('trueValueList') && record.get('trueValueList').length) {
            const temp = record.get('trueValueList')
            return <> <Tag color="geekblue">{temp}</Tag></>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {
      name: 'falseValue',
      renderer: ({ value, record }) => {
        if (record.get('valueType') === 'VALUE') {
          if (record.get('falseValueList') && record.get('falseValueList').length) {
            const temp = record.get('falseValueList')
            return <> <Tag color="geekblue">{temp}</Tag></>
          }
          return ''
        }
        return <span>{value}</span>
      },
    },
    {
      name: 'recordDate',
    },
    {
      name: 'loginName',
    },
    {
      name: 'tagGroupCode',
    },
    {
      name: 'tagGroupDescription',
    },
    {
      name: 'operationName',
    },
    {
      name: 'operationDescription',
    },
    {
      name: 'workcellCode',
    },
    {
      name: 'workcellName',
    },
    {
      name: 'recordRemark',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'lastUpdateDate',
    },
  ];
  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    window.queryDataSet = queryDataSet;
    return (
      <Row
        gutter={24}
        style={{
          display: 'flex',
          alignItems: 'flex-start',
        }}
      >
        <Col span={18}>
          <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
            <TextField
              name="identifications"
              suffix={
                <div className="c7n-pro-select-suffix">
                  <Icon
                    type="search"
                    onClick={() =>
                      onOpenInputModal(true, 'identifications', '条码号', queryDataSet)
                    }
                  />
                </div>
              }
            />
            <DateTimePicker
              name="startDate"
              min={queryDataSet.current?.get('endDate') ? moment(queryDataSet.current?.get('endDate').format())?.subtract(3, 'days') : null}
              max={queryDataSet.current?.get('endDate')}
            />
            <DateTimePicker
              name="endDate"
              min={queryDataSet.current?.get('startDate')}
              max={queryDataSet.current?.get('startDate') ? moment(queryDataSet.current?.get('startDate').format())?.add(3, 'days') : null}
            />
            {expandForm && (
              <>
                <Lov name="areaLov" />
                <Lov name="prodlineLov" />
                <Lov name="equipmentLov" />
                <Lov name="materialLov" />
                <Lov name="operationLov" />
                <TextField name="tagValue" />
                <Lov name="tagLov" />
                <Lov name="tagGroupLov" />
                <Select name="tagCalculateResult" />
                <Select name="levelConfigTags" />
              </>
            )}
          </Form>
        </Col>
        <Col span={6}>
          <div>
            <Button
              funcType="link"
              icon={expandForm ? 'expand_less' : 'expand_more'}
              onClick={toggleForm}
            >
              {expandForm
                ? intl.get('hzero.common.button.collected').d('收起')
                : intl.get(`hzero.common.button.viewMore`).d('更多')}
            </Button>
            <Button
              onClick={() => {
                queryDataSet.current.reset();
                dataSet.fireEvent('queryBarReset', {
                  dataSet,
                  queryFields,
                });
              }}
            >
              {intl.get('hzero.common.button.reset').d('重置')}
            </Button>
            <Button dataSet={null} onClick={handleSearch} color="primary">
              {intl.get('hzero.common.button.search').d('查询')}
            </Button>
            {buttons}
          </div>
        </Col>
      </Row>
    );
  };

  const handleSearch = async () => {
    const {
      identifications,
      startDate,
      endDate,
      equipmentIdList,
      materialId,
      operationIdList,
      tagIdList,
      tagGroupIdList,
      levelConfigTags
    } = tableDs?.queryDataSet?.toJSONData()[0];
    const validate = await tableDs?.queryDataSet.validate();
    if (!validate) {
      return;
    }
    if (!identifications && !startDate && !endDate) {
      notification.error({
        message: intl.get(`${modelPrompt}.queryFieldRequired`).d('条码号与时间区间必须输入其一！'),
      });
      return;
    }
    if (
      (startDate && !endDate) ||
      (!startDate && endDate)
    ) {
      notification.error({
        message: intl.get(`${modelPrompt}.dateRequired`).d('时间开始与时间结束必须同时输入！'),
      });
      return;
    }
    if (!identifications&&
      startDate &&
      endDate &&
      !((equipmentIdList || []).length) &&
      !materialId &&
      !((operationIdList || []).length) &&
      !((tagIdList || []).length) &&
      !((tagGroupIdList || []).length)&&
      !((levelConfigTags || []).length)
    ) {
      notification.error({
        message: intl.get(`${modelPrompt}.fiveQueryField`).d('请至少输入设备编码、物料编码、工艺编码、收集项编码、收集组编码、分档参数其中一个进行查询'),
      });
      return;
    }
    tableDs.query();
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleSearch();
    }
  };
  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };
  const getExportQueryParams = () => {
    const queryParams = tableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i]) || i.includes('Lov') || i === '__dirty') {
        delete queryParams[i];
      }
    });
    return queryParams
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('特殊参数查询报表')}>
        <ExcelExport
          method="POST"
          allBody
          exportAsync={false}
          otherButtonProps={{ disabled: !tableDs.toData().length }}
          queryParams={getExportQueryParams}
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-special-param-query/export/ui`}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="SpecialParameterQuery"
          customizedCode="SpecialParameterQuery"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.specialParameterQuery', 'tarzan.common'],
})(SpecialParameterQuery);


