import intl from 'utils/intl';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.material-pfep-manager.model.dataItem';
// BASIC.TARZAN_METHOD = '/yp-wms-38719'
const tenantId = getCurrentOrganizationId();
/**
 * 基础信息
 */
const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  // dataKey: 'rows',
  paging: false,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'siteIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.siteId`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteIdObj.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteIdObj.siteCode',
    },
    {
      name: 'materialIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.materialId`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'materialId',
      bind: 'materialIdObj.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialIdObj.materialCode',
    },
    {
      name: 'materialSiteId',
      type: 'string',
      bind: 'materialIdObj.materialSiteId',
    },
    {
      name: 'materialName',
      bind: 'materialIdObj.materialName',
      type: 'string',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'makeBuyCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.makeBuyCode`).d('制造采购标识'),
      lookupCode: 'MT.METHOD.MAKE_BUY_CODE1',
    },
    {
      name: 'highPriceFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.highPriceFlag`).d('高价值物料标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'coaApproveGlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.coaApproveGlag`).d('COA自动审核标识'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'pickFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.pickFlag`).d('取货标识'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'deliverGroup',
      type: 'string',
      label: intl.get(`${modelPrompt}.deliverGroup`).d('送货是否组托'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'receivedGroup',
      type: 'string',
      label: intl.get(`${modelPrompt}.receivedGroup`).d('上架是否组托'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'lotControlFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.lotControlFlag`).d('是否批次限制'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'containerTypeObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.CONTAINER_TYPE_CODE`,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      required: false,
    },
    {
      name: 'containerType',
      bind: 'containerTypeObj.containerTypeCode',
    },
    // {
    //   name: 'containerTypeValue',
    //   bind: 'containerTypeObj.containerTypeCode',
    // },
    {
      name: 'snpControlFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.snpControlFlag`).d('SNP管控标识'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'mmsEnableFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.mmsEnableFlag`).d('启用状态'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'minRemainShelfLife',
      type: 'string',
      label: intl.get(`${modelPrompt}.minRemainShelfLife`).d('期望最小剩余寿命'),
    },
    {
      name: 'shelfLife',
      type: 'string',
      label: intl.get(`${modelPrompt}.shelfLife`).d('保质期'),
    },
    {
      name: 'extendedShelfLife',
      type: 'string',
      label: intl.get(`${modelPrompt}.extendedShelfLife`).d('延保期'),
    },
    {
      name: 'dullPeriod',
      type: 'string',
      label: intl.get(`${modelPrompt}.dullPeriod`).d('呆滞期'),
    },
    {
      name: 'dullPeriodSecondary',
      type: 'string',
      label: intl.get(`${modelPrompt}.dullPeriodSecondary`).d('二级呆滞期'),
    },
    {
      name: 'earlyWarningLeadTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.earlyWarningLeadTime`).d('预警提前期'),
    },
    {
      name: 'shelfLifeUomIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.shelfLifeUomId`).d('时间单位编码'),
      lovCode: 'MT.COMMON.UOM',
      ignore: 'always',
      lovPara: {
        tenantId,
        uomType: 'TIME',
      },
      required: false,
    },
    {
      name: 'shelfLifeUomId',
      bind: 'shelfLifeUomIdObj.uomId',
    },
    {
      name: 'shelfLifeUomCode',
      bind: 'shelfLifeUomIdObj.uomCode',
    },
    {
      name: 'minStockQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.minStockQty`).d('最小存储库存'),
      min: 0,
      dynamicProps: {
        max: ({ record }) => {
          return record.get('maxStockQty');
        },
      },
    },
    {
      name: 'maxStockQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.maxStockQty`).d('最大存储库存'),
      dynamicProps: {
        min: ({ record }) => {
          return record.get('minStockQty');
        },
      },
    },
    {
      name: 'minPackageQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.minPackageQty`).d('最小单包量'),
    },
    {
      name: 'packMinQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.packMinQty`).d('领料最小包装数'),
    },
    {
      name: 'packageWeight',
      type: 'number',
      label: intl.get(`${modelPrompt}.packageWeight`).d('存储包装重量'),
    },
    {
      name: 'weightUomIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.weightUomId`).d('重量单位'),
      lovCode: 'MT.COMMON.UOM',
      ignore: 'always',
      lovPara: {
        tenantId,
        uomType: 'WEIGHT',
      },
      required: false,
    },
    {
      name: 'weightUomId',
      bind: 'weightUomIdObj.uomId',
    },
    {
      name: 'weightUomCode',
      bind: 'weightUomIdObj.uomCode',
    },
    {
      name: 'packageLength',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageLength`).d('单位包装长'),
    },
    {
      name: 'packageWidth',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageWidth`).d('单位包装宽'),
    },
    {
      name: 'packageHeight',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageHeight`).d('单位包装高'),
    },
    {
      name: 'packageSizeUomIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.packageSizeUomId`).d('包装尺寸单位'),
      lovCode: 'MT.COMMON.UOM',
      ignore: 'always',
      lovPara: {
        tenantId,
        uomType: 'LENGTH',
      },
      required: false,
    },
    {
      name: 'packageSizeUomId',
      bind: 'packageSizeUomIdObj.uomId',
    },
    {
      name: 'packageSizeUomCode',
      bind: 'packageSizeUomIdObj.uomCode',
    },
    {
      name: 'loadingArea',
      type: 'string',
      label: intl.get(`${modelPrompt}.loadingArea`).d('单托装载面积'),
    },
    {
      name: 'loadingFloor',
      type: 'string',
      label: intl.get(`${modelPrompt}.loadingFloor`).d('装载堆叠层数'),
    },
    {
      name: 'packageType',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageType`).d('包装类型'),
    },
    {
      name: 'singleBoxes',
      type: 'string',
      label: intl.get(`${modelPrompt}.singleBoxes`).d('单托箱数'),
    },
    {
      name: 'mpiEnableFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.mpiEnableFlag`).d('启用状态'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'mixLotFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.mixLotFlag`).d('是否混批'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'mrpType',
      type: 'string',
      label: intl.get(`${modelPrompt}.mrpType`).d('MRP类型'),
      lookupCode: 'WMS_MRP_TYPE',
      dynamicProps: {
        required: ({ record }) => {
          return (
            record.get('mrpType') ||
            record.get('mrpController') ||
            record.get('batchProgram') ||
            record.get('maxBatch') ||
            record.get('minBatch') ||
            record.get('fixedBatch') ||
            record.get('safetyInventory') ||
            (record.get('receiveLocatorIdObj') && record.get('receiveLocatorIdObj').locatorId) ||
            record.get('seifmadeProductionTime') ||
            record.get('receiveProcessTime') ||
            record.get('procureAdvance') ||
            record.get('longCycleFlag') === 'Y'
          );
        },
      },
    },
    {
      name: 'mrpController',
      type: 'string',
      label: intl.get(`${modelPrompt}.mrpController`).d('MRP控制者'),
      lookupCode: 'WMS_MRP_CONTROLLER',
      dynamicProps: {
        required: ({ record }) => {
          return (
            record.get('mrpType') ||
            record.get('mrpController') ||
            record.get('batchProgram') ||
            record.get('maxBatch') ||
            record.get('minBatch') ||
            record.get('fixedBatch') ||
            record.get('safetyInventory') ||
            (record.get('receiveLocatorIdObj') && record.get('receiveLocatorIdObj').locatorId) ||
            record.get('seifmadeProductionTime') ||
            record.get('receiveProcessTime') ||
            record.get('procureAdvance')
          );
        },
      },
    },
    {
      name: 'batchProgram',
      type: 'string',
      label: intl.get(`${modelPrompt}.batchProgram`).d('批量程序'),
      lookupCode: 'WMS_MRP_PROGRAM',
      dynamicProps: {
        required: ({ record }) => {
          return (
            record.get('mrpType') ||
            record.get('mrpController') ||
            record.get('batchProgram') ||
            record.get('maxBatch') ||
            record.get('minBatch') ||
            record.get('fixedBatch') ||
            record.get('safetyInventory') ||
            (record.get('receiveLocatorIdObj') && record.get('receiveLocatorIdObj').locatorId) ||
            record.get('seifmadeProductionTime') ||
            record.get('receiveProcessTime') ||
            record.get('procureAdvance')
          );
        },
      },
    },
    {
      name: 'maxBatch',
      type: 'string',
      label: intl.get(`${modelPrompt}.maxBatch`).d('最大批量'),
      dynamicProps: {
        min: ({ record }) => {
          if(Number(record.get('minBatch')) > Number(record.get('minPackageQty'))){
            return Number(record.get('minBatch'));
          }
          return record.get('minPackageQty');
        },
      },
    },
    {
      name: 'minBatch',
      type: 'string',
      label: intl.get(`${modelPrompt}.minBatch`).d('最小批量'),
    },
    {
      name: 'fixedBatch',
      type: 'string',
      label: intl.get(`${modelPrompt}.fixedBatch`).d('固定批量'),
      dynamicProps: {
        required: ({ record }) => {
          return record.get('batchProgram') === 'FX';
        },
      },
    },
    {
      name: 'safetyInventory',
      type: 'string',
      label: intl.get(`${modelPrompt}.safetyInventory`).d('安全库存'),
    },
    {
      name: 'receiveLocatorIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.receiveLocatorId`).d('默认收货仓库'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')],
            type: 'LOCATOR',
            locatorCategoryList: ['AREA'],
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        required: ({ record }) => {
          return (
            record.get('mrpType') ||
            record.get('mrpController') ||
            record.get('batchProgram') ||
            record.get('maxBatch') ||
            record.get('minBatch') ||
            record.get('fixedBatch') ||
            record.get('safetyInventory') ||
            (record.get('receiveLocatorIdObj') && record.get('receiveLocatorIdObj').locatorId) ||
            record.get('seifmadeProductionTime') ||
            record.get('receiveProcessTime') ||
            record.get('procureAdvance')
          );
        },
      },
    },
    {
      name: 'receiveLocatorId',
      bind: 'receiveLocatorIdObj.locatorId',
    },
    {
      name: 'receiveLocatorCode',
      bind: 'receiveLocatorIdObj.locatorCode',
    },
    {
      name: 'seifmadeProductionTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.seifmadeProductionTime`).d('自制生产时间'),
    },
    {
      name: 'receiveProcessTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.receiveProcessTime`).d('收货处理时间'),
    },
    {
      name: 'procureAdvance',
      type: 'string',
      label: intl.get(`${modelPrompt}.procureAdvance`).d('采购提前期'),
    },
    {
      name: 'longCycleFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.longCycleFlag`).d('长周期标识'),

      trueValue: 'Y',
      falseValue: 'N',
    },
    // {
    //   name: 'businessSector',
    //   type: 'string',
    //   label: intl.get(`${modelPrompt}.businessSector`).d('业务板块'),
    // },
    {
      name: 'jitFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.jitFlag`).d('JIT物料'),
    },
    // {
    //   name: 'moq',
    //   type: 'number',
    //   label: intl.get(`${modelPrompt}.moq`).d('最小起订量'),
    // },
    // {
    //   name: 'perCompletedQty',
    //   type: 'number',
    //   label: intl.get(`${modelPrompt}.perCompletedQty`).d('单件完工数量'),
    // },
    // {
    //   name: 'receiptDate',
    //   type: 'string',
    //   label: intl.get(`${modelPrompt}.receiptDate`).d('采购提前期'),
    // },
    {
      name: 'specialPurchaseType',
      type: 'string',
      label: intl.get(`${modelPrompt}.specialPurchaseType`).d('特殊采购类型'),
    },
    {
      name: 'standardPackageQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.standardPackageQty`).d('标准包装量'),
    },
    {
      name: 'receiptDate',
      type: 'string',
      label: intl.get(`${modelPrompt}.receiptDate`).d('采购接收预设值'),
    },
    {
      name: 'aPointSupplier',
      type: 'string',
      label: intl.get(`${modelPrompt}.aPointSupplier`).d('A点供应商'),
    },
    {
      name: 'poLineCreationFlag',
      lookupCode: 'MT.YES_NO',
      type: 'string',
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.poLineCreationFlag`).d('PO行创建标识'),
    },
    // {
    //   name: 'wkcBoudingFlag',
    //   type: 'string',
    //   label: intl.get(`${modelPrompt}.wkcBoudingFlag`).d('校验工位绑定标识'),
    //   trueValue: 'Y',
    //   falseValue: 'N',
    // },
    {
      name: 'minPackageQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.minPackageQty`).d('最小包装数量'),
    },
    {
      name: 'expirationDateFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.expirationDateFlag`).d('保质期管理标识'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'freezeDate',
      type: 'string',
      label: intl.get(`${modelPrompt}.freezeDate`).d('冻结提前期'),
    },
    {
      name: 'mareqPackType',
      type: 'string',
      label: intl.get(`${modelPrompt}.mareqPackType`).d('领料包装类型'),
      lookupCode:'WMS.MAREQ_PACK_TYPE'
    },
    // {
    //   name: 'singleFlag',
    //   type: 'string',
    //   label: intl.get(`${modelPrompt}.singleFlag`).d('单件追溯标识'),
    //   trueValue: 'Y',
    //   falseValue: 'N',
    // },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/wms-mrp-inventorys/detail`,
        method: 'get',
      };
    },
  },
});

const orgListDS = headDs => ({
  name: 'orgListDS',
  primaryKey: 'organizationId',
  paging: true,
  autoQuery: false,
  selection: false,
  fields: [
    {
      name: 'organizationIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.organizationId`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      ignore: 'always',
      lovPara: {
        tenantId,
        locatorCategory: ['AREA'],
      },
      dynamicProps: {
        // lovPara: () => {
        //   const siteId = headDs.current.get('siteId');
        //   return {
        //     tenantId,
        //     siteId,
        //     enableFlag: 'Y',
        //   };
        // },
        disabled: () => {
          const siteId = headDs.current.get('siteId');
          return !siteId;
        },
      },
      required: true,
    },
    {
      name: 'organizationId',
      bind: 'organizationIdObj.locatorId',
    },
    {
      name: 'organizationCode',
      bind: 'organizationIdObj.locatorCode',
    },
    {
      name: 'shelfLife',
      type: 'string',
      label: intl.get(`${modelPrompt}.shelfLife`).d('保质期'),
    },
    {
      name: 'extendedShelfLife',
      type: 'string',
      label: intl.get(`${modelPrompt}.extendedShelfLife`).d('延保期'),
    },
    {
      name: 'dullPeriod',
      type: 'string',
      label: intl.get(`${modelPrompt}.dullPeriod`).d('呆滞期'),
    },
    {
      name: 'dullPeriodSecondary',
      type: 'string',
      label: intl.get(`${modelPrompt}.dullPeriodSecondary`).d('二级呆滞期'),
    },
    {
      name: 'earlyWarningLeadTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.earlyWarningLeadTime`).d('预警提前期'),
    },
    {
      name: 'shelfLifeUomIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.shelfLifeUomId`).d('时间单位编码'),
      lovCode: 'MT.COMMON.UOM',
      ignore: 'always',
      lovPara: {
        tenantId,
        uomType: 'TIME',
      },
      required: false,
    },
    {
      name: 'shelfLifeUomId',
      bind: 'shelfLifeUomIdObj.uomId',
    },
    {
      name: 'shelfLifeUomCode',
      bind: 'shelfLifeUomIdObj.uomCode',
    },
    {
      name: 'minStockQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.minStockQty`).d('最小存储库存'),
      min: 0,
      dynamicProps: {
        max: ({ record }) => {
          return record.get('maxStockQty');
        },
      },
    },
    {
      name: 'maxStockQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.maxStockQty`).d('最大存储库存'),
      dynamicProps: {
        min: ({ record }) => {
          return record.get('minStockQty');
        },
      },
    },
    {
      name: 'enableFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),

      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

export { detailDS, orgListDS };
