/**
 * @Description: 物料防呆防错-列表页
 * @Author: <EMAIL>
 * @Date: 2023-03-14 10:19:56
 */

import React, { useEffect } from 'react';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Tag, Badge } from 'choerodon-ui';
import intl from 'utils/intl';
import { openTab } from 'utils/menuTab';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import queryString from 'querystring';
import { getCurrentOrganizationId } from 'utils/utils';
import { tableDS } from '../stores/MaterialPreventErrorDS';

const tenantId = getCurrentOrganizationId();

const tagClassName = {
  时效维护: 'purple',
  // 状态维度: 'blue',
};

const MaterialPreventErrorList = props => {
  const { tableDs } = props;
  useEffect(() => {
    tableDs.query(props.tableDs.currentPage);
  }, []);
  const columns = [
    {
      name: 'strategyCode',
      width: 300,
      align: 'center',
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(`/hmes/material-prevent-error/detail/${record.data.strategyId}`);
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'strategyDesc',
      align: 'center',
    },
    {
      name: 'dimensionList',
      align: 'center',
      width: 200,
      renderer: ({ value, record }) =>
        record.get('dimensionList') && String(record.get('dimensionList')).length === 9 ? (
          <>
            <Tag color={tagClassName[String(record.get('dimensionList')).slice(0, 4)]}>
              {String(record.get('dimensionList')).slice(0, 4)}
            </Tag>
            <Tag color={tagClassName[String(record.get('dimensionList')).slice(5, 9)]}>
              {String(record.get('dimensionList')).slice(5, 9)}
            </Tag>
          </>
        ) : (
          record.get('dimensionList') && (
            <Tag color={tagClassName[record.get('dimensionList')]}>{value}</Tag>
          )
        ),
    },
    {
      name: 'businessTypeDesc',
      align: 'center',
      width: 200,
    },
    {
      name: 'enableFlag',
      align: 'center',
      width: 200,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  // 跳转新建
  const handleCreate = () => {
    props.history.push('/hmes/material-prevent-error/detail/create');
  };

  // 导入
  const handleImport = ()=>{
    const code = 'HME.ERR_PROOFING_STRGY_OBJECT';
    // openTab({
    //   key: `/material-prevent-error/import`,
    //   path: `/hmes/material-prevent-error/import/${code}`,
    //   title: '物料防呆防错导入',
    //   closable: true,
    // });
    openTab({
      key: `/himp/commentImport/${code}`,
      title: intl.get('tarzan.hmes.materialPreventError.import').d('物料防呆防错导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.hmes.materialPreventError.title').d('物料防呆防错配置')}>
        <Button
          // type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <Button
          // type="c7n-pro"
          color={ButtonColor.primary}
          icon="daorucanshu"
          onClick={handleImport}
        >
          {intl.get('tarzan.common.button.repeat').d('导入')}
        </Button>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={4}
          searchCode="materialPreventErrorList"
          customizedCode="materialPreventErrorList"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.hmes.materialPreventError', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MaterialPreventErrorList),
);
