import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.event.accountProcessing';

const ncRecordDS = () => ({
  name: 'ncRecordDS',
  primaryKey: 'ncRecordId',
  paging: false,
  autoQuery: false,
  selection: 'multiple',
  fields: [
    {
      name: 'ncRecordId',
      type: 'number',
    },
    {
      name: 'identification',
      type: 'string',
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
    },
    {
      name: 'materialLotCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'qty',
      type: 'number',
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'uomName',
      type: 'string',
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'workNumOrderLov',
      type: 'object',
      required: true,
      lovCode: 'HME.WO_NOT_CLOSED',
      textField: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workNumOrder`).d('工单'),
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            materialLotId: record?.get('materialLotId'),
          };
        },
      },
    },
    {
      name: 'workOrderId',
      bind: 'workNumOrderLov.workOrderId',
    }, {
      name: 'workOrderNum',
      bind: 'workNumOrderLov.workOrderNum',
    },
    {
      name: 'scrapReason',
      type: 'string',
      required: true,
      textField: 'description',
      lookupCode: 'HME_TRANS_REASON',
      label: intl.get(`${modelPrompt}.scrapReason`).d('报废原因'),
    },
  ],
});

export { ncRecordDS };
