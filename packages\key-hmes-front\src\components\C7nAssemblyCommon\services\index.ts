/**
 * @Description: 装配清单-service
 * @Author: <EMAIL>
 * @Date: 2022/8/3 15:53
 * @LastEditTime: 2023-05-18 15:04:18
 * @LastEditors: <<EMAIL>>
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { AxiosRequestConfig } from 'axios';
import { RequestParams } from '@components/tarzan-hooks/useRequest';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 装配清单-列表页-查询装配清单
export function queryAssemblyList(serverCode: string): AxiosRequestConfig {
  return {
    url: `${serverCode}/v1/${tenantId}/mt-bom/list/ui`,
    method: 'GET',
  };
}

// 装配清单-详情页-查询装配清单明细
export function queryAssemblyDetail(serverCode: string): AxiosRequestConfig {
  return {
    url: `${serverCode}/v1/${tenantId}/mt-bom/all/data/ui`,
    method: 'GET',
  };
}

// 装配清单-详情页-获取站点下拉框数据
export function fetchSiteOptions(serverCode: string): AxiosRequestConfig {
  return {
    url: `${serverCode}/v1/${tenantId}/mt-component/user/distribution/site/list/ui`,
    method: 'GET',
  };
}

// 装配清单-详情页-保存装配清单
export function saveAssemblyDetail(serverCode, custCode): RequestParams {
  return {
    url: `${serverCode}/v1/${tenantId}/mt-bom/save/all/data/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.${custCode}.BASIC,${BASIC.CUSZ_CODE_BEFORE}.${custCode}.COMP`,
    method: 'POST',
  };
}

// 装配清单-详情页-保存装配清单前进行验证
export function confirmAssembly(serverCode): RequestParams {
  return {
    url: `${serverCode}/v1/${tenantId}/mt-bom/confirm/save/all/data/ui`,
    method: 'POST',
  };
}

// 装配清单-详情页-复制
export function copyBom(serverCode): RequestParams {
  return {
    url: `${serverCode}/v1/${tenantId}/mt-bom/copy/ui`,
    method: 'POST',
  };
}
