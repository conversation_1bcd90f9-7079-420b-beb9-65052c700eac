/**
 * @Description: 用户权限编辑抽屉-右侧表格
 * @Author: <<EMAIL>>
 * @Date: 2022-10-12 19:08:08
 * @LastEditTime: 2022-10-17 14:03:23
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import { Popover, Radio } from 'choerodon-ui';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useRequest } from '@components/tarzan-hooks';
import { SetUserDefaultOrganization } from './services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.model.hmes.userRights';

interface OrganizationTableProps {
  ds: DataSet,
  siteListDs: DataSet,
  areaListDs: DataSet,
  prolineListDs: DataSet,
  workcellListDs: DataSet,
  areaLocatorListDs: DataSet,
  inventoryLocatorListDs: DataSet,
  locationLocatorListDs: DataSet,
  maxHeight: number,
}

const OrganizationTable = observer((props: OrganizationTableProps) => {
  const {
    ds,
    siteListDs,
    areaListDs,
    prolineListDs,
    workcellListDs,
    areaLocatorListDs,
    inventoryLocatorListDs,
    locationLocatorListDs,
    maxHeight,
  } = props;

  const allotType: 'Organization' | 'Location' = ds!.current!.get('allotType');
  const setUserDefaultOrganization = useRequest(SetUserDefaultOrganization(), { manual: true });

  const labelMap = useMemo(() => {
    return {
      SITE: intl.get(`${modelPrompt}.site`).d('站点'),
      AREA: intl.get(`${modelPrompt}.area`).d('区域'),
      PROD_LINE: intl.get(`${modelPrompt}.proline`).d('生产线'),
      WORKCELL: intl.get(`${modelPrompt}.workcell`).d('工作单元'),
      LOCATORAREA: intl.get(`${modelPrompt}.areaLocator`).d('区域库位'),
      LOCATORINVENTORY: intl.get(`${modelPrompt}.inventoryLocator`).d('库存库位'),
      LOCATORLOCATION: intl.get(`${modelPrompt}.locationLocator`).d('地点库位'),
    };
  }, []);

  const changeFlag = (record: Record, _ds: DataSet) => {
    setUserDefaultOrganization.run({
      params: record.get('userOrganizationId'),
      onSuccess: () => {
        const oldDefaultRecord = _ds.find((record) => record.get('defaultOrganizationFlag') === 'Y')
        if (oldDefaultRecord) {
          oldDefaultRecord.init('defaultOrganizationFlag', 'N')
        }
        record.init('defaultOrganizationFlag', 'Y');
      },
    })
  }

  const siteListColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'organizationCode',
        title: labelMap.SITE,
        renderer: ({ value }) => <Popover content={value}>{value}</Popover>,
      },
      {
        name: 'defaultOrganizationFlag',
        title: intl.get(`${modelPrompt}.defaultOrganizationFlag`).d('默认状态'),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ value, record }) => {
          return (
            <Radio
              checked={value === 'Y'}
              onChange={() => changeFlag(record!, siteListDs)}
            />
          )
        },
      },
    ];
  }, []);
  const areaListColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'organizationCode',
        title: labelMap.AREA,
        renderer: ({ value }) => <Popover content={value}>{value}</Popover>,
      },
      {
        name: 'defaultOrganizationFlag',
        title: intl.get(`${modelPrompt}.defaultOrganizationFlag`).d('默认状态'),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ value, record }) => {
          return (
            <Radio
              checked={value === 'Y'}
              onChange={() => changeFlag(record!, areaListDs)}
            />
          )
        },
      },
    ];
  }, []);
  const prolineListColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'organizationCode',
        title: labelMap.PROD_LINE,
        renderer: ({ value }) => <Popover content={value}>{value}</Popover>,
      },
      {
        name: 'defaultOrganizationFlag',
        title: intl.get(`${modelPrompt}.defaultOrganizationFlag`).d('默认状态'),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ value, record }) => {
          return (
            <Radio
              checked={value === 'Y'}
              onChange={() => changeFlag(record!, prolineListDs)}
            />
          )
        },
      },
    ];
  }, []);
  const workcellListColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'organizationCode',
        title: labelMap.WORKCELL,
        renderer: ({ value }) => <Popover content={value}>{value}</Popover>,
      },
      {
        name: 'defaultOrganizationFlag',
        title: intl.get(`${modelPrompt}.defaultOrganizationFlag`).d('默认状态'),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ value, record }) => {
          return (
            <Radio
              checked={value === 'Y'}
              onChange={() => changeFlag(record!, workcellListDs)}
            />
          )
        },
      },
    ];
  }, []);
  const areaLocatorListColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'organizationCode',
        title: labelMap.LOCATORAREA,
        renderer: ({ value }) => <Popover content={value}>{value}</Popover>,
      },
      {
        name: 'defaultOrganizationFlag',
        title: intl.get(`${modelPrompt}.defaultOrganizationFlag`).d('默认状态'),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ value, record }) => {
          return (
            <Radio
              checked={value === 'Y'}
              onChange={() => changeFlag(record!, areaLocatorListDs)}
            />
          )
        },
      },
    ];
  }, []);
  const inventoryLocatorListColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'organizationCode',
        title: labelMap.LOCATORINVENTORY,
        renderer: ({ value }) => <Popover content={value}>{value}</Popover>,
      },
      {
        name: 'defaultOrganizationFlag',
        title: intl.get(`${modelPrompt}.defaultOrganizationFlag`).d('默认状态'),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ value, record }) => {
          return (
            <Radio
              checked={value === 'Y'}
              onChange={() => changeFlag(record!, inventoryLocatorListDs)}
            />
          )
        },
      },
    ];
  }, []);
  const locationLocatorListColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'organizationCode',
        title: labelMap.LOCATORLOCATION,
        renderer: ({ value }) => <Popover content={value}>{value}</Popover>,
      },
      {
        name: 'defaultOrganizationFlag',
        title: intl.get(`${modelPrompt}.defaultOrganizationFlag`).d('默认状态'),
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ value, record }) => {
          return (
            <Radio
              checked={value === 'Y'}
              onChange={() => changeFlag(record!, locationLocatorListDs)}
            />
          )
        },
      },
    ];
  }, []);

  return (
    <>
      {allotType === 'Organization' && (
        <>
          <Table
            virtual
            virtualCell
            className={styles.tableToolHide}
            style={{ height: maxHeight / 4 }}
            dataSet={siteListDs}
            columns={siteListColumns}
          />
          <Table
            virtual
            virtualCell
            className={styles.tableToolHide}
            style={{ height: maxHeight / 4 }}
            dataSet={areaListDs}
            columns={areaListColumns}
          />
          <Table
            virtual
            virtualCell
            className={styles.tableToolHide}
            style={{ height: maxHeight / 4 }}
            dataSet={prolineListDs}
            columns={prolineListColumns}
          />
          <Table
            virtual
            virtualCell
            className={styles.tableToolHide}
            style={{ height: maxHeight / 4 }}
            dataSet={workcellListDs}
            columns={workcellListColumns}
          />
        </>
      )}
      {allotType === 'Location' && (
        <>
          <Table
            virtual
            virtualCell
            className={styles.tableToolHide}
            style={{ height: maxHeight / 3 }}
            dataSet={areaLocatorListDs}
            columns={areaLocatorListColumns}
          />
          <Table
            virtual
            virtualCell
            className={styles.tableToolHide}
            style={{ height: maxHeight / 3 }}
            dataSet={inventoryLocatorListDs}
            columns={inventoryLocatorListColumns}
          />
          <Table
            virtual
            virtualCell
            className={styles.tableToolHide}
            style={{ height: maxHeight / 3 }}
            dataSet={locationLocatorListDs}
            columns={locationLocatorListColumns}
          />
        </>
      )}
    </>
  )
});

export default OrganizationTable;
