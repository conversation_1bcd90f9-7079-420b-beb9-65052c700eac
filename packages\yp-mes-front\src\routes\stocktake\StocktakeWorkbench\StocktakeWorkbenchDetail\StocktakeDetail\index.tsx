/**
 * @Description: 盘点工作台详情页-盘点明细Tab
 * @Author: <<EMAIL>>
 * @Date: 2022-02-14 13:31:26
 * @LastEditTime: 2023-01-10 11:00:39
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Table, Button } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useRequest } from '@components/tarzan-hooks';
import styles from './index.module.less';
import {
  SaveDifferencesReasons,
} from '../../services';

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';

export default ({ ds, stocktakeStatus, setSelectedStocktakeBarcodeList }) => {
  const [canEdit, setCanEdit] = useState(false);
  const saveDifferencesReasons = useRequest(SaveDifferencesReasons(), { manual: true, needPromise: true });

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 生成行列表DS查询项
  const listener = flag => {
    // 列表交互监听
    if (ds) {
      const handler = flag ? ds.addEventListener : ds.removeEventListener;
      // 选中和撤销选中事件
      handler.call(ds, 'batchSelect', handleLocatorRangeTableChange);
      handler.call(ds, 'batchUnSelect', handleLocatorRangeTableChange);
    }
  };

  // 库位范围表格行选中事件
  const handleLocatorRangeTableChange = ({ dataSet }) => {
    const _selectedTableLines: number[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLines.push(item.toData());
    });
    setSelectedStocktakeBarcodeList(_selectedTableLines);
  };

  const getColumns = useMemo(() => {
    const columns: ColumnProps[] = [
      {
        name: 'materialCode',
        width: 180,
      },
      {
        name: 'materialName',
        minWidth: 180,
      },
      {
        name: 'revisionCode',
        width: 180,
      },
      {
        name: 'locatorCode',
        width: 180,
      },
      {
        name: 'locatorName',
        minWidth: 180,
      },
      {
        name: 'sumQuantity',
        width: 100,
      },
    ];
    const dynamicColumns: ColumnProps[] = [
      {
        name: 'adjustQty',
        width: 100,
      },
      {
        name: 'firstCountQty',
        width: 100,
      },
      {
        name: 'firstCountDiffQty',
        width: 100,
        renderer: ({ value }) => {
          return (
            value && <Tag color={value < 0 ? 'red' : 'green'}>{value}</Tag>
          );
        },
      },
      {
        name: 'reCountQty',
        width: 100,
      },
      {
        name: 'reCountDiffQty',
        width: 100,
        renderer: ({ value }) => {
          return (
            value && <Tag color={value < 0 ? 'red' : 'green'}>{value}</Tag>
          );
        },
      },
    ];
    const endColumns: ColumnProps[] = [
      {
        name: 'uomCode',
        width: 100,
      },
      {
        name: 'contrastReason',
        width: 100,
        // editor: canEdit,
        editor: record => record.getState('editing'),
      },
    ];
    if (['NEW', 'RELEASED'].includes(stocktakeStatus)) {
      return columns.concat(endColumns);
    }
    return columns.concat(dynamicColumns).concat(endColumns);
  }, [canEdit, stocktakeStatus]);
  const handleEntry = () => {
    ds.selected.forEach(record => {
      record.setState('editing', true);
    })
    setCanEdit(prev => !prev);
  }
  const handleCancel = async () => {
    setCanEdit(prev => !prev);
    await ds.query();
    ds.unSelectAll()
  }
  const handleSave = async () => {
    if(await ds.validate(false, true)){
      saveDifferencesReasons.run({
        params: ds.toJSONData(),
      }).then(async res => {
        if(res && res.success){
          setCanEdit(prev => !prev);
          await ds.query()
        }
      })
    }
  }

  const buttonsObject = useMemo(() => {
    return [
      canEdit?<>
        <Button onClick={handleCancel} >
          {intl.get('tarzan.common.button.cancel').d('取消')}

        </Button>
        <Button onClick={handleSave} color={ButtonColor.primary}>
          {intl.get('tarzan.common.button.save').d('保存')}
        </Button>
      </>:<>
        <Button onClick={handleEntry} disabled={stocktakeStatus !== 'COMPLETED'||!ds.selected.length}>
          {intl.get(`${modelPrompt}.reasonEntry`).d('差异原因录入')}
        </Button>
      </>,
    ]
  }, [canEdit, ds.selected.length, stocktakeStatus]);

  return (
    <div className={styles['card-table']}>
      <Table
        buttons={buttonsObject}
        className={styles['expand-table']}
        queryBar={TableQueryBarType.bar}
        dataSet={ds}
        columns={getColumns}
      />
    </div>
  );
};
