import React, { useState, useMemo, useEffect } from 'react';
import {
  DataSet,
  Table,
  Button,
  Row,
  Col,
  TextField,
  Form,
  Icon,
  Lov,
  DateTimePicker,
  Select,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import ExcelExport from 'components/ExcelExport';
import ExcelExportPro from 'components/ExcelExportPro';
import { isNil } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { tableDS } from './stores';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.NcRecordQuery';

const NcRecordQuery = observer(props => {
  const {
    location: { state },
    history,
  } = props;
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds

  const columns = [
    {
      name: 'siteCode',
      width: 200,
    },
    {
      name: 'ncRecordNum',
      width: 150,
    },
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'eoNum',
      width: 150,
    },
    {
      name: 'materialLotCode',
      width: 150,
    },
    {
      name: 'ncRecodeTypeDesc',
      width: 150,
    },
    {
      name: 'ncRecordStatusDesc',
    },
    {
      name:'equipmentFlag',
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'disposalFunctionDescription',
      width: 150,
    },
    {
      name: 'ncCode',
      width: 150,
    },
    {
      name: 'ncCodeName',
      width: 150,
    },
    {
      name: 'ncCodeStatusDesc',
      width: 150,
    },
    {
      name: 'workcellName',
      width: 150,
    },
    {
      name: 'equipmentName',
      width: 150,
    },
    {
      name: 'operationDesc',
      width: 150,
    },
    {
      name: 'organizationName',
      width: 150,
    },
    {
      name: 'ncRecordTime',
      width: 150,
    },
    {
      name: 'ncRecordUserIdRealName',
      width: 150,
    },
    {
      name: 'ncRecordClosedTime',
      width: 150,
    },
    {
      name: 'ncRecordClosedRealName',
      width: 150,
    },
  ];
  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  useEffect(() => {
    if (state?.identifications) {
      tableDs.queryDataSet?.loadData([{ identificationstr: state?.identifications }]);
      tableDs.query(tableDs.currentPage);
      history.replace({ ...history.location, state: undefined });
    }
  }, [history.location.state]);

  useEffect(() => {
    listener();
    return function clean() {
      listener();
    };
  });

  const listener = () => {
    // 列表交互监听
    if (tableDs) {
      if (state && state.data) {
        tableDs.queryDataSet.create({
          identificationstr: state.data.identification,
        });
        tableDs.query();
      }
    }
  };

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>

              <TextField
                name="ncRecordNumStr"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'ncRecordNumStr', '不良记录编码', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <TextField
                name="identificationstr"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'identificationstr', '条码号', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <Lov name="defaultNcCodeLov" />

              {expandForm && (
                <>
                  <Select name="ncIncidentStatus" />
                  <Select name="dispositionFunctionIds" />
                  <Select name="equipmentFlag" />
                  <DateTimePicker name="dateFrom" />
                  <DateTimePicker name="dateTo" />
                  <Lov name="workCellLov" />
                  <Lov name="equipmentLov" />
                  <Lov name="operationObj" />
                  <Lov name="prodLineObj" />
                  <Lov name="materialLov" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                funcType="link"
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  queryDataSet.create();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleSearch = async () => {
    const {
      identificationstr,
      ncRecordNumStr,
      ncCodeIds,
      dateFrom,
      dateTo,
      workcellIds,
      equipmentIds,
      operationIds,
      ncIncidentStatus,
    } = tableDs?.queryDataSet.current.toData();
    if (!ncRecordNumStr &&
      !identificationstr &&
      !ncCodeIds.length &&
      !dateFrom &&
      !dateTo &&
      !workcellIds.length &&
      !equipmentIds.length &&
      !operationIds.length &&
      !ncIncidentStatus.length
    ) {
      notification.error({
        message: intl.get(`${modelPrompt}.pleaseEnter`).d('请输入查询条件'),
      });
      return;
    }
    if (!identificationstr && !ncRecordNumStr) {
      if (ncCodeIds.length || workcellIds.length || equipmentIds.length || operationIds.length || ncIncidentStatus.length) {
        if (!dateFrom && !dateTo) {
          notification.error({ message: intl.get(`${modelPrompt}.queryDate`).d('请输入时间查询！') });
          return;
        }
      }
      if ((dateFrom && !dateTo) || (!dateFrom && dateTo)) {
        notification.error({
          message: intl.get(`${modelPrompt}.dateValidate`).d('开始时间和结束时间必须同时输入！'),
        });
        return;
      }
    }
    tableDs.query();
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleSearch();
    }
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = tableDs.queryDataSet.current.toData();
    const LOV_NAME = ['materialLov', 'prodLineObj', 'operationObj', 'equipmentLov', 'workCellLov', 'defaultNcCodeLov']
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i]) || LOV_NAME.includes(i)) {
        delete queryParams[i];
      }
    });
    const { defaultNcCodeLov, equipmentLov, operationObj, prodLineObj, workCellLov, ncRecordNumStr, identificationstr, ...other } = queryParams;
    return {
      ...other,
      identifications: identificationstr ? identificationstr.split(',') : null,
      ncRecordNums: ncRecordNumStr ? ncRecordNumStr.split(',') : null,
    };
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('不良记录查询')}>
        <ExcelExportPro
          method="POST"
          allBody
          exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-nc-record/export`}
          queryParams={getExportQueryParams}
          buttonText="导出"
        />
      </Header>
      <Content>
        <Table
          searchCode="NcRecordQuery"
          customizedCode="NcRecordQuery"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.NcRecordQuery', 'tarzan.common'],
})(NcRecordQuery);
