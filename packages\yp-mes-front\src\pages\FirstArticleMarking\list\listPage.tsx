import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, TextField, Form, Row, Col } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor, FuncType, } from 'choerodon-ui/pro/lib/button/enum';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import listPageFactory from '../stores/listPageDs';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import LovModal from '@/components/BatchInput/LovModal';
import axios from 'axios';
import { Icon, } from 'choerodon-ui';
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.firstArticleMarking';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {
  const [edit, setEdit] = useState(false);

  const inputLovDS = new DataSet(InputLovDS());

  const [inputLovFlag, setInputLovFlag] = useState('');

  const [inputLovTitle, setInputLovTitle] = useState('');

  const [inputLovVisible, setInputLovVisible] = useState(false);

  useDataSetEvent(listDs, 'query', () => {
    setEdit(false)
  });

  useEffect(() => {
    listDs.query();
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'identification',
      width: 250,
      // editor: record => record.getState('editing'),
      renderer: ({ record }) => {
        if (record!.getState('editing')) {
          return <TextField name="identification" record={record} onChange={(value) => onHandleChange(record, value)} />
        }
        return record!.get('identification')
      }
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'qty',
    },
    {
      name: 'qualityStatusMeaning',
    },
    {
      name: 'statusMeaning',
    },
    {
      name: 'workOrderNum',
    },
    {
      name: 'routerStepDesc',
    },
    {
      name: 'wipStatusMeaning',
    },
    {
      name: 'specifiedLevel',
    },
    {
      name: 'firstArticleLov',
      width: 180,
      editor: record => record.getState('editing'),
    },
    {
      width: 150,
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      lock: ColumnLock.right,
      renderer: ({ record }) =>
        record!.getState('editing') ? (
          <>
            <Button color={ButtonColor.primary} funcType={FuncType.flat} onClick={() => handleEdit(record, false)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button color={ButtonColor.primary} funcType={FuncType.flat} onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        ) : (
          <>
            <Button color={ButtonColor.primary}
              disabled={listDs.records.some(record => record.getState('editing'))}
              funcType={FuncType.flat} onClick={() => handleEdit(record, true)}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Button color={ButtonColor.primary} funcType={FuncType.flat}
              onClick={() => onHandleClean(record)}
            >
              {intl.get('tarzan.common.button.clean').d('清除')}
            </Button>
          </>
        ),
    },
  ];

  const onHandleChange = async (record, value) => {
    if (value) {
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-first-project-marking-pda/scan`
      const res: any = await axios.post(url, [value])
      if (res && res.success && !res.message) {
        const { rows } = res
        Object.keys(rows[0]).forEach(item => {
          record.set(item, rows[0][item])
        })
      } else {
        notification.error({
          message: res.message
        })
      }
    }

  }

  const onHandleClean = async (record) => {
    record.set('stepObjectId', undefined)
    record.set('firstArticleOperation', undefined)
  }

  const handleSave = async (record) => {
    const validate = await record.validate()
    if (validate) {
      const params = record.toData()
      if (params.firstArticleOperation && params.firstArticleOperation.stepObjectId) {
        delete params.firstArticleOperation
      }
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-first-project-marking-pda/save`;
      const res: any = await axios.post(url, [params])
      if (res && res.success) {
        await listDs.query();
      } else {
        notification.error({ message: res.message })
      }
    }
  }

  const handleCreate = () => {
    listDs.create({ status: 'add' }, 0)
    listDs.current?.setState('editing', true)
    setEdit(true)
  }

  const handleEdit = (record, flag) => {
    record.setState('editing', flag);
    if (!flag) {
      if (record.status === 'add') {
        listDs.remove(record)
      } else {
        record.reset()
      }
    }
  }

  const renderQueryBar = ({ buttons, queryDataSet, queryFields, dataSet }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={2} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identifications', '产品条码')}
                    />
                  </div>
                }
              />
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                  queryDataSet.loadData([]);
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button onClick={handleSearch} color={ButtonColor.primary}>
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };

  const handleSearch = async () => {
    listDs.query();
  };

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: listDs,
    onOpenInputModal,
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.firstArticleMarking`).d('首件标记')}>
        <Button
          icon="add"
          color={ButtonColor.primary}
          disabled={edit}
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新增')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="firstArticleMarking"
          rowHeight={35}
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={4} // 头部显示的查询字段的数量
          searchCode="firstArticleMarking" // 动态筛选条后端接口唯一编码
          customizedCode="firstArticleMarking" // 个性化编码
        />
      </Content>
      <LovModal {...lovModalProps} />
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.firstArticleMarking'],
})(ListPage);
