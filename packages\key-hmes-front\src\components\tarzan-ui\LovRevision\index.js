/**
 * @Description: c7n的带版本的lov
 * @Author: <<EMAIL>>
 * @Date: 2021-02-01 16:40:56
 * @LastEditTime: 2021-08-10 14:56:03
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo } from 'react';
import { Lov, Select, DataSet } from 'choerodon-ui/pro';
import uuid from 'uuid/v4';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import './index.modules.less';

const tenantId = getCurrentOrganizationId();

// TODO 在切换转盘对象时，这里是会报错的，但是生产环境页面不会崩溃能继续进行
const LovRevision = props => {
  const { dataSet, name, revisionShow, leftWidth = '60%', ...others } = props;
  const _revisionCode = dataSet.current.toData().revisionCode;
  const materialRevisionDS = useMemo(
    () =>
      new DataSet({
        autoQuery: true,
        dataKey: 'rows',
        paging: false,
        transport: {
          read: () => {
            const { siteId, objectId } = dataSet.current.toData();
            return {
              url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
              method: 'GET',
              params: { siteIds: siteId, materialId: objectId, tenantId },
              transformResponse: data => {
                const { rows } = JSON.parse(data);
                let firstlyQueryData = [];
                if (rows instanceof Array) {
                  firstlyQueryData = rows.map(item => {
                    return {
                      kid: uuid,
                      description: item,
                    };
                  });
                }
                return firstlyQueryData;
              },
            };
          },
        },
      }),
    [],
  );

  useEffect(() => {
    dataSet.addField('revisionFlag', { type: 'string', ignore: 'always' });
    dataSet.addField('revisionCode', {
      type: 'string',
      textField: 'description',
      valueField: 'description',
      disabled: true,
      options: materialRevisionDS,
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            !record.get(name) ||
            record.get('objectType') !== 'MATERIAL' ||
            record.get('revisionFlag') !== 'Y'
          );
        },
        required: ({ record }) => {
          return (
            record.get('revisionFlag') === 'Y' &&
            record.get('siteId') &&
            record.get('objectType') === 'MATERIAL'
          );
        },
      },
    });
  }, []);

  useEffect(() => {
    materialRevisionDS.query();
    if (_revisionCode) {
      dataSet.current.set('revisionFlag', 'Y');
    } else {
      dataSet.current.set('revisionFlag', null);
    }
  }, [_revisionCode]);

  const changeLov = record => {
    if (record) {
      dataSet.current.set('revisionCode', record.currentRevisionCode || null);
      dataSet.current.set('revisionFlag', record.revisionFlag ? record.revisionFlag : 'N');
    } else {
      dataSet.current.set('revisionCode', undefined);
      dataSet.current.set('revisionFlag', 'N');
    }
    materialRevisionDS.query();
    if (props.onChange) {
      props.onChange();
    }
  };

  return (
    <div name={name} className={revisionShow ? 'revision-left' : null}>
      <Lov
        name={name}
        style={{ width: revisionShow ? leftWidth : '100%' }}
        onChange={changeLov}
        {...others}
      />
      <Select
        style={{ width: `calc(100% - ${leftWidth})`, display: revisionShow ? '' : 'none' }}
        name="revisionCode"
        className={revisionShow ? 'revision-right' : null}
        required
      />
    </div>
  );
};

export default LovRevision;
