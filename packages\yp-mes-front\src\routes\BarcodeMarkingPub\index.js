import React, { useMemo, useEffect } from 'react';
import { DataSet, Table, } from 'choerodon-ui/pro';
import { Badge, } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { tableDS } from './stories';


const modelPrompt = 'tarzan.hmes.barcodeMarkingBinding';
const BarcodeMarkingBinding = observer(props => {
  const {
    match: { params },
  } = props;

  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  useEffect(() => {
    if (params?.id) {
      tableDs.setQueryParameter('batchId', params?.id)
      tableDs.query();
    }
  }, [params]);

  const columns = [
    // 站点
    {
      name: 'identificationLov',
      align: 'left',
      width: 200,
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'markingCodeLov',
      width: 200,
    },
    {
      name: 'statusMeaning',
    },
    {
      name: 'sourceWayMeaning',
    },
    {
      name: 'sourceIdentification',
      width: 150
    },
    {
      name: 'sourceMaterialName',
      width: 150,
    },
    {
      name: 'originalIdentification',
      width: 200,
    },
    {
      name: 'originalMaterialName',
      width: 150,
    },
    {
      name: 'typeMeaning',
    },
    {
      name: 'markingLotStatus',
      width: 150,
    },
    {
      name: 'markingContentMeaning',
    },
    {
      name: 'enableFlag',
      width: 100,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'expirationDate',
      width: 150
    },
    {
      name: 'applyReason',
      width: 150
    },
    {
      name: 'description',
      width: 150
    },
    {
      name: 'interceptionDisposalWay',
      width: 150
    },
    {
      name: 'disposalResult',
    },
    {
      name: 'realName',
    },
    {
      name: 'lastUpdateDate',
      width: 150
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('条码标记绑定')}>
      </Header>
      <Content>
        <Table
          header={`解绑原因：${tableDs.toData().length > 0 ? tableDs.toData()[0].unbindReason : ''}`}
          dataSet={tableDs}
          columns={columns}
          searchCode="BarcodeMarkingBinding"
          customizedCode="BarcodeMarkingBinding"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.barcodeMarkingBinding', 'tarzan.common'],
})(BarcodeMarkingBinding);
