import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { searchCopy } from '@utils/utils';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.ass.inOutStorage';

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'workOrderId',
    selection: DataSetSelection.multiple,
    paging: false,
    autoQuery: false,
    dataKey: 'rows',
    events: {
      update({ record, name, value }) {
        if (name === 'materialLotCodes') {
          const arr = [...new Set(value)]
          searchCopy(
            ['materialLotCodes',], name, record, arr,);
          if (name === 'docTypeCode' && !record.get('docTypeCode')) {
            record.set('scrapReason', undefined)
          }
        }
        // if (name === 'docTypeCode' && record.get('docType') === 'OFFCUT_SCRAP') {
        //   record.set('scrapReasonLov', undefined)
        // }
        if (name === 'docTypeCode') {
          record.set('scrapReasonLov', undefined)
        }
      }
    },
    fields: [
      {
        name: 'siteLov',
        required: true,
        ignore: FieldIgnore.always,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
        lovCode: 'MT.MODEL.SITE',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'siteId',
        bind: 'siteLov.siteId',
      },
      {
        name: 'siteCode',
        bind: 'siteLov.siteCode',
      },
      {
        name: 'docNum',
        disabled: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.docNums`).d('单据编码'),
      },
      {
        name: 'docTypeCode',
        required: true,
        lookupCode: 'HME_WEIGHING_SCRAP_DOC_TYPE',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
      },
      {
        name: 'docType',
        bind: 'docTypeCode.value'
      },
      {
        name: 'tag',
        bind: 'docTypeCode.tag'
      },
      {
        name: 'meaning',
        bind: 'docTypeCode.meaning'
      },
      {
        name: 'docStatus',
        required: true,
        // lookupCode: 'HME_WEIGHING_SCRAP_DOC_TYPE',
        type: FieldType.string,
        textField: 'description',
        valueField: 'statusCode',
        disabled: true,
        defaultValue: 'NEW',
        label: intl.get(`${modelPrompt}.docStatus`).d('单据状态'),
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'scrapReasonLov',
        textField: 'meaning',
        type: FieldType.object,
        lovCode: 'HME_WEIGHING_SCRAP_REASON',
        label: intl.get(`${modelPrompt}.scrapReason`).d('报废原因'),
        dynamicProps: {
          disabled: ({ record }) => ['1', '2'].includes(record.get('tag')),
          required: ({ record }) => !['1', '2'].includes(record.get('tag'))
        },
        lovPara: {
          tenantId
        }
      },
      {
        name: 'scrapReason',
        bind: 'scrapReasonLov.value'
      },
      {
        name: 'scrapReasonMeaning',
        bind: 'scrapReasonLov.meaning'
      },
      {
        name: 'workOrderLov',
        required: true,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
        lovCode: 'MT.WORK_ORDER_FILTER_NEW',
        lovPara: {
          tenantId
        }
      },
      {
        name: 'workOrderNum',
        type: FieldType.string,
        bind: 'workOrderLov.workOrderNum',
      },
      {
        name: 'workOrderId',
        type: FieldType.string,
        bind: 'workOrderLov.workOrderId',
      },
      {
        name: 'equipmentLov',
        required: true,
        ignore: FieldIgnore.always,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.equipmentObj`).d('设备编码'),
        lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
        // cascadeMap: {
        //   workOrderId: 'workOrderId',
        // },
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId,
            };
          },
        },
      },
      {
        name: 'equipmentCode',
        bind: 'equipmentLov.equipmentCode',
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'weightFactor',
        disabled: true,
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.weightFactor`).d('重量换算系数'),
      },
      {
        name: 'meterFactor',
        disabled: true,
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.meterFactor`).d('米数换算系数'),
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
      {
        name: 'scarpWeight',
        required: true,
        min: 0,
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.scarpWeight`).d('报废重量'),
      },
      {
        name: 'scarpMeter',
        disabled: true,
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.scarpMeter`).d('报废米数'),
      },
      {
        name: 'materialLotCodes',
        multiple: ',',
        label: intl.get(`${modelPrompt}.form.materialLotCode`).d('物料批编码'),
        type: FieldType.string,
      },
      {
        name: 'scrapTime',
        label: intl.get(`${modelPrompt}.form.scrapTime`).d('报废时间'),
        type: FieldType.dateTime,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/query/detail/ui`,
          // transformResponse: value => {
          //   let listData: any = {};
          //   try {
          //     listData = JSON.parse(value);
          //   } catch (err) {
          //     listData = {
          //       message: err,
          //     };
          //   }
          //   if (!listData.success) {
          //     return {
          //       ...listData,
          //       failed: true,
          //     };
          //   }
          //   return listData;
          // },
        };
      },
    },
  });

export default listPageFactory;
