/**
 * @Description: 站点维护-基础属性Tab
 * @Author: <<EMAIL>>
 * @Date: 2021-02-03 18:24:14
 * @LastEditTime: 2023-05-26 17:50:31
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, TextField, Lov } from 'choerodon-ui/pro';

const BasicInfoTab = props => {
  const { ds, canEdit, columns = 1, focus = true } = props;

  const handleChangeSupplier = () => {
    ds.current.init('supplierSite', undefined)
  }

  return (
    <Form
      disabled={!canEdit || focus}
      dataSet={ds}
      columns={columns}
      labelLayout="horizontal"
      labelWidth={112}
    >
      <Lov name="supplier" onChange={handleChangeSupplier} />
      <TextField name="supplierName" />
      <Lov newLine name="supplierSite" />
      <TextField name="supplierSiteName" />
    </Form>
  );
};

export default BasicInfoTab;
