/**
 * @feature 班次作业实绩
 * @date 2021-5-10
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';
import moment from 'moment';

const modelPrompt = 'tarzan.workshop.jobReport.model';
const tenantId = getCurrentOrganizationId();

/**
 * 列表和详情页
 */
const entranceDS = () => ({
  primaryKey: 'tagCode',
  queryUrl: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-shift-operation-actual/list/ui`,
  autoQuery: true,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  queryFields: [
    {
      name: 'prodLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLine`).d('生产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      ignore: 'always',
      noCache: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLine.prodLineId',
    },
    {
      name: 'operation',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      ignore: 'always',
      noCache: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'operationId',
      bind: 'operation.operationId',
    },
    {
      name: 'workcell',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcell`).d('工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: 'always',
      noCache: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workcellId',
      bind: 'workcell.workcellId',
    },
    {
      name: 'calendarShiftDateFrom',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.calendarShiftDateFrom`).d('时间从'),
      max: 'calendarShiftDateTo',
    },
    {
      name: 'calendarShiftDateTo',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.calendarShiftDateTo`).d('时间至'),
      min: 'calendarShiftDateFrom',
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次'),
      textField: 'description',
      valueField: 'description',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-shift-operation-actual/shift-code/box`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          if (rows && rows.length) {
            const returnData = [];
            // eslint-disable-next-line array-callback-return
            rows.map((item, index) => {
              returnData.push({
                typeCode: index,
                description: rows[index],
              });
            });
            return returnData;
          } 
          return [];
          
        },
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('calendarShiftDateFrom') && !record.get('calendarShiftDateTo');
        },
        lovPara: ({ record }) => {
          return {
            calendarShiftDateFrom: record.get('calendarShiftDateFrom')
              ? moment(record.get('calendarShiftDateFrom')).format('YYYY-MM-DD')
              : undefined,
            calendarShiftDateTo: record.get('calendarShiftDateTo')
              ? moment(record.get('calendarShiftDateTo')).format('YYYY-MM-DD')
              : undefined,
          };
        },
      },
    },
  ],
  fields: [
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元编码'),
    },
    {
      name: 'operationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺描述'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元'),
    },
    {
      name: 'shiftDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftDate`).d('日期'),
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'eoMaterialRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoMaterialRevisionCode`).d('版本'),
    },
    {
      name: 'assignQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assignQty`).d('调度数量'),
    },
    {
      name: 'queueQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.queueQty`).d('排队数量'),
    },
    {
      name: 'workingQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workingQty`).d('加工数量'),
    },
    {
      name: 'completePendingQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completePendingQty`).d('完成暂存数量'),
    },
    {
      name: 'completedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completedQty`).d('完成数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
    },
    {
      name: 'scrappedConfirmQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrappedConfirmQty`).d('报废确认数量'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-shift-operation-actual/list/ui`,
        data: {
          ...data,
          calendarShiftDateFrom: data.calendarShiftDateFrom
            ? moment(data.calendarShiftDateFrom).format('YYYY-MM-DD')
            : undefined,
          calendarShiftDateTo: data.calendarShiftDateTo
            ? moment(data.calendarShiftDateTo).format('YYYY-MM-DD')
            : undefined,
        },
        method: 'GET',
      };
    },
  },
});

const detailDS = () => ({
  primaryKey: 'tagCode',
  queryUrl: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-shift-operation-actual/list-detail/ui`,
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows',
  selection: false,
  paging: false,
  fields: [
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
    },
    {
      name: 'dispatchFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispatchFlag`).d('调度标识'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'movingFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.movingFlag`).d('移动标识'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'assembleFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleFlag`).d('装配标识'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'assignQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assignQty`).d('调度数量'),
    },
    {
      name: 'queueQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.queueQty`).d('排队数量'),
    },
    {
      name: 'workingQty', // 新增参数
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workingQty`).d('加工数量'),
    },
    {
      name: 'completePendingQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completePendingQty`).d('完成暂存数量'),
    },
    {
      name: 'completedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completedQty`).d('完成数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
    },
    {
      name: 'scrappedConfirmQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrappedConfirmQty`).d('报废确认数量'),
    },
    {
      name: 'routerEntryStepFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerEntryStepFlag`).d('入口步骤标识'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'routerDoneStepFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerDoneStepFlag`).d('完成步骤标识'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-shift-operation-actual/list-detail/ui`,
        method: 'GET',
      };
    },
  },
});

export { entranceDS, detailDS };
