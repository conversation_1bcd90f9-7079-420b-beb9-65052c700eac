/**
 * @Description: 盘点工作台-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2021-11-15 14:41:53
 * @LastEditTime: 2023-05-30 13:40:33
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  // selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'stocktakeId',
  queryFields: [
    {
      name: 'stocktakeNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeNum`).d('盘点单编码'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('盘点单说明'),
    },
    {
      name: 'stocktakeStatus',
      type: FieldType.string,
      multiple: true,
      label: intl.get(`${modelPrompt}.stocktakeStatus`).d('状态'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=STOCKTAKE_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'openFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.openFlag`).d('盘点类型'),
      lookupCode: 'MT.MES.STOCKTAKE_TYPE',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'areaLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategories: 'AREA',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'areaLocatorId',
      bind: 'areaLocatorLov.locatorId',
    },
    {
      name: 'locatorRangeLov',
      type: FieldType.object,
      multiple: true,
      label: intl.get(`${modelPrompt}.locatorRange`).d('库位范围'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategories: 'INVENTORY',
            areaLocatorId: record.get('areaLocatorId') || null,
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'locatorRangeIds',
      bind: 'locatorRangeLov.locatorId',
    },
    {
      name: 'materialRangeLov',
      type: FieldType.object,
      multiple: true,
      label: intl.get(`${modelPrompt}.materialRange`).d('物料范围'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'materialRangeIds',
      bind: 'materialRangeLov.materialId',
    },
    {
      name: 'userLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.creator`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      noCache: true,
      multiple: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createBy',
      bind: 'userLov.id',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      multiple: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialCodes',
      type: FieldType.object,
      bind: 'material.materialCode',
    },
    {
      name: 'materialLotCodes',
      multiple: true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCodes`).d('物料批编码'),
    },
  ],
  fields: [
    {
      name: 'stocktakeNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeNum`).d('盘点单编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeIdentification`).d('盘点单标识'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('盘点单说明'),
    },
    {
      name: 'stocktakeStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeStatus`).d('状态'),
    },
    {
      name: 'openFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.openFlag`).d('盘点类型'),
      lookupCode: 'MT.MES.STOCKTAKE_TYPE',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lookupCode: 'MT.SPC.CHART_TYPE',
    },
    {
      name: 'areaLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库'),
    },
    {
      name: 'createByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createByName`).d('创建人'),
    },
    {
      name: 'createDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.createDate`).d('创建时间'),
    },
    {
      name: 'lastUpdateByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateByName`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.LIST`,
        data,
        method: 'POST',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && !datas.success) {
            if (datas.message) {
              notification.error({ message: datas.message });
            }
            return;
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
  // record: {
  //   dynamicProps: {
  //     // 关闭类型的盘点单不可选择
  //     selectable: record => !['CLOSED'].includes(record.get('stocktakeStatus')),
  //   },
  // },
});

const tableOADS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'stocktakeId',
  fields: [
    {
      name: 'stocktakeNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeNum`).d('盘点单编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeIdentification`).d('盘点单标识'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('盘点单说明'),
    },
    {
      name: 'stocktakeStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeStatus`).d('状态'),
    },
    {
      name: 'openFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.openFlag`).d('盘点类型'),
      lookupCode: 'MT.MES.STOCKTAKE_TYPE',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lookupCode: 'MT.SPC.CHART_TYPE',
    },
    {
      name: 'areaLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库'),
    },
    {
      name: 'createByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createByName`).d('创建人'),
    },
    {
      name: 'createDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.createDate`).d('创建时间'),
    },
    {
      name: 'lastUpdateByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateByName`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.LIST`,
        data,
        method: 'POST',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && !datas.success) {
            if (datas.message) {
              notification.error({ message: datas.message });
            }
            return;
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
  // record: {
  //   dynamicProps: {
  //     // 关闭类型的盘点单不可选择
  //     selectable: record => !['CLOSED'].includes(record.get('stocktakeStatus')),
  //   },
  // },
});

const batchCreateDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      required: true,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'openFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.openFlag`).d('盘点类型'),
      lookupCode: 'MT.MES.STOCKTAKE_TYPE',
      required: true,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('盘点单说明'),
    },
    {
      name: 'tableWarehouseRangeLov',
      type: FieldType.object,
      multiple: true,
      label: intl.get(`${modelPrompt}.tableWarehouseRange`).d('仓库范围'),
      lovCode: 'MT.MODEL.LOCATOR',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategories: 'AREA',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'areaLocatorIds',
      bind: 'tableWarehouseRangeLov.locatorId',
    },
    {
      name: 'tableLocatorRangeLov',
      type: FieldType.object,
      multiple: true,
      label: intl.get(`${modelPrompt}.tableLocatorRange`).d('库位范围'),
      lovCode: 'MT.MODEL.LOCATOR',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategories: 'INVENTORY',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'locatorIds',
      bind: 'tableLocatorRangeLov.locatorId',
    },
  ],
});

const batchCreateTableDS: () => DataSetProps = () => ({
  // selection: true,
  autoQuery: false,
  autoCreate: false,
  fields: [
    {
      name: 'locatorCode',
      type: FieldType.string,
      // 仓库编码/库位编码
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      // 仓库描述/库位描述
    },
    {
      name: 'typeDesc',
      type: FieldType.string,
      // 仓库类型/库位类型
    },
  ],
});

const costCenterDS: () => DataSetProps = () => ({
  forceValidate: true,
  fields: [
    {
      name: 'costCenterLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.costCenterLov`).d('成本中心'),
      lovCode: 'MT.WMS.COST_CENTER',
      lovQueryUrl: `${BASIC.HMES_BASIC}/v1/0/mt-costcenter/lov/ui`,
      textField: 'costcenterCode',
      valueField: 'costcenterId',
      required: true,
      lovPara: {
        tenantId: getCurrentOrganizationId(),
        accountType: 'COST_CENTER',
        enableFlag: 'Y',
      },
    },
    {
      name: 'costCenterId',
      bind: 'costCenterLov.costcenterId',
    },
  ],
});

export { costCenterDS, tableDS, batchCreateDS, batchCreateTableDS, tableOADS };
