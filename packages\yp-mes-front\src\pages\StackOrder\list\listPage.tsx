import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor, } from 'choerodon-ui/pro/lib/button/enum';
import { openTab } from 'utils/menuTab';
import { useDataSetEvent } from 'utils/hooks';
import queryString from 'query-string';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import listPageFactory from '../stores/listPageDs';
import axios from 'axios';
import { Popconfirm } from 'choerodon-ui';

const tenantId = getCurrentOrganizationId();
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.stackOrder';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {
  const [edit, setEdit] = useState(false);

  useDataSetEvent(listDs, 'query', () => {
    setEdit(false)
  });

  useEffect(() => {
    listDs.query();
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'materialCode',
      editor: record => record.getState('editing'),
    },
    {
      name: 'materialName',
      editor: record => record.getState('editing'),
    },
    {
      name: 'scanSequence',
      editor: record => record.getState('editing'),
    },
    {
      name: 'stackSequence',
      editor: record => record.getState('editing'),
    },
    {
      width: 150,
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      lock: ColumnLock.right,
      renderer: ({ record }) =>
        record!.getState('editing') ? (
          <>
            <Button color={ButtonColor.primary} funcType="flat" onClick={() => handleEdit(record, false)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button color={ButtonColor.primary} funcType="flat" onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        ) : (
          <>
            <Button color={ButtonColor.primary}
              disabled={listDs.records.some(record => record.getState('editing'))}
              funcType="flat" onClick={() => handleEdit(record, true)}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => handleDelete(record)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <Button color={ButtonColor.primary}
                disabled={listDs.records.some(record => record.getState('editing'))} funcType="flat"
              >
                {intl.get('tarzan.common.button.delete').d('删除')}
              </Button>
            </Popconfirm>

          </>
        ),
    },
  ];

  // 导入
  const handleImport = () => {
    openTab({
      key: '/himp/commentImport/MT.MES.STACK_SEQUENCE',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };


  const handleDelete = async (record) => {
    if (record.get('stackSequenceId')) {
      handleSave(record, 'delete')
    } else {
      listDs.remove(record)
    }
  }

  const handleSave = async (record, type?) => {
    const validate = await record.validate()
    if (validate) {
      const params = record.toData()
      delete params.materialCode
      if (type === 'delete') {
        params.deleteFlag = 'Y'
      }
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-stack-sequences/save`;
      const res: any = await axios.post(url, [params])
      if (res && res.success) {
        await listDs.query();
      } else {
        notification.error({ message: res.message })
      }
    }
  }

  const handleCreate = () => {
    listDs.create({ status: 'add' }, 0)
    listDs.current?.setState('editing', true)
    setEdit(true)
  }
  const handleEdit = (record, flag) => {
    record.setState('editing', flag);
    if (!flag) {
      if (record.status === 'add') {
        listDs.remove(record)
      } else {
        record.reset()
      }
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.stackOrder`).d('堆叠顺序维护')}>
        <Button
          icon="add"
          color={ButtonColor.primary}
          disabled={edit}
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新增')}
        </Button>
        <Button icon="daorucanshu" onClick={handleImport}>{intl.get('tarzan.common.button.import').d('导入')}</Button>

      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="stackOrder"
          rowHeight={35}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={4} // 头部显示的查询字段的数量
          searchCode="stackOrder" // 动态筛选条后端接口唯一编码
          customizedCode="stackOrder" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.stackOrder'],
})(ListPage);
