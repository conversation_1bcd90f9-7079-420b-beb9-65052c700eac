#include: https://api.choerodon.com.cn/devops/v1/projects/1417/ci_contents/pipelines/48d3fef6-cf4d-4745-ad9d-681478f7f71c/content.yaml
# 注意 gitlab-runner 需要设置 /cache 目录为缓存目录， 做好数据持久化。
image: registry.cn-shanghai.aliyuncs.com/c7n/cibase:0.11.4
variables:
  GIT_CLEAN_FLAGS: none
stages:
  - node_build
  - docker
node_build:
  image: registry.cn-hangzhou.aliyuncs.com/hzero-cli/cifront:0.0.1
  stage: node_build
  script:
    - free -h
    - node -v
    - npm -v
    - rm -rf yarn.lock
    - rm -rf ./dist_parent
    - rm -rf ./dist
    - rm -rf ./node_modules


    - yarn config set network-timeout 300000
    - yarn bootstrap
    # - yarn build:ms hcm-mes-front,key-hmes-front,key-hmes-oa-front,yp-mes-front
    - WEBPACK_FS_CACHE=none UMI_ENV=prod JSXINJS=true SKIP_NO_CHANGE_MODULE=false NODE_PROFILE=production node ./node_modules/umi/bin/umi.js hzero-build --only-build-micro hcm-mes-front,key-hmes-front,key-hmes-oa-front,yp-mes-front
    # - yarn build:c7n
    # - yarn hzero-build:dep-all
    # - yarn build:app:prod
    # - yarn build:ms-all
    # 启用c7n多版本开启下面三行
    # - yarn run build:additional-dep choerodon-ui
    # - yarn run build:additional-dep hzero-front-ui-c7n-ui
    # - yarn build:additional:ms hcmp-front,hcmp-purchase
    #    - yarn build:additional:ms hcmp-front,hcmp-purchase  #构建微前端业务模块
    #    - yarn build:ms hcm-api-front,hcm-common-front,hcm-report-front,hcm-model-front,hcm-mes-front,hspc-front,hcm-method-front,hcm-sampling-front,hcm-hlct-front,hcm-message-front
    # - yarn run build:dep-all:prod # todo: 如果是 基座入口应用 就加上这句，非基座应用可以不加 节约编译时间
    # - yarn run build:app:prod
    - mkdir -p $TEMP_DIR
    - tar -zcf $TEMP_DIR/dist.tar.gz ./dist
  only:
    # - master
    - develop

docker_build:
  image: registry.cn-shanghai.aliyuncs.com/c7n/cibase:0.11.4
  stage: docker
  script:
    - tar -zxf $TEMP_DIR/dist.tar.gz
    - saveImageMetadata
    - chart_build
    - kaniko --skip-tls-verify -c $PWD/. -f $PWD/Dockerfile -d ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${C7N_VERSION}
    - echo "${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${C7N_VERSION}"
  only:
    # - master
    - develop

.auto_devops: &auto_devops |
  http_status_code=`curl -o .auto_devops.sh -s -m 10 --connect-timeout 10 -w %{http_code} "${CHOERODON_URL}/devops/ci?token=${Token}&type=microservice"`
  if [ "$http_status_code" != "200" ]; then
    cat ./.auto_devops.sh
    exit 1
  fi
  source ./.auto_devops.sh

  export TEMP_DIR=/cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME}-front
  echo "gitlab-ci -- 缓存目录: $TEMP_DIR"

before_script:
  - *auto_devops
