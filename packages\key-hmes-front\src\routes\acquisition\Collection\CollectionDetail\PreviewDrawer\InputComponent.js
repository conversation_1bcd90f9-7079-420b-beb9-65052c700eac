/**
 * @Description: 几种不同的输入格式
 * @Author: <<EMAIL>>
 * @Date: 2021-04-28 18:59:45
 * @LastEditTime: 2022-06-28 10:22:55
 * @LastEditors: <<EMAIL>>
 */
import React, { forwardRef } from 'react';
import { Input, Select, Upload, Icon } from 'choerodon-ui';
import { NumberField } from 'choerodon-ui/pro';
import styles from './index.module.less';

const { Option } = Select;

const ValueInput = forwardRef((props, ref) => {
  const { value, requiredFlag, onChange, onPressEnter } = props;
  const className = [styles['hcm-dataItem-group-add-input-number']];
  if (requiredFlag === 'required') {
    className.push(styles['hcm-dataItem-group-add-input-number-required']);
  }
  const handleOnBlur = () => {
    if (value || value === 0) {
      onPressEnter();
    }
  };
  const handleKeyDown = e => {
    if (e.keyCode === 13 && onPressEnter) {
      onPressEnter();
    }
  };
  return (
    <NumberField
      value={value}
      ref={ref}
      className={className.join(' ')}
      data-type={requiredFlag}
      step={1}
      onChange={_value => {
        onChange(_value);
      }}
      onInput={e => {
        const { value: _value } = e.target;
        onChange(_value);
      }}
      onBlur={handleOnBlur}
      onKeyDown={handleKeyDown}
    />
  );
});
const DecisionValueInput = props => {
  const { value, requiredFlag, onPressEnter, trueValue, falseValue } = props;
  // 没有onPressEnter事件的输入组件，用onPressEnter代替onChange方法，此时需要传改变的值来替代fieldValue使用
  const className = [styles['hcm-dataItem-group-add-select']];
  if (requiredFlag === 'required') {
    className.push(styles['hcm-dataItem-group-add-select-required']);
  }
  return (
    <Select value={value} className={className.join(' ')} onChange={onPressEnter}>
      <Option value={trueValue}>{trueValue}</Option>
      <Option value={falseValue}>{falseValue}</Option>
    </Select>
  );
};
const AttachmentsInput = props => {
  const { requiredFlag, onPressEnter } = props;
  const className = [styles['hcm-dataItem-group-add-upload']];
  if (requiredFlag === 'required') {
    className.push(styles['hcm-dataItem-group-add-upload-required']);
  }
  const handleUpload = file => {
    const uploadFild = {
      lastModified: file.lastModified,
      lastModifiedDate: file.lastModifiedDate,
      name: file.filename || file.name,
      size: file.size,
      type: file.type,
      uid: file.uid,
    };
    if (['image/jpeg', 'image/png'].includes(file.type)) {
      uploadFild.url = URL.createObjectURL(file);
    }
    onPressEnter(uploadFild);
    return false;
  };
  return (
    <Upload
      showUploadList={false}
      // beforeUpload需要传一个方法返回false，而不能直接false
      beforeUpload={handleUpload}
    >
      <div className={className.join(' ')}>
        <Icon type="add" />
      </div>
    </Upload>
  );
};
const TextInput = forwardRef((props, ref) => {
  const { value, requiredFlag, onChange, onPressEnter } = props;
  const _handleOnBlur = () => {
    if (value) {
      onPressEnter();
    }
  };
  return (
    <Input
      dbc2sbc={false}
      value={value}
      ref={ref}
      className={styles['hcm-dataItem-group-add-input']}
      data-input-type={requiredFlag}
      onChange={e => {
        onChange(e.target.value.trim());
      }}
      onBlur={_handleOnBlur}
      onPressEnter={() => {
        onPressEnter();
      }}
    />
  );
});
const ValueListInput = props => {
  const { value, requiredFlag, onPressEnter, valueList } = props;
  // 没有onPressEnter事件的输入组件，用onPressEnter代替onChange方法，此时需要传改变的值来替代fieldValue使用
  const className = [styles['hcm-dataItem-group-add-select']];
  if (requiredFlag === 'required') {
    className.push(styles['hcm-dataItem-group-add-select-required']);
  }
  const optionsList = valueList.split(',');
  return (
    <Select value={value} className={className.join(' ')} onChange={onPressEnter}>
      {optionsList.map(item => (
        <Option key={item} value={item}>
          {item}
        </Option>
      ))}
    </Select>
  );
};

const InputComponent = forwardRef((props, ref) => {
  const { rule = {}, ...otherProps } = props;
  const { valueType, trueValue, falseValue, valueList } = rule;
  const descisionValueInputProps = {
    trueValue,
    falseValue,
  };
  const valueListInputProps = {
    valueList,
  };
  const getInputComponent = () => {
    switch (valueType) {
      case 'VALUE':
        return <ValueInput {...otherProps} ref={ref} />;
      case 'DECISION_VALUE':
        return <DecisionValueInput {...otherProps} {...descisionValueInputProps} />;
      case 'ENCLOSURE':
        return <AttachmentsInput {...otherProps} />;
      case 'TEXT':
        return <TextInput {...otherProps} ref={ref} />;
      case 'VALUE_LIST':
        return <ValueListInput {...otherProps} {...valueListInputProps} />;
      default:
        return null;
    }
  };
  return getInputComponent();
});

export default InputComponent;
