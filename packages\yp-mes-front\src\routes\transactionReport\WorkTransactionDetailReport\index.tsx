/**
 * @Description: 事务明细报表
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-08 14:09:22
 * @LastEditors: <<EMAIL>>
 */

import React, { useCallback, useMemo } from 'react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { isNil } from 'lodash';
import { useDataSetEvent } from 'utils/hooks';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import { tableDS } from './stories';

// const prefix = '/mes-42638'

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.event.workTransactionDetailReport';

const WorkTransactionDetailReport = (props) => {

  const { tableDs, history } = props;

  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialId':
        record.set('revisionCode', null);
        break;
      case 'sourceOrderType':
        record.set('sourceOrderLov', null);
        break;
      case 'sourceOrderLov':
        record.set('sourceOrderLineLov', null);
        break;
      default:
        break;
    }
  });

  // useDataSetEvent(tableDs, 'query', () => {
  //   // 由于查询头是「动态筛选条」在给ds.queryDataSet的字段重新赋值的时候，值变更会自动查询，所以会查询两遍
  //   const currentDate = new Date();
  //   currentDate.setMinutes(currentDate.getMinutes() + 1);
  //   tableDs!.queryDataSet!.current!.set('lastUpdateDateTo', currentDate);
  // });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'transTypeCode',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'woReportTransIfaceId',
        lock: ColumnLock.left,
        width: 120,
      },
      {
        name: 'status',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'message',
        width: 150,
      },
      {
        name: 'eventId',
        width: 120,
      },
      // {
      //   name: 'transCode',
      //   lock: ColumnLock.left,
      //   width: 150,
      // },
      // {
      //   name: 'sumFlagDesc',
      //   lock: ColumnLock.left,
      //   width: 150,
      // },
      {
        name: 'eventTypeCode',
        width: 120,
      },
      {
        name: 'eventTypeCodeDesc',
        width: 120,
      },
      // {
      //   name: 'businessTypeCodeDesc',
      //   width: 120,
      // },
      {
        name: 'transTime',
        width: 150,
      },
      {
        name: 'accountTime',
        width: 150,
      },
      {
        name: 'plantCode',
        width: 150,
      },
      // {
      //   name: 'siteCode',
      //   width: 150,
      // },
      {
        name: 'workOrderNum',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'revisionCode',
        width: 150,
      },
      {
        name: 'qty',
        width: 150,
      },
      {
        name: 'scrapQty',
        width: 150,
      },
      {
        name: 'reworkQty',
        width: 150,
      },
      {
        name: 'uomCode',
        width: 150,
      },
      {
        name: 'cancelRueck',
        width: 150,
      },
      {
        name: 'cancelRmzhl',
        width: 150,
      },
      {
        name: 'cancelFlag',
        width: 150,
      },
      {
        name: 'workCenter',
        width: 150,
      },
      {
        name: 'operationSeqNum',
        width: 150,
      },
      {
        name: 'operationStep',
        width: 150,
      },
      {
        name: 'sourceOperationSeqNum',
        width: 150,
      },
      {
        name: 'sourceOperationStep',
        width: 150,
      },
      {
        name: 'manualDuration',
        width: 150,
      },
      {
        name: 'manualDurationUomCode',
        width: 150,
      },
      {
        name: 'machineDuration',
        width: 150,
      },
      {
        name: 'machineDurationUomCode',
        width: 150,
      },
      {
        name: 'otherDuration',
        width: 150,
      },
      {
        name: 'otherDurationUomCode',
        width: 150,
      },
      {
        name: 'eventTime',
        width: 150,
      },
      {
        name: 'transAccount',
        width: 150,
      },
      {
        name: 'transReasonCode',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'createdByName',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 150,
      },
      {
        name: 'attribute1',
        width: 150,
      },
      {
        name: 'attribute2',
        width: 150,
      },
      {
        name: 'attribute3',
        width: 150,
      },
      {
        name: 'attribute4',
        width: 150,
      },
      {
        name: 'attribute5',
        width: 150,
      },
    ];
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  const handleJumpToMobileEventDetailReport = useCallback(() => {
    history.push(`/hmes/work-transaction-report/mobile-event-detail-report`);
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('报工事务明细报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-wo-report-trans-dtls/export/ui`}
          // requestUrl={`${prefix}/v1/${tenantId}/mt-wo-report-trans-dtls/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <Button onClick={handleJumpToMobileEventDetailReport}>{intl.get(`${modelPrompt}.jumpToMobileEventDetailReport`).d('移动事件明细查看')}</Button>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="WorkTransactionDetailReport"
          customizedCode="WorkTransactionDetailReport"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.mes.event.workTransactionDetailReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(WorkTransactionDetailReport),
);
