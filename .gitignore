# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
/.pnp
.pnp.js
/packages/*/lib
/public/src
/public/scs_models
/public/js

# testing
/coverage

# development
/dll
/lib

# production
dist
html
build
dist-ext

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
*.bak
*.rej

npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*


_deployment.yaml

.env.*.local
.env.*.local.yml
.umi
.umi-production
packages/yp-mes-front/.idea/
.idea/vcs.xml
.idea/workspace.xml
.idea/yp-mes-front.iml
.idea/inspectionProfiles/Project_Default.xml
.idea/misc.xml
.idea/libraries/tsconfig_roots.xml
.idea/jsLibraryMappings.xml
.idea/modules.xml
micro-front-run.sh

.smock
