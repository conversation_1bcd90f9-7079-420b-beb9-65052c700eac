import React, { useMemo } from 'react';
import { Button, DataSet, Table, Modal } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { tableDS } from './stores/EquipmentGroupMaintenanceDs';
import { openTab } from 'utils/menuTab';
import { useDataSet, } from 'utils/hooks';
import historyFactory from './stores/historyDs';
import queryString from "querystring";
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.hmes.EquipmentGroupMaintenance';

const EquipmentGroupMaintenance = props => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds

  const historyDs = useDataSet(historyFactory, 'EquipmentGroupHistory');


  const create = () => {
    props.history.push({
      pathname: `/hmes/equipment-group-maintenance/create`,
    });
  };

  const edit = record => {
    props.history.push({
      pathname: `/hmes/equipment-group-maintenance/${record.data.assembleGroupId}`,
    });
  };

  const columns = [
    // 站点
    {
      name: 'siteCode',
      align: 'left',
    },
    // 装配点编码
    {
      name: 'assembleGroupCode',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => edit(record)}>{value}</a>
          </span>
        );
      },
    },
    // 装配点描述
    {
      name: 'description',
      align: 'left',
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '有效' : '无效'}>
          { }
        </Badge>
      ),
    },
    {
      name: 'groupTypeDesc',
      align: 'left',
    },
  ];

  const columnHistory = [
    {
      name: 'siteCode',
    },
    {
      name: 'assembleGroupCode',
    },
    {
      name: 'description',
    },
    {
      name: 'enableFlag',
    },
    {
      name: 'creationDate',
      width: 150,
    },
    {
      name: 'realName'
    }
  ]

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MT.MES.ASSEMBLE_GROUP`,
      title: '装配组维护导入',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }

  const handleHistory = () => {
    const assembleGroupIds = tableDs.selected.map(item => item.get('assembleGroupId'))
    if (assembleGroupIds.length) {
      historyDs.setQueryParameter('assembleGroupIds', assembleGroupIds)
    }
    historyDs.query()
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history`).d('历史查询'),
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table
        dataSet={historyDs}
        columns={columnHistory}
      />,
    });
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('装配组维护')}>
        <Button onClick={create} style={{ marginRight: 15 }} icon="add" color="primary">
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <Button icon="daorucanshu" onClick={handleImport} style={{ marginRight: 15 }} color="primary">
          {intl.get('tarzan.common.button.import').d('导入')}
        </Button>
        <Button onClick={handleHistory} >
          {intl.get('tarzan.common.button.history').d('历史查询')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
          dragRow
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={4}
          searchCode="EquipmentGroupMaintenance"
          customizedCode="EquipmentGroupMaintenance"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.EquipmentGroupMaintenance', 'tarzan.common'],
})(EquipmentGroupMaintenance);
