/**
 * 工艺子步骤-抽屉表格中的ds
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.process.routes.model.routes';

const childStepDrawerDS = () => ({
  selection: false,
  paging: false,
  fields: [
    {
      name: 'uuid',
      type: FieldType.string,
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.componentSequence`).d('顺序'),
    },
    {
      name: 'substep',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.subStep`).d('编码'),
      required: true,
      lovCode: 'MT.METHOD.SUBSTEP',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'substepId',
      type: FieldType.string,
      bind: 'substep.substepId',
    },
    {
      name: 'substepName',
      type: FieldType.string,
      bind: 'substep.substepName',
    },
    {
      name: 'substepDesc',
      bind: 'substep.description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.substepDesc`).d('描述'),
      disabled: true,
    },
  ],
});


export { childStepDrawerDS };
