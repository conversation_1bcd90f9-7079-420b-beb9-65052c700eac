import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { Button as PermissionButton } from 'components/Permission';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor, } from 'choerodon-ui/pro/lib/button/enum';
import { openTab } from 'utils/menuTab';
import { useDataSetEvent } from 'utils/hooks';
import queryString from 'query-string';
import { getCurrentOrganizationId } from 'utils/utils';
import ExcelExport from 'components/ExcelExportPro';
import notification from 'utils/notification';
import listPageFactory from '../stores/listPageDs';
import request from 'utils/request';
import { isNil } from 'lodash';

const tenantId = getCurrentOrganizationId();
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.equipmentMaintenance';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {
  const [edit, setEdit] = useState(false);

  useDataSetEvent(listDs, 'query', () => {
    setEdit(false)
  });

  useEffect(() => {
    listDs.query();
  }, []);

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleAdd}
          disabled={!edit}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      name: 'editColumn',
      align: ColumnAlign.center,
      lock: ColumnLock.left,
      width: 80,
      hideable: false,
      renderer: ({ record }) => (
        <PermissionButton
          onClick={() => deleteRecord(record)}
          disabled={!(edit && !record?.get('keyId'))}
          type="c7n-pro"
          icon="remove"
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
    },
    {
      name: 'userName',
      editor: record => record?.get('editing') || edit
    },
    {
      name: 'equipmentCode',
      editor: record => record?.get('editing') || edit
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'levelInfo',
      editor: record => record?.get('editing') || edit
    },
    {
      name: 'cardNum',
    },
    {
      name: 'enableFlag',
      editor: record => record?.get('editing') || edit
    },
  ];

  const handleAdd = () => {
    listDs.create({ editing: true }, 0);
  };
  // 删除表格某一行的回调
  const deleteRecord = async record => {
    listDs.remove(record);
  };

  // 导入
  const handleImport = () => {
    openTab({
      key: '/himp/commentImport/HME.EMPLOYEE_EQUIP_LEVEL',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const onHandleSave = async () => {
    const updateList = listDs.updated.map(item => {
      const params = item.toData()
      delete params.userName
      delete params.equipmentCode
      return params
    })
    const createList = listDs.created.map(item => {
      const params = item.toData()
      delete params.userName
      delete params.equipmentCode
      return params
    })
    const validate = await listDs.validate()
    if (validate) {
      const list = [...createList, ...updateList]
      if (list.length > 0) {
        const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-employee-equipment-levels/save/info`;
        const res: any = await request(url, {
          method: 'post',
          body: list,
        })
        if (res && res.success) {
          notification.success({});
          setEdit(false);
          listDs.query();
          if(res.message){
            notification.warning({
              message: res.message,
            });
          }
        } else {
          notification.error({
            message: res.message,
          });
        }
      } else {
        notification.error({
          message: intl.get(`${modelPrompt}.title.unMessage`).d('未有信息变更')
        })
      }
    }
  };

  const onHandleCancel = () => {
    setEdit(false);
    listDs.query();
  };

  const onHandleEdit = () => {
    setEdit(true);
  };

  const getExportQueryParams = () => {
    if (!listDs.queryDataSet || !listDs.queryDataSet.current) {
      return {};
    }
    const queryParams = listDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return {
      ...queryParams,
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.equipmentMaintenance`).d('员工登陆设备维护')}>
        {edit ? (
          <div>
            <Button onClick={() => onHandleCancel()}>
              {intl.get(`${modelPrompt}.title.cancel`).d('取消')}
            </Button>
            <Button color={ButtonColor.primary} onClick={onHandleSave}>
              {intl.get(`${modelPrompt}.title.save`).d('保存')}
            </Button>
          </div>
        ) : (
          <Button color={ButtonColor.primary} onClick={() => onHandleEdit()}>
            {intl.get(`${modelPrompt}.title.edit`).d('编辑')}
          </Button>
        )}
        <Button  onClick={handleImport}>{intl.get(`${modelPrompt}.import`).d('导入')}</Button>
        <ExcelExport
          method="POST"
          exportAsync
          allBody
          requestUrl={`${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-employee-equipment-levels/export/info`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`tarzan.acquisition.dataItem..export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="equipmentMaintenance"
          rowHeight={35}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={4} // 头部显示的查询字段的数量
          searchCode="equipmentMaintenance" // 动态筛选条后端接口唯一编码
          customizedCode="equipmentMaintenance" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.equipmentMaintenance'],
})(ListPage);
