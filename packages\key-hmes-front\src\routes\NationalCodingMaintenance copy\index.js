import React, { useMemo, useEffect } from 'react';
import { Button, DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import { routerRedux } from 'dva/router';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { tableDS } from './stores/NationalCodingMaintenanceDS';

const modelPrompt = 'tarzan.message.message.NationalCodingMaintenance';

const NationalCodingMaintenance = props => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds

  useEffect(() => {}, []);

  const create = () => {
    const { dispatch } = props;
    dispatch(
      routerRedux.push({
        pathname: `/hmes/national-coding-maintenance/create`,
        payload: { flag: 'NEW' },
      }),
    );
  };

  const edit = record => {
    const { dispatch } = props;
    dispatch(
      routerRedux.push({
        pathname: `/hmes/national-coding-maintenance/create`,
        payload: { ...record.data, flag: 'EDIT' },
      }),
    );
  };


  const columns = [
    // 站点
    {
      name: 'siteCode',
      align: 'left',
    },
    // 规则编码
    {
      name: 'ruleCode',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => edit(record)}>{value}</a>
          </span>
        );
      },
    },
    // 规则描述
    {
      name: 'description',
      align: 'left',
    },
    // 位数
    {
      name: 'numberBit',
      align: 'left',
    },
    // 生产线
    {
      name: 'prodLineCode',
      align: 'left',
    },
    // 生产线描述
    {
      name: 'prodLineName',
      align: 'left',
    },
    // 物料
    {
      name: 'materialCode',
      align: 'left',
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
    },
    // 物料版本
    {
      name: 'revisionCode',
      align: 'left',
    },
    // 生产类型
    {
      name: 'workOrderTypeDesc',
      align: 'left',
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '有效' : '无效'}>
          {}
        </Badge>
      ),
    },
  ];

  return (
    <React.Fragment>
      <Header title={intl.get(`${modelPrompt}.title`).d('国标码编码规则维护')}>
        <Button onClick={create} style={{ marginRight: 15 }} icon="add" color="primary">
          新建
        </Button>
      </Header>
      <Content>
        {/* <Spin loading={false}> */}
        <Table
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
          dragRow
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={3}
          searchCode="NationalCodingMaintenance"
          customizedCode="NationalCodingMaintenance"
        />
        {/* </Spin> */}
      </Content>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.NationalCodingMaintenance', 'tarzan.common'],
})(NationalCodingMaintenance);
