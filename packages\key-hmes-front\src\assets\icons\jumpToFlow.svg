<svg id="组件_1_2" data-name="组件 1 – 2" xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0.8px;
      }

      .cls-1, .cls-2 {
        stroke: #2abdce;
      }

      .cls-2, .cls-4 {
        fill: none;
      }

      .cls-3 {
        stroke: none;
      }
    </style>
  </defs>
  <g id="矩形_1" data-name="矩形 1" class="cls-1" transform="translate(10)">
    <rect class="cls-3" width="6" height="6" rx="1"/>
    <rect class="cls-4" x="0.4" y="0.4" width="5.2" height="5.2" rx="0.6"/>
  </g>
  <g id="矩形_2" data-name="矩形 2" class="cls-1" transform="translate(10 7)">
    <rect class="cls-3" width="6" height="6" rx="1"/>
    <rect class="cls-4" x="0.4" y="0.4" width="5.2" height="5.2" rx="0.6"/>
  </g>
  <g id="矩形_3" data-name="矩形 3" class="cls-1" transform="translate(0 3)">
    <rect class="cls-3" width="6" height="6" rx="1"/>
    <rect class="cls-4" x="0.4" y="0.4" width="5.2" height="5.2" rx="0.6"/>
  </g>
  <line id="直线_1" data-name="直线 1" class="cls-2" x1="2" transform="translate(8 3)"/>
  <line id="直线_4" data-name="直线 4" class="cls-2" x1="2" transform="translate(6 6)"/>
  <line id="直线_3" data-name="直线 3" class="cls-2" y1="8" transform="translate(8 2.5)"/>
  <line id="直线_2" data-name="直线 2" class="cls-2" x1="2" transform="translate(8 10)"/>
</svg>
