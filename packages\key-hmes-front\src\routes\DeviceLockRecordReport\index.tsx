import React, { useMemo } from 'react';
import { Table, DataSet, Modal } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { tableDS } from './stores';

const modelPrompt = 'device.lock.record.report.model';

const DeviceLockRecordReport = props => {
  const { tableDs } = props;

  const tableColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'lockName',
        align: ColumnAlign.left,
        width: 190,
      },
      {
        name: 'equipmentCode',
        align: ColumnAlign.left,
        width: 130,
      },
      {
        name: 'lockTypeDesc',
        align: ColumnAlign.center,
      },
      {
        name: 'processBarcode',
        align: ColumnAlign.center,
        width: 210,
      },
      {
        name: 'materialLotCode',
        align: ColumnAlign.center,
        width: 210,
      },
      {
        name: 'lockKey',
        align: ColumnAlign.center,
        renderer: ({ record }) => <a onClick={() => handleLockObjectModalShow(record)}>明细</a>,
        width: 80,
      },
      {
        name: 'attribute1',
        align: ColumnAlign.center,
        renderer: ({ value }) => <>{value === 'S' ? '成功' : '失败'}</>,
        width: 80,
      },
      {
        name: 'attribute2',
        align: ColumnAlign.center,
        width: 120,
      },
      {
        name: 'startTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'waitTime',
        align: ColumnAlign.center,
      },
      {
        name: 'executeTime',
        align: ColumnAlign.center,
      },
      {
        name: 'lockTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'unlockTime',
        align: ColumnAlign.center,
        width: 150,
      },
    ];
  }, []);

  const handleLockObjectModalShow = record => {
    const { lockKeyEntity } = record.toData();

    Modal.open({
      maskClosable: false,
      destroyOnClose: true,
      title: intl.get(`${modelPrompt}.title.lockObject`).d('锁对象'),
      style: {
        width: '600px',
      },
      children: (
        <>
          <div style={{ marginTop: '14px', color: '#000' }}>
            原报文：
            {lockKeyEntity.map(item => {
              return <div key={item}>{item}</div>;
            })}
          </div>
        </>
      ),
      closable: true,
      footer: false,
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('设备锁定记录报表')} />
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={8}
          dataSet={tableDs}
          columns={tableColumns}
          searchCode="deviceLockRecordReport"
          customizedCode="deviceLockRecordReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet(tableDS());
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(DeviceLockRecordReport),
);
