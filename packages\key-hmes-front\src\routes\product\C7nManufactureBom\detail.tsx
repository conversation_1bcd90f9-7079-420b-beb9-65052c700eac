/**
 * @Description: 制造装配清单 - 详情页面（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/8/1 14:14
 * @LastEditTime: 2023-05-18 15:14:22
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import formatterCollections from 'utils/intl/formatterCollections';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { AssemblyDetail, AssemblyDetailProps } from '@/components/C7nAssemblyCommon/AssemblyDetail';

const C7nManufactureBomDetail = props => {
  // 装配组件详情页入参
  const assemblyDetailProps: AssemblyDetailProps = {
    history: props.history,
    match: props.match,
    featureTitle: intl.get('tarzan.product.bom.title.manufactureBom').d('制造装配清单'), // 列表页标题title
    attributeServerCode: BASIC.HMES_BASIC, // 扩展属性也需要访问不同的服务
    uiServerCode: BASIC.HMES_BASIC, // 功能接口要访问的不同的服务
    typeGroup: 'MES_BOM_TYPE', // 查询条件与详情页中类型的值集code
    customizeForm: props.customizeForm,
    customizeTable: props.customizeTable,
    custConfig: props.custConfig,
    custCode: 'MES_BOM_DETAIL',
    location: props.location,
  };

  return <AssemblyDetail {...assemblyDetailProps} />;
};

export default flow(
  formatterCollections({ code: ['tarzan.product.bom', 'tarzan.common'] }),
  withCustomize({
    unitCode:
      [
        `${BASIC.CUSZ_CODE_BEFORE}.MES_BOM_DETAIL.BASIC`,
        `${BASIC.CUSZ_CODE_BEFORE}.MES_BOM_DETAIL.COMP`,
        `${BASIC.CUSZ_CODE_BEFORE}.MES_BOM_DETAIL.COMP_DTL`,
        `${BASIC.CUSZ_CODE_BEFORE}.MES_BOM_DETAIL.BUTTON`,
        `${BASIC.CUSZ_CODE_BEFORE}.MES_BOM_DETAIL.BUTTON_COMP`,
      ],
  }),
)(C7nManufactureBomDetail);
