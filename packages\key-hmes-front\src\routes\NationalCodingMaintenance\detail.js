import React, { useState, useMemo, useEffect } from 'react';
import {
  DataSet,
  Table,
  Button,
  Form,
  TextField,
  Lov,
  Switch,
  NumberField,
  Select,
  Spin,
  Modal,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Collapse, Popconfirm, Badge } from 'choerodon-ui';
import { C7nFormItemSort, drawerPropsC7n } from '@components/tarzan-ui';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import myInstance from '@/utils/myAxios';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { useDataSet, } from 'utils/hooks';
import request from 'utils/request';
import { detailTableDS, formDS } from './stores/NationalCodingMaintenanceDS';
import historyFactory from './stores/detailHistoryDs';
import { Host } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.NationalCodingMaintenance';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();

const Create = props => {
  const [loading, setLoading] = useState(false);
  const [title, setTitle] = useState('');
  const [canEdit, setcanEdit] = useState(false);
  const [lovDisabled, setLovDisabled] = useState(false);
  const [formRecord, setFormRecord] = useState([]);

  const formDs = useMemo(() => new DataSet(formDS()), []);
  const copyDs = useMemo(() => new DataSet(formDS()), []);
  const detailTableDs = useMemo(() => new DataSet(detailTableDS(formDs)), []); // 复制ds
  const historyDs = useDataSet(historyFactory, 'nationalCoding1');

  const {
    match: {
      params: { id },
    },
  } = props;
  useEffect(() => {
    if (id === 'create') {
      queryBasicData();
      setTitle(intl.get(`${modelPrompt}.detail.title`).d('国标码编码规则新建'));
      setcanEdit(true);
    } else {
      setTitle(intl.get(`${modelPrompt}.detail.title`).d('国标码编码规则详情'));
      handleSearchTable(id);
    }
  }, [id]);

  // 查询站点
  const queryBasicData = async () => {
    const url = `${Host}/v1/${tenantId}/hme-assemble-points/get/user/def/site`;
    const res = await myInstance.get(url);
    if (res) {
      formDs.create({
        siteCode: res.data.siteCode,
        siteId: res.data.siteId,
        enableFlag: 'Y',
      });
    }
  };

  // 查询头，行
  const handleSearchTable = async gbCodeRuleId => {
    const res = await request(`${Host}/v1/${tenantId}/hme-gb-coding-rules/detail/ui?gbCodeRuleId=${gbCodeRuleId}`, {
      method: 'GET',
    });
    const result = getResponse(res);
    if (result) {
      formDs.create(res);
      setFormRecord(res);
      const lovValue = formDs.current.toData();
      if (lovValue.prodLineId && lovValue.materialId) {
        setLovDisabled(true);
      } else {
        setLovDisabled(false);
      }
    }
    detailTableDs.setQueryParameter('gbCodeRuleId', gbCodeRuleId);
    detailTableDs.query()
  };

  // 确定新增
  const createLine = () => {
    if (detailTableDs.toData().length === 0) {
      detailTableDs.create({
        serialNumber: 1,
        enableFlag: 'Y',
        gbCodeRuleId: formRecord.gbCodeRuleId,
      });
    } else {
      const newLineNumber =
        (detailTableDs.toData().sort((a, b) => b.serialNumber - a.serialNumber)[0].serialNumber ||
          0) + 1;
      detailTableDs.create({
        serialNumber: Number(newLineNumber),
        enableFlag: 'Y',
        gbCodeRuleId: formRecord.gbCodeRuleId,
      });
    }
  };

  // 保存
  const handelSave = async () => {
    const validate = await formDs.validate();
    if (!validate) {
      return;
    }
    if (!await detailTableDs.validate(false, true)) return
    let params = {};
    const formObj = formDs.current.toData();
    const tableList = detailTableDs.toJSONData();
    if (tableList.length > 0) {
      const filterNumList = [];
      const numberList = [];
      // eslint-disable-next-line array-callback-return
      tableList.map(ele => {
        if (ele.codingValue) {
          const codeLength = ele.codingValue.length;
          const finalNum = Number(ele.toBit) - Number(ele.fromBit) + 1;
          if (Number(codeLength) !== Number(finalNum)) {
            filterNumList.push(ele);
            numberList.push(ele.serialNumber);
          }
        }
      });
      if (filterNumList.length > 0) {
        const numberString = numberList.join(',');
        return notification.warning({
          message: intl
            .get(`${modelPrompt}.error.numberString`)
            .d(`当前数据第${numberString}行存在编码值长度不等于截止位-起始位+1，请检查`),
          placement: 'bottomRight',
        });
      }
    }
    const lineList = tableList.map(item => {
      return {
        ...item,
        deleteFlag: item._status === 'delete' ? 'Y' : null,
      };
    });
    params = {
      ...formObj,
      lineList,
    };
    const res = await request(`${Host}/v1/${tenantId}/hme-gb-coding-rules/save/ui`, {
      method: 'post',
      body: { ...params },
    })
    const result = getResponse(res);
    if (result) {
      notification.success();
      setcanEdit(false);
      if (id === 'create') {
        props.history.push(`/hmes/national-coding-maintenance/${res}`);
      } else {
        handleSearchTable(res);
      }
    }
  };

  // 编辑按钮
  const handelEdit = () => {
    setcanEdit(true);
  };

  // 取消按钮
  const handelCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/national-coding-maintenance/list');
    } else {
      setcanEdit(false);
      handleSearchTable(id);
    }
  };

  // 版本下拉框变化事件
  const changeVersion = (value, record) => {
    if (value) {
      if (value === 'SERIAL_NUMBER' || value === 'DATE') {
        record.set('codingValue', '');
        record.getField('codingValue').set('required', false);
        record.getField('codingValue').set('disabled', true);
      } else {
        record.getField('codingValue').set('required', true);
        record.getField('codingValue').set('disabled', false);
      }
    } else {
      record.set('codingValue', '');
      record.getField('codingValue').set('required', false);
      record.getField('codingValue').set('disabled', true);
    }
    if (value === 'FIXED_VALUE') {
      record.set('shieldField', '');
      record.getField('shieldField').set('disabled', true);
    } else {
      record.getField('shieldField').set('disabled', false);
    }
  };

  // 示例生成
  const handleIncrementalSynchronization = async () => {
    const res = await request(
      `${Host}/v1/${tenantId}/hme-gb-coding-rules/example/generation?gbCodeRuleId=${formRecord.gbCodeRuleId}`,
      {
        method: 'GET',
      },
    )
    const result = getResponse(res);
    if (result) {
      notification.success();
      formDs.current.set('barCode', res);
    }
  };

  // lov变化事件
  const changeObject = lovRecords => {
    if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
      formDs.current.getField('revisionCode').set('required', true);
      formDs.current.getField('revisionCode').set('disabled', false);
      formDs.current.set('revisionFlag', 'Y');
      formDs.current.set('revisionCode', lovRecords.revisionCode);
    } else {
      formDs.current.getField('revisionCode').set('required', false);
      formDs.current.getField('revisionCode').set('disabled', true);
      formDs.current.set('revisionCode', '');
      formDs.current.set('revisionFlag', 'N');
    }
    const lovValue = formDs.current.toData();
    if (lovValue.prodLineId || lovValue.materialId) {
      setLovDisabled(true);
    } else {
      setLovDisabled(false);
    }
  };

  const lovChange = () => {
    const lovValue = formDs.current.toData();
    if (lovValue.prodLineId || lovValue.materialId) {
      setLovDisabled(true);
    } else {
      setLovDisabled(false);
    }
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!canEdit}
          onClick={() => createLine()}
          funcType="flat"
          // shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl
            .get(`${modelPrompt}.error.delete`)
            .d(`是否确认删除?`)}
          onConfirm={() => {
            detailTableDs.remove(record);
          }}
        >
          <Button
            funcType="flat"
            icon="remove"
            shape="circle"
            size="small"
            disabled={!canEdit}
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    // 序号
    {
      name: 'serialNumber',
      align: 'left',
      editor: canEdit && <NumberField />,
    },
    // 编码含义
    {
      name: 'description',
      align: 'left',
      editor: canEdit && <TextField />,
    },
    // 起始位
    {
      name: 'fromBit',
      align: 'left',
      editor: canEdit && <NumberField />,
    },
    // 截止位
    {
      name: 'toBit',
      align: 'left',
      editor: canEdit && <NumberField />,
    },
    // 编码类型
    {
      name: 'codingType',
      align: 'left',
      editor: record => {
        return (
          canEdit && (
            <Select onChange={value => changeVersion(value, record)} />
          )
        );
      },
    },
    // 编码值
    {
      name: 'codingValue',
      align: 'left',
      editor: canEdit && <TextField />,
    },
    // 屏蔽字段
    {
      name: 'shieldField',
      align: 'left',
      editor: canEdit && <TextField />,
    },
    {
      name: 'enableFlag',
      width: 100,
      editor: () => canEdit && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  const handleCopy = () => {
    copyDs.reset()
    copyDs.getField('ruleCode').set('label', '目标规则编码');
    copyDs.create({
      oldSiteId: formDs.current?.get('siteId'),
      oldRuleCode: formDs.current?.get('ruleCode'),
    })
    Modal.open({
      ...drawerPropsC7n({ copyDs }),
      drawer: true,
      title: intl.get('tarzan.common.button.copy').d('复制'),
      style: {
        width: 480,
      },
      children: (
        <Form dataSet={copyDs} columns={1} labelWidth={100}>
          <Lov name="siteObj" />
          <TextField name="ruleCode" />
          <TextField name="description" />
          <NumberField name="numberBit" />
          <Lov name="prodLineObj" />
          <TextField name="prodLineName" />
          <C7nFormItemSort name="materialObj" itemWidth={['70%', '30%']}>
            <Lov
              name="materialObj"
              onChange={lovRecords => changeObject(lovRecords)}
              colSpan={1}
            />
            <Select name="revisionCode" colSpan={1} />
          </C7nFormItemSort>
          <TextField name="materialName" />
          <Select name="workOrderType" />
          <Switch name="enableFlag" />
        </Form>
      ),
      afterClose: () => {
        copyDs.loadData([]);
      },
      onOk: async () => {
        if (await copyDs.validate()) {
          const res = await request(`${Host}/v1/${getCurrentOrganizationId()}/hme-gb-coding-rules/copy/ui`, {
            body: copyDs.toJSONData()[0],
            method: 'POST',
          });
          const result = getResponse(res);
          if (result) {
            props.history.push(`/hmes/national-coding-maintenance/${result}`);
            return true;
          }
        }
        return false;
      },
    });
  }

  const columnsHistory =[
    // 编码含义
    {
      name: 'description',
      width:150
    },
    // 起始位
    {
      name: 'fromBit',
    },
    // 截止位
    {
      name: 'toBit',
    },
    // 编码类型
    {
      name: 'codingType',
    },
    // 编码值
    {
      name: 'codingValue',
    },
    // 屏蔽字段
    {
      name: 'shieldField',
    },
    {
      name: 'enableFlag',
      width: 100,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'operationTime',
      width:150
    },
    {
      name: 'username',
    },
  ]

  const handleHistory = () => {
    historyDs.setQueryParameter('gbCodingRuleId', id)
    historyDs.query()
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history`).d('历史查询'),
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table
        dataSet={historyDs}
        columns={columnsHistory}
      />,
    });
  }

  return (
    <div className="hmes-style" style={{ height: '98%', overflow: 'auto' }}>
      <Header title={title} backPath="/hmes/national-coding-maintenance/list">
        {!canEdit && (
          <>
            <Button onClick={handelEdit} style={{ marginRight: 15 }} icon="edit" color="primary">
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Button onClick={handleCopy} disabled={!formDs.current?.get('siteId')} style={{ marginRight: 15 }} icon="edit" color="primary">
              {intl.get('tarzan.common.button.copy').d('复制')}
            </Button></>
        )}
        {canEdit && (
          <>
            <Button onClick={handelCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button onClick={handelSave} style={{ marginRight: 15 }} icon="save" color="primary">
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        )}
        <Button onClick={handleHistory}  >
          {intl.get(`${modelPrompt}.history`).d('历史查询')}
        </Button>
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Collapse bordered={false} defaultActiveKey={['1', '2']}>
            <Panel header="基本属性" key="1">
              <Form dataSet={formDs} columns={3}>
                <Lov name="siteObj" disabled={lovDisabled || !canEdit} />
                <TextField name="ruleCode" disabled={id !== 'create'} />
                <TextField name="description" disabled={!canEdit} />
                <NumberField name="numberBit" disabled={!canEdit} />
                <Lov name="prodLineObj" onChange={lovChange} disabled={!canEdit} />
                <TextField name="prodLineName" disabled />
                <C7nFormItemSort name="materialObj" itemWidth={['70%', '30%']} disabled={!canEdit}>
                  <Lov
                    name="materialObj"
                    onChange={lovRecords => changeObject(lovRecords)}
                    colSpan={2} disabled={!canEdit}
                  />
                  <Select name="revisionCode" disabled={!canEdit} colSpan={1} />
                </C7nFormItemSort>
                <TextField name="materialName" disabled />
                <Select name="workOrderType" disabled={!canEdit} />
                <Switch name="enableFlag" disabled={!canEdit} />
                {!canEdit && (
                  <>
                    <Button
                      onClick={handleIncrementalSynchronization}
                      style={{ float: 'right', marginTop: '4px', width: '50%' }}
                      color="primary"
                      colSpan={1}
                    >
                      {intl.get(`${modelPrompt}.exampleGeneration`).d('示例生成')}
                    </Button>
                    <TextField name="barCode" disabled />
                  </>
                )}
              </Form>
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.detail.title`).d('编码信息')} key="2">
              <Table
                dataSet={detailTableDs}
                columns={columns}
                style={{ height: 400 }}
              // dragRow
              />
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.NationalCodingMaintenance', 'tarzan.common'],
})(Create);
