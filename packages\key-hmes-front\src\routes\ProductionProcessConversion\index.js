import React, { useState, useMemo, useEffect } from 'react';
import { DataSet, Table, Spin, Button, Switch, NumberField, Lov, Select } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import request from 'utils/request';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import intl from 'utils/intl';
import moment from 'moment';
import { useRequest } from '@components/tarzan-hooks';
import { Host,BASIC } from '@/utils/config';
import { isNil } from 'lodash';
import { GetOperationByEquipment } from './services';
import { tableDS } from './stores/tableDS';
import queryString from "querystring";
import { openTab } from 'utils/menuTab';
import ExcelExport from 'components/ExcelExportPro';

const tenantId = getCurrentOrganizationId();
const userInfo = getCurrentUser();

const modelPrompt = 'tarzan.hmes.ProductionProcessConversion';

const ProductionProcessConversion = () => {
  const [saveLock, setSaveLock] = useState(true);
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const { run: fetchOperation } = useRequest(GetOperationByEquipment(), { manual: true });

  useEffect(() => {
    tableDs.query();
  }, []);

  const handleChangeEquipment = value => {
    setSaveLock(false);
    if (value?.equipmentCode) {
      fetchOperation({
        params: {
          equipmentCode: value?.equipmentCode,
        },
        onSuccess: res => {
          tableDs.current?.set('operationObj', {
            ...res,
            description: res.operationDescription,
          });
        },
      })
    } else {
      tableDs.current?.set('operationObj', undefined);
    }
  };

  const handleChangeStretchNumber = (value, record) => {
    setSaveLock(false);
    if (!value) {
      return '';
    }
    // 截取小数点后三位，而非四舍五入
    const newValue = typeof value === 'string' ? value : String(value);
    const pointIndex = newValue.indexOf('.');
    if (pointIndex === -1) {
      return;
    }
    record.set('stretchNumber', newValue.substring(0, pointIndex + 4)); // 截取小数点后三位
  }

  const columns = [
    {
      name: 'siteObj',
      align: 'center',
      editor: editing && (
        <Lov dataSet={tableDs} name="siteObj" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'materialObj',
      align: 'center',
      editor: editing && (
        <Lov dataSet={tableDs} name="materialObj" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'materialName',
      align: 'center',
    },
    {
      name: 'revisionCode',
      align: 'center',
      editor: record =>
        (record.status === 'add' || (editing && record.get('revisionFlag') === 'Y')) && (
          <Select dataSet={tableDs} name="revisionCode" onChange={() => setSaveLock(false)} />
        ),
    },
    {
      name: 'equipmentObj',
      align: 'center',
      editor: editing && (
        <Lov dataSet={tableDs} name="equipmentObj" onChange={(val) => handleChangeEquipment(val)} />
      ),
    },
    {
      name: 'equipmentName',
      align: 'center',
    },
    {
      name: 'operationObj',
      align: 'center',
    },
    {
      name: 'description',
      align: 'center',
    },
    {
      name: 'materialConsumption',
      editor: editing,
      align: 'center',
      width: 130,
      editor: record => editing && <NumberField name="materialConsumption" onChange={() => setSaveLock(false)} />,
    },
    {
      name: 'stretchNumber',
      align: 'center',
      editor: record => editing && <NumberField name="stretchNumber" onChange={(value) => handleChangeStretchNumber(value, record)} />,
    },
    {
      name: 'weightConversionFactorForSingle',
      align: 'center',
      width: 140,
      editor: editing && (
        <NumberField name="weightConversionFactor" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'meterConversionFactorForSingle',
      align: 'center',
      width: 140,
      editor: editing && (
        <NumberField name="weightConversionFactor" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'weightConversionFactor',
      align: 'center',
      width: 140,
      editor: editing && (
        <NumberField name="weightConversionFactor" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'meterConversionFactor',
      align: 'center',
      width: 140,
      editor: editing && (
        <NumberField name="meterConversionFactor" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'weightConversionFactorForBright',
      align: 'center',
      width: 140,
      editor: editing && (
        <NumberField name="meterConversionFactor" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'meterConversionFactorForBright',
      align: 'center',
      width: 140,
      editor: editing && (
        <NumberField name="meterConversionFactor" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'weightConversionFactorForComplete',
      align: 'center',
      width: 140,
      editor: editing && (
        <NumberField name="weightConversionFactorForComplete" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'meterConversionFactorForComplete',
      align: 'center',
      width: 140,
      editor: editing && (
        <NumberField name="meterConversionFactorForComplete" onChange={() => setSaveLock(false)} />
      ),
    },
    {
      name: 'enableFlag',
      align: 'center',
      editor: editing && <Switch name="enableFlag" onChange={() => setSaveLock(false)} />,
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '有效' : '无效'}>
          { }
        </Badge>
      ),
    },
    {
      name: 'createdName',
      align: 'center',
    },
    {
      name: 'creationDate',
      align: 'center',
    },
    {
      name: 'lastUpdatedName',
      align: 'center',
    },
    {
      name: 'lastUpdateDate',
      align: 'center',
    },
  ];

  const handleAdd = () => {
    setSaveLock(false);
    tableDs.create(
      {
        createdName: userInfo?.realName,
        creationDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        lastUpdatedName: userInfo?.realName,
        lastUpdateDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      },
      0,
    );
  };

  const handleSave = async () => {
    const validateBase = await tableDs.validate();
    if (validateBase) {
      const data = tableDs.toJSONData();
      const validateData = data
        .map(item => {
          const { siteCode, materialCode, revisionCode, operationName } = item;
          return `${siteCode}站点+${materialCode}物料编码+${revisionCode}物料版本+${operationName}工艺编码`;
        })
        .reduce((value, item) => {
          if (value[item]) {
            value[item]++;
          } else {
            value[item] = 1;
          }
          return value;
        }, {});
      if (Object.values(validateData).filter(item => item > 1).length > 0) {
        for (const key in validateData) {
          if (validateData[key] > 1) {
            notification.warning({
              message: intl
                .get(`${modelPrompt}.error.operation`)
                .d(`${key}数据重复，请检查！`)
            });
          }
        }
      } else {
        setLoading(true);
        const temp = data.map(item => {
          if (item._status !== 'create') {
            return {
              ...item,
              lastUpdatedName: userInfo?.realName,
              lastUpdateDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            };
          }
          return item;
        });
        request(`${Host}/v1/${tenantId}/hme-operation-conversions/save/ui`, {
          method: 'POST',
          body: temp,
        }).then(res => {
          if (res && !res.failed) {
            notification.success();
            setEditing(false);
            tableDs.query();
            setSaveLock(true);
          } else {
            notification.error({ message: res.message });
          }
          setLoading(false);
        });
      }
    }
  };

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MT.MES.OPERATION_CONVERSION`,
      title: '导入',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = tableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    delete queryParams.__dirty;
    return {
      ...queryParams,
      operationConversionIds: tableDs.selected.map((item) => {
        return item.get('operationConversionId');
      }),
    };
  };

  return (
    <React.Fragment>
      <Header title={intl.get(`${modelPrompt}.title`).d('生产工艺折算系数维护')}>
        {!editing ? (
          <Button onClick={() => setEditing(true)} style={{ marginRight: 15 }} color="primary">
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        ) : (
          <>
            <Button
              onClick={() => {
                setEditing(false);
                setSaveLock(true)
                tableDs.query(tableDs.currentPage);
              }}
              style={{ marginRight: 15 }}
              color="primary"
            >
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button onClick={handleAdd} style={{ marginRight: 15 }} color="primary">
              {intl.get('tarzan.common.button.create').d('新建')}
            </Button>
            <Button
              disabled={saveLock}
              onClick={handleSave}
              style={{ marginRight: 15 }}
              color="primary"
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        )}
        <ExcelExport
          method="POST"
          allBody
          exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-operation-conversion/export/ui`}
          queryParams={getExportQueryParams}
          buttonText="导出"
        />
        <Button icon="daorucanshu" onClick={handleImport} >
          {intl.get('tarzan.common.button.import').d('导入')}
        </Button>
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Table
            dataSet={tableDs}
            columns={columns}
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            queryFieldsLimit={4}
            searchCode="ProductionProcessConversion"
            customizedCode="ProductionProcessConversion"
          />
        </Spin>
      </Content>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.ProductionProcessConversion', 'tarzan.common'],
})(ProductionProcessConversion);
