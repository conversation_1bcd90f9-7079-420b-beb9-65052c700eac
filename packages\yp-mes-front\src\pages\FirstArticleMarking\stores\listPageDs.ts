import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.hmes.firstArticleMarking';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'eoId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'identifications',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
        },
      ]
    }),
    fields: [
      {
        name: 'identification',
        required: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'qty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.qty`).d('主单位数量'),
      },
      {
        name: 'qualityStatusMeaning',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
      },
      {
        name: 'statusMeaning',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.status`).d('条码状态'),
      },
      {
        name: 'workOrderNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单信息'),
      },
      {
        name: 'routerStepDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.routerStepDesc`).d('当前工艺'),
      },
      {
        name: 'wipStatusMeaning',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.wipStatus`).d('工序状态'),
      },
      {
        name: 'specifiedLevel',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.specifiedLevel`).d('电池等级'),
      },
      {
        name: 'firstArticleLov',
        type: FieldType.object,
        multiple:true,
        label: intl.get(`${modelPrompt}.firstArticleOperation`).d('首检标识'),
        lovCode: 'HME.FIRST_ARTICLE_TYPE',
        dynamicProps: {
          disabled: ({ record }) => !(record.get('routerId')&&record.get('status')==='CLOSED' ),
          lovPara: ({ record }) => {
            return {
              tenantId,
              routerIds: [record.get('routerId')]
            }
          }
        },
      },
      {
        name: 'firstArticleOperations',
        type: FieldType.string,
        bind:'firstArticleLov.description'
      },
      {
        name: 'stepObjectIds',
        type: FieldType.string,
        bind:'firstArticleLov.stepObjectId'
        // dynamicProps: {
        //   bind: ({ record }) => {
        //     if (record.get('firstArticleOperation') && record.get('firstArticleOperation').stepObjectId) {
        //       return 'firstArticleOperation.stepObjectId'
        //     }
        //   }
        // },
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'POST',
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-first-project-marking-pda/pc/scan`,
        };
      },
    },
  });

export default listPageFactory;
