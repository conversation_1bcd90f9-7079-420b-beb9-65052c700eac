/**
 * 工艺路线-列表页入口页面
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */
import React, { FC, useEffect, useState, useMemo } from 'react';
import { Table, DataSet, Modal } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import { useDataSet } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { BASIC } from '@/utils/config';
import { listDS, historyTableDS } from '../stories/ListDS';
import '../index.module.less';

export interface RoutesListProps {
  history: any;
  featureTitle: string;
  featureTitleCode: string;
  serveCode: string; // 功能服务
  typeGroup: string;
  searchCode: string; // 表格的key
  detailUrl: string; // 详情页Url
  match: any;
  customizeTable: any;
  custCode: string;
}

const RoutesList: FC<RoutesListProps> = ({
  history,
  match: { path },
  featureTitle,
  featureTitleCode,
  typeGroup,
  searchCode,
  detailUrl,
  serveCode,
  customizeTable,
  custCode,
}) => {
  const basePath = path.substring(0, path.indexOf('/list'));

  /**
   * 入口页面表格DS-用useDataSet处理可以启用DataSet的缓存，从详情界面返回主页面时保留主页面的查询条件
   */
  const dataSet = useDataSet(() => new DataSet({ ...listDS(typeGroup, serveCode) }), basePath);
  // const historyTableDs = useDataSet(() => new DataSet({ ...historyTableDS(typeGroup, serveCode) }), basePath);
  const historyTableDs = useMemo(() => new DataSet({ ...historyTableDS(typeGroup) }), []);
  const [historyBtnDisabled, setHistoryBtnDisabled] = useState(true);

  useEffect(() => {
    dataSet.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.QUERY,${BASIC.CUSZ_CODE_BEFORE}.${custCode}.LIST`,
    );
    dataSet.query();

    dataSet.addEventListener('select', handleDataSetSelect);
    dataSet.addEventListener('unSelect', handleDataSetSelect);
    return () => {
      dataSet.removeEventListener('select', handleDataSetSelect);
      dataSet.removeEventListener('unSelect', handleDataSetSelect);
    };
  }, []);

  /**
   *@description 创建新的工艺路线
   * record ？进入到详情页 : 进入到新建页
   */
  const createList = () => {
    history.push(`${detailUrl}/create`);
  };

  const handleDataSetSelect = () => {
    const selectedLength = dataSet.selected;
    if (selectedLength.length > 0) {
      setHistoryBtnDisabled(false);
    }
  };

  // 历史查询
  const handleHistoryQuery = () => {
    const selectedRecords = dataSet.selected;
    const routerIds = selectedRecords.map(record => {
      return record.data.routerId;
    });
    
    historyTableDs.setQueryParameter('routerIds', routerIds);
    historyTableDs.query();

    Modal.open({
      className: 'hmes-style-modal',
      key: Modal.key(),
      title: intl.get('tarzan.common.button.history.query').d('历史查询'),
      drawer: true,
      style: {
        width: 1080,
      },
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      closable: true,
      maskClosable: false,
      destroyOnClose: true,
      children: (
        <Table
          searchCode={searchCode}
          customizedCode={searchCode}
          queryBar={TableQueryBarType.filterBar}
          dataSet={historyTableDs}
          columns={historyColumns}
        />
      ),
    });
  };

  const historyColumns = [
    {
      name: 'routerName',
    },
    { name: 'routerType', align: 'center' },
    { name: 'description' },
    { name: 'routerStatus', align: 'center', width: 90 },
    { name: 'revision', width: 60 },
    {
      name: 'currentFlag',
      width: 90,
      renderer: ({ record }) => (
        <Badge
          status={record.get('currentFlag') !== 'N' ? 'success' : 'error'}
          text={
            record.get('currentFlag') !== 'N'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
      align: 'center',
    },
    { name: 'dateFrom', align: 'center', width: 150 },
    { name: 'dateTo', align: 'center', width: 150 },
    { name: 'creationDate', align: 'center', width: 150 },
    { name: 'createdByName', align: 'center', width: 150 },
  ];

  const columns = [
    {
      name: 'routerName',
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              history.push(`${detailUrl}/${record.get('routerId')}`);
            }}
          >
            {value}
          </a>
        );
      },
    },
    { name: 'routerType', align: 'center' },
    { name: 'description' },
    { name: 'routerStatus', align: 'center', width: 90 },
    { name: 'revision', width: 60 },
    {
      name: 'currentFlag',
      width: 90,
      renderer: ({ record }) => (
        <Badge
          status={record.get('currentFlag') !== 'N' ? 'success' : 'error'}
          text={
            record.get('currentFlag') !== 'N'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
      align: 'center',
    },
    { name: 'dateFrom', align: 'center', width: 150 },
    { name: 'dateTo', align: 'center', width: 150 },
  ];

  return (
    <div className="hmes-style">
      <Header title={featureTitleCode ? intl.get(featureTitleCode).d(featureTitle) : featureTitle}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={createList}
          permissionList={[
            {
              code: `tarzan${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          disabled={historyBtnDisabled}
          onClick={handleHistoryQuery}
          permissionList={[
            {
              code: `tarzan${path}.button.history`,
              type: 'button',
              meaning: '列表页-历史查询按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.history.query').d('历史查询')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.LIST`,
          },
          <Table
            searchCode={searchCode}
            customizedCode={searchCode}
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={dataSet}
            columns={columns as ColumnProps[]}
            queryFieldsLimit={3}
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.process.routes', 'tarzan.common'],
  // @ts-ignore
})(RoutesList);
