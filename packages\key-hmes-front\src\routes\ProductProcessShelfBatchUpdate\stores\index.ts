import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetSelection, FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import moment from 'moment'

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.ass.deviceLock';

const tableDS = () =>
  new DataSet({
    primaryKey: 'equipmentLockId',
    selection: DataSetSelection.multiple,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'materialObj',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.material`).d('物料'),
          lovCode: 'MT.MATERIAL',
          ignore: FieldIgnore.always,
          textField: 'materialCode',
          required: true,
          dynamicProps: {
            lovPara: ({ record }) => {
              return {
                tenantId,
                siteId: record?.get('siteId'),
              };
            },
          },
        },
        {
          name: 'materialId',
          bind: 'materialObj.materialId',
        },
        {
          name: 'process',
          label: intl.get(`${modelPrompt}.form.process`).d('工艺'),
          type: FieldType.object,
          lovCode: 'MT.OPERATION_CONVERSION',
          ignore: FieldIgnore.always,
          required: true,
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'operationId',
          bind: 'process.operationId',
        },
        {
          name: 'outBoundDateFrom',
          type: FieldType.dateTime,
          required: true,
          label: intl.get(`${modelPrompt}.outBoundDateFrom`).d('出站时间从'),
          dynamicProps: {
            min: ({ record }) => {
              if (record?.get('outBoundDateTo')) {
                return moment(record?.get('outBoundDateTo')).subtract(15, 'days');
              }
              return undefined;
            },
          },
        },
        {
          name: 'outBoundDateTo',
          type: FieldType.dateTime,
          required: true,
          label: intl.get(`${modelPrompt}.outBoundDateTo`).d('出站时间至'),
          dynamicProps: {
            max: ({ record }) => {
              if (record?.get('outBoundDateFrom')) {
                return moment(record?.get('outBoundDateFrom')).add(15, 'days');
              }
              return undefined;
            },
          },
        },
      ],
    }),
    fields: [
      {
        name: 'processBarcode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.processBarcode`).d('产品条码'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编号'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料名称'),
      },

      {
        name: 'quantityStatus',
        label: intl.get(`${modelPrompt}.form.quantityStatus`).d('质量状态'),
        type: FieldType.string,
      },
      {
        name: 'quantityStatusDesc',
        label: intl.get(`${modelPrompt}.form.quantityStatusDesc`).d('质量状态描述'),
        type: FieldType.string,
      },
      {
        name: 'fromOperationDesc',
        label: intl.get(`${modelPrompt}.form.fromOperationDesc`).d('起始工艺'),
      },
      {
        name: 'toOperationDesc',
        label: intl.get(`${modelPrompt}.form.toOperationDesc`).d('截至工艺'),
      },
      {
        name: 'dateFrom',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.dateFrom`).d('出站时间'),
      },
      {
        name: 'dateTo',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.dateTo`).d('到期时间'),
      },
      {
        name: 'expirationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.expirationDate`).d('保质期'),
      },
      {
        name: 'lastUpdatedByRealName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.lastUpdatedByRealName`).d('更新人'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-material-op-expirations-batch/list/ui`,
          transformResponse: value => {
            let listData: any = {};
            try {
              listData = JSON.parse(value);
            } catch (err) {
              listData = {
                message: err,
              };
            }
            if (!listData.success) {
              return {
                ...listData,
                failed: true,
              };
            }
            return listData;
          },
        };
      },
    },
  });

const createDS = () =>
  new DataSet({
    autoQuery: false,
    fields: [
      {
        name: 'firstProcessLov',
        label: intl.get(`${modelPrompt}.form.firstProcessLov`).d('起始工艺'),
        type: FieldType.object,
        lovCode: 'MT.OPERATION',
        required: true,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'fromOperationId',
        bind: 'firstProcessLov.operationId',
      },
      {
        name: 'endProcessLov',
        label: intl.get(`${modelPrompt}.form.endProcessLov`).d('截止工艺'),
        type: FieldType.object,
        lovCode: 'MT.OPERATION',
        required: true,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'toOperationId',
        bind: 'endProcessLov.operationId',
      },
      {
        name: 'toDate',
        type: FieldType.dateTime,
        required: true,
        label: intl.get(`${modelPrompt}.form.toDate`).d('到期时间'),
      },
    ],
  });

const updateDS = () =>
  new DataSet({
    autoQuery: false,
    fields: [
      {
        name: 'toDate',
        type: FieldType.dateTime,
        required: true,
        label: intl.get(`${modelPrompt}.form.toDate`).d('到期时间'),
      },
    ],
  });

export { tableDS, createDS, updateDS };
