import { BASIC } from '@/utils/config';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.barcodeMarkingBinding';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'identificationMarkingId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'identificationLov',
        type: 'object',
        lovCode: 'HME.IDENTIFICATION_MARKING',
        required: true,
        ignore: 'always',
        label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      },
      {
        name: 'identificationId',
        bind: 'identificationLov.identificationId',
      },
      {
        name: 'identification',
        bind: 'identificationLov.identification',
      },
      {
        name: 'identificationType',
        bind: 'identificationLov.identificationType',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'identificationLov.materialName',
      },
      {
        name: 'markingCodeLov',
        type: 'object',
        lovCode: 'HME.MARKING_CODE',
        required: true,
        ignore: 'always',
        lovPara: { tenantId },
        label: intl.get(`${modelPrompt}.markingCodeLov`).d('标记编码'),
      },
      {
        name: 'markingCode',
        bind: 'markingCodeLov.markingCode',
      },
      {
        name: 'markingId',
        bind: 'markingCodeLov.markingId',
      },
      {
        name: 'statusMeaning',
        type: 'string',
        bind: 'markingCodeLov.statusMeaning',
        label: intl.get(`${modelPrompt}.statusMeaning`).d('标记状态'),
      },
      {
        name: 'sourceWayMeaning',
        type: 'string',
        defaultValue: '新建',
        label: intl.get(`${modelPrompt}.sourceWayMeaning`).d('标记来源'),
      },
      {
        name: 'sourceIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.sourceIdentification`).d('来源条码号'),
      },
      {
        name: 'sourceMaterialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.sourceMaterialName`).d('来源物料描述'),
      },
      {
        name: 'originalIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.originalIdentification`).d('首次来源条码标识'),
      },
      {
        name: 'originalMaterialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.originalMaterialName`).d('原始物料描述'),
      },
      {
        name: 'typeMeaning',
        bind: 'markingCodeLov.typeMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.typeMeaning`).d('标记类型'),
      },
      {
        name: 'markingLotStatus',
        lookupCode: 'HME.MARKING_LOT_STATUS',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingLotStatus`).d('条码标记绑定状态'),
      },
      {
        name: 'markingContentMeaning',
        bind: 'markingCodeLov.markingContentMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.markingContentMeaning`).d('标记内容'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        required: true,
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
        bind: 'markingCodeLov.enableFlag',
        dynamicProps: {
          disabled: ({ record }) => {
            return record.get('enableFlag') === 'Y'
          }
        },
      },
      {
        name: 'expirationDate',
        type: 'string',
        bind: 'markingCodeLov.expirationDate',
        label: intl.get(`${modelPrompt}.expirationDate`).d('有效期'),
      },
      {
        name: 'applyReason',
        bind: 'markingCodeLov.applyReason',
        type: 'string',
        label: intl.get(`${modelPrompt}.applyReason`).d('申请原因'),
      },
      {
        name: 'description',
        bind: 'markingCodeLov.description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('拦截工艺'),
      },
      {
        name: 'interceptionDisposalWay',
        type: 'string',
        bind: 'markingCodeLov.interceptionDisposalWay',
        label: intl.get(`${modelPrompt}.interceptionDisposalWay`).d('拦截处置方法'),
      },
      {
        name: 'disposalResult',
        type: 'string',
        bind: 'markingCodeLov.disposalResult',
        label: intl.get(`${modelPrompt}.disposalResult`).d('处置结果'),
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('操作人'),
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-identification-markings/approve/query`,
          method: 'GET',
        };
      },
    },
  };
};

export { tableDS,  };
