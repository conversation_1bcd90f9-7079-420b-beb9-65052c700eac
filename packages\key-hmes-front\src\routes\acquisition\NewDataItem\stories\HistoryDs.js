/**
 * @feature 新数据收集项维护-列表页-历史记录
 * @date 2021-4-14
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';
import moment from 'moment';

const modelPrompt = 'tarzan.acquisition.dataItem.model.dataItem';
const tenantId = getCurrentOrganizationId();

/**
 * 列表和详情页
 */
const historyDS = () => ({
  primaryKey: 'tagCode',
  queryUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-assemble-group/list/ui`,
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  paging: false,
  queryFields: [
    {
      name: 'startTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
      required: true,
      defaultValue: moment(
        moment()
          .subtract(6, 'months')
          .format('YYYY-MM-DD HH:mm:ss'),
      ),
    },
    {
      name: 'endTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
      required: true,
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
  ],
  fields: [
    {
      name: 'tagCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagCode`).d('数据项编码'),
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tagDescription`).d('数据项描述'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'collectionMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.collectionMethod`).d('数据收集方式'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=GENERAL&typeGroup=TAG_COLLECTION_METHOD`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'valueAllowMissing',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueAllowMissing`).d('允许缺失值'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.enable`).d('启用') },
          { value: 'N', key: intl.get(`tarzan.common.label.disable`).d('禁用') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'displayValueFlag', // 新增参数
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.displayValueFlag`).d('是否显示标准值'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'valueType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueType`).d('数据类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=GENERAL&typeGroup=TAG_VALUE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'trueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
    },
    {
      name: 'falseValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
    },
    {
      name: 'minimumValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.minimumValue`).d('最小值'),
    },
    {
      name: 'maximalValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maximalValue`).d('最大值'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('计量单位'),
    },
    {
      name: 'valueList', // 新增参数
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueList`).d('值列表'),
    },
    {
      name: 'mandatoryNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mandatoryNum`).d('必须的数据条数'),
    },
    {
      name: 'optionalNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.optionalNum`).d('可选的数据条数'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag-his/query/ui`,
        method: 'GET',
      };
    },
  },
});

const targetPointOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'DISTRIBUTION_COVER_LOCATION_TYPE' },
      };
    },
  },
});

export { historyDS, targetPointOptionDs };
