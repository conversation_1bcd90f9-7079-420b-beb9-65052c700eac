import React, { useMemo, useCallback, useState } from 'react';
import {
  Table,
  DataSet,
  Button,
  Row,
  Col,
  Form,
  TextField,
  DateTimePicker,
  Lov,
} from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { TableQueryBarHook } from 'choerodon-ui/pro/lib/table/Table';
import { Badge } from 'choerodon-ui';
import { tableDS } from './stories';

const BackupWorkEoSkips = props => {
  const { tableDs } = props;
  const [expandForm, setExpandForm] = useState(false);

  const handleQuery = useCallback(async () => {
    await tableDs.query();
  }, [tableDs]);

  const handleReset = useCallback(() => {
    tableDs.queryDataSet?.reset();
    tableDs.fireEvent('queryBarReset', {
      dataSet: tableDs,
      queryFields: tableDs.queryDataSet?.fields,
    });
  }, [tableDs]);

  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const renderQueryBar: TableQueryBarHook = ({ buttons, queryDataSet }) => {
    if (!queryDataSet) {
      return null;
    }

    const current = queryDataSet.current;
    const dateFrom = current?.get('dateFrom');
    const dateTo = current?.get('dateTo');

    return (
      <Row
        gutter={24}
        style={{
          display: 'flex',
          alignItems: 'flex-start',
        }}
      >
        <Col span={18}>
          <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
            <TextField name="identifications" />
            <Lov name="skipOperationLov" />
            <Lov name="interceptOperationLov" />
            {expandForm && (
              <>
                <DateTimePicker name="dateFrom" max={dateTo} />
                <DateTimePicker name="dateTo" min={dateFrom} />
              </>
            )}
          </Form>
        </Col>
        <Col span={6}>
          <div>
            <Button
              funcType={FuncType.flat}
              icon={expandForm ? 'expand_less' : 'expand_more'}
              onClick={toggleForm}
            >
              {expandForm
                ? intl.get('hzero.common.button.collected').d('收起')
                : intl.get('hzero.common.button.viewMore').d('更多')}
            </Button>
            <Button onClick={handleReset}>{intl.get('hzero.common.button.reset').d('重置')}</Button>
            <Button onClick={handleQuery} funcType={FuncType.raised}>
              {intl.get('hzero.common.button.search').d('查询')}
            </Button>
            {buttons}
          </div>
        </Col>
      </Row>
    );
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        lock: ColumnLock.left,
      },
      {
        name: 'siteCode',
        align: ColumnAlign.center,
      },
      {
        name: 'skipOperationName',
        align: ColumnAlign.center,
      },
      {
        name: 'skipOperationDesc',
        align: ColumnAlign.center,
      },
      {
        name: 'interceptOperationName',
        align: ColumnAlign.center,
      },
      {
        name: 'interceptOperationDesc',
        align: ColumnAlign.center,
      },
      {
        name: 'additionalDataFlag',
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '是' : '否'} />
        ),
      },
      {
        name: 'backupWorkStatus',
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '是' : '否'} />
        ),
      },
      {
        name: 'creationDate',
      },
    ];
  }, []);

  return (
    <div className="hmes-style">
      <Header
        title={intl.get('tarzan.mes.backupWorkEoSkips.title.list').d('后备作业跳站记录查询报表')}
      />
      <Content>
        <Table
          queryFieldsLimit={10}
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="backupWorkEoSkips"
          customizedCode="backupWorkEoSkips"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.mes.backupWorkEoSkips', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(BackupWorkEoSkips),
);
