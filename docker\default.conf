server {
    listen       80;
    server_name  localhost;

    root   /usr/share/nginx/html;

    location / {
      try_files $uri /index.html;
      add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Credentials' 'true';
    }

    #新版本的hzero-cli和hzero-boot在微前端下需要子模块（业务模块和基础服务模块）做跨域配置
    location /packages/ {
      root /usr/share/nginx/html;
      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Headers X-Requested-With;
      add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
    }

    #解决发布后访问页面出现无法获取到资源的问题
    location /packages/microConfig.json {
      root   /usr/share/nginx/html;
      etag off;
      if_modified_since off;
      add_header Last-Modified "";
      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Headers X-Requested-With;
      add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
    }

    location /packages {
        root   /usr/share/nginx/html;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Headers X-Requested-With;
        add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # Media: images, icons, video, audio, HTC
    location ~* \.(jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc)$ {
    expires 1M;
    access_log off;
    add_header Cache-Control "public";
    }

    # CSS and Javascript
    location ~* \.(css|js)$ {
    expires 1y;
    access_log off;
    add_header Cache-Control "public";
    }
}
