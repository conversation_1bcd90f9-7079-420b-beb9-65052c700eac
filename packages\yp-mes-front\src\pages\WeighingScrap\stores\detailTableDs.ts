import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.ass.inOutStorage';

const detailTableFactory = () =>
  new DataSet({
    selection: false,
    paging: false,
    autoQuery: false,
    dataKey: 'rows',
    fields: [
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
        type: FieldType.string,
      },
      {
        name: 'materialLotCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'materialDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialDesc`).d('物料描述'),
      },
      {
        name: 'lot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.qty`).d('批次'),
      },
      {
        name: 'primaryUomQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.form.primaryUomQty`).d('物料批当前数量'),
      },
      {
        name: 'scrapReasonLov',
        required: true,
        type: FieldType.object,
        lovCode: 'HME_WEIGHING_SCRAP_REASON',
        label: intl.get(`${modelPrompt}.scrapReason`).d('报废原因'),
        dynamicProps: {
          disabled: ({ dataSet }) => !['1', '2'].includes(dataSet.getState('tag')),
          required: ({ dataSet }) => ['1', '2'].includes(dataSet.getState('tag'))
        },
        lovPara: {
          tenantId
        }
      },
      {
        name: 'scrapReason',
        bind: 'scrapReasonLov.value'
      },
      {
        name: 'scrapReasonMeaning',
        bind: 'scrapReasonLov.meaning'
      },
      {
        name: 'scarpQty',
        required: true,
        min: 0,
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.form.scarpQty`).d('报废数量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.uomCode`).d('主单位'),
      },
    ],
  });

export default detailTableFactory;
