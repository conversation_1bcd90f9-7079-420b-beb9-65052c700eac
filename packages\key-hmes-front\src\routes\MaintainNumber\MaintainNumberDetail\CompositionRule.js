import React, { Component } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Form, Input, Icon, Tooltip } from 'hzero-ui';
import intl from 'utils/intl';
import styles from './index.module.less';
import RulesBlock from './RulesBlock.js';

//  显示值会有以下几种类型
const INPUT_MAP = [
  'fixInput',
  'numLowerLimit',
  'dateFormat',
  'timeFormat',
  'callStandardObject',
  'incomeValueLength',
  'incomeValueLengthLimit',
];

const iconMargin = {
  marginRight: '2px',
  pointerEvents: 'all',
};

const disIconMargin = {
  marginRight: '2px',
  pointerEvents: 'none',
};

const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';
/**
 * 表单数据展示
 * @extends {Component} - React.Component
 * @return React.element
 */

@connect(({ maintainNumber }) => ({
  maintainNumber,
}))
@Form.create({ fieldNameProp: null })
export default class CompositionRule extends Component {
  rulesBlock;

  constructor(props) {
    super(props);
    props.onRef(this);
    this.state = {
      rulesList: [{}, {}, {}, {}, {}],
      usingRuleDetail: {},
      focusInputKey: 0,
    };
  }

  initCompositionRule = create => {
    if (create) {
      this.setState({
        rulesList: [{}, {}, {}, {}, {}],
        usingRuleDetail: {},
        focusInputKey: 0,
      });
    } else {
      const listLength = this.props.maintainNumber.maintainNumberDetailRules.length > 0;

      this.setState({
        rulesList:
          listLength > 0
            ? this.props.maintainNumber.maintainNumberDetailRules
            : [{}, {}, {}, {}, {}],
        usingRuleDetail: this.props.maintainNumber.maintainNumberDetailRules[0] || {},
        focusInputKey: 0,
      });
    }
  };

  //  仅在新增编码规则时，给予父组件使用，
  //  切换编码对象时，标准对象编码需要修改
  //  用来重置正在使用【调用标准对象编码】的rule
  //  numRule为5，则为使用调用标准对象编码
  resetObjectCode = () => {
    const { rulesList, usingRuleDetail } = this.state;
    const newRuleDetail = usingRuleDetail.numRule === '5' ? {} : usingRuleDetail;
    const newRulesList = rulesList.map(item => {
      if (item.numRule === '5') {
        return {};
      }
      return item;
    });
    this.setState({
      rulesList: newRulesList,
      usingRuleDetail: newRuleDetail,
    });
  };

  //  修改了组成规则内的某一项后，重置表单，给父组件用
  resetNumRangeRuleId = () => {
    const { focusInputKey } = this.state;
    const {
      maintainNumber: { maintainNumberDetailRules },
    } = this.props;
    this.setState({
      rulesList: maintainNumberDetailRules || [{}, {}, {}, {}, {}],
      usingRuleDetail: maintainNumberDetailRules[focusInputKey] || {},
    });
  };

  //  子组件RulesBlock的ref
  onRefRulesBlock = (ref = {}) => {
    this.rulesBlock = ref;
  };

  /**
   * 获取号段示例值
   * @param {Array} ruleList
   * @returns exampleInputValue
   */
  getExampleInputValue = () => {
    const { rulesList } = this.state;
    let value = '';
    rulesList.forEach(element => {
      value += this.getExampleValue(element);
    });
    return value;
  };

  /**
   * 获取号段示例位数
   * @param {Array} ruleList
   * @param {boolean} wordFlag
   * @returns wordFlag?exampleInputLength+word:exampleInputLength
   */
  getExampleInputLength = wordFlag => {
    const { rulesList } = this.state;
    let value = '';
    let hasCallStandardObject = false;
    rulesList.forEach(element => {
      if (element && element.numRule !== '5') {
        value += this.getExampleValue(element);
      } else if (element && element.displayFlag === 'Y') {
        hasCallStandardObject = true;
        value += 'X';
      }
    });
    if (wordFlag) {
      return hasCallStandardObject
        ? intl
          .get('tarzan.mes.maintainNumber.label.leastLength', {
            number: value.length,
          })
          .d(`(至少${value.length}位)`)
        : intl
          .get('tarzan.mes.maintainNumber.label.length', {
            number: value.length,
          })
          .d(`(共${value.length}位)`);
    }
    return value.length;
  };

  //  获取示例
  getExampleValue = rule => {
    if (!rule || !rule.numRule) {
      return '';
    }
    switch (rule.numRule) {
      case '1': //  固定输入值
        return rule.fixInput ? rule.fixInput : '';
      case '2': //  序列号段
        return this.getLimitExample(rule || {});
      case '3': //  日期格式
        return rule.dateFormat ? this.getDateExample(rule.dateFormat.toUpperCase()) : '';
      case '4': //  时间格式
        return rule.timeFormat ? this.getDateExample(rule.timeFormat) : '';
      case '5': //  对象属性值
        return rule.callStandardObject && rule.displayFlag === 'Y'
          ? `[${rule.callStandardObject}]`
          : '';
      case '6': {
        const value = rule.incomeValueLength ? rule.incomeValueLength : rule.incomeValueLengthLimit;
        return this.getIncomeValueLengthExample(value);
      }
      default:
        return '';
    }
  };

  getLimitExample = rule => {
    let value = rule.numLowerLimit;
    if (value >= 0 && rule.numLengthLimit && `${value}`.length < rule.numLengthLimit) {
      const times = rule.numLengthLimit - `${value}`.length;
      value -= 0;
      for (let i = 0; i < times; i++) {
        value = `0${value}`;
      }
      return value;
    }
    if (value === '') {
      value = '';
    } else {
      return value || '';
    }
  };

  //  根据日期格式返回当前日期信息
  getDateExample = dateFormat => {
    if (dateFormat === 'MMWW') {
      //  选项为MMWW时，返回当前月和当日在当前月的第几周
      return `${moment()
        .format('MM')
        .toString()}0${(
        moment()
          .startOf('day')
          .format('WW') -
        moment()
          .startOf('month')
          .format('WW') +
        1
      ).toString()}`;
    }
    return moment().format(dateFormat);
  };

  //  根据时间格式返回当前日期信息
  getTimeExample = dateFormat => {
    if (dateFormat === 'MMWW') {
      //  选项为MMWW时，返回当前月和当日在当前月的第几周
      return (
        moment()
          .startOf('day')
          .format('WW') -
        moment()
          .startOf('month')
          .format('WW') +
        1
      ).toString();
    }
    return moment().format(dateFormat);
  };

  //  根据外部输入值长度显示对应的X在号码段示例
  getIncomeValueLengthExample = exampleValue => {
    const length = parseInt(exampleValue, 10);
    let value = '';
    for (let i = 0; i < length; i++) {
      value = `${value}X`;
    }
    return value || '';
  };

  //  渲染输入框
  renderInputList = (list, canEdit) => {
    const { focusInputKey: fk, rulesList } = this.state;
    const {
      maintainNumber: { maintainNumberDetail = {}, userRole = 'N' },
    } = this.props;
    const { initialFlag = 'N' } = maintainNumberDetail;
    return list.map(item => {
      const ruleObject = rulesList[item] || {};
      //  获得Input显示值
      let value = ruleObject[INPUT_MAP[Number(ruleObject.numRule) - 1]];
      if (ruleObject.numRule - 6 === 0) {
        value =
          ruleObject[INPUT_MAP[Number(ruleObject.numRule) - 1]] ||
          ruleObject[INPUT_MAP[Number(ruleObject.numRule)]];
      }

      if (value >= 0) {
        if (ruleObject.numLengthLimit && `${value}`.length < ruleObject.numLengthLimit) {
          const times = ruleObject.numLengthLimit - `${value}`.length;
          value -= 0;
          for (let i = 0; i < times; i++) {
            value = `0${value}`;
          }
        } else {
          value = `${value}`;
        }
        if (value === 0) {
          value = `${value}`;
        }
      } else if (value === '') {
        value = '';
      } else {
        value = value || '';
      }

      return (
        <Input
          dbc2sbc={false}
          key={item}
          className={
            item === fk ? `${styles.ruleInput} ${styles.focusRuleInput}` : styles.ruleInput
          }
          onClick={this.onFocusInput.bind(this, item)}
          value={value}
          suffix={
            ruleObject.numRule && (
              <>
                {ruleObject.numRule === '5' && ruleObject.displayFlag === 'Y' ? (
                  <Tooltip
                    placement="topLeft"
                    title={intl
                      .get('tarzan.mes.maintainNumber.info.display')
                      .d('“对象属性值”值在生成的编码中展示')}
                  >
                    <i
                      className="iconfont iconyincang"
                      style={canEdit ? iconMargin : disIconMargin}
                      onClick={this.changeShowFlag.bind(this, item)}
                    />
                  </Tooltip>
                ) : (
                  ruleObject.displayFlag && (
                    <Tooltip
                      placement="topLeft"
                      title={intl
                        .get('tarzan.mes.maintainNumber.info.hidden')
                        .d(
                          '“对象属性值”值不在生成的编码中展示，但“特定对象流水”依然按照本对象进行流水',
                        )}
                    >
                      <i
                        className="iconfont iconyincang1"
                        style={canEdit ? iconMargin : disIconMargin}
                        onClick={this.changeShowFlag.bind(this, item)}
                      />
                    </Tooltip>
                  )
                )}
                {(userRole === 'Y' || initialFlag !== 'Y') && canEdit && (
                  <Icon type="close" onClick={this.handleCleanInput.bind(this, item)} />
                )}
              </>
            )
          }
          readOnly
        />
      );
    });
  };

  //  五个规则框，保存的时候至少得有一个
  validateInputList = () => {
    const { rulesList } = this.state;
    const inputList = [0, 1, 2, 3, 4];
    const flag = inputList.some(item => {
      const ruleObject = rulesList[item] || {};
      //  获得Input显示值
      let value = ruleObject[INPUT_MAP[Number(ruleObject.numRule) - 1]];
      if (ruleObject.numRule - 6 === 0) {
        value =
          ruleObject[INPUT_MAP[Number(ruleObject.numRule) - 1]] ||
          ruleObject[INPUT_MAP[Number(ruleObject.numRule)]];
      }

      return !(value === undefined || value === null || value === '');
    });
    return flag;
  };

  //  修改号段示例中，对象编码的显隐
  changeShowFlag = index => {
    const {
      maintainNumber: { maintainNumberDetail = {} },
    } = this.props;
    const { initialFlag = 'N' } = maintainNumberDetail;
    if (initialFlag === 'Y') {
      return;
    }
    const { rulesList, usingRuleDetail } = this.state;
    const newDisplayFlag = rulesList[index].displayFlag === 'Y' ? 'N' : 'Y';
    const newRulesList = rulesList;
    newRulesList[index] = {
      ...newRulesList[index],
      displayFlag: newDisplayFlag,
    };
    this.setState({
      rulesList: newRulesList,
      usingRuleDetail: {
        ...usingRuleDetail,
        displayFlag: newRulesList[index].displayFlag,
      },
    });
  };

  //  输入框的清空按钮
  handleCleanInput = (index, e) => {
    e.stopPropagation();
    const { focusInputKey: fk, rulesList, usingRuleDetail } = this.state;
    const {
      dispatch,
      maintainNumber: { usingObjectColumnCodeArray = [] },
    } = this.props;
    const newRulesList = [...rulesList];
    newRulesList[index] = {
      numrangeRuleId: newRulesList[index].numrangeRuleId,
      _backUpRule: newRulesList[index]._backUpRule,
    };

    let hasFk = false;

    if (['5', '6'].includes(rulesList[index].numRule)) {
      newRulesList.forEach((item, newListIndex) => {
        const _relatedRuleSequence = [];
        if (item && item.relatedRuleSequence && item.relatedRuleSequence.length > 0) {
          item.relatedRuleSequence.forEach(value => {
            if (fk === newListIndex) {
              hasFk = true;
            }
            if (value - 1 - index !== 0) {
              _relatedRuleSequence.push(value);
            }
          });
          item.relatedRuleSequence = _relatedRuleSequence;
        }
      });
    }

    //  当清空的输入框时选中输入框时
    if (index === fk) {
      this.setState({
        rulesList: newRulesList,
        usingRuleDetail: {
          numrangeRuleId: usingRuleDetail.numrangeRuleId,
          _backUpRule: usingRuleDetail._backUpRule,
        },
      });
    } else if (hasFk) {
      const fkadd = fk + 1;
      this.setState({
        focusInputKey: fkadd,
        rulesList: newRulesList,
        usingRuleDetail: {
          ...usingRuleDetail,
          relatedRuleSequence: newRulesList[fk].relatedRuleSequence,
        },
      });
      setTimeout(() => {
        this.setState({
          focusInputKey: fkadd - 1,
        });
      }, 0);
    } else {
      this.setState({
        rulesList: newRulesList,
      });
    }
    usingObjectColumnCodeArray[index] = null;
    dispatch({
      type: 'maintainNumber/updateState',
      payload: {
        usingObjectColumnCodeArray,
      },
    });
  };

  //  记录选中输入框
  onFocusInput = focusInputKey => {
    const that = this;
    //  点击时进行表单验证
    if (this.rulesBlock.ruleComponent) {
      this.rulesBlock.ruleComponent.validateFields(err => {
        if (!err) {
          that.setState({
            focusInputKey,
            usingRuleDetail: that.state.rulesList[focusInputKey] || {},
          });
        }
      });
    } else {
      that.setState({
        focusInputKey,
        usingRuleDetail: that.state.rulesList[focusInputKey] || {},
      });
    }
  };

  //  给子组件提供修改this.state.usingRuleDetail的方法
  setUsingRuleDetail = detail => {
    const { rulesList = [], focusInputKey } = this.state;
    const newRulesList = [...rulesList];
    newRulesList[focusInputKey] = detail;
    this.setState({
      rulesList: newRulesList,
      usingRuleDetail: detail,
    });
  };

  /**
   * render
   * @returns React.element
   */
  render() {
    const { rulesList, usingRuleDetail, focusInputKey } = this.state;
    const {
      ruleId,
      canEdit,
      // maintainNumber: { userRole = 'N' },
    } = this.props;
    const inputList = [0, 1, 2, 3, 4, 5, 6, 7];
    const exampleInputValue = this.getExampleInputValue();
    //  把所有null处理成undefined,undefined才能赋默认值,null不行
    Object.keys(usingRuleDetail).forEach(item => {
      const element = usingRuleDetail[item];
      if (element === null) {
        usingRuleDetail[item] = undefined;
      }
    });
    const initProps = {
      canEdit,
      dataSource: usingRuleDetail,
      rulesList,
      setUsingRuleDetail: this.setUsingRuleDetail,
      onRef: this.onRefRulesBlock,
      ruleId,
      focusInputKey,
    };

    const formItemLayout = {
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
    };

    return (
      <>
        <Form.Item
          {...formItemLayout}
          label={intl.get(`${modelPrompt}.numberSegment`).d('号段组合')}
          className="label-width-cover"
        >
          <div className={styles.rightDiv}>{this.renderInputList(inputList, canEdit)}</div>
        </Form.Item>
        <Form.Item
          {...formItemLayout}
          label={intl.get(`${modelPrompt}.numExample`).d('编码示例')}
          className="label-width-cover"
        >
          <div className={styles.rightDiv}>
            <Input dbc2sbc={false} className={styles.ruleDemo} value={exampleInputValue} disabled />
            {exampleInputValue.length > 0 && (
              <div className={styles.ruleDemoLength}>{this.getExampleInputLength(true)}</div>
            )}
            <Tooltip
              placement="topLeft"
              title={intl
                .get('tarzan.mes.maintainNumber.info.numberWarning')
                .d('若规则中有“对象属性值”和“外部输入值”，可能会使号段示例位数不准确')}
            >
              <Icon
                type="question-circle-o"
                style={{
                  margin: '13px 4px 0',
                }}
              />
            </Tooltip>
          </div>
        </Form.Item>
        <RulesBlock {...initProps} />
      </>
    );
  }
}
