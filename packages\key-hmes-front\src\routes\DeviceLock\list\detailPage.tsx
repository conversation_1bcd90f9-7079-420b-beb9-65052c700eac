import React, { useEffect, } from 'react';
import {
  Table,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { useDataSet, } from 'utils/hooks';
import { RouteComponentProps } from 'react-router';
import detailTable from '../stores/detailTableDs';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';

const modelPrompt = 'tarzan.ass.deviceLock';
interface RouterId {
  id: string;
}

const DetailPage: React.FC<RouteComponentProps<RouterId>> = ({ match: { params } }) => {

  const detailTableDs = useDataSet(detailTable, 'deviceLockTable');

  useEffect(() => {
    if (params.id) {
      detailTableDs.setQueryParameter('equipmentLockId', params.id.split(','));
      detailTableDs.query()
    }
  }, [params]);


  const columns: ColumnProps[] = [
    {
      name: 'equipmentCode',
      width: 150,
    },
    {
      name: 'equipmentName',
      width: 180,
    },
    {
      name: 'workcellCode',
      width: 180,
    },
    {
      name: 'lockStatus',
    },
    {
      name: 'lockReasonMeaning',
    },
    {
      name: 'lockTime',
      width: 150,
    },
    {
      name: 'inspectRequestCode',
      width: 180,
    },
    {
      name: 'inspectDocCode',
      width: 180,
    },
    {
      name: 'inspectBusinessType',
      width: 150,
    },
    {
      name: 'inspectReqUserRealName',
    },
    {
      name: 'inspectorRealName',
    },
    {
      name: 'unlockReason',
    },
    {
      name: 'unlockTime',
      width: 150,
    },
    {
      name: 'status',
    },
    {
      name: 'eventId',
    },
  ]


  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.deviceLockHistory`).d('设备锁定历史')}
        backPath="/mes/deviceLock/list"
      >
      </Header>
      <Content>
        <Table
          dataSet={detailTableDs}
          columns={columns}
          key="deviceLockTable"
          queryBar={TableQueryBarType.none}
          searchCode="deviceLockTable" // 动态筛选条后端接口唯一编码
          customizedCode="deviceLockTable" // 个性化编码
        />
      </Content>
    </div>
  );
};

export default DetailPage;
