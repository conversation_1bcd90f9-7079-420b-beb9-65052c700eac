import React, { FC, useEffect, } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Modal, Form, Output, TextArea } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import listPageFactory from '../stores/listPageDs';
import { useDataSet, } from 'utils/hooks';
import { Badge } from 'choerodon-ui';

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.spsInterfaceRecord';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {

  const detailDs = useDataSet(listPageFactory, 'andengMonitorApplicationDetail');

  useEffect(() => {
    listDs.query();
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'interfaceName',
      width: 270
    },
    {
      name: 'requestMethod',
    },
    {
      name: 'requestTime',
    },
    {
      name: 'responseTime',
    },
    {
      name: 'requestStatus',
    },
    {
      name: 'action',
      width: 70,
      renderer: ({ record }) => {
        return <a onClick={() => onHandleDetail(record)}>详情</a>

      }
    },
  ]

  const onHandleDetail = (record) => {
    detailDs.loadData([record.toData()])
    Modal.open({
      key: 'sps',
      title: '详细信息',
      className: 'hmes-style-modal',
      style: {
        width: '1080px',
      },
      children: <Form dataSet={detailDs} columns={3} >
        <Output name="interfaceName"  />
        <Output name="requestMethod"  />
        <Output name="requestTime" />
        <Output name="responseTime"  />
        <Output
          name="requestStatus"
          renderer={({ record }) => (
            <Badge
              status={record!.get('requestStatus') === 'success' ? 'success' : 'error'}
              text={
                record!.get('requestStatus') === 'success'
                  ? intl.get(`tarzan.common.label.success`).d('成功')
                  : intl.get(`tarzan.common.label.failure`).d('失败')
              }
            />
          )} />
        <TextArea name="requestBodyParameter" newLine disabled colSpan={8} rows={6}/>
        <TextArea name="responseContent" newLine disabled colSpan={8} rows={6}/>
      </Form>,
    })
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.spsInterfaceRecord`).d('外部接口调用记录')}>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="spsInterfaceRecord"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={5} // 头部显示的查询字段的数量
          searchCode="spsInterfaceRecord" // 动态筛选条后端接口唯一编码
          customizedCode="spsInterfaceRecord" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.spsInterfaceRecord'],
})(ListPage);
