/**
 * 组织关系service
 * @date: 2021-1-25
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

/**
 * 获取完整树节点关系
 * @async
 * @function queryTreeData
 * @param {object} params - 请求参数
 * @returns {object} fetch Promise
 */
export async function allTreeData(params) {
  return request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-organization-rel/all/tree/ui`, {
    method: 'GET',
    query: params,
  });
}

/**
 * 向目标节点添加子节点
 * @async
 * @function queryTreeData
 * @param {object} params - 请求参数
 * @returns {object} fetch Promise
 */
export async function addTreeNodes(params) {
  return request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-organization-rel/create/ui`, {
    method: 'POST',
    body: params,
  });
}

/**
 * 删除目标节点
 * @async
 * @function queryTreeData
 * @param {object} params - 请求参数
 * @returns {object} fetch Promise
 */
export async function deleteTreeNodes(params) {
  return request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-organization-rel/delete/ui`, {
    method: 'POST',
    body: params,
  });
}

/**
 * 复制节点至目标节点
 * @async
 * @function queryTreeData
 * @param {object} params - 请求参数
 * @returns {object} fetch Promise
 */
export async function copyTreeNodes(params) {
  return request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-organization-rel/copy/ui`, {
    method: 'POST',
    body: params,
  });
}

/**
 * 剪切节点至目标节点
 * @async
 * @function queryTreeData
 * @param {object} params - 请求参数
 * @returns {object} fetch Promise
 */
export async function cutTreeNodes(params) {
  return request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-organization-rel/cut/ui`, {
    method: 'POST',
    body: params,
  });
}

/**
 * 同级排序
 * @async
 * @function queryTreeData
 * @param {object} params - 请求参数
 * @returns {object} fetch Promise
 */
export async function reorderTreeNodes(params) {
  return request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-organization-rel/reorder/ui`, {
    method: 'POST',
    body: params,
  });
}
