import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from 'hcm-components-front/lib/utils/config';

const tenantId = getCurrentOrganizationId();
const Host = `${BASIC.HMES_BASIC}`
// const Host = '/yp-mes-20000'
// 物料批扫描
export function ScanMaterialLotRelatedInfo() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/material/lot/scan`,
    method: 'POST',
  };
}
export function ScanEoRelatedInfo() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/eo/scan`,
    method: 'POST',
  };
}
/**
 * 保存不良记录表
 * @function SaveNcRecord
 * @returns {object} fetch Promise
 */
export function SaveNcRecord(): object {
  return {
    // url: `${Host}/v1/${tenantId}/hme-nc-incident/platform/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.DETAIL`,
    url: `${Host}/v1/${tenantId}/hme-nc-incident/platform/save/ui`,
    method: 'POST',
  };
}

export function CancelNcRecord(): object {
  return {
    url: `${Host}/v1/${tenantId}/mt-nc-record/platform/cancel/ui`,
    method: 'POST',
  };
}

// 删除行
export function LineDelete(): object {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/platform/delete/ui`,
    method: 'POST',
  };
}
export function QueryRevision(): object {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
    method: 'GET',
  };
}

// 查询设备工艺
export function FetchOperation() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-record/equipment/operation/query`,
    method: 'GET',
  };
}

/**
 * 获取物料批关联信息
 * @function FetchMaterialLotRelatedInfo
 * @returns {object} fetch Promise
 */
export function FetchMaterialLotRelatedInfo() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/material/lot/related`,
    method: 'POST',
  };
}

/**
 * 获取EO关联信息
 * @function FetchEoRelatedInfo
 * @returns {object} fetch Promise
 */
export function FetchEoRelatedInfo() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/eo/related`,
    method: 'POST',
  };
}

/**
 * 查询EO或物料批
 * @function QueryMaterialLot
 * @returns {object} fetch Promise
 */
export function QueryMaterialLot(): object {
  return {
    url: `${Host}/v1/${tenantId}/mt-nc-incidents/material/lot/lov/ui`,
    method: 'GET',
  };
}
/**
 * 查询EO或物料批
 * @function QueryMaterialLot
 * @returns {object} fetch Promise
 */
export function QueryEo(): object {
  return {
    url: `${Host}/v1/${tenantId}/mt-nc-incidents/eo/lov/ui`,
    method: 'GET',
  };
}

export function RecordCancel(): object {
  return {
    url: `${Host}/v1/${tenantId}/mt-nc-incidents/cancel`,
    method: 'POST',
  };
}

// 快速报废
export function QuickScrapping(): object {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/platform/close-oa/ui`,
    method: 'POST',
  };
}
// 发起评审
export function InitiateReview(): object {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/platform/review/ui`,
    method: 'POST',
  };
}
// 报废确认
export function ScrappingConfirm(): object {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/platform/close/confirm/ui`,
    method: 'POST',
  };
}

// 报废取消
export function ScrappingCancel(): object {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/platform/close/cancel/ui`,
    method: 'POST',
  };
}

// 报废取消检查
export function ScrappingCancelCheck(): object {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-incident/platform/close/cancel/ui/check`,
    method: 'POST',
  };
}
