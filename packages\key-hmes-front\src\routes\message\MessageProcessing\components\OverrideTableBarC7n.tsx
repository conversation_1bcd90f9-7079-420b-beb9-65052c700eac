/**
 * @Description: 覆盖C7N表格的搜索表单
 * @Author: <<EMAIL>>
 * @Date: 2021-01-12 17:08:54
 * @LastEditTime: 2023-03-06 14:24:14
 * @LastEditors: <<EMAIL>>
 *
 * @example
 *  import { overrideTableBar } from '@/components/tarzan-ui'
 *  <Table dataSet={tableDs} columns={columns} queryBar={overrideTableBar} />
 */

import React, { useMemo, useState } from 'react';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { Button, Form, Row, Col, DataSet } from 'choerodon-ui/pro';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import { TableQueryBarHook, TableQueryBarHookProps } from 'choerodon-ui/pro/lib/table/Table';

const buttonColumn = {
  lineHeight: '40px',
  paddingLeft: '16px',
};

const rowStyle = {
  marginBottom: '11px',
};

interface TableQueryBarHookPropsWithDisabled extends TableQueryBarHookProps {
  queryValidateDs: DataSet;
  // queryDisabled?: boolean;
}

const RenderTableSearchBar: React.FC<TableQueryBarHookPropsWithDisabled> = observer(({
  queryFields,
  queryDataSet,
  dataSet,
  queryValidateDs,
  // queryDisabled = false,
}) => {
  const [hidden, setVisible] = useState(true);
  const queryDisabled = !queryValidateDs?.current!.get('messageType') || !queryValidateDs?.current!.get('sendServicesCode')
  const firstFlowFidlds = useMemo(() => {
    return queryFields.slice(0, 3);
  }, [queryFields]);
  const otherFidlds = useMemo(() => {
    return queryFields.slice(3);
  }, [queryFields, hidden]);

  const keyDownClick = async e => {
    if (e.keyCode === 13) {
      await dataSet.query();
    }
  };

  const clickHandler = async () => {
    const validate = await queryDataSet!.validate();
    const { dateFrom, dateTo } = queryDataSet!.toData()[0];
    if (!validate || !dateFrom || !dateTo) {
      notification.error({ message: '未输入消息处理时间，请检查！' });
      return;
    }
    await dataSet.query();
  };

  const loadEmptyData = () => {
    queryDataSet!.loadData([{}]);
  };

  return (
    <Row style={rowStyle}>
      <Col span={18}>
        <Form columns={3} labelWidth={121} onKeyDown={keyDownClick} dataSet={queryDataSet}>
          {firstFlowFidlds}
          {!hidden && otherFidlds}
        </Form>
      </Col>
      <Col span={6} style={buttonColumn}>
        {queryFields.length > 3 && (
          <Button
            onClick={() => {
              setVisible(prev => !prev);
            }}
          >
            {hidden
              ? intl.get('tarzan.common.button.moreQueries').d('更多查询')
              : intl.get('tarzan.common.button.lessQueries').d('收起查询')}
          </Button>
        )}
        <Button onClick={loadEmptyData}>{intl.get('tarzan.common.button.reset').d('重置')}</Button>
        <Button
          disabled={queryDisabled}
          onClick={clickHandler}
          color={queryDisabled ? ButtonColor.gray : ButtonColor.primary}
          type={ButtonType.submit}
        >
          {intl.get('tarzan.common.button.search').d('查询')}
        </Button>
      </Col>
    </Row>
  );
});

/**
 * 覆盖C7N表格的搜索表单
 *
 * @param {TableQueryBarHookProps} props queryBar的返回参数
 * @return ReactDOM
 * @example
 *  import { overrideTableBar } from '@/components/tarzan-ui'
 *  <Table dataSet={tableDs} columns={columns} queryBar={overrideTableBar} />
 */
const overrideTableBar: TableQueryBarHook = (props: TableQueryBarHookProps) => (
  // @ts-ignore
  <RenderTableSearchBar {...props} />
);

export default overrideTableBar;
