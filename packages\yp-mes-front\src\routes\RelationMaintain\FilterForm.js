/**
 * SelectTree - 组织关系模糊查询
 * @date: 2021-1-25
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React, { useState } from 'react';
import { connect } from 'dva';
import { Row, Col } from 'choerodon-ui';
import { TextField } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import Order from './Order';

import styles from './index.module.less';

const modelPrompt = 'tarzan.model.org.relation';
let searchInterval = null; // 搜索定时器

function FilterForm(props) {
  const [searchRows, setSearchRows] = useState(0); // 模糊搜索匹配条数
  const [iconType, setIconType] = useState(true); // 全部展开/收起按钮状态
  const { loading, treeData, dispatch, searchValue, initTreeData, enableFlagVisible } = props;

  // 延时执行搜索方法
  const textFieldChange = e => {
    const { value } = e.target;
    if (searchInterval) {
      clearTimeout(searchInterval);
    }
    searchInterval = setTimeout(() => {
      searchInterval = null;
      dispatch({
        type: 'relationMaintain/updateState',
        payload: {
          searchValue: value || '',
        },
      });
      searchTree(value || '');
    }, 800);
  };

  // 搜索目标树
  const searchTree = (value = searchValue) => {
    if (loading) {
      return;
    }

    const searchTarget = treeDataList => {
      let result = [];
      let sum = 0;
      treeDataList.forEach(item => {
        if (item.organizationMessageList && item.organizationMessageList.length > 0) {
          const resultObj = searchTarget(item.organizationMessageList);
          result = result.concat(resultObj.result);
          sum += resultObj.sum;
          item.organizationMessageList.forEach(childItem => {
            if (
              childItem.description.toUpperCase().indexOf(value.toUpperCase()) > -1 &&
              result.indexOf(item.organizationRelId) === -1
            ) {
              result.push(`${item.organizationRelId}`);
            }
          });
          // if (item.description.indexOf(value) > -1 && result.indexOf(item.organizationRelId) === -1) {
          //   result.push(`${item.organizationRelId}`);
          // }
        }
        if (item.description.toUpperCase().indexOf(value.toUpperCase()) > -1) {
          if (enableFlagVisible === 'N' && item.enableFlag === 'Y') {
            sum++;
          }
          if (enableFlagVisible === 'Y') {
            sum++;
          }
        }
      });
      return {
        result,
        sum,
      };
    };

    // 开始查询
    setSearchRows(0);
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        loading: true,
        expandedKeys: [],
        autoExpandParent: true,
      },
    });
    setTimeout(() => {
      if (!value || value === '') {
        dispatch({
          type: 'relationMaintain/updateState',
          payload: {
            loading: false,
          },
        });
      } else {
        const searchResult = searchTarget(treeData);
        dispatch({
          type: 'relationMaintain/updateState',
          payload: {
            loading: false,
            expandedKeys: searchResult.result.sort(),
          },
        });
        setSearchRows(searchResult.sum);
      }
    }, 200);
  };

  // 展开所有节点
  const expandAll = () => {
    const getTreeParendKey = data => {
      let keys = [];
      data.forEach(item => {
        if (item.organizationMessageList && item.organizationMessageList.length > 0) {
          keys = keys.concat(getTreeParendKey(item.organizationMessageList));
          if (keys.indexOf(item.organizationRelId) === -1) {
            keys.push(`${item.organizationRelId}`);
          }
        }
      });
      return keys;
    };
    const allDataKeys = getTreeParendKey(treeData);
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        expandedKeys: iconType ? allDataKeys : [],
        loading: false,
      },
    });
    setIconType(!iconType);
  };

  const resetTextField = () => {
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        loading: true,
      },
    });
    initTreeData(treeData);
    setSearchRows(0);
    setTimeout(() => {
      dispatch({
        type: 'relationMaintain/updateState',
        payload: {
          searchValue: '',
          loading: false,
        },
      });
    }, 0);
  };

  return (
    <>
      <div className={styles.search}>
        <Row gutter={24}>
          <Col span={12} className={styles.btnCol}>
            <TextField
              autoComplete="on"
              className={styles['text-field']}
              placeholder={intl.get(`${modelPrompt}.organizationSearch`).d('组织查询')}
              onInput={textFieldChange}
              suffix={
                searchRows === 0
                  ? ''
                  : `${searchRows}${' '}${
                    searchRows > 1
                      ? intl.get(`${modelPrompt}.organizationSearchUnits`).d('条')
                      : intl.get(`${modelPrompt}.organizationSearchUnit`).d('条')
                  }`
              }
              value={searchValue}
              onChange={() => {}}
            />
          </Col>
          <Col span={10} className={styles.btnCol}>
            {iconType && (
              <PermissionButton
                className={styles.btn}
                icon="expand_more"
                onClick={expandAll}
                loading={loading}
                type="c7n-pro"
                key="expand_more"
              >
                {intl.get('tarzan.common.button.open').d('全部展开')}
              </PermissionButton>
            )}
            {!iconType && (
              <PermissionButton
                className={styles.btn}
                icon="expand_less"
                onClick={expandAll}
                loading={loading}
                type="c7n-pro"
                key="expand_less"
              >
                {intl.get('tarzan.common.button.retract').d('全部收起')}
              </PermissionButton>
            )}
            <PermissionButton
              className={styles.btn}
              onClick={resetTextField}
              loading={loading}
              type="c7n-pro"
            >
              {intl.get(`tarzan.common.button.reset`).d('重置')}
            </PermissionButton>
            <PermissionButton
              className={styles.btn}
              onClick={() => {
                searchTree();
              }}
              loading={loading}
              type="primary"
            >
              {intl.get(`tarzan.common.button.search`).d('查询')}
            </PermissionButton>
          </Col>
          <Col span={2} style={{ textAlign: 'right' }}>
            <Order />
          </Col>
        </Row>
      </div>
    </>
  );
}

export default formatterCollections({
  code: ['tarzan.model.org.relation', 'tarzan.common'],
})(
  connect(({ relationMaintain }) => {
    return relationMaintain;
  })(FilterForm),
);
