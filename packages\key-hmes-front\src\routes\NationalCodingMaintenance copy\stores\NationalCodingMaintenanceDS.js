// import { Host } from '@/utils/config';
import { BASIC, Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.NationalCodingMaintenance';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'gbCodeRuleId',
    paging: true,
    autoQuery: true,
    selection: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'ruleCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ruleCode`).d('规则编码'),
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('规则描述'),
      },
      {
        name: 'numberBit',
        type: 'number',
        label: intl.get(`${modelPrompt}.numberBit`).d('位数'),
      },
      {
        name: 'prodLineCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('生产线描述'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'workOrderTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderTypeDesc`).d('生产类型'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
      },
    ],
    queryFields: [
      {
        name: 'siteCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        labelWidth: 150,
      },
      {
        name: 'siteId',
        type: 'number',
        bind: 'siteCode.siteId',
        labelWidth: 150,
      },
      {
        name: 'ruleCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ruleCode`).d('规则编码'),
        pattern: '^[a-zA-Z0-9_]{0,}$',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('规则描述'),
      },
      {
        name: 'prodLineObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineObj`).d('生产线'),
        lovCode: 'MT.MODEL.PRODLINE',
        labelWidth: 150,
        ignore: 'always',
        textField: 'prodLineCode',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'prodLineCode',
        type: 'string',
        bind: 'prodLineObj.prodLineCode',
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线描述'),
        bind: 'prodLineObj.prodLineName',
      },
      {
        name: 'prodLineId',
        type: 'number',
        bind: 'prodLineObj.prodLineId',
      },
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料'),
        lovCode: 'HME.PERMISSION_MATERIAL',
        labelWidth: 150,
        textField: 'materialCode',
        dynamicProps: {
          lovPara: ({ record }) => {
            const siteId = record.get('siteId');
            return {
              tenantId: getCurrentOrganizationId(),
              siteId,
            };
          },
        },
      },
      {
        name: 'materialCode',
        type: 'string',
        bind: 'materialObj.materialCode',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'materialObj.materialName',
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'workOrderType',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderType`).d('生产类型'),
        lovPara: { tenantId },
        textField: 'description',
        valueField: 'typeCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        type: 'string',
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'MT.ENABLE_FLAG',
        // defaultValue: 'Y',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-gb-coding-rules/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const formDS = () => {
  return {
    name: 'formDS',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'siteObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.v`).d('站点'),
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        labelWidth: 150,
        textField: 'siteCode',
        ignore: 'always',
        required: true,
      },
      {
        name: 'siteCode',
        type: 'string',
        bind: 'siteObj.siteCode',
        labelWidth: 150,
      },
      {
        name: 'siteId',
        type: 'number',
        bind: 'siteObj.siteId',
        labelWidth: 150,
      },
      {
        name: 'ruleCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ruleCode`).d('规则编码'),
        required: true,
        pattern: '^[a-zA-Z0-9_-]{0,}$',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('规则描述'),
      },
      {
        name: 'numberBit',
        type: 'number',
        label: intl.get(`${modelPrompt}.numberBit`).d('规则位数'),
        required: true,
        pattern: '^[1-9]*[1-9][0-9]*$',
      },
      {
        name: 'prodLineObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineObj`).d('产线'),
        lovCode: 'HME.PERMISSION_PROD_LINE',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        textField: 'prodLineCode',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: getCurrentOrganizationId(),
              siteId: record?.get('siteId'),
            };
          },
        },
      },
      {
        name: 'prodLineCode',
        type: 'string',
        bind: 'prodLineObj.prodLineCode',
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线描述'),
        bind: 'prodLineObj.prodLineName',
      },
      {
        name: 'prodLineId',
        type: 'number',
        bind: 'prodLineObj.prodLineId',
      },
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料'),
        lovCode: 'HME.PERMISSION_MATERIAL',
        labelWidth: 150,
        textField: 'materialCode',
        required: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: getCurrentOrganizationId(),
              siteId: record?.get('siteId'),
            };
          },
        },
      },
      {
        name: 'materialCode',
        type: 'string',
        bind: 'materialObj.materialCode',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'materialObj.materialName',
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'materialSiteId',
        type: 'string',
        bind: 'materialObj.materialSiteId',
      },
      {
        name: 'revisionCode',
        type: 'string',
        bind: 'materialObj.revisionCode',
        textField: 'revisionCode',
        valueField: 'revisionCode',
        lookupUrl: `${Host}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
        lookupAxiosConfig: ({ record }) => {
          const _params = record?.toData() || {};
          if (_params && _params.materialSiteId && _params.siteId) {
            return {
              params: {
                materialSiteId: _params.materialSiteId,
                siteId: _params.siteId,
              },
              transformResponse(data) {
                // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
                if (data instanceof Array) {
                  return data;
                }
                const rows = JSON.parse(data);
                // let rows = []
                // console.log('data', data);
                // debugger;
                // let rows = [];
                // if (data.failed) {
                //   debugger
                //   rows = [];
                // } else {
                //   debugger
                //   rows = JSON.parse(data);
                // }
                // console.log('rows', rows)
                // debugger
                // const rows = JSON.parse(data);
                return rows;
              },
            };
          }
        },
        dynamicProps: {
          required: record => {
            const _params = record?.record.data || {};
            if (_params && _params.revisionFlag && _params.revisionFlag === 'Y') {
              return true;
            }
            return false;
          },
          disabled: record => {
            const _params = record?.record.data || {};
            if (_params && _params.revisionFlag && _params.revisionFlag === 'Y') {
              return false;
            }
            return true;
          },
        },
      },
      {
        name: 'workOrderType',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderType`).d('生产类型'),
        lovPara: { tenantId },
        textField: 'description',
        valueField: 'typeCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'barCode',
        type: 'string',
      },
      {
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        type: 'string',
        trueValue: 'Y',
        falseValue: 'N',
        required: true,
      },
    ],
  };
};

const detailTableDS = () => {
  return {
    name: 'detailTableDS',
    primaryKey: 'gbCodeRuleLineId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'gbCodeRuleId',
        type: 'number',
      },
      {
        name: 'serialNumber',
        label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
        type: 'number',
        required: true,
      },
      {
        name: 'description',
        label: intl.get(`${modelPrompt}.description`).d('编码含义'),
        type: 'string',
      },
      {
        name: 'fromBit',
        type: 'number',
        label: intl.get(`${modelPrompt}.fromBit`).d('起始位'),
        required: true,
        pattern: '^[1-9]*[1-9][0-9]*$',
      },
      {
        name: 'toBit',
        type: 'number',
        label: intl.get(`${modelPrompt}.toBit`).d('编码类截止位型'),
        required: true,
        pattern: '^[1-9]*[1-9][0-9]*$',
      },
      {
        name: 'codingType',
        type: 'string',
        label: intl.get(`${modelPrompt}.codingType`).d('编码类型'),
        lookupCode: 'HME.GB_CODING_RULE_TYPE',
      },
      {
        name: 'codingValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.codingValue`).d('编码值'),
        pattern: '^[a-zA-Z0-9_]{0,}$',
        dynamicProps: {
          disabled: record => {
            const _params = record?.record.data || {};
            if (_params.codingType) {
              if (_params.codingType === 'SERIAL_NUMBER' || _params.codingType === 'DATE') {
                return true;
              }
              return false;
            }
            return true;
          },
          required: record => {
            const _params = record?.record.data || {};
            if (_params.codingType) {
              if (_params.codingType === 'SERIAL_NUMBER' || _params.codingType === 'DATE') {
                return false;
              }
              return true;
            }
            return false;
          },
        },
      },
      {
        name: 'shieldField',
        type: 'string',
        label: intl.get(`${modelPrompt}.shieldField`).d('屏蔽字段'),
        pattern: '^[a-zA-Z0-9_,]{0,}$',
        dynamicProps: {
          disabled: record => {
            const _params = record?.record.data || {};
            if (_params.codingType) {
              if (_params.codingType === 'FIXED_VALUE') {
                return true;
              }
              return false;
            }
            return false;
          },
        },
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'MT.ENABLE_FLAG',
        trueValue: 'Y',
        falseValue: 'N',
        required: true,
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-gb-coding-rule-lines/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

export { tableDS, formDS, detailTableDS };
