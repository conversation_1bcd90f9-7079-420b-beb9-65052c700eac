/*
 * @Author: 47844 <EMAIL>
 * @Date: 2024-12-09 15:30:52
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2024-12-12 10:15:13
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\process\ProcessRouteC7n\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-05-10 15:24:27
 * @LastEditTime: 2023-05-18 15:18:28
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import RoutesList, { RoutesListProps } from '../RouteC7n/RouteList';

const C7nRoutesList = props => {

  const {
    match: { path },
  } = props;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
  const pbtn = (
    <PermissionButton
      type="c7n-pro"
      permissionList={[
        {
          code: `tarzan${path}.button.edit`,
          type: 'button',
          meaning: '详情页-编辑新建删除复制按钮',
        },
      ]}
    >
      {intl.get('tarzan.common.button.save').d('保存')}
    </PermissionButton>
  );
  // 装配组件列表页入参
  const routesProps: RoutesListProps = {
    detailUrl: '/hmes/new/manufacture-process/routes-c7n/dist', // 详情页Url
    featureTitle: intl.get('tarzan.process.routes.manufacture.title').d('制造工艺路线'), // 列表页标题title
    featureTitleCode: 'tarzan.process.routes.manufacture.title',
    typeGroup: 'MES_ROUTER_TYPE', // 类型
    history: props.history,
    match: props.match,
    serveCode: BASIC.HMES_BASIC,
    searchCode: 'zhizaogongyiluxian',
    customizeTable: props.customizeTable,
    custCode: 'MES_ROUTER_LIST',
  };

  return <RoutesList {...routesProps} />;
};

export default withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.MES_ROUTER_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.MES_ROUTER_LIST.LIST`],
  // @ts-ignore
})(C7nRoutesList);
