/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-05-11 15:57:07
 * @LastEditTime: 2023-05-18 15:18:44
 * @LastEditors: <<EMAIL>>
 */
/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2022-11-09 15:45:22
 * @LastEditTime: 2023-05-11 15:52:39
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import RouteDist, { RoutesDetailProps } from '../RouteC7n/RouteDist';

const C7nRoutesList = props => {

  const {
    match: { path },
  } = props;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
  const pbtn = (
    <PermissionButton
      type="c7n-pro"
      permissionList={[
        {
          code: `tarzan${path}.button.edit`,
          type: 'button',
          meaning: '详情页-编辑新建删除复制按钮',
        },
      ]}
    >
      {intl.get('tarzan.common.button.save').d('保存')}
    </PermissionButton>
  );
  // 装配组件列表页入参
  const routesProps: RoutesDetailProps = {
    listUrl: '/hmes/new/manufacture-process/routes-c7n/list', // 列表页Url
    detailUrl: '/hmes/new/manufacture-process/routes-c7n/dist', // 详情页Url
    featureTitle: intl.get('tarzan.process.routes.manufacture.title').d('制造工艺路线'), // 列表页标题title
    typeGroup: 'MES_ROUTER_TYPE', // 类型
    history,
    match: props.match,
    routerLov: 'MT.MES.ROUTER',
    serveCode: BASIC.HMES_BASIC,
    serveCodeMid: 'mes',
    methodServeCode: BASIC.TARZAN_METHOD,
    assemblyLov: 'MT.MES.BOM_FOR_ROUTER',
    GraphicalUrl: '/hmes/process/manufacture-flow', // 工艺路线图形化url
    componentLov: 'MT.MES.BOM_COMPONENT',
    operationLov: 'MT.METHOD.OPERATION',
    AssemblyUrl: '/hmes/product/manufacture-list/dist', // 装配清单rul
    location: props.location,
    customizeForm: props.customizeForm,
    customizeTable: props.customizeTable,
    custConfig: props.custConfig,
    custCode: 'MES_ROUTER_DETAIL',
  };

  return <RouteDist {...routesProps} />;
};

export default withCustomize({
  unitCode: [
    `${BASIC.CUSZ_CODE_BEFORE}.MES_ROUTER_DETAIL.BASIC`,
    `${BASIC.CUSZ_CODE_BEFORE}.MES_ROUTER_DETAIL.STEP_LIST`,
    `${BASIC.CUSZ_CODE_BEFORE}.MES_ROUTER_DETAIL.STEP_BASIC`,
    `${BASIC.CUSZ_CODE_BEFORE}.MES_ROUTER_DETAIL.BUTTON`,
    `${BASIC.CUSZ_CODE_BEFORE}.MES_ROUTER_DETAIL.BUTTON_STEP`,
  ],
  // @ts-ignore
})(C7nRoutesList);
