{
  "extends": "./../tsconfig.base.json",
  "compilerOptions": {
    "outDir": "./lib" /* Redirect output structure to the directory. */,
    "rootDir": "./src" /* Specify the root directory of input files. Use to control the output directory structure with --outDir. */,
    "baseUrl": "./" /* Base directory to resolve non-absolute module names. */,
    // "paths": {
    //   "@/*": ["./src/*"],
    //   "@components/*": ["./../../node_modules/hcm-components-front/lib/components/*"],
    //   "@services/*": ["./../../node_modules/hcm-components-front/lib/services/*"],
    //   "@utils/*": ["./../../node_modules/hcm-components-front/lib/utils/*"],
    //   "@assets/*": ["./../../node_modules/hcm-components-front/lib/assets/*"],
    //   "components/*": ["./../../node_modules/hzero-front/lib/components/*"],
    //   "utils/*": ["./../../node_modules/hzero-front/lib/utils/*"],
    //   "@src/*": ["./../../node_modules/hzero-front/lib/*"]
    // }
  },
  "include": ["src"],
  "exclude": [
    "lib",
    "node_modules",
    "build",
    "scripts",
    "acceptance-tests",
    "webpack",
    "jest",
    "src/setupTests.ts",
    "tslint:latest",
    "tslint-config-prettier"
  ]
}
