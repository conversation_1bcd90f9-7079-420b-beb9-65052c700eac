import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import ExcelExport from 'components/ExcelExport';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor, FuncType, } from 'choerodon-ui/pro/lib/button/enum';
import { isNil } from 'lodash';
import { openTab } from 'utils/menuTab';
import { useDataSetEvent } from 'utils/hooks';
import queryString from 'query-string';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import listPageFactory from '../stores/listPageDs';
import axios from 'axios';

const tenantId = getCurrentOrganizationId();
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.unitWork';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {
  const [edit, setEdit] = useState(false);
  const [deleteFlag, setDeleteFlag] = useState(true)

  useDataSetEvent(listDs, 'query', () => {
    setEdit(false)
    setDeleteFlag(true)
  });

  useDataSetEvent(listDs, 'select', () => {
    setStatus()
  });

  useDataSetEvent(listDs, 'unSelect', () => {
    setStatus()
  });

  useDataSetEvent(listDs, 'selectAll', () => {
    setStatus()
  });

  useDataSetEvent(listDs, 'unSelectAll', () => {
    setStatus()
  });

  const setStatus = () => {
    if (listDs.selected.length) {
      setDeleteFlag(false)
    } else {
      setDeleteFlag(true)
    }
  }

  useEffect(() => {
    listDs.query();
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'prodLineCode',
      width: 150,
    },
    {
      name: 'prodLineName',
      width: 180,
    },
    {
      name: 'equipmentLov',
      width: 150,
      editor: (record) => record.get('editing')
    },
    {
      name: 'equipmentName',
      width: 180,
    },
    {
      name: 'operationLov',
      width: 150,
      editor: (record) => record.get('editing')
    },
    {
      name: 'operationDesc',
      width: 180,
    },
    {
      name: 'workcellLov',
      width: 150,
      editor: (record) => record.get('editing')
    },
    {
      name: 'workcellName',
      width: 180,
    },
    {
      name: 'opWorkcellLov',
      width: 150,
      editor: (record) => record.get('editing')
    },
    {
      name: 'opWorkcellName',
      width: 180,
    },
    {
      width: 120,
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      renderer: ({ record }) =>
        record!.get('editing') ? (
          <>
            <Button funcType={FuncType.flat} onClick={() => handleEdit(record, false)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button funcType={FuncType.flat} onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        ) : (
          <Button funcType={FuncType.flat} onClick={() => handleEdit(record, true)}>
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        ),
    },
  ];

  // 导入
  const handleImport = () => {
    openTab({
      key: '/himp/commentImport/YP.EQUIP_OPERATION_WKC_IMPORT',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const handleDelete = async () => {
    const data = listDs.selected.map(item => ({
      operationWkcDispatchRelId: item.get('operationWkcDispatchRelId'),
      equipmentWkcId: item.get('equipmentWkcId'),
    }))
    const url = `${BASIC.TARZAN_METHOD}/v1/${getCurrentOrganizationId()}/hme-equipment-op-wkc/delete/ui`;
    const res: any = await axios.post(url, data)
    if (res && res.success) {
      await listDs.query();
    } else {
      notification.error({ message: res.message })
    }
  }

  const handleSave = async (record) => {
    const validate = await record.validate()
    if (validate) {
      const params = record.toData()
      delete params.prodLineLov
      delete params.equipmentLov
      delete params.operationLov
      delete params.opWorkcellLov
      delete params.workcellLov
      const url = `${BASIC.TARZAN_METHOD}/v1/${getCurrentOrganizationId()}/hme-equipment-op-wkc/save/ui`;
      const res: any = await axios.post(url, [params])
      if (res && res.success) {
        await listDs.query();
      } else {
        notification.error({ message: res.message })
      }
    }
  }

  const handleCreate = () => {
    listDs.create({ status: 'add', editing: true }, 0)
    setEdit(true)
  }

  const handleEdit = (record, flag) => {
    record.set('editing', flag);
    if (!flag) {
      if (record.status === 'add') {
        listDs.remove(record)
      } else {
        record.reset()
      }
    }
  }

  const getExportQueryParams = () => {
    if (!listDs.queryDataSet || !listDs.queryDataSet.current) {
      return {};
    }
    const queryParams = listDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return {
      ...queryParams,
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.unitWork`).d('工艺与工作单元维护-新')}>

        <Button
          icon="add"
          color={ButtonColor.primary}
          disabled={edit}
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新增')}
        </Button>
        <Button
          disabled={deleteFlag}
          onClick={handleDelete}
        >
          {intl.get('tarzan.common.button.delete').d('删除')}
        </Button>
        <Button icon="daorucanshu" onClick={handleImport}>{intl.get('tarzan.common.button.import').d('导入')}</Button>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.TARZAN_METHOD
            }/v1/${getCurrentOrganizationId()}/hme-equipment-op-wkc/export/ui`}
          queryParams={getExportQueryParams}
          buttonText="导出"
        />
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="unitWork"
          rowHeight={35}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={5} // 头部显示的查询字段的数量
          searchCode="unitWork" // 动态筛选条后端接口唯一编码
          customizedCode="unitWork" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.unitWork'],
})(ListPage);
