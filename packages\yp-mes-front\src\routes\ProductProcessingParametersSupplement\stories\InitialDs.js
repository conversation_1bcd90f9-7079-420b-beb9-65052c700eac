import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { getResponse } from '@utils/utils';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.inventory.initial.model';

const tenantId = getCurrentOrganizationId();
const prefix = '/mes-38546'

const initialDs = () => ({
  autoQuery: true,
  primaryKey: 'lineNumber',
  // queryUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-customer/list/ui`,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      noCache: true,
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      // required: true,
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'site.siteId',
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION ',
      lovPara: { tenantId },
      textField: 'materialCode',
      valueField: 'materialId',
      // required: true,
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialObj.materialCode',
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialObj.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      bind: 'materialObj.revisionCode',
    },
    {
      name: 'equipObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      lovPara: { tenantId },
      textField: 'equipmentCode',
      valueField: 'equipmentId',
      // required: true,
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      bind: 'equipObj.equipmentCode',
    },
    {
      name: 'equipmentId',
      type: FieldType.number,
      bind: 'equipObj.equipmentId',
    },
    {
      name: 'workcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
      lovCode: 'MT.METHOD.ROUTER_STATION',
      lovPara: {
        tenantId,
        workcellType: 'STATION',
      },
      textField: 'workcellCode',
      valueField: 'workcellId',
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      bind: 'workcellObj.workcellCode',
    },
    {
      name: 'workcellId',
      type: FieldType.number,
      bind: 'workcellObj.workcellId',
    },
  ],
  fields: [
    {
      name: 'seq',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.seq`).d('序号'),
    },
    {
      name: 'lineNumber',
      type: FieldType.string,
      // label: intl.get(`${modelPrompt}.lineNumber`).d('序号'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('是否成功'),
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('导入信息'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
    },
    {
      name: 'processBarcode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processBarcode`).d('产品条码'),
    },
    {
      name: 'recordDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.recordDate`).d('采集时间'),
    },
    {
      name: 'userName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userName`).d('采集人'),
    },
  ],
  transport: {
    read: ({ data }) => {
      // let queryData = JSON.parse(JSON.stringify(data));
      // delete queryData.revisionCodes;
      // if (data.revisionCodes.length) {
      //   queryData = {
      //     ...queryData,
      //     revisionCodes: data.revisionCodes.join(','),
      //   };
      // }
      return {
        method: 'GET',
        url: `${
          BASIC.HMES_BASIC
        }/v1/${getCurrentOrganizationId()}/hme-product-ps-pa-supps/select/import/ui`,
        // data: queryData,
      };
    },
    submit: config => {
      return {
        ...config,
        url: `${
          BASIC.HMES_BASIC
        }/v1/${getCurrentOrganizationId()}/hme-product-ps-pa-supps/save/excel/import/ui`,
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return getResponse(parsedData);
          }
        },
      };
    },
    destroy: config => {
      return {
        ...config,
        url: `${
          BASIC.HMES_BASIC
        }/v1/${getCurrentOrganizationId()}/hme-product-ps-pa-supps/delete/import/ui`,
      };
    },
  },
});


export { initialDs };
