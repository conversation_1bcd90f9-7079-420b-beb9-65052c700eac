/**
 * 工艺路线-详情页入口页面
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */
import React, { FC, useEffect, useState, useMemo, useRef } from 'react';
import {
  Table,
  DataSet,
  Button,
  Form,
  Select,
  Lov,
  Switch,
  TextField,
  DateTimePicker,
  NumberField,
  Modal,
} from 'choerodon-ui/pro';
import { Tag, Popconfirm, Collapse } from 'choerodon-ui';
import { Modal as HModal } from 'hzero-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor, FuncType, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableMode } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import { useRequest } from '@components/tarzan-hooks';
import myInstance from '@/utils/myAxios';
import formatterCollections from 'utils/intl/formatterCollections';
import { AttributeDrawer, drawerPropsC7n } from '@components/tarzan-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import { openTab } from 'utils/menuTab';
import jumpToFlow from '../../../assets/icons/jumpToFlow.svg';
import { BASIC } from '@/utils/config';
import { formDS, detailTableDS, siteListDS } from '../stories/FormDS';
import { copyDrawerDS } from '../stories/CopyDS';
import { nextStepDecisionOptionDS, stepOptionDS, routeStepOptionDS } from '../stories/CommonDS';
import { allocateObjectDS } from '../stories/AllocateObjectDS';
import { childStepDrawerDS } from '../stories/ChildStepDrawerDS';
import NewStepDrawer from '../Components/NewStepDrawer';
import ComponentDistributionDrawer from '../Components/ComponentDistributionDrawer';
import PathDrawer from '../Components/PathDrawer';
import OperationComponentDrawer from '../Components/OperationComponentDrawer';
import ChildStepDrawer from '../Components/ChildStepDrawer';
import { C7nFormItemSort } from '../Components';
import { copyRoute } from '../services/index';
import '../index.module.less';
import styles from './index.module.less';

const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

const modelPrompt = 'tarzan.process.routes.model.routes';

export interface RoutesDetailProps extends RouteComponentProps {
  featureTitle: string;
  serveCode: string; // 功能服务
  serveCodeMid: string; // 功能服务中间路由
  typeGroup: string;
  detailUrl: string; // 详情页Url
  assemblyLov: string; // 装配清单的lov
  GraphicalUrl: string; // 工艺路线图形化url
  match: any;
  history: any;
  listUrl: string; // 列表页Url
  componentLov: string;
  routerLov;
  operationLov;
  AssemblyUrl;
  methodServeCode;
  customizeForm: any;
  customizeTable: any;
  custConfig: any;
  custCode: string;
  location: any;
}

const RoutesDist: FC<RoutesDetailProps> = ({
  history,
  match: { path, params },
  location: { state },
  featureTitle,
  typeGroup,
  serveCode,
  serveCodeMid,
  assemblyLov,
  GraphicalUrl,
  listUrl,
  detailUrl,
  componentLov,
  routerLov,
  operationLov,
  AssemblyUrl,
  methodServeCode,
  customizeForm,
  customizeTable,
  custConfig,
  custCode,
}) => {
  const [canEdit, setCanEdit] = useState(false);
  const [routerId, setRouterId] = useState('');
  const [oldBomId, setOldBomId] = useState('');
  const [stepsList, setStepsList] = useState([]);
  const [compList, setCompList] = useState([]);
  const [selectSiteIds, setSelectSiteIds] = useState([]); // 选中的siteId[] / 用于存放站点id
  const [routesItem, setRoutesItem] = useState<any>({});
  const [nextStepDecisionList, setNextStepDecisionList] = useState([]);
  const [selectedRouterType, setSelectedRouterType] = useState('');
  const [hasBomId, setHasBomId] = useState(false);
  const [routerStatus, setRouterStatus] = useState('');
  const [saveLoading, setSaveLoading] = useState(false);
  const [currentBomId, setCurrentBomId] = useState('');
  const [editStatus, setEditStatus] = useState(false);
  // 工艺步骤抽屉用的数据源
  let tableList: Array<object> = []; // 所有工艺类型数据
  let currentDataSource: any = []; // 当前数据源
  // 站点列表的DS
  const siteListDs = useMemo(() => new DataSet({ ...siteListDS(serveCode) }), []);
  const formDs = useMemo(
    () => new DataSet({ ...formDS(typeGroup, assemblyLov, serveCode, siteListDs) }),
    [],
  );
  // 分配对象的DS
  const allocateObjectDs = useMemo(
    () => new DataSet({ ...allocateObjectDS(serveCode, serveCodeMid) }),
    [],
  );
  const nextStepDecisionOptionDs = useMemo(
    () => new DataSet({ ...nextStepDecisionOptionDS() }),
    [],
  );
  const stepOptionDs = useMemo(() => new DataSet({ ...stepOptionDS() }), []);
  const [stepOption, setStepOption] = useState([]);
  // 复制页面目标类型的DS
  const routeStepOptionDs = useMemo(() => new DataSet({ ...routeStepOptionDS(typeGroup) }), []);
  // 复制页面
  const copyDrawerDs = useMemo(
    () => new DataSet({ ...copyDrawerDS(typeGroup, routeStepOptionDs) }),
    [],
  );
  // 复制抽屉的DS
  const basePath = useMemo(() => path.substring(0, path.indexOf('/dist')), []);
  // 工艺子步骤-抽屉的DS
  // @ts-ignore
  const childStepDrawerDs = useMemo(() => new DataSet({ ...childStepDrawerDS() }), []);
  // 详情页表格抽屉的DS
  // @ts-ignore
  const detailTableDs = useMemo(() => new DataSet({ ...detailTableDS(serveCode) }), []);

  // 装配清单-复制
  const { run: handleCopyRoute } = useRequest(copyRoute(serveCode), {
    manual: true,
  });
  // 分配对象的ref
  const openComponentDistributionDrawerRef = useRef();
  // 路径选择
  const openPathDrawerRef = useRef();
  // 步骤抽屉的ref
  const newStepDrawerRef = useRef();
  // 子步骤信息抽屉的ref
  const childStepDrawerRef = useRef();

  /**
   * 入口初始化数据
   */
  useEffect(() => {
    initFun(params.id);
  }, [params.id]);

  // 禁用状态联动关系
  useEffect(() => {
    const { usageFlag, routerStatus } = routesItem as any;
    setEditStatus(
      routerId !== 'create' &&
      (!canEdit ||
        (usageFlag === 'Y' && routerStatus !== 'HOLD') ||
        routerStatus === 'ABANDON' ||
        routerStatus === 'FREEZE'),
    );
  }, [canEdit, routerId]);

  /**
   * 初始化函数
   */
  const initFun = async id => {
    // 获取公共数据
    await nextStepDecisionOptionDs.query();
    await stepOptionDs.query();
    const nextStepDecisionOption = nextStepDecisionOptionDs.toData();
    const stepOptionList = stepOptionDs.toData();
    // @ts-ignore
    setNextStepDecisionList(nextStepDecisionOption);
    // @ts-ignore
    setStepOption(stepOptionList);
    setStepsList([]);
    setCompList([]);

    // 「新建」状态下,页面进入可编辑状态
    if (id === 'create') {
      setCanEdit(true);
      formDs.current?.set('routerId', 'create');
    } else {
      // 获取工艺路线明细信息
      await getRouterDetails(id);
    }

    // 使用routerId记录页面状态，为create的情况下，为新增页面
    setRouterId(id);
  };

  /**
   * 获取工艺路线明细信息
   * @param id:工艺路线id
   */
  const getRouterDetails = async id => {
    // 查询表单详情
    formDs.queryParameter = {
      routerId: id,
    };
    formDs.query().then(res => {
      // 超链接的状态要跟着bomId走
      if (res.rows.bomId) {
        setHasBomId(true);
      } else {
        // 没有装配清单时bomid默认返回的0，输入框会有x
        formDs.current?.set('bomId', null);
        setHasBomId(false);
      }
      setCurrentBomId(res.rows.bomId || '');
      setOldBomId(res.rows.bomId || '');
      setRoutesItem({ ...res.rows });
      // 工艺路线为废弃状态时，编辑按钮不可点击
      setRouterStatus(res.rows.routerStatus);
      copyDrawerDs.setState('sourceRouterType', res.rows.routerType);
      formDs.current?.setState('originRouterStatus', res.rows.routerStatus);

      // selectedRouterType 相关特殊逻辑，几个功能做兼容处理
      // 物料工艺路线因类型有默认值，所以需要指定该参数
      if (serveCode === '/tznd') {
        // @ts-ignore
        setSelectedRouterType('MATERIAL');
        formDs.current?.set('selectedRouterType', 'MATERIAL');
      } else {
        setSelectedRouterType(res.rows.routerType);
        formDs.current?.set('selectedRouterType', res.rows.routerType);
      }
    });

    // 查询表格详情
    detailTableDs.setQueryParameter('routerId', id);
    await detailTableDs.query();
    // @ts-ignore
    setStepsList(operateData(detailTableDs.toData()));
    siteListDs.setQueryParameter('routerId', id);
    siteListDs.query().then(res => {
      const arr: any = [];
      res.forEach((item: any) => {
        if (item.distributedFlag) {
          arr.push(item.siteId);
        }
      });
      // 记录站点的id
      setSelectSiteIds(arr);
      formDs.current!.init('site', arr);
    });
    setCanEdit(false);
  };

  /**
   *@description 保存工艺路线
   */
  const handleSaveList = async () => {
    const validate = await formDs.validate(false, true);
    if (validate) {
      setSaveLoading(true);
      await saveData();
      setSaveLoading(false);
    }
  };

  /**
   * 获取并组合表单的值
   */
  const getFormValue = () => {
    const data: Array<any> = formDs.toData();
    const arr = [];
    data[0].site.forEach(i => {
      // @ts-ignore
      arr.push({ siteId: i });
    });
    const { bomType, bomName, revision } = routesItem as any;

    return {
      ...data[0],
      mtRouterSiteAssignDTO: arr,
      oldBomId,
      routerId: routerId === 'create' ? '' : routerId,
      bomType,
      bomName,
      bomTypeDesc: bomName,
      bomRevision: revision,
    };
  };

  /**
   *@description
   */
  const saveData = async () => {
    const data = operateData(detailTableDs.toData());

    const nextStepFlag = data.some(
      (item: any) => item.mtRouterNextStepDTO && item.mtRouterNextStepDTO.length > 0,
    );
    if (data.length === 1) {
      await saveAll(
        data.map((item: object) => {
          return {
            ...item,
            entryStepFlag: 'Y',
            routerDoneStepFlag: 'Y',
          };
        }),
      );
      return;
    }
    if (data.length < 2 || nextStepFlag) {
      // 调用保存接口
      await saveAll(data);
    } else {
      HModal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        content: intl
          .get(`${modelPrompt}.autoSortSequence`)
          .d('当前工艺路线存在步骤未维护步骤间顺序，是否按照步骤顺序自动生成步骤间顺序？'),
        onOk: async () => {
          // 按顺序自动生成
          const url = `${serveCode}/v1/${tenantId}/mt-router/sort/ui`;
          const paramsData = {
            ...getFormValue(),
            mtRouterStepDTO: data,
          };
          setSaveLoading(true);
          const res = await myInstance.post(url, paramsData);
          setSaveLoading(false);
          if (res.data.rows) {
            saveAll(res.data.rows.mtRouterStepDTO || []);
          } else {
            notification.error({
              message:
                res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
        },
        onCancel() {
          openPathDrawer();
        },
        cancelText: intl.get('tarzan.common.button.cancel').d('取消'),
        okText: intl.get('tarzan.common.button.confirm').d('确定'),
      });
    }
  };

  /**
   * 最终保存触发请求
   * @param list
   */
  const saveAll = async list => {
    const url = `${serveCode}/v1/${tenantId}/mt-router/save/ui`;
    const data = {
      ...getFormValue(),
      mtRouterStepDTO: list,
    };
    setSaveLoading(true);
    const res = await myInstance.post(url, data);
    setSaveLoading(false);
    if (res && res.data.success) {
      notification.success({});
      getRouterDetails(res.data.rows);
      history.push(`${detailUrl}/${res.data.rows}`);
    } else if (
      res &&
      !res.data.success &&
      res.data.rows &&
      (res.data.rows === 'MT_ROUTER_0073' || res.data.rows === 'MT_ROUTER_0091')
    ) {
      const _errorMessage = res.data.rows;
      HModal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        content: res.data.message,
        onOk: async () => {
          setSaveLoading(true);
          const url = `${serveCode}/v1/${tenantId}/mt-router/confirm/save/ui`;
          setSaveLoading(false);
          const data = {
            ...getFormValue(),
            mtRouterStepDTO: list,
            errorMessage: _errorMessage,
          };
          const res = await myInstance.post(url, data);
          if (res.data.success) {
            notification.success({});
            getRouterDetails(res.data.rows);
            history.push(`${detailUrl}/${res.data.rows}`);
          } else {
            notification.error({
              message:
                res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
        },
        onCancel() { },
        cancelText: intl.get('tarzan.common.button.cancel').d('取消'),
        okText: intl.get('tarzan.common.button.confirm').d('确定'),
      });
    } else {
      notification.error({
        message: res.data.message || intl.get('hzero.common.notification.error').d('操作失败'),
      });
    }
  };

  /**
   *  路径选择-抽屉
   */
  const openPathDrawer = () => {
    const newList = operateData(detailTableDs.toData());
    // @ts-ignore
    setStepsList(newList);
    const pathDrawerProps = {
      editStatus,
      routerId,
      componentLov,
      tenantId,
      routesItem, // 表单详情
      stepsList: newList, // 步骤列表
      UpdateStepList, // 更新StepList
      nextStepDecisionList,
      serveCode,
      currentBomId, // 当前的bomId
    };

    _Modal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.path`).d('路径选择'),
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <PathDrawer ref={openPathDrawerRef} {...pathDrawerProps} />
        </>
      ),
      footer: openPathDrawerFooter(false),
    });
  };

  /**
   *  路径选择-抽屉-footer
   */
  const openPathDrawerFooter = (val: boolean) => {
    return (
      <>
        <div style={{ float: 'right' }}>
          {!editStatus ? (
            <>
              <Button onClick={closeModal}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button
                onClick={openPathDrawerFooterOk}
                type={ButtonType.submit}
                color={ButtonColor.primary}
                loading={val}
              >
                {intl.get('tarzan.common.button.confirm').d('确定')}
              </Button>
            </>
          ) : (
            <Button onClick={closeModal}>{intl.get('tarzan.common.button.back').d('返回')}</Button>
          )}
        </div>
      </>
    );
  };

  /**
   *  路径选择-抽屉-ok按钮
   */
  const openPathDrawerFooterOk = () => {
    // @ts-ignore
    openPathDrawerRef.current.onOk();
  };

  /**
   *@description 右上角取消按钮，取消编辑工艺路线
   * case1: 新建页面进来，返回至列表页
   * case2: 详情页取消，重新加载所有数据
   */
  const handleCancel = () => {
    if (routerId === 'create') {
      history.push(`${listUrl}`);
    } else {
      // 重新获取工艺路线明细信息
      initFun(routerId);
    }
  };

  /**
   *@description 点击「编辑」按钮，切换状态
   */
  const changeStatus = () => {
    setCanEdit(!canEdit);
  };

  /**
   * 跳转到工艺路线图形化
   */
  const goToFlow = () => {
    history.push(`${GraphicalUrl}/${params.id}?type=officialDataDraft`);
  };

  /**
   * 定义弹窗(好几个抽屉公用
   */
  let _Modal;

  // 关闭弹窗
  const closeModal = () => {
    _Modal.close();
  };

  /**
   * 步骤信息抽屉
   * @param record: 行的数据
   */
  const handleAddTableData = record => {
    let data = {};
    if (record) {
      data = record.toData();
    }

    /**
     * 步骤抽屉需要的数据
     */
    const NewStepDrawerProps = {
      editStatus: !editStatus,
      dataSource: data || {},
      stepsList, // 步骤列表
      routerLov,
      UpdateStepList,
      closeModal,
      serveCode,
      operationLov,
      tenantId,
      selectedRouterType,
      methodServeCode,
      customizeForm,
      custCode,
    };

    _Modal = Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.process.routes.title.stepDrawer').d('步骤信息'),
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <NewStepDrawer ref={newStepDrawerRef} {...NewStepDrawerProps} />
        </>
      ),
      footer: handleNewStepDrawerFooter(),
    });
  };

  /**
   * 步骤信息-抽屉的footer
   */
  const handleNewStepDrawerFooter = () => {
    return (
      <>
        <div style={{ float: 'right' }}>
          {!editStatus ? (
            <>
              <Button onClick={closeModal}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button
                onClick={handleNewStepDrawerFooterOk}
                type={ButtonType.submit}
                color={ButtonColor.primary}
              >
                {intl.get('tarzan.common.button.confirm').d('确定')}
              </Button>
            </>
          ) : (
            <Button onClick={closeModal}>{intl.get('tarzan.common.button.back').d('返回')}</Button>
          )}
        </div>
      </>
    );
  };

  /**
   * 步骤信息-抽屉的保存按钮
   */
  const handleNewStepDrawerFooterOk = () => {
    // @ts-ignore
    newStepDrawerRef.current.handleOK();
  };

  /**
   * 详情页表格-删除步骤
   */
  const deleteRecord = async record => {
    await detailTableDs.remove(record);
    let currentList = detailTableDs.toData();
    const currentRouterStepId = record.get('routerStepId');
    const currentRouterStepGroupStepId = record.get('routerStepGroupStepId');
    if (currentRouterStepId) {
      (currentList || []).forEach((item: any) => {
        (item.mtRouterStepGroupStepDTO || []).forEach((it, index) => {
          if (it.routerStepGroupStepId === currentRouterStepGroupStepId) {
            (item.mtRouterStepGroupStepDTO || []).splice(index, 1);
          }
        });
        item.mtRouterStepGroupDTO = {
          ...item.mtRouterStepGroupDTO,
          mtRouterStepGroupStepDTO: item.mtRouterStepGroupStepDTO,
        };
      });
    } else {
      currentList = currentList.filter((item: any) => item.routerStepId !== currentRouterStepId);
    }
    for (let index = 0; index < currentList.length; index++) {
      const item: any = currentList[index];
      if ((item.mtRouterNextStepDTO || []).length > 0) {
        item.mtRouterNextStepDTO = [];
      }
    }
    // @ts-ignore
    setStepsList(operateData(currentList));
    detailTableDs.loadData([...operateData(currentList)]);
  };

  const fomatFloat = (src, pos) => {
    // eslint-disable-next-line
    return Math.round(src * Math.pow(10, pos)) / Math.pow(10, pos);
  };

  const detailTableColumn = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={editStatus}
          onClick={() => {
            handleAddTableData(false);
          }}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `tarzan${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      name: 'editColumn',
      align: 'center',
      width: 130,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={editStatus}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
    },
    {
      name: 'sequence',
      width: 120,
      editor: () => !editStatus && <NumberField step={1} dataSet={detailTableDs} name="sequence" />,
    },
    {
      name: 'description',
      width: 150,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              handleAddTableData(record);
            }}
          >
            {record.get('stepName') ? `${value} / ${record.get('stepName')}` : value}
          </a>
        );
      },
    },
    {
      name: 'routerDoneStepFlag',
      width: 200,
      renderer: ({ record }) => {
        const mtRouterOperationDTO = record.get('mtRouterOperationDTO');
        const mtRouterLinkDTO = record.get('mtRouterLinkDTO');
        if (mtRouterOperationDTO) {
          return (mtRouterOperationDTO || {}).operationName && (mtRouterOperationDTO || {}).revision
            ? `${(mtRouterOperationDTO || {}).operationName} / ${mtRouterOperationDTO.revision}`
            : '-';
        }
        if (mtRouterLinkDTO) {
          return ((mtRouterLinkDTO || {}).stepName || (mtRouterLinkDTO || {}).routerName) &&
            (mtRouterLinkDTO || {}).revision
            ? `${mtRouterLinkDTO.stepName || (mtRouterLinkDTO || {}).routerName}
             / ${mtRouterLinkDTO.revision}`
            : '-';
        }
        return '-';
      },
    },
    {
      name: 'routerStepType',
      width: 160,
      renderer: ({ value }) => {
        // @ts-ignore
        return (stepOption.filter((ele: any) => ele.typeCode === value)[0] || {}).description;
      },
    },
    {
      name: 'entryStepFlag',
      width: 240,
      renderer: ({ record }) => {
        return (
          <>
            <Tag className={record.get('keyStepFlag') === 'Y' ? 'hcm-tag-green' : 'hcm-tag-red'}>
              {record.get('keyStepFlag') === 'Y'
                ? intl.get(`${modelPrompt}.keyStep`).d('关键')
                : intl.get(`${modelPrompt}.NotKeyStep`).d('非关键')}
            </Tag>
            {record.get('entryStepFlag') === 'Y' && (
              <Tag className="hcm-tag-blue">{intl.get(`${modelPrompt}.entryStep`).d('入口')}</Tag>
            )}
            {record.get('mtRouterReturnStepDTO')?.returnType && (
              <Tag className="hcm-tag-blue">{intl.get(`${modelPrompt}.returnStep`).d('返回')}</Tag>
            )}
            {record.get('routerDoneStepFlag') === 'Y' && (
              <Tag className="hcm-tag-blue">
                {intl.get(`${modelPrompt}.routerDoneStep`).d('完成')}
              </Tag>
            )}
          </>
        );
      },
    },
    {
      name: 'requiredTimeInProcess',
      width: 130,
      renderer: ({ record }) => {
        const data = record.get('mtRouterOperationDTO');
        if (data?.requiredTimeInProcess) {
          return fomatFloat(data?.requiredTimeInProcess, 6);
        }
        return '-';
      },
    },
    {
      width: 250,
      header: intl.get(`tarzan.common.label.action`).d('操作'),
      lock: 'right',
      align: 'center',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              disabled={record.get('routerStepType') !== 'OPERATION'}
              onClick={() => setOperationComponent(record)}
            >
              {intl.get(`${modelPrompt}.operationComponent`).d('工艺组件')}
            </a>
            <a
              onClick={() => setChildStep(record)}
              disabled={record.get('routerStepType') !== 'OPERATION'}
            >
              {intl.get(`${modelPrompt}.operationChildStep`).d('工艺子步骤')}
            </a>
            {record.get('_status') === 'create' ? (
              <a disabled>{intl.get('tarzan.common.button.extendedField').d('扩展属性')}</a>
            ) : (
              <AttributeDrawer
                type="text"
                canEdit={!editStatus}
                disabled={!record.get('routerStepId')}
                kid={record.get('routerStepId')}
                // tablename="mt_router_step_attr"
                className="org.tarzan.method.domain.entity.MtRouterStep"
                serverCode={serveCode}
                custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.${custCode}.BUTTON_STEP`}
                custConfig={custConfig}
              />
            )}
          </span>
        );
      },
    },
  ];

  /**
   * 工艺步骤-工艺组件抽屉
   * @param record
   */
  const setOperationComponent = record => {
    const data = record.toData();
    const { routerStepId } = data;
    const newList: Array<object> = [];
    (stepsList || []).forEach((item: any) => {
      if (item.routerStepType === 'OPERATION') {
        // 如果是工艺类型则直接取出来
        newList.push({ ...item });
      } else if (
        item.routerStepType === 'GROUP' &&
        ((item.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO || []).length > 0
      ) {
        item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.forEach(it => {
          newList.push({ ...it }); // 是步骤组类型且子步骤不为空,取步骤组下的工艺,
        });
      }
    });

    let currentData: any;
    (newList || []).forEach((item: any) => {
      if (routerStepId === item.routerStepId) {
        currentData = item;
        currentDataSource = item;
      }
    });
    tableList = newList;
    const { description, stepName } = currentData;

    const operationComponentDrawerProps = {
      currentDataSource: currentData,
      description,
      stepName,
    };

    _Modal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.operationComponent`).d('工艺组件'),
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <OperationComponentDrawer {...operationComponentDrawerProps} />
        </>
      ),
      footer: operationComponentDrawerFooter(),
    });
  };

  /**
   * 工艺组件-展示抽屉
   */
  const operationComponentDrawerFooter = () => {
    return (
      <>
        <div style={{ float: 'right' }}>
          {
            <>
              <Button onClick={handleBack}>
                {intl.get(`${modelPrompt}.backOperation`).d('上一工艺')}
              </Button>
              <Button onClick={handleNext}>
                {intl.get(`${modelPrompt}.nextOperation`).d('下一工艺')}
              </Button>
              <Button onClick={closeModal}>
                {intl.get('tarzan.common.button.back').d('返回')}
              </Button>
            </>
          }
        </div>
      </>
    );
  };

  /**
   * 工艺组件-展示抽屉-上一工艺
   */
  const handleBack = () => {
    const { routerStepId } = currentDataSource;
    let currentData: any;
    tableList.forEach((item: any, index) => {
      if (routerStepId === item.routerStepId) {
        currentData = index > 0 ? tableList[index - 1] : tableList[tableList.length - 1];
        currentDataSource = currentData;
      }
    });

    const { description, stepName } = currentData;
    const operationComponentDrawerProps = {
      currentDataSource: currentData,
      description,
      stepName,
    };

    _Modal.update({
      children: (
        <>
          <OperationComponentDrawer {...operationComponentDrawerProps} />
        </>
      ),
    });
  };

  /**
   * 工艺组件-展示抽屉-下一工艺
   */
  const handleNext = () => {
    const { routerStepId } = currentDataSource;
    let currentData: any;
    tableList.forEach((item: any, index) => {
      if (routerStepId === item.routerStepId) {
        currentData = tableList.length > index + 1 ? tableList[index + 1] : tableList[0] || {};
        currentDataSource = currentData;
      }
    });

    const { description, stepName } = currentData;
    const operationComponentDrawerProps = {
      currentDataSource: currentData,
      description,
      stepName,
    };

    _Modal.update({
      children: (
        <>
          <OperationComponentDrawer {...operationComponentDrawerProps} />
        </>
      ),
    });
  };

  /**
   * 工艺步骤-子步骤抽屉
   * @param record
   */
  const setChildStep = record => {
    const data = record.toData();
    const { routerStepId } = data;

    const newList: Array<object> = [];
    (stepsList || []).forEach((item: any) => {
      if (item.routerStepType === 'OPERATION') {
        // 如果是工艺类型则直接取出来
        newList.push({ ...item });
      } else if (
        item.routerStepType === 'GROUP' &&
        ((item.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO || []).length > 0
      ) {
        item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.forEach(it => {
          newList.push({ ...it }); // 是步骤组类型且子步骤不为空,取步骤组下的工艺,
        });
      }
    });

    let currentData: any;
    (newList || []).forEach((item: any) => {
      if (routerStepId === item.routerStepId) {
        currentData = item;
        currentDataSource = item;
      }
    });
    tableList = newList;
    const { description, stepName } = currentData;
    const dataArr = detailTableDs.toData().filter((i: any) => i.routerStepId === routerStepId);

    const childStepDrawerProps = {
      currentDataSource: { ...dataArr[0] },
      description,
      stepName,
      path,
      canEdit: !editStatus,
      closeModal,
      childStepDrawerDs,
      operateData,
      detailTableDs,
      UpdateStepList,
    };

    _Modal = Modal.open({
      ...drawerPropsC7n({
        canEdit: !editStatus,
        ds: childStepDrawerDs,
      }),
      key: Modal.key(),
      title: intl.get('tarzan.process.routes.title.childStep').d('子步骤信息'),
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <ChildStepDrawer ref={childStepDrawerRef} {...childStepDrawerProps} />
        </>
      ),
      footer: ChildStepDrawerFooter(),
    });
  };

  /**
   * 工艺子步骤-展示抽屉
   */
  const ChildStepDrawerFooter = () => {
    return (
      <>
        <div style={{ float: 'right' }}>
          {editStatus ? (
            <>
              <Button onClick={handleBackChildStep}>
                {intl.get(`${modelPrompt}.backOperation`).d('上一工艺')}
              </Button>
              <Button onClick={handleNextChildStep}>
                {intl.get(`${modelPrompt}.nextOperation`).d('下一工艺')}
              </Button>
              <Button onClick={closeModal}>
                {intl.get('tarzan.common.button.back').d('返回')}
              </Button>
            </>
          ) : (
            <>
              <Button onClick={closeModal}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button onClick={handleSaveListChildStep} color={ButtonColor.primary}>
                {intl.get('tarzan.common.button.confirm').d('确认')}
              </Button>
            </>
          )}
        </div>
      </>
    );
  };

  /**
   * 工艺子步骤-展示抽屉-保存
   */
  const handleSaveListChildStep = () => {
    // @ts-ignore
    childStepDrawerRef.current.handleOK();
  };

  /**
   * 工艺子步骤-展示抽屉-上一工艺
   */
  const handleBackChildStep = () => {
    const { routerStepId } = currentDataSource;
    let currentData: any;
    tableList.forEach((item: any, index) => {
      if (routerStepId === item.routerStepId) {
        currentData = index > 0 ? tableList[index - 1] : tableList[tableList.length - 1];
        currentDataSource = currentData;
      }
    });

    const { description, stepName } = currentData;
    const dataArr = detailTableDs
      .toData()
      .filter((i: any) => i.routerStepId === currentDataSource.routerStepId);

    const childStepDrawerProps = {
      currentDataSource: { ...dataArr[0] },
      description,
      stepName,
      path,
      canEdit: !editStatus,
      closeModal,
      childStepDrawerDs,
    };

    _Modal.update({
      children: (
        <>
          <ChildStepDrawer ref={childStepDrawerRef} {...childStepDrawerProps} />
        </>
      ),
    });
  };

  /**
   * 工艺子步骤-展示抽屉-下一工艺
   */
  const handleNextChildStep = () => {
    const { routerStepId } = currentDataSource;
    let currentData: any;
    tableList.forEach((item: any, index) => {
      if (routerStepId === item.routerStepId) {
        currentData = tableList.length > index + 1 ? tableList[index + 1] : tableList[0] || {};
        currentDataSource = currentData;
      }
    });

    const { description, stepName } = currentData;
    const dataArr = detailTableDs
      .toData()
      .filter((i: any) => i.routerStepId === currentDataSource.routerStepId);
    const childStepDrawerProps = {
      currentDataSource: { ...dataArr[0] },
      description,
      stepName,
      path,
      canEdit: !editStatus,
      closeModal,
      childStepDrawerDs,
    };

    _Modal.update({
      children: (
        <>
          <ChildStepDrawer ref={childStepDrawerRef} {...childStepDrawerProps} />
        </>
      ),
    });
  };

  // 站点select的下拉事件
  const handleSite = (value = []) => {
    // 站点为空或者改变时，需要清除装配清单的值，并且将超链接置灰
    if (value !== selectSiteIds) {
      // 修改bomId
      formDs.current?.set('bomLov', {});
      setHasBomId(false);
      setCurrentBomId('');
      setRoutesItem({
        ...routesItem,
        bomId: '',
      });
      clearComponentDis();
    }
    setSelectSiteIds(value);
  };

  const expandIconTitle = (
    <a onClick={goToFlow} className={styles['flow-content']}>
      <img alt="" src={jumpToFlow} className={styles['flow-img']} />
      <span>{intl.get(`${modelPrompt}.graphicalFlag`).d('图形化标识')}</span>
    </a>
  );

  /**
   * 更新StepList
   * @param val
   */
  const UpdateStepList = val => {
    val.forEach((i: any) => {
      if (i.mtRouterStepGroupDTO?.routerStepGroupId) {
        i.mtRouterStepGroupStepDTO = i.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO;
      }
    });
    setStepsList(val);
    detailTableDs.loadData([...val]);
  };

  /**
   * 处理打平后的数据
   * @param list
   */
  const operateData = list => {
    const newList: Array<object> = [];
    list.forEach((i: any) => {
      if (!i.routerStepGroupStepId) {
        newList.push(i);
      }
    });
    newList.forEach((i: any) => {
      if (i.mtRouterStepGroupDTO?.routerStepGroupId) {
        const newGroupList: Array<object> = [];
        i.mtRouterStepGroupDTO?.mtRouterStepGroupStepDTO.forEach((j: any) => {
          list.forEach((q: any) => {
            if (j.routerStepGroupStepId === q.routerStepGroupStepId) {
              newGroupList.push({
                ...j,
                ...q,
              });
            }
          });
        });
        i.mtRouterStepGroupDTO = {
          ...i.mtRouterStepGroupDTO,
          mtRouterStepGroupStepDTO: [...newGroupList],
        };
      }
    });
    return newList;
  };

  /**
   * 更新CompList
   * @param val
   */
  const UpdateCompList = val => {
    setCompList(val);
  };

  /**
   * 组件分配-抽屉
   */
  const openComponentDistributionDrawer = () => {
    const newList = operateData(detailTableDs.toData());
    // @ts-ignore
    setStepsList(newList);
    const componentDistributionDrawerProps = {
      editStatus,
      routerId,
      stepsList: newList, // 步骤列表
      compList,
      serveCode,
      tenantId,
      UpdateCompList,
      UpdateStepList,
      currentBomId,
    };

    _Modal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.componentDistribution`).d('组件分配'),
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <ComponentDistributionDrawer
            ref={openComponentDistributionDrawerRef}
            {...componentDistributionDrawerProps}
          />
        </>
      ),
      footer: openComponentDistributionDrawerFooter(false),
    });
  };

  /**
   * 组件分配-抽屉的footer
   * @param val
   */
  const openComponentDistributionDrawerFooter = (val: boolean) => {
    return (
      <>
        <div style={{ float: 'right' }}>
          {!editStatus ? (
            <>
              <Button onClick={closeModal}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
              <Button
                onClick={openComponentDistributionDrawerFooterOk}
                type={ButtonType.submit}
                color={ButtonColor.primary}
                loading={val}
              >
                {intl.get('tarzan.common.button.confirm').d('确定')}
              </Button>
            </>
          ) : (
            <Button onClick={closeModal}>{intl.get('tarzan.common.button.back').d('返回')}</Button>
          )}
        </div>
      </>
    );
  };

  /**
   * 组件分配OKbutton
   */
  const openComponentDistributionDrawerFooterOk = () => {
    // @ts-ignore
    openComponentDistributionDrawerRef.current.onOk();
  };

  /**
   * 分配对象-抽屉
   */
  const openObjectDrawer = () => {
    allocateObjectDs.setQueryParameter('routerId', routerId);
    allocateObjectDs.query();
    _Modal = Modal.open({
      ...drawerPropsC7n({
        ds: allocateObjectDs,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get('tarzan.process.routes.title.operationComponent').d('已分配对象'),
      destroyOnClose: true,
      style: {
        width: 720,
      },
      onOk: handleCopyDrawerOk,
      children: (
        <Table dataSet={allocateObjectDs} columns={allocateObjectColumn as ColumnProps[]} />
      ),
      footer: allocateObjectFooter(),
    });
  };

  /**
   * 分配对象-抽屉-footer
   */
  const allocateObjectFooter = () => {
    return (
      <>
        <div style={{ float: 'right' }}>
          {<Button onClick={closeModal}>{intl.get('tarzan.common.button.back').d('返回')}</Button>}
        </div>
      </>
    );
  };

  /**
   * 分配对象-表格-列配置
   */
  const allocateObjectColumn = [
    {
      name: 'objectCode',
      renderer: ({ value, record }) => {
        return <a onClick={() => goToLink(record)}>{value}</a>;
      },
    },
    { name: 'objectTypeDesc' },
    {
      name: 'objectStatus',
      renderer: ({ value }) => {
        return value || '-';
      },
    },
  ];

  /**
   * 分配对象-表格-跳转
   */
  const goToLink = record => {
    const objectType = record.get('objectType');
    const objectId = record.get('objectId');
    const key =
      (objectType === 'MATERIAL' && `/hmes/product/material-manager`) ||
      (objectType === 'WO' && `/hmes/workshop/production-order-mgt`) ||
      (objectType === 'EO' && `/hmes/workshop/execute-operation-management`);
    const path =
      (objectType === 'MATERIAL' && `/hmes/product/material-manager/dist/${objectId}`) ||
      (objectType === 'WO' && `/hmes/workshop/production-order-mgt/detail/${objectId}`) ||
      (objectType === 'EO' && `/hmes/workshop/execute-operation-management/detail/${objectId}`);
    const title =
      (objectType === 'MATERIAL' &&
        `${intl.get('tarzan.product.materialManager.title.list').d('物料维护')}`) ||
      (objectType === 'WO' &&
        `${intl
          .get('tarzan.workshop.productionOrderMgt.model.productionOrderMgt.productionOrderMgt')
          .d('生产指令管理')}`) ||
      (objectType === 'EO' &&
        `${intl.get('tarzan.workshop.execute.title.list').d('执行作业管理')}`);
    // @ts-ignore
    openTab({ title, key, path, closable: true });
  };

  /**
   * 复制按钮
   */
  const copyDrawer = () => {
    Modal.open({
      ...drawerPropsC7n({
        ds: copyDrawerDs?.current,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get('tarzan.process.routes.title.copy').d('工艺路线复制'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: handleCopyDrawerOk,
      children: (
        <Form dataSet={copyDrawerDs} columns={1}>
          <TextField name="targetRouterName" />
          <TextField name="targetRevision" />
          <Select name="targetRouterType" />
        </Form>
      ),
    });
  };

  // 复制按钮后的回调
  const handleCopyDrawerOk = async () => {
    return new Promise(async resolve => {
      const valRes = await copyDrawerDs.validate();
      if (!valRes) {
        return resolve(false);
      }
      return handleCopyRoute({
        params: {
          ...copyDrawerDs.toData()[0],
          sourceRouterId: routerId,
          targetSiteId: selectSiteIds,
        },
        onSuccess: res => {
          notification.success({});
          history.push(`${detailUrl}/${res}`);
          return resolve(true);
        },
        onFailed: () => {
          return resolve(false);
        },
      });
    });
  };

  /**
   * 装配清单change事件
   * @param val
   */
  const changeBomId = val => {
    if (!val) {
      setHasBomId(false);
      setCurrentBomId('');
      setRoutesItem({
        ...routesItem,
        bomId: '',
      });
    } else {
      setHasBomId(true);
      setCurrentBomId(val.bomId);
      setRoutesItem({
        ...routesItem,
        bomId: val.bomId,
      });
    }
    clearComponentDis();
  };

  // 情况组件分配相关信息
  const clearComponentDis = () => {
    const newStepsList = stepsList.map((item: any) => {
      const step = { ...item };
      if ((item.mtRouterOperationDTO || {}).mtRouterOperationComponentDTO) {
        step.mtRouterOperationDTO.mtRouterOperationComponentDTO = [];
      }
      if (
        item.routerStepType === 'GROUP' &&
        ((item.mtRouterStepGroupDTO || {}).mtRouterStepGroupStepDTO || []).length > 0
      ) {
        const stepGroupStep = item.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO.map(it => {
          return {
            ...it,
            mtRouterOperationDTO: {
              ...it.mtRouterOperationDTO,
              mtRouterOperationComponentDTO: [],
            },
          };
        });
        step.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO = stepGroupStep;
        step.mtRouterStepGroupStepDTO = stepGroupStep;
      }
      return step;
    });
    // @ts-ignore
    setStepsList(newStepsList);
    detailTableDs.loadData(newStepsList);
    setCompList([]);
  };

  /**
   *  跳转到装配清单
   */
  const goToBom = () => {
    const data: Array<any> = formDs.toData();
    history.push(`${AssemblyUrl}/${data[0].bomId}`);
  };

  /**
   *  跳转到来源工艺路线
   */
  const goToRouter = () => {
    const data: Array<any> = formDs.toData();
    const copiedFromService = data[0].copiedFromService;
    if (copiedFromService === 'MES') {
      // 跳转到制造工艺路线
      history.push(`/hmes/new/manufacture-process/routes-c7n/dist/${data[0].copiedFromRouterId}`);
    } else if (copiedFromService === 'APS') {
      // 跳转到APS工艺路线
      history.push(`/aps/orderRouting-c7n/dist/${data[0].copiedFromRouterId}`);
    } else if (copiedFromService === 'METHOD') {
      // 跳转到物料工艺路线
      history.push(`/hmes/new/process/routes-c7n/dist/${data[0].copiedFromRouterId}`);
    } else {
      // 为空的话跳转到自身
      history.push(`${detailUrl}/${data[0].copiedFromRouterId}`);
    }
  };

  /**
   * 切换自动升版本标识
   * @param val
   */
  const changeAutoRevisionFlag = val => {
    formDs.current!.set('autoRevisionFlag', val);
    formDs.current!.set('revision', '');
  };

  /**
   * 切换工艺类型
   * @param value
   */
  const changeRouterType = value => {
    formDs.current?.set('bomRevision', '');
    formDs.current?.set('bomId', '');
    formDs.current?.set('bomName', '');
    setHasBomId(false);
    // @ts-ignore
    if (
      (['SPECIAL', 'NC'].includes(selectedRouterType) && ![value].includes(selectedRouterType)) ||
      (!['SPECIAL', 'NC'].includes(selectedRouterType) && [value].includes(selectedRouterType))
    ) {
      UpdateStepList([]);
    }
    setSelectedRouterType(value);
    setCompList([]);
    setRoutesItem({
      ...routesItem,
      bomRevision: undefined,
      bomName: undefined,
      bomId: undefined,
    });
    clearComponentDis();
  };

  return (
    <div className="hmes-style">
      <Header title={featureTitle} backPath={`${basePath}/list`}>
        {canEdit ? (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSaveList}
              loading={saveLoading}
              permissionList={[
                {
                  code: `tarzan${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button color={ButtonColor.default} icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={changeStatus}
            disabled={routerStatus === 'ABANDON' || state?.from === 'productionOrderMgt'}
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          icon="content_copy-o"
          onClick={copyDrawer}
          disabled={routerId === 'create' || !editStatus}
          permissionList={[
            {
              code: `tarzan${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.copy').d('复制')}
        </PermissionButton>
        <AttributeDrawer
          canEdit={!editStatus}
          disabled={routerId === 'create'}
          kid={routerId}
          // tablename="mt_router_attr"
          className="org.tarzan.method.domain.entity.MtRouter"
          serverCode={serveCode}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.${custCode}.BUTTON`}
          custConfig={custConfig}
        />
        <Button disabled={routerId === 'create'} onClick={openObjectDrawer} icon="FRqueren-o">
          {intl.get(`${modelPrompt}.setSites`).d('分配对象')}
        </Button>
        <Button onClick={openComponentDistributionDrawer} icon="fenpeiquanxian-o">
          {intl.get(`${modelPrompt}.componentDistribution`).d('组件分配')}
        </Button>
        <Button onClick={openPathDrawer} icon="fenpei-o">
          {intl.get(`${modelPrompt}.path`).d('路径选择')}
        </Button>
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get('tarzan.process.routes.title.detail').d('工艺路线基础信息')}
            extra={routerId && routerId !== 'create' && expandIconTitle}
            key="basicInfo"
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.BASIC`,
              },
              <Form dataSet={formDs} columns={3} labelWidth={112}>
                <TextField name="routerName" disabled={editStatus} />
                <TextField name="revision" disabled={editStatus} />
                <Switch name="currentFlag" disabled={editStatus} />
                <Select
                  disabled={editStatus}
                  name="site"
                  searchable
                  colSpan={2}
                  onChange={handleSite}
                  onOption={({ record }) => {
                    const disabled = !record.get('permissionFlag');
                    return {
                      disabled,
                    };
                  }}
                />
                <Switch name="relaxedFlowFlag" disabled={editStatus} />
                <TextField name="description" disabled={editStatus} />
                <Select
                  clearButton={false}
                  name="routerStatus"
                  disabled={
                    !canEdit ||
                    (routesItem?.usageFlag === 'Y' && routesItem?.routerStatus !== 'HOLD') ||
                    routesItem?.routerStatus === 'ABANDON'
                  }
                />
                <Switch name="usageFlag" disabled={editStatus || routerId === 'create'} />
                <Select name="routerType" disabled={editStatus} onChange={changeRouterType} />
                <C7nFormItemSort name="bomLov" itemWidth={['90%', '10%']}>
                  <Lov
                    name="bomLov"
                    disabled={editStatus || !(selectSiteIds || []).length}
                    onChange={changeBomId}
                  />
                  <Button
                    funcType={FuncType.link}
                    icon="link"
                    onClick={goToBom}
                    disabled={!hasBomId}
                  />
                </C7nFormItemSort>
                <Switch
                  disabled={editStatus || routerId !== 'create'}
                  name="autoRevisionFlag"
                  onChange={changeAutoRevisionFlag}
                />
                <DateTimePicker name="dateFrom" disabled={editStatus} />
                <DateTimePicker name="dateTo" disabled={editStatus} />
                <C7nFormItemSort name="copiedFromRouterName" itemWidth={['70%', '15%', '15%']}>
                  <TextField name="copiedFromRouterName" disabled />
                  <TextField name="copiedFromRouterRevision" disabled />
                  <Button
                    funcType={FuncType.link}
                    icon="link"
                    onClick={goToRouter}
                    disabled={
                      routerId === 'create' || !formDs.current?.get('copiedFromRouterRevision')
                    }
                  />
                </C7nFormItemSort>
              </Form>,
            )}
          </Panel>
        </Collapse>
        <Collapse bordered={false} defaultActiveKey={['routerStep']}>
          <Panel header={intl.get(`${modelPrompt}.routerStep`).d('工艺步骤')} key="routerStep">
            {
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.STEP_LIST`,
                },
                <Table
                  disabled={editStatus}
                  dataSet={detailTableDs}
                  columns={detailTableColumn as ColumnProps[]}
                  mode={TableMode.tree}
                  indentSize={30}
                  defaultRowExpanded
                />,
              )
            }
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.process.routes', 'tarzan.common'],
  // @ts-ignore
})(RoutesDist);
