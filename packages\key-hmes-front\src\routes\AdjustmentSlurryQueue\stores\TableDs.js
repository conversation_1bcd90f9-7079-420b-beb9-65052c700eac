import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

// const Host = '/mes-38546'
const modelPrompt = 'tarzan.hmes.adjustmentSlurryQueue';
const tenantId = getCurrentOrganizationId();
const tableDS = () => {
  return {
    name: 'tableDS',
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'workCellLov',
        type: 'object',
        lovCode: 'HME.TB_WORKCELL',
        ignore: 'always',
        required:true,
        label: intl.get(`${modelPrompt}.table.workCellCode`).d('涂布工位编码'),
      },
      {
        name: 'workCellId',
        type: 'number',
        bind: 'workCellLov.workCellId',
      },
      {
        name: 'workCellName',
        type: 'string',
        bind: 'workCellLov.workCellName',
        label: intl.get(`${modelPrompt}.table.workCellName`).d('涂布工位描述'),
      },
      {
        name: 'workCellCode',
        type: 'string',
        bind: 'workCellLov.workCellCode',
        label: intl.get(`${modelPrompt}.table.workCellCode`).d('涂布工位编码'),
      },
      {
        name: 'equipmentCodeTb',
        type: 'string',
        bind: 'workCellLov.equipmentCodeTb',
        label: intl.get(`${modelPrompt}.table.equipmentCodeTb`).d('涂布机设备编码'),
      },
      {
        name: 'equipmentNameTb',
        bind: 'workCellLov.equipmentNameTb',
        type: 'string',
        label: intl.get(`${modelPrompt}.table.equipmentNameTb`).d('涂布机设备描述'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.table.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.table.materialName`).d('物料描述'),
      },
      {
        name: 'materialLotCode',
        required: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.table.materialLotCode`).d('浆料条码'),
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.table.revisionCode`).d('物料版本'),
      },
      {
        name: 'materialLotSequence',
        type: 'number',
        min: 1,
        required: true,
        label: intl.get(`${modelPrompt}.table.materialLotSequence`).d('顺序'),
      },
      {
        name: 'creationDate',
        type: 'dateTime',
        // required: true,
        label: intl.get(`${modelPrompt}.table.creationDate`).d('入罐时间'),
        dynamicProps: {
          required: ({record}) => {
            return record.status === 'add'
          },
        },
      },
      {
        name: 'outCreationDate',
        type: 'dateTime',
        required: true,
        label: intl.get(`${modelPrompt}.table.outCreationDate`).d('出罐时间'),
      },
      {
        name: 'equipmentLov',
        required: true,
        type: 'object',
        lovCode: 'HME.FINISHED_PRODUCT_TANK',
        ignore: 'always',
        textField: 'value',
        label: intl.get(`${modelPrompt}.table.equipmentLov`).d('成品罐设备编码'),
      },
      {
        name: 'equipmentCode',
        type: 'string',
        bind: 'equipmentLov.value',
      },
      {
        name: 'equipmentName',
        type: 'string',
        bind: 'equipmentLov.meaning',
        label: intl.get(`${modelPrompt}.table.equipmentName`).d('成品罐设备描述'),
      },
    ],
    queryFields: [
      {
        name: 'workCellObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.workCell`).d('工作单元'),
        lovCode: 'HME.TB_WORKCELL',
        labelWidth: 150,
        ignore: 'always',
      },
      {
        name: 'workCellCode',
        type: 'string',
        bind: 'workCellObj.workCellCode',
      },
      {
        name: 'workCellId',
        type: 'string',
        bind: 'workCellObj.workCellId',
      },
      {
        name: 'workCellName',
        type: 'string',
        bind: 'workCellObj.workCellName',
      },
      // {
      //   name: 'assemblePointObj',
      //   type: 'object',
      //   label: intl.get(`${modelPrompt}.assemblePoint`).d('装配点'),
      //   lovCode: 'HME.ASSEMBLE_POINT',
      //   ignore: 'always',
      //   labelWidth: 150,
      //   dynamicProps: {
      //     lovPara() {
      //       return {
      //         tenantId: getCurrentOrganizationId(),
      //       };
      //     },
      //   },
      // },
      // {
      //   name: 'assemblePointId',
      //   type: 'string',
      //   bind: 'assemblePointObj.assemblePointId',
      // },
      // {
      //   name: 'assemblePointCode',
      //   type: 'string',
      //   bind: 'assemblePointObj.assemblePointCode',
      // },
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('条码'),
      },
      {
        name: 'startTime',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.startTime`).d('入罐开始时间'),
        dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      {
        name: 'endTime',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.endTime`).d('入罐结束时间'),
        min: 'startTime',
        dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-slurry-sequence-adjust-new/query/for/ui`,
          method: 'GET',
        };
      },
    },
  };
};


export { tableDS };
