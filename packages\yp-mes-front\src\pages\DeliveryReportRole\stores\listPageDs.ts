import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.hmes.deliveryReportRole';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'keyId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'customerLov',
          label: intl.get(`${modelPrompt}.customerLov`).d('客户编码'),
          lovCode: 'YP_MES.CUSTOMER',
          type: FieldType.object,
          textField: 'templateName',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'customerCode',
          bind: 'customerLov.customerCode'
        },
        {
          name: 'customerId',
          bind: 'customerLov.customerId'
        },
        {
          name: 'customerName',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.customerName`).d('客户名称'),
        },
        {
          name: 'supplierInfo',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.supplierInfo`).d('角色'),
        },
      ]
    }),
    fields: [
      {
        name: 'roleLov',
        required: true,
        label: intl.get(`${modelPrompt}.roleLov`).d('角色编码'),
        lovCode: 'AMTC.HOME_SHOW_ROLE',
        type: FieldType.object,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'roleCode',
        bind: 'roleLov.code'
      },
      {
        name: 'roleId',
        bind: 'roleLov.id'
      },
      {
        name: 'roleName',
        type: FieldType.string,
        disabled: true,
        bind: 'roleLov.name',
        label: intl.get(`${modelPrompt}.roleName`).d('角色名称'),
      },
      {
        name: 'customerLov',
        required: true,
        label: intl.get(`${modelPrompt}.customerLov`).d('客户编码'),
        lovCode: 'YP_MES.CUSTOMER',
        type: FieldType.object,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'customerId',
        bind: 'customerLov.customerId'
      },
      {
        name: 'customerCode',
        bind: 'customerLov.customerCode'
      },
      {
        name: 'customerName',
        bind: 'customerLov.customerName',
        disabled: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.customerName`).d('客户名称'),
      },
      {
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'MT.YES_NO',
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'POST',
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-shipment-report-roles/query/table`,
        };
      },
    },
  });

export default listPageFactory;
