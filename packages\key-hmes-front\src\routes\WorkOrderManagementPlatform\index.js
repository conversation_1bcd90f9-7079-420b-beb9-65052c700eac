import React, { useEffect, useState, useMemo } from 'react';
import {
  Button,
  DataSet,
  Table,
  Modal,
  Form,
  NumberField,
  Select,
  Spin,
  Lov,
} from 'choerodon-ui/pro';
import { useRequest } from '@components/tarzan-hooks';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import request from 'utils/request';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { drawerPropsC7n } from '@components/tarzan-ui';
import axios from 'axios';
import {
  tableDS,
  dispatchFormDS,
  splitFormDS,
  levelDS,
  bomAndRouterDS,
  prodDS,
  drawerDS,
  historyDS,
} from './stores/WorkOrderManagementPlatformDS';
import HistoryDrawer from './HistoryDrawer';
import { Host, BASIC } from '@/utils/config';
import BomAndRouterDrawer from './BomAndRouterDrawer';

const tenantId = getCurrentOrganizationId();
// const Host = `/yp-mes-24308`;
const modelPrompt = 'tarzan.hmes.WorkOrderManagementPlatform';

const WorkOrderManagementPlatform = props => {
  const {
    location: { state },
    history,
    match: { path },
  } = props;
  const [selectedLength, setSelectedLength] = useState(0);
  const [selectedRow, setSelectedRow] = useState([]);
  const [loading, setLoading] = useState(false);
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  const dispatchFormDs = useMemo(() => new DataSet(dispatchFormDS()), []); // 复制ds
  const splitFormDs = useMemo(() => new DataSet(splitFormDS()), []); // 复制ds
  const levelDs = useMemo(() => new DataSet(levelDS()), []);
  const bomAndRouterDs = useMemo(() => new DataSet(bomAndRouterDS()), []);
  const prodDs = useMemo(() => new DataSet(prodDS()), []);
  const drawerDs = useMemo(() => new DataSet(drawerDS()), []);
  const historyDs = useMemo(() => new DataSet(historyDS()), []);

  let splitModal;
  let dispatchModal;
  let drawerModal;
  const { run } = useRequest(
    {
      url: `${Host}/v1/${tenantId}/hme-work-order-new/bom/router/update`,
      method: 'POST',
    },
    { manual: true, needPromise: true },
  );

  // 工单下达
  const { run: issueWorkOrded, loading: issueWorkOrdedLoading } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-new/woStatusUpdateRelease`,
      method: 'GET',
    },
    { manual: true, needPromise: true },
  );

  // 工单完成
  const { run: completeWorkOrder, loading: completeWorkOrderLoading } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-new/woStatusUpdateCompleted`,
      method: 'GET',
    },
    { manual: true, needPromise: true },
  );

  useEffect(() => {
    // 添加选中监听事件
    tableDs.addEventListener('select', handleDataSetSelect);
    tableDs.addEventListener('unSelect', handleDataSetSelect);
    tableDs.addEventListener('selectAll', handleDataSetSelect);
    tableDs.addEventListener('unSelectAll', handleDataSetSelect);
    tableDs.addEventListener('query', () => {
      setSelectedLength(0);
      setSelectedRow([]);
    });
  }, []);

  useEffect(() => {
    if (state?.workOrderNum) {
      tableDs.queryDataSet?.loadData([{ workOrderNum: state?.workOrderNum }]);
      tableDs.query(tableDs.currentPage);
      history.replace({ ...history.location, state: undefined });
    }
  }, [history.location.state]);

  // 选中事件
  const handleDataSetSelect = () => {
    setSelectedLength(tableDs.selected.length);
    setSelectedRow(tableDs.selected);
  };

  // 跳转生产指令详情
  const goWorkOrderDeatil = id => {
    props.history.push(`/hmes/workshop/production-order-mgt/detail/${id}`);
  };

  // 跳转制造装配详情
  const goBomDeatil = record => {
    if (record.data.status === 'RELEASED' && record.data.workOrderType === 'REPAIR') {
      props.history.push(`/hmes/product/assembly-list/dist/${record.data.bomId}`);
    } else {
      props.history.push(`/hmes/product/manufacture-list/dist/${record.data.bomId}`);
    }
  };

  // 跳转制造工艺详情
  const goRouterDeatil = record => {
    if (record.data.status === 'RELEASED' && record.data.workOrderType === 'REPAIR') {
      props.history.push(`/hmes/new/process/routes-c7n/dist/${record.data.routerId}`);
    } else {
      props.history.push(`/hmes/new/manufacture-process/routes-c7n/dist/${record.data.routerId}`);
    }
  };

  // 拆分数量弹框
  const openSplitModal = () => {
    splitFormDs.current.init('splitQty', null);
    splitFormDs.reset();
    splitModal = Modal.open({
      key: Modal.key(),
      title: '',
      width: 400,
      destroyOnClose: true,
      children: (
        <Form dataSet={splitFormDs} columns={1} style={{ height: '190px' }}>
          <NumberField name="splitQty" required />
        </Form>
      ),
      footer: (
        <div style={{ float: 'right', marginTop: '-50px' }}>
          <Button
            onClick={() => {
              cancelSplit();
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            color="primary"
            type="submit"
            onClick={() => {
              splitConfirm();
            }}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  // 拆分数量确定
  const splitConfirm = async () => {
    const validateResult = await splitFormDs.validate();
    if (validateResult) {
      const data = splitFormDs.toData()[0];
      const params = {
        workOrderId: selectedRow[0].data.workOrderId,
        ...data,
      };
      const qtyNum = Number(selectedRow[0].data.qty || 0);
      if (Number(data.splitQty) >= qtyNum) {
        return notification.error({
          message: intl
            .get(`${modelPrompt}.error.qtyBiggerworkOrderNumQty`)
            .d(`当前输入数量大于或等于工单${selectedRow[0].data.workOrderNum}数量，请检查！`),
        });
      }
      setLoading(true);
      request(`${Host}/v1/${tenantId}/hme-work-order-new/wo/split/ui`, {
        method: 'POST',
        body: { ...params },
      }).then(res => {
        setLoading(false);
        if (res && res.success) {
          notification.success();
          splitModal.close();
          tableDs.query();
        } else {
          notification.error({ message: res.message });
        }
      });
    }
  };

  // 拆分数量弹框取消
  const cancelSplit = async () => {
    splitModal.close();
  };

  // 派工数量确定
  // const dispatchConfirm = async flag => {
  //   const validateResult = await dispatchFormDs.validate();
  //   if (validateResult) {
  //     const data = dispatchFormDs.toData()[0];
  //     const params = {
  //       workOrderId: selectedRow[0].data.workOrderId,
  //       ...data,
  //     };
  //     // const qtyNum =
  //     //   Number(selectedRow[0].data.qty || 0) - Number(selectedRow[0].data.releasedQty || 0);
  //     // if (Number(data.qty) > qtyNum) {
  //     //   return notification.error({
  //     //     message: `录入的派工数量大于工单${selectedRow[0].data.workOrderNum}的未派工数量，请重新输入！`,
  //     //   });
  //     // }
  //     let url = '';
  //     if (flag === 'RELEASE') {
  //       url = `${Host}/v1/${tenantId}/hme-work-order-new/release`;
  //     } else {
  //       url = `${Host}/v1/${tenantId}/hme-work-order-new/dispatch`;
  //     }
  //     setLoading(true);
  //     request(url, {
  //       method: 'GET',
  //       query: { ...params },
  //     }).then(res => {
  //       setLoading(false)
  //       if (res && res.success) {
  //         notification.success();
  //         tableDs.query();
  //         dispatchModal.close();
  //       } else {
  //         notification.error({ message: res.message });
  //       }
  //     });
  //   }
  // };

  const fetchCancel = async (selectedData, maxConcurrent = 50) => {
    let results = [];
    let i = 0;
    const fetchBatchCancel = async () => {
      const url = `${Host}/v1/${tenantId}/hme-work-order-new/close`;
      const batch = selectedData.slice(i, i + maxConcurrent);
      if (i >= selectedData.length) return;
      const promises = batch.map(item => axios.get(`${url}?workOrderId=${item.workOrderId}`));
      const responses = await Promise.all(promises)
        .then(res => {
          return res;
        })
        .catch(err => {
          notification.error({
            message: err || err.message,
          });
        });
      results = responses;
      i += maxConcurrent;
      await fetchBatchCancel();
    };
    await fetchBatchCancel();
    return results;
  };

  // 停止生产
  const handleProductCancel = () => {
    if (selectedRow.length > 50) {
      return notification.error({
        message: intl.get(`${modelPrompt}.error.tenLength`).d('批量停止生产一次不能超过50条订单'),
      });
    }
    Modal.confirm({
      title: intl.get('tarzan.common.button.notice').d('提示'),
      children: (
        <div>
          <p>确认是否关闭工单？</p>
        </div>
      ),
    }).then(async button => {
      if (button === 'ok') {
        const list = tableDs.selected.map(item => item.toData());
        setLoading(true);
        const dataFlag = await fetchCancel(list).then(data => {
          setLoading(false);
          if (data.length > 0) {
            data.forEach(item => {
              if (item && item.success) {
                if (item.message) {
                  notification.warning({ message: `${list[0].workOrderNum}订单${item.message}` });
                } else {
                  notification.success({ message: `${list[0].workOrderNum}订单操作成功` });
                }
              } else {
                notification.error({ message: `${list[0].workOrderNum}订单${item.message}` });
              }
            });
          }
          return true;
        });
        setLoading(false);
        if (dataFlag) {
          tableDs.query();
        }
      }
    });
  };

  // 上移
  const handleUp = () => {
    const params = {
      workOrderId: selectedRow[0].data.workOrderId,
    };
    setLoading(true);
    request(`${Host}/v1/${tenantId}/hme-work-order-new/priority/up`, {
      method: 'GET',
      query: { ...params },
    }).then(res => {
      if (res && res.success) {
        notification.success();
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
      setLoading(false);
    });
  };

  // 下移
  const handleDown = () => {
    const params = {
      workOrderId: selectedRow[0].data.workOrderId,
    };
    setLoading(true);
    request(`${Host}/v1/${tenantId}/hme-work-order-new/priority/down`, {
      method: 'GET',
      query: { ...params },
    }).then(res => {
      if (res && res.success) {
        notification.success();
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
      setLoading(false);
    });
  };

  // 置顶
  const handleTop = () => {
    const params = {
      workOrderId: selectedRow[0].data.workOrderId,
    };
    setLoading(true);
    request(`${Host}/v1/${tenantId}/hme-work-order-new/priority/top`, {
      method: 'GET',
      query: { ...params },
    }).then(res => {
      if (res && res.success) {
        notification.success();
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
      setLoading(false);
    });
  };

  // 指令拆分
  const handleWorkOrderSplit = () => {
    splitFormDs.create({});
    openSplitModal();
  };

  const fetchUrls = async (urls, maxConcurrent = 10) => {
    let results = [];
    let i = 0;
    const fetchBatch = async () => {
      const url = `${Host}/v1/${tenantId}/hme-work-order-new/dispatch`;
      const batch = urls.slice(i, i + maxConcurrent);
      if (i >= urls.length) return;
      const promises = batch.map(item =>
        axios.post(url, {
          workOrderId: item.workOrderId,
          qty: item.qty,
        }),
      );
      const responses = await Promise.all(promises)
        .then(res => {
          return res;
        })
        .catch(err => {
          notification.error({
            message: err || err.message,
          });
        });
      results = responses;
      i += maxConcurrent;
      await fetchBatch();
    };
    await fetchBatch();
    return results;
  };

  // 指令派工
  const handleWorkOrderDispatch = async () => {
    if (tableDs.selected && tableDs.selected.length <= 10) {
      const list = tableDs.selected.map(item => item.toData());
      const data = await fetchUrls(list).then(data => {
        if (data.length > 0) {
          data.forEach(item => {
            if (item && item.success) {
              if (item.message) {
                notification.warning({ message: `${list[0].workOrderNum}订单${item.message}` });
              } else {
                notification.success({ message: `${list[0].workOrderNum}订单操作成功` });
              }
            } else {
              notification.error({ message: `${list[0].workOrderNum}订单${item.message}` });
            }
          });
        }
        console.log('ddd');
        return true;
      });
      console.log(data);
      if (data) {
        tableDs.query();
        dispatchModal.close();
      }
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.title.tenLength`).d('批量指令派工一次不能超过10条订单'),
      });
    }

    // const selectedData = selectedRow[0].data;
    // if (selectedData.status !== 'RELEASED' && selectedData.status !== 'EORELEASED') {
    //   return notification.error({
    //     message: intl
    //       .get(`${modelPrompt}.error.workorderStatus`)
    //       .d(
    //         `当前工单${selectedData.workOrderNum}状态不为下达或下达作业状态，不允许派工，请检查！`,
    //       ),
    //   });
    // }
    // dispatchFormDs.create({});
    // // dispatchFormDs.current.set('splitQty', selectedData.qty);
    // // dispatchConfirm('DISPATCH');
    // // dispatchFormDs.create({});
    // // openDispatchModal('DISPATCH');
    // setLoading(true);
    // request(`${Host}/v1/${tenantId}/hme-work-order-new/dispatch`, {
    //   method: 'POST',
    //   body: {
    //     workOrderId: tableDs.selected[0].data.workOrderId,
    //     qty: tableDs.selected[0].data.qty,
    //   },
    // }).then(res => {
    //   setLoading(false);
    //   if (res && res.success) {
    //     notification.success();
    //     tableDs.query();
    //     dispatchModal.close();
    //   } else {
    //     notification.error({ message: res.message });
    //   }
    // });
  };

  const columns = [
    // 工厂
    {
      name: 'siteCode',
      align: 'left',
      lock: 'left',
      width: 150,
    },
    // 工单号
    {
      name: 'workOrderNum',
      align: 'left',
      lock: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goWorkOrderDeatil(record.data.workOrderId)}>{value}</a>
          </span>
        );
      },
    },
    // 工单状态
    {
      name: 'statusDesc',
      align: 'left',
      lock: 'left',
    },
    // 工单类型
    {
      name: 'workOrderTypeDesc',
      align: 'left',
      lock: 'left',
    },
    // 供应商信息
    {
      name: 'supplierName',
      align: 'left',
      lock: 'left',
    },
    // 物料编码
    {
      name: 'materialCode',
      align: 'left',
      lock: 'left',
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
      lock: 'left',
    },
    {
      name: 'materialDesignCode',
    },
    // 是否分解
    {
      name: 'splitFlag',
      align: 'left',
    },
    // 生产线
    {
      name: 'prodLineName',
      align: 'left',
    },
    // 生产线
    {
      name: 'prodLineCode',
      align: 'left',
    },
    // 生产数量
    {
      name: 'qty',
      align: 'left',
    },
    // 完工数量
    {
      name: 'completedQty',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => openModal(record.data.workOrderId, 'COMPLETED')}>{value}</a>
          </span>
        );
      },
    },
    // 在制数量
    {
      name: 'releasedQty',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => openModal(record.data.workOrderId, 'WORKING', 'IN_PRODUCTION')}>
              {value}
            </a>
          </span>
        );
      },
    },
    // 报废数量
    {
      name: 'scrappedQty',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => openModal(record.data.workOrderId, 'CLOSED')}>{value}</a>
          </span>
        );
      },
    },
    // 可用数量
    {
      name: 'availableQty',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => openModal(record.data.workOrderId, 'WORKING', 'AVAILABLE')}>
              {value}
            </a>
          </span>
        );
      },
    },
    // 优先级
    {
      name: 'priority',
      align: 'left',
    },
    // 装配清单名称
    {
      name: 'bomName',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goBomDeatil(record)}>{value}</a>
          </span>
        );
      },
    },
    // 装配清单版本
    {
      name: 'bomRevision',
      align: 'left',
    },
    // 工艺路线名称
    {
      name: 'routerName',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goRouterDeatil(record)}>{value}</a>
          </span>
        );
      },
    },
    // 工艺路线版本
    {
      name: 'routerRevision',
      align: 'left',
    },
    // 计划开始时间
    {
      name: 'planStartTime',
      align: 'left',
    },
    // 计划结束时间
    {
      name: 'planEndTime',
      align: 'left',
    },
    // 实际完成时间
    {
      name: 'actualEndDate',
      align: 'left',
    },
    {
      name: 'dispatchTime',
      align: 'left',
    },
    {
      name: 'attribute1',
      align: 'left',
    },
    {
      name: 'organizationName',
      align: 'left',
    },
    {
      name: 'uomCode',
      align: 'left',
    },
    {
      name: 'attribute2',
      align: 'left',
    },
    {
      name: 'level',
      align: 'left',
    },
    {
      name: 'parentWorkOrderNum',
      align: 'left',
    },
    {
      name: 'productionVision',
      align: 'left',
    },
    {
      name: 'packWorkOrderNum',
      align: 'left',
    },
  ];
  const handleOpenTab = eoId => {
    drawerModal.close();
    props.history.push(`/hmes/workshop/execute-operation-management/detail/${eoId}`);
  };

  const drawerColumns = [
    {
      name: 'eoNum',
      width: 200,
      lock: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => handleOpenTab(record?.data?.eoId)}>{value}</a>
          </span>
        );
      },
    },
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'materialCode',
    },
    {
      name: 'revisionCode',
      width: 90,
    },
    {
      name: 'materialName',
    },
    {
      name: 'statusDesc',
    },
    {
      name: 'qty',
      width: 120,
    },
    {
      name: 'completedQty',
      width: 90,
    },
    {
      name: 'scrappedQty',
      width: 90,
    },
  ];

  const openModal = async (workOrderId, status, type) => {
    drawerDs.setQueryParameter('workOrderId', workOrderId);
    drawerDs.setQueryParameter('status', status);
    if (type) {
      drawerDs.setQueryParameter('type', type);
    } else {
      drawerDs.setQueryParameter('type', '');
    }
    await drawerDs.query();
    let title = '';
    if (status === 'COMPLETED') {
      title = intl.get(`${modelPrompt}.modal.completionDetails`).d('完工明细');
    } else if (status === 'WORKING' && type === 'IN_PRODUCTION') {
      title = intl.get(`${modelPrompt}.modal.workingDetails`).d('在制明细');
    } else if (status === 'CLOSED') {
      title = intl.get(`${modelPrompt}.modal.scrappedDetails`).d('报废明细');
    } else if (status === 'WORKING' && type === 'AVAILABLE') {
      title = intl.get(`${modelPrompt}.modal.availableDetails`).d('可用明细');
    }
    drawerModal = Modal.open({
      key: Modal.key(),
      style: {
        width: '1080px',
      },
      title,
      drawer: true,
      closable: true,
      resizable: true,
      children: <Table dataSet={drawerDs} columns={drawerColumns} style={{ height: 400 }} />,
      footer: null,
    });
  };

  const handleLevel = () => {
    levelDs.reset();
    splitModal = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.specifyLevel`).d('指定等级'),
      width: 400,
      children: (
        <Form dataSet={levelDs} columns={1}>
          <Select name="level" />
        </Form>
      ),
      footer: (
        <div>
          <Button
            onClick={() => {
              cancelSplit();
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            color="primary"
            type="submit"
            loading={loading}
            onClick={() => {
              levelConfirm();
            }}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  const levelConfirm = async () => {
    setLoading(true);

    const res = await request(`${Host}/v1/${tenantId}/hme-work-order-new/designate/leave`, {
      method: 'POST',
      body: {
        workOrderIds: selectedRow.map(item => item.data.workOrderId),
        level: levelDs?.current?.get('level'),
      },
    });
    setLoading(false);
    const result = getResponse(res);
    if (result?.success) {
      notification.success();
      tableDs.query();
      splitModal.close();
    } else {
      notification.error({
        message: result.message || intl.get('hzero.common.notification.error').d('操作失败'),
      });
    }
  };
  const handleProd = async () => {
    prodDs.getField('prodLineObj').setLovPara('organizationId', selectedRow[0].data.organizationId);
    prodDs.getField('prodLineObj').setLovPara('workOrderId', selectedRow[0].data.workOrderId);
    if (selectedRow[0].data.itemGroup !== 'ZB12') {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.error.workorderStatusNoProd`)
          .d(`当前工单${selectedRow[0].data.workOrderNum}不是模组工单，不可指定产线，请检查！`),
      });
      return;
    }
    prodDs.reset();
    await Modal.open({
      width: 400,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.designatedProductionLine`).d('指定生产线'),
      children: (
        <Form dataSet={prodDs} columns={1}>
          <Lov name="prodLineObj" />
        </Form>
      ),
      onOk: async () => {
        if (await prodDs.validate()) {
          setLoading(true);
          return request(`${Host}/v1/${tenantId}/hme-work-order-new/designate/prodline`, {
            method: 'POST',
            body: {
              workOrderId: selectedRow[0].data.workOrderId,
              prodLineId: prodDs.current.get('organizationId'),
            },
          }).then(res => {
            setLoading(false);
            if (res && res.success) {
              notification.success();
              tableDs.query();
            } else {
              notification.error({
                message: res.message || intl.get('hzero.common.notification.error').d('操作失败'),
              });
              return Promise.resolve(false);
            }
          });
        }
        return false;
      },
    });
  };

  const handleChangeBomAndRouter = async () => {
    const selectedData = selectedRow[0].data;
    bomAndRouterDs.current.set('siteId', selectedData.siteId);
    bomAndRouterDs.current.set('materialId', selectedData.materialId);
    bomAndRouterDs.current.set('workOrderId', selectedData.workOrderId);
    Modal.open({
      ...drawerPropsC7n({ ds: bomAndRouterDs }),
      drawer: false,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.modal.assemblyList`).d('装配清单/工艺路线'),
      okText: intl.get(`${modelPrompt}.button.confirm`).d('确定'),
      children: <BomAndRouterDrawer ds={bomAndRouterDs} queryRouter={queryRouter} />,
      onOk: async () => {
        if (!(await bomAndRouterDs.validate())) {
          return false;
        }
        const data = bomAndRouterDs.current.toData();
        const _saveData = {};
        _saveData.bomId = data.bom?.bomId;
        _saveData.bomName = data.bom?.bomName;
        _saveData.bomRevision = data.bom?.revision;
        _saveData.routerId = data.router.routerId;
        _saveData.routerName = data.router.routerName;
        _saveData.routerRevision = data.router.revision;
        _saveData.productionVersion = data.productionVersionCode;
        setLoading(true);
        return run({
          params: {
            ...selectedRow[0].data,
            ..._saveData,
            validateFlag: 'Y',
          },
        }).then(res => {
          setLoading(false);
          if (res && res.success) {
            notification.success();
            tableDs.query(tableDs.currentPage);
          } else {
            return Promise.resolve(false);
          }
        });
      },
    });
  };

  const queryRouter = productionVersionCode => {
    const { workOrderId } = selectedRow[0].data;
    // 获取重读使用的bom和router
    setLoading(true);
    request(
      `${Host}/v1/${tenantId}/hme-work-order-new/bom/router/query?workOrderId=${workOrderId ||
        ''}&productionVersion=${productionVersionCode}`,
      {
        method: 'GET',
      },
    ).then(response => {
      setLoading(false);
      const res = getResponse(response);
      if (res) {
        const rows = res.content[0];
        if (rows.bomId) {
          const bom = {
            bomId: rows.bomId,
            bomName: rows.bomName,
            revision: rows.bomRevision,
          };
          bomAndRouterDs.current.set('bom', bom);
        }
        const router = {
          routerId: rows.routerId,
          routerName: rows.routerName,
          revision: rows.routerRevision,
        };
        bomAndRouterDs.current.set('router', router);
      }
    });
  };

  const handleQueryHistory = () => {
    historyDs.setQueryParameter(
      'workOrderIdList',
      tableDs.selected.map(item => item.get('workOrderId')),
    );
    historyDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}</div>
        </div>
      ),
      destroyOnClose: true,
      children: <HistoryDrawer ds={historyDs} />,
    });
  };

  const handlePack = async () => {
    const res = await request(
      `${Host}/v1/${tenantId}/hme-work-order-new/association/pack/wo/determine?workOrderId=${selectedRow[0].get(
        'workOrderId',
      )}`,
      {
        method: 'GET',
      },
    );
    if (res && res.success) {
      const packDs = new DataSet({
        autoCreate: true,
        fields: [
          {
            name: 'packObj',
            type: 'object',
            label: intl.get(`${modelPrompt}.packObj`).d('关联PACK工单'),
            lovCode: 'HME.WO.PACK',
            required: true,
          },
          {
            name: 'packWorkOrderId',
            bind: 'packObj.workOrderId',
          },
        ],
      });
      Modal.open({
        className: 'hmes-style-modal',
        closable: true,
        drawer: false,
        maskClosable: false,
        style: {
          width: 480,
        },
        okText: intl.get('tarzan.common.button.confirm').d('确定'),
        cancelText: intl.get('tarzan.common.button.back').d('返回'),
        key: Modal.key(),
        title: '',
        destroyOnClose: true,
        children: (
          <Form dataSet={packDs} labelWidth={120}>
            <Lov name="packObj" />
          </Form>
        ),
        onOk: async () => {
          if (await packDs.current.validate(true)) {
            const res = await request(
              `${Host}/v1/${tenantId}/hme-work-order-new/association/pack/wo`,
              {
                method: 'GET',
                query: {
                  moduleWorkOrderId: selectedRow[0].get('workOrderId'),
                  packWorkOrderId: packDs.current.get('packWorkOrderId'),
                },
              },
            );
            if (res && res.success) {
              notification.success();
              tableDs.query();
              return Promise.resolve(true);
            }
            return Promise.resolve(false);
          }
          return false;
        },
      });
    } else {
      notification.error({ message: res.message });
    }
  };

  const handleModuleCellQuery = async () => {
    const res = await request(`${Host}/v1/${tenantId}/hme-work-order-new/module/cell/query`, {
      method: 'GET',
      query: {
        workOrderId: selectedRow[0].get('workOrderId'),
      },
    });
    if (res && res.success) {
      history.push({
        pathname: `/hwms/inventory/Level`,
        state: {
          materialId: res.rows[0].materialId,
          materialCode: res.rows[0].materialCode,
          siteId: selectedRow[0].get('siteId'),
          siteCode: selectedRow[0].get('siteCode'),
        },
      });
    } else {
      notification.error({ message: res.message });
    }
  };

  const handleModuleRouter = async () => {
    const workOrderNumList = selectedRow.map(item => item.get('workOrderNum'));
    const res = await request(
      `${Host}/v1/${tenantId}/hme-work-order-new/wo/router/synchronization/ui`,
      {
        method: 'POST',
        body: workOrderNumList.length ? workOrderNumList : null,
      },
    );
    if (res && res.success) {
      notification.success();
      tableDs.query();
    } else {
      notification.error({ message: res.message });
    }
  };

  // 工单下达
  const handleWorkOrderIssued = async () => {
    const workOrderIds = selectedRow.map(item => item.get('workOrderId')).join(',');
    const res = await issueWorkOrded({ params: { workOrderIds } });
    if (res && res.success) {
      notification.success();
      tableDs.query();
    }
  };

  // 工单完成
  const handleWorkOrderCompleted = async () => {
    const workOrderIds = selectedRow.map(item => item.get('workOrderId')).join(',');
    const res = await completeWorkOrder({ params: { workOrderIds } });
    if (res && res.success) {
      notification.success();
      tableDs.query();
    }
  };

  return (
    <React.Fragment>
      <Spin spinning={loading}>
        <Header title={intl.get(`${modelPrompt}.title`).d('工单管理平台')}>
          <Button
            onClick={handleChangeBomAndRouter}
            style={{ marginRight: 15 }}
            color="primary"
            icon="cached"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(item => item.data.status !== 'RELEASED') ||
              selectedRow?.some(item => item.data.workOrderType !== 'REPAIR')
            }
          >
            {intl.get(`${modelPrompt}.button.assemblyList`).d('装配清单/工艺路线变更')}
          </Button>
          <Button
            onClick={handleProd}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 || selectedRow?.some(item => item.data.status !== 'RELEASED')
            }
          >
            {intl.get(`${modelPrompt}.button.designatedProductionLine`).d('指定生产线')}
          </Button>
          <Button
            onClick={handleLevel}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={!selectedLength || selectedRow?.some(item => item.data.status !== 'RELEASED')}
          >
            {intl.get(`${modelPrompt}.button.specifyLevel`).d('指定等级')}
          </Button>
          <Button
            onClick={handleWorkOrderDispatch}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              !(selectedLength && selectedLength <= 10) ||
              selectedRow?.some(item => item.data.status !== 'RELEASED')
            }
          >
            {intl.get(`${modelPrompt}.button.commandDispatch`).d('指令派工')}
          </Button>
          <Button
            onClick={handleWorkOrderSplit}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(
                item =>
                  item.data.status !== 'RELEASED' ||
                  item.data.workOrderNum !== item.data.parentWorkOrderNum,
              )
            }
          >
            {intl.get(`${modelPrompt}.button.instructionSplitting`).d('指令拆分')}
          </Button>
          <Button
            onClick={handleTop}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(
                item => item.data.status !== 'RELEASED' && item.data.status !== 'EORELEASED',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.toTop`).d('置顶')}
          </Button>
          <Button
            onClick={handleDown}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(
                item => item.data.status !== 'RELEASED' && item.data.status !== 'EORELEASED',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.down`).d('下移')}
          </Button>
          <Button
            onClick={handleUp}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(
                item => item.data.status !== 'RELEASED' && item.data.status !== 'EORELEASED',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.top`).d('上移')}
          </Button>
          <Button
            onClick={handleProductCancel}
            style={{ marginRight: 15 }}
            color="primary"
            loading={loading}
            disabled={
              !selectedLength ||
              selectedRow?.some(
                item => item.data.status !== 'COMPLETED' && item.data.status !== 'PENDING',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.stopProduction`).d('停止生产')}
          </Button>
          <Button disabled={!tableDs.selected.length} onClick={handleQueryHistory}>
            {intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}
          </Button>
          <Button
            onClick={handlePack}
            disabled={
              selectedLength !== 1 || selectedRow?.some(item => item.data.status !== 'RELEASED')
            }
          >
            {intl.get(`${modelPrompt}.queryHistory`).d('关联PACK')}
          </Button>
          <Button onClick={handleModuleCellQuery} disabled={selectedLength !== 1}>
            {intl.get(`${modelPrompt}.moduleCellQuery`).d('模组电芯查询')}
          </Button>
          <Button onClick={handleModuleRouter} disabled={selectedLength !== 1}>
            {intl.get(`${modelPrompt}.moduleCellQuery`).d('工艺路线同步')}
          </Button>
          <PermissionButton
            type="c7n-pro"
            onClick={handleWorkOrderIssued}
            loading={issueWorkOrdedLoading}
            disabled={!selectedLength}
            permissionList={[
              {
                code: `${path}.button.workOrderIssued`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.workOrderIssued`).d('工单下达')}
          </PermissionButton>
          <PermissionButton
            type="c7n-pro"
            onClick={handleWorkOrderCompleted}
            loading={completeWorkOrderLoading}
            disabled={!selectedLength}
            permissionList={[
              {
                code: `${path}.button.workOrderComplete`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.workOrderComplete`).d('工单完成')}
          </PermissionButton>
        </Header>
        <Content>
          <Table
            searchCode="WorkOrderManagementPlatform"
            customizedCode="WorkOrderManagementPlatform"
            queryBar="filterBar"
            queryFieldsLimit={10}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            style={{ height: 400 }}
          />
        </Content>
      </Spin>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.WorkOrderManagementPlatform', 'tarzan.common'],
})(WorkOrderManagementPlatform);
