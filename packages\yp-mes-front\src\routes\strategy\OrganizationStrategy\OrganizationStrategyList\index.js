/**
 * @Description: 策略组织关系列表查询
 * @Author: <<EMAIL>>
 * @Date: 2021-09-13 09:43:15
 * @LastEditTime: 2022-11-07 16:40:30
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { tableDS } from '../stores/DistributionListDS';

const modelPrompt = 'tarzan.addressing.strategy';

const StrategyList = props => {
  const { dataSet } = props;

  useEffect(() => {
    dataSet.query(props.dataSet.currentPage);
  }, []);

  const orderDetail = id => {
    props.history.push(`/hmes/strategy/addressing/detail/${id}`);
  };

  const columns = [
    {
      name: 'siteCode',
      width: 110,
    },
    {
      name: 'locatorCode',
      width: 140,
    },
    {
      name: 'locatorName',
      width: 140,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'materialCategoryCode',
      width: 160,
    },
    {
      name: 'materialCategoryName',
      width: 160,
    },
    {
      name: 'description',
      width: 140,
    },
    {
      name: 'addressingStrategyCode',
      width: 140,
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              orderDetail(record.data.addressingStrategyId);
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'enableFlag',
      width: 120,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('策略组织关系查询')} />
      <Content>
        <Table
          queryBar='filterBar'
          queryBarProps={{
            fuzzyQuery: false,
          }}
          highLightRow={false}
          dataSet={dataSet}
          columns={columns}
          searchCode="dxxzclcx"
          customizedCode="dxxzclcx"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.addressing.strategy', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...tableDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(StrategyList),
);
