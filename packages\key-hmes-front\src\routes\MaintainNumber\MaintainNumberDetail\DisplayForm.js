import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Form, Row, Col, Switch, Select, Input } from 'hzero-ui';
import { EditableTagGroup } from '@components/tarzan-ui';

import {
  FORM_COL_3_LAYOUT,
  SEARCH_FORM_ROW_LAYOUT,
  DRAWER_FORM_ITEM_LAYOUT,
} from '@utils/constants';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';
const { Option } = Select;

let timeout;
/**
 * 表单数据展示
 * @extends {PureComponent} - React.PureComponent
 * @return React.element
 */

@connect(({ maintainNumber }) => ({
  maintainNumber,
}))
@Form.create({ fieldNameProp: null })
export default class DisplayForm extends PureComponent {
  constructor(props) {
    super(props);
    props.onRef(this);
    this.state = {
      data: [],
      selectRuleId: '',
    };
  }

  handleSearch = value => {
    const that = this;
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }

    function fake() {
      if (value) {
        that.props
          .dispatch({
            type: 'maintainNumber/fetchCodingObject',
            payload: {
              objectCode: value,
            },
          })
          .then(res => {
            if (res) {
              const newArray = res.rows;
              if (!newArray.some(item => item.objectCode === value)) {
                newArray.unshift({
                  ruleId: null,
                  objectCode: value,
                  ruleDescription: null,
                });
              }
              that.setState({
                data: newArray,
              });
            }
          });
      } else {
        that.setState({
          data: [],
        });
      }
    }

    timeout = setTimeout(fake, 300);
  };

  handleChange = (value, e) => {
    const { changeObjectCode = ee => ee } = this.props;
    let ruleId;
    let ruleDescription;
    let selectRuleId = '';
    let objectCode = '';
    if (value) {
      ({ ruleId, ruleDescription, ruleId: selectRuleId, objectCode } = e.props.data);
    } else {
      this.setState({
        data: [],
      });
    }
    this.props.form.setFieldsValue({
      ruleId,
      ruleDescription,
      objectCode,
    });
    this.setState({
      selectRuleId,
    });
    changeObjectCode(value);
  };

  /**
   * render
   * @returns React.element
   */
  render() {
    const { selectRuleId } = this.state;
    const {
      form,
      maintainNumber: { maintainNumberDetail = {}, userRole = 'N' },
      canEdit,
      kid,
    } = this.props;
    const {
      enableFlag = 'Y',
      initialFlag = 'N',
      ruleId,
      objectCode,
      ruleDescription,
      objectTypeCodeList,
      siteCodeList,
    } = maintainNumberDetail;

    const { getFieldDecorator } = form;
    const options = this.state.data.map(d => (
      <Option key={d.objectCode} data={{ ruleDescription: d.ruleDescription, ruleId: d.ruleId }}>
        {d.objectCode}
      </Option>
    ));
    return (
      <React.Fragment>
        <Form>
          <Row {...SEARCH_FORM_ROW_LAYOUT}>
            <Form.Item style={{ display: 'none' }}>
              {getFieldDecorator('ruleId', {
                initialValue: ruleId,
              })(<Input dbc2sbc={false} />)}
            </Form.Item>
            <Col {...FORM_COL_3_LAYOUT}>
              <Form.Item
                {...DRAWER_FORM_ITEM_LAYOUT}
                label={intl.get(`${modelPrompt}.objectId`).d('编码对象')}
              >
                {getFieldDecorator('objectCode', {
                  rules: [
                    {
                      required: true,
                      message: intl.get('hzero.common.validation.notNull', {
                        name: intl.get(`${modelPrompt}.objectId`).d('编码对象'),
                      }),
                    },
                  ],
                  initialValue: objectCode,
                })(
                  <Select
                    showSearch
                    showArrow={false}
                    filterOption={false}
                    onSearch={this.handleSearch}
                    onChange={this.handleChange}
                    notFoundContent={null}
                    disabled={!canEdit || kid !== 'create'}
                    allowClear
                  >
                    {options}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col {...FORM_COL_3_LAYOUT}>
              <Form.Item
                {...DRAWER_FORM_ITEM_LAYOUT}
                label={intl.get(`${modelPrompt}.objectName`).d('短描述')}
              >
                {getFieldDecorator('ruleDescription', {
                  initialValue: ruleDescription,
                })(
                  <Input
                    label={intl.get(`${modelPrompt}.objectName`).d('短描述')}
                    disabled={
                      !canEdit ||
                      (kid === 'create' && selectRuleId === '') ||
                      (userRole !== 'Y' && initialFlag === 'Y')
                    }
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row {...SEARCH_FORM_ROW_LAYOUT}>
            <Col {...FORM_COL_3_LAYOUT}>
              <Form.Item
                {...DRAWER_FORM_ITEM_LAYOUT}
                label={intl.get(`tarzan.common.label.initialFlag`).d('初始化')}
              >
                {getFieldDecorator('initialFlag', {
                  initialValue: initialFlag,
                })(<Switch checkedValue="Y" unCheckedValue="N" disabled />)}
              </Form.Item>
            </Col>
            <Col {...FORM_COL_3_LAYOUT}>
              <Form.Item
                {...DRAWER_FORM_ITEM_LAYOUT}
                label={intl.get(`tarzan.common.label.enableFlag`).d('启用状态')}
              >
                {getFieldDecorator('enableFlag', {
                  initialValue: enableFlag,
                })(
                  <Switch
                    checkedValue="Y"
                    unCheckedValue="N"
                    disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row {...SEARCH_FORM_ROW_LAYOUT}>
            <Col {...FORM_COL_3_LAYOUT}>
              <Form.Item
                {...DRAWER_FORM_ITEM_LAYOUT}
                label={intl.get(`${modelPrompt}.objectTypeCodeList`).d('类型编码')}
              >
                {getFieldDecorator('objectTypeCodeList', {
                  initialValue: objectTypeCodeList,
                })(
                  <EditableTagGroup
                    color="blue"
                    disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                    style={{ width: '400%' }}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row {...SEARCH_FORM_ROW_LAYOUT}>
            <Col {...FORM_COL_3_LAYOUT}>
              <Form.Item
                {...DRAWER_FORM_ITEM_LAYOUT}
                label={intl.get(`${modelPrompt}.siteCodeList`).d('站点编码')}
              >
                {getFieldDecorator('siteCodeList', {
                  initialValue: siteCodeList,
                })(
                  <EditableTagGroup
                    color="green"
                    disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                    style={{ width: '400%' }}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </React.Fragment>
    );
  }
}
