/**
 * @Description: 寻址策略详情
 * @Author: <<EMAIL>>
 * @Date: 2021-09-08 14:56:42
 * @LastEditTime: 2022-06-14 15:15:33
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState, useRef } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { isEmpty, isArray } from 'lodash';
import {
  DataSet,
  Form,
  Select,
  Lov,
  TextField,
  SelectBox,
  Switch,
  Row,
  Col,
} from 'choerodon-ui/pro';
import { Icon, Collapse, Popover } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import { C7nFormItemSort } from '@/components/tarzan-ui';
import { DndProvider } from 'react-dnd-9.3.4';
import HTMLBackend from 'react-dnd-html5-backend-9.3.4';
import SliderComponents from './SliderComponents';
import DragComponentsRow from './DragComponentsRow';
import { formDS, locatorCoordinateDS } from '../stores/StrategyDetailDS';
import styles from '../index.module.less';

const { Option } = SelectBox;
const { Panel } = Collapse;

const modelPrompt = 'tarzan.addressing.strategy';

const StrategyDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
  } = props;

  // 加工实绩装配实绩组件
  const sliderComponentsRef = useRef();

  const retrievalOrderRef = useRef();

  // 加工实绩装配实绩组件
  // 编辑开关
  const [canEdit, setCanEdit] = useState(false);

  // 寻址范围类型
  const [rangeType, setRangeType] = useState(undefined);
  // 策略类型
  const [addressingStrategyType, setAddressingStrategyType] = useState('STORE');
  // 筛选条件类型
  const [screeningConditionSwitch, setScreeningConditionSwitch] = useState(undefined);
  // 坐标系范围默认值
  const [coordinateList, setCoordinateList] = useState([]);
  // 上层库位ID
  const [locatorId, setLocatorId] = useState(null);
  // 策略层级
  const [addressingStrategyLevel, setAddressingStrategyLevel] = useState('MAIN');

  const [retrievalOrderList, setRetrievalOrderList] = useState([]);

  const [questionTuidMap] = useState({
    LOCATOR_CAPACITY: intl.get(`${modelPrompt}.locatorCapacity`).d('库存存量'),
    IN_SITE_TIME: intl.get(`${modelPrompt}.inSiteTime`).d('入库时间'),
    IN_LOCATOR_TIME: intl.get(`${modelPrompt}.inLocatorTime`).d('入站时间'),
    PRODUCTION_DATE: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    EXPIRATION_DATE: intl.get(`${modelPrompt}.expirationDate`).d('到期日期'),
    LOT_CODE: intl.get(`${modelPrompt}.lotCode`).d('批次'),
    COORDINATE: intl.get(`${modelPrompt}.coordinateOrder`).d('坐标优先级'),
  });

  // 表单信息DS
  const formDs = useMemo(() => {
    return new DataSet(formDS());
  }, []);

  const locatorCoordinateDs = useMemo(() => {
    return new DataSet(locatorCoordinateDS());
  }, []);

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      setCoordinateList([]);
    } else {
      // 编辑开关
      setCanEdit(false);
      queryForm(id);
    }
  }, [id]);

  // 查询上方表单,下方表格
  const queryForm = addressingStrategyId => {
    formDs.queryParameter = {
      addressingStrategyId,
    };
    formDs.query().then(res => {
      if (res && res.rows) {
        const { addressingRange, processingOrder } = res.rows;
        setAddressingStrategyLevel(res.rows.addressingStrategyLevel);
        if (res.rows.addressingStrategyLevel === 'PARTIAL') {
          setRangeType('COORDINATE');
        }
        setAddressingStrategyType(res.rows.addressingStrategyType);
        if (
          res.rows.screeningCondition &&
          res.rows.screeningCondition.approximateLimit.length > 0
        ) {
          setScreeningConditionSwitch('b');
          formDs.current.init('screeningConditionSwitch', 'b');
        } else {
          setScreeningConditionSwitch('a');
          formDs.current.init('screeningConditionSwitch', 'a');
        }

        const _retrievalOrderList = [];
        if (processingOrder) {
          const { priority } = processingOrder;
          if (priority && priority.length > 0) {
            formDs.current.init('processingOrderSwitch', priority);
            const { locatorCapacity, inLocatorTime, inSiteTime, productionDate, expirationDate, lotCode } = processingOrder;
            priority.forEach(item => {
              if (item === 'LOCATOR_CAPACITY') {
                _retrievalOrderList.push({
                  questionTuid: item,
                  questionContent: questionTuidMap[item],
                  child: [
                    {
                      questionTuid: 'locatorDefault',
                      questionContent: '',
                      value: locatorCapacity,
                    },
                  ],
                });
              }
              if (item === 'IN_LOCATOR_TIME') {
                _retrievalOrderList.push({
                  questionTuid: item,
                  questionContent: questionTuidMap[item],
                  child: [
                    {
                      questionTuid: 'locatorDefault',
                      questionContent: '',
                      value: inLocatorTime,
                    },
                  ],
                });
              }
              if (item === 'IN_SITE_TIME') {
                _retrievalOrderList.push({
                  questionTuid: item,
                  questionContent: questionTuidMap[item],
                  child: [
                    {
                      questionTuid: 'locatorDefault',
                      questionContent: '',
                      value: inSiteTime,
                    },
                  ],
                });
              }
              if (item === 'PRODUCTION_DATE') {
                _retrievalOrderList.push({
                  questionTuid: item,
                  questionContent: questionTuidMap[item],
                  child: [
                    {
                      questionTuid: 'locatorDefault',
                      questionContent: '',
                      value: productionDate,
                    },
                  ],
                });
              }
              if (item === 'EXPIRATION_DATE') {
                _retrievalOrderList.push({
                  questionTuid: item,
                  questionContent: questionTuidMap[item],
                  child: [
                    {
                      questionTuid: 'locatorDefault',
                      questionContent: '',
                      value: expirationDate,
                    },
                  ],
                });
              }
              if (item === 'LOT_CODE') {
                _retrievalOrderList.push({
                  questionTuid: item,
                  questionContent: questionTuidMap[item],
                  child: [
                    {
                      questionTuid: 'locatorDefault',
                      questionContent: '',
                      value: lotCode,
                    },
                  ],
                });
              }
              if (item === 'COORDINATE') {
                const {
                  coordinate: { coordinatePriority, coordinateValue },
                } = processingOrder;
                _retrievalOrderList.push({
                  questionTuid: 'COORDINATE',
                  questionContent: `${intl.get(`${modelPrompt}.coordinateOrder`).d('坐标优先级')}`,
                  child: coordinatePriority.map(_item => {
                    return {
                      questionTuid: _item,
                      questionContent: _item,
                      value: coordinateValue[`${_item.toLowerCase()}`],
                    };
                  }),
                });
              }
            });
          }
        }

        const { xMax, xMin, yMax, yMin, zMax, zMin } = addressingRange.coordinateValueRange || {};
        if (addressingRange.locatorId || res.rows.addressingStrategyLevel === 'PARTIAL') {
          setLocatorId(addressingRange.locatorId);
          setRangeType(addressingRange.rangeType);
          formDs.current.init('rangeType', addressingRange.rangeType);
          formDs.current.init('locatorId', addressingRange.locatorId);
          formDs.current.init('locatorCode', addressingRange.locatorCode);
          formDs.current.init('locatorTypeValueRange', addressingRange.locatorTypeValueRange);
          getOrder(
            addressingRange.locatorId,
            { xMax, xMin, yMax, yMin, zMax, zMin },
            _retrievalOrderList,
          );
        } else {
          formatXYZ(
            {
              xMaxValue: 5,
              yMaxValue: 5,
              zMaxValue: 5,
              xMinValue: -5,
              yMinValue: -5,
              zMinValue: -5,
            },
            {
              xMax,
              yMax,
              zMax,
              xMin,
              yMin,
              zMin,
            },
            _retrievalOrderList,
          );
        }
      }
    });
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/strategy/addressing/list');
    } else {
      setCanEdit(false);
      formDs.reset();
      queryForm(id);
    }
  };

  const goDistribution = () => {
    if (id !== 'create') {
      props.history.push(`/hmes/strategy/addressing/distribution/${id}`);
    }
  };

  const handleSave = async () => {
    formDs.current.set({ nowDate: new Date().getTime() });
    let addressingRange = {};
    if (sliderComponentsRef && sliderComponentsRef.current) {
      addressingRange = sliderComponentsRef.current.getCoordinateList();
    }

    const newAddressingRange = {
      ...formDs.current.get('addressingRange'),
    };
    if (isArray(addressingRange)) {
      const coordinateValueRange = {};
      addressingRange.forEach(item => {
        const [_min, _max] = item.value;
        coordinateValueRange[`${item.key.toLowerCase()}Min`] = _min;
        coordinateValueRange[`${item.key.toLowerCase()}Max`] = _max;
      });
      newAddressingRange.coordinateValueRange = coordinateValueRange;
    }
    formDs.current.set('addressingRange', newAddressingRange);

    let processingOrder = {};
    if (retrievalOrderRef && retrievalOrderRef.current) {
      processingOrder = retrievalOrderRef.current.getCoordinateList();
    }

    const newProcessingOrder = {
      ...formDs.current.get('processingOrder'),
    };
    const priority = [];
    const coordinatePriority = [];
    const coordinate = {};
    const coordinateValue = {};
    let locatorCapacity = '';
    let inLocatorTime = '';
    let inSiteTime = '';
    let productionDate = '';
    let expirationDate = '';
    let lotCode = '';

    const { rowList } = processingOrder || {};
    if (isArray(rowList)) {
      rowList.forEach(item => {
        priority.push(item.questionTuid);
        if (item.questionTuid === 'LOCATOR_CAPACITY') {
          locatorCapacity = (item.child || [])[0].value;
        }
        if (item.questionTuid === 'IN_LOCATOR_TIME') {
          inLocatorTime = (item.child || [])[0].value;
        }
        if (item.questionTuid === 'IN_SITE_TIME') {
          inSiteTime = (item.child || [])[0].value;
        }
        if (item.questionTuid === 'PRODUCTION_DATE') {
          productionDate = (item.child || [])[0].value;
        }
        if (item.questionTuid === 'EXPIRATION_DATE') {
          expirationDate = (item.child || [])[0].value;
        }
        if (item.questionTuid === 'LOT_CODE') {
          lotCode = (item.child || [])[0].value;
        }
        if (item.questionTuid === 'COORDINATE') {
          const { child } = item;
          if (isArray(child)) {
            child.forEach(_item => {
              coordinatePriority.push(_item.questionTuid);
              coordinateValue[_item.questionTuid.toLowerCase()] = _item.value;
            });
          }
        }
      });
    }
    newProcessingOrder.priority = priority;
    newProcessingOrder.locatorCapacity = locatorCapacity;
    newProcessingOrder.inLocatorTime = inLocatorTime;
    newProcessingOrder.inSiteTime = inSiteTime;
    newProcessingOrder.productionDate = productionDate;
    newProcessingOrder.expirationDate = expirationDate;
    newProcessingOrder.lotCode = lotCode;
    coordinate.coordinatePriority = coordinatePriority;
    coordinate.coordinateValue = coordinateValue;
    newProcessingOrder.coordinate = coordinate;

    formDs.current.set('processingOrder', newProcessingOrder);
    const validate = await formDs.validate();
    if (validate) {
      await formDs.submit().then(res => {
        const { rows = [] } = res || {};
        if (!isEmpty(rows) && rows[0].success) {
          setCanEdit(false);
          const newId = res.rows[0].rows;
          if (id === 'create') {
            props.history.push(`/hmes/strategy/addressing/detail/${newId}`);
          } else {
            queryForm(newId);
          }
        }
      });
    }
  };

  const addressingStrategyTypeChange = value => {
    setScreeningConditionSwitch(undefined);
    formDs.current.set('screeningConditionSwitch', 'a');
    formDs.current.set('approximateLimit', []);
    formDs.current.set('judgmentCondition', []);
    if (value) {
      setAddressingStrategyType(value);
    } else {
      setAddressingStrategyType(undefined);
    }
  };

  const rangeTypeChange = option => {
    if (option && option.currentTarget && option.currentTarget.value) {
      if (sliderComponentsRef && sliderComponentsRef.current) {
        const masks = sliderComponentsRef.current.getCoordinateList();
        setCoordinateList(masks);
      }
      setRangeType(option.currentTarget.value);
      if (option.currentTarget.value === formDs.current.get(`rangeType`)) {
        formDs.current.set(`rangeType`, null);
        setRangeType(undefined);
        option.currentTarget.blur();
      }
    } else {
      setRangeType(undefined);
    }
  };

  const descendantStrategyChange = value => {
    if (!value) {
      formDs.current.set(`areaAddressingTrigger`, '');
    }
  };

  const addressingStrategyLevelChange = value => {
    setAddressingStrategyLevel(value);
    if (value === 'PARTIAL') {
      getOrder();
      setRangeType('COORDINATE');
      setLocatorId(undefined);
      formDs.current.init('rangeType', 'COORDINATE');
      // formDs.current.init(`rangeType`, undefined);
      formDs.current.init(`locator`, undefined);
      formDs.current.init(`locatorTypeValueRange`, undefined);
    } else {
      setRangeType(undefined);
      formatXYZ({}, {});
    }
  };

  const screeningConditionSwitchChange = value => {
    if (value) {
      setScreeningConditionSwitch(value);
      formDs.current.set('approximateLimit', []);
    } else {
      setScreeningConditionSwitch(undefined);
    }
  };

  const locatorChange = (value, valueOld) => {
    if (value && valueOld && value.locatorId === valueOld.locatorId) {
      return;
    }
    if (value && value.locatorId) {
      getOrder(value.locatorId);
      setLocatorId(value.locatorId);
    } else {
      setLocatorId(null);
      formDs.current.set(`rangeType`, null);
      setRangeType(undefined);
      orderListOption('COORDINATE', true);
      const processingOrderSwitchValue = formDs.current.toData().processingOrderSwitch;
      formDs.current.set(
        'processingOrderSwitch',
        processingOrderSwitchValue.map(item => {
          if (item !== 'COORDINATE') {
            return item;
          }
          return null;

        }),
      );
    }
  };

  // 查询库位对应详情
  const getOrder = async (_locatorId, list = {}, _orderList) => {
    if (_locatorId) {
      locatorCoordinateDs.queryParameter = {
        locatorId: _locatorId,
      };
      locatorCoordinateDs.query().then(_res => {
        if (_res && _res.rows) {
          formatXYZ(_res.rows, list, _orderList);
        } else {
          formatXYZ(
            {
              xMaxValue: list.xMax,
              yMaxValue: list.yMax,
              zMaxValue: list.zMax,
              xMinValue: list.xMin,
              yMinValue: list.yMin,
              zMinValue: list.zMin,
            },
            list,
            _orderList,
          );
        }
      });
    } else {
      formatXYZ(
        {
          xMaxValue: 5,
          yMaxValue: 5,
          zMaxValue: 5,
          xMinValue: -5,
          yMinValue: -5,
          zMinValue: -5,
        },
        {
          xMax: list.xMax === null || list.xMax === undefined ? 1 : list.xMax,
          yMax: list.yMax === null || list.yMax === undefined ? 1 : list.yMax,
          zMax: list.zMax === null || list.zMax === undefined ? 1 : list.zMax,
          xMin: list.xMin === null || list.xMin === undefined ? -1 : list.xMin,
          yMin: list.yMin === null || list.yMin === undefined ? -1 : list.yMin,
          zMin: list.zMin === null || list.zMin === undefined ? -1 : list.zMin,
        },
        _orderList,
      );
    }
  };

  const formatXYZ = (ranges = {}, values = {}, _orderList) => {
    const { xMaxValue, yMaxValue, zMaxValue, xMinValue, yMinValue, zMinValue } = ranges;
    const _coordinateList = [];
    const { xMax, xMin, yMax, yMin, zMax, zMin } = values;
    if (xMaxValue) {
      _coordinateList.push({
        key: 'X',
        range: [xMinValue || 0, xMaxValue || 0],
        value: [xMin || 0, xMax || 0],
      });
    }
    if (yMaxValue) {
      _coordinateList.push({
        key: 'Y',
        range: [yMinValue || 0, yMaxValue || 0],
        value: [yMin || 0, yMax || 0],
      });
    }
    if (zMaxValue) {
      _coordinateList.push({
        key: 'Z',
        range: [zMinValue || 0, zMaxValue || 0],
        value: [zMin || 0, zMax || 0],
      });
    }

    setCoordinateList(_coordinateList);
    if (_orderList) {
      setRetrievalOrderList(_orderList);
    } else {
      const processingOrderSwitchValue = formDs.current.toData().processingOrderSwitch;
      const index = processingOrderSwitchValue.indexOf('COORDINATE');
      orderListOption('COORDINATE', index === -1, 'update', _coordinateList);
    }
  };

  const processingOrderSwitchChange = option => {
    const key = option.currentTarget.value;
    const processingOrderSwitchValue = formDs.current.toData().processingOrderSwitch;
    const value = processingOrderSwitchValue.indexOf(key) > -1;
    option.currentTarget.blur();
    orderListOption(key, value);
  };

  const orderListOption = (questionTuid, type, state, _coordinateList) => {
    let processingOrder = {};
    if (retrievalOrderRef && retrievalOrderRef.current) {
      processingOrder = retrievalOrderRef.current.getCoordinateList();
    }
    const { rowList } = processingOrder;

    const newRowList = [];
    if (type) {
      rowList.forEach(item => {
        if (`${item.questionTuid}` !== `${questionTuid}`) {
          newRowList.push(item);
        }
      });
    } else {
      let hasQuestionTuid = false;
      rowList.forEach(item => {
        if (`${item.questionTuid}` !== `${questionTuid}`) {
          newRowList.push(item);
        } else if (state === 'update') {
          hasQuestionTuid = true;
          const childList = _coordinateList.map(_item => {
            return {
              questionTuid: `${_item.key}`,
              questionContent: `${_item.key}`,
              value: 'ASC',
            };
          });
          if (childList.length > 0) {
            newRowList.push({
              questionTuid: 'COORDINATE',
              questionContent: `${intl.get(`${modelPrompt}.coordinateOrder`).d('坐标优先级')}`,
              child: childList,
            });
          }
        } else {
          hasQuestionTuid = true;
          newRowList.push(item);
        }
      });
      if (!hasQuestionTuid) {
        if (questionTuid === 'COORDINATE') {
          let childList = [];
          if (_coordinateList) {
            childList = _coordinateList.map(item => {
              return {
                questionTuid: `${item.key}`,
                questionContent: `${item.key}`,
                value: 'ASC',
              };
            });
          } else {
            childList = coordinateList.map(item => {
              return {
                questionTuid: `${item.key}`,
                questionContent: `${item.key}`,
                value: 'ASC',
              };
            });
          }
          if (childList.length > 0) {
            newRowList.push({
              questionTuid: 'COORDINATE',
              questionContent: `${intl.get(`${modelPrompt}.coordinateOrder`).d('坐标优先级')}`,
              child: childList,
            });
          }
        } else {
          newRowList.push({
            questionTuid,
            questionContent: questionTuidMap[questionTuid],
            child: [
              {
                questionTuid: 'locatorDefault',
                questionContent: '',
                value: 'ASC',
              },
            ],
          });
        }
      }
    }
    setRetrievalOrderList(newRowList);
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.detail.title`).d('寻址策略')}
        backPath="/hmes/strategy/addressing/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton type="c7n-pro" icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          icon="insert_drive_file-o"
          disabled={canEdit || id === 'create' || addressingStrategyLevel !== 'MAIN'}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
          onClick={goDistribution}
        >
          {intl.get(`${modelPrompt}.organizationStrategy`).d('组织策略分配')}
        </PermissionButton>
      </Header>
      <Content>
        <Form
          disabled={!canEdit}
          dataSet={formDs}
          columns={3}
          labelLayout="horizontal"
          labelWidth={0}
        >
          <SelectBox
            className={styles['select-box-address']}
            name="addressingStrategyType"
            mode="button"
            onChange={addressingStrategyTypeChange}
          />
        </Form>
        {addressingStrategyType && addressingStrategyType === 'STORE' &&
        <Collapse
          bordered={false}
          defaultActiveKey={[
            'basicAttributes',
            'strategyRange',
            'screenRule',
            'retrievalOrder',
            'judgmentCondition',
          ]}
        >
          <Panel
            header={intl.get(`${modelPrompt}.basicAttributes`).d('基本属性')}
            key="basicAttributes"
            dataSet={formDs}
          >
            <Form
              disabled={!canEdit}
              dataSet={formDs}
              columns={3}
              labelLayout="horizontal"
              labelWidth={110}
            >
              <TextField name="addressingStrategyCode" />
              <Select name="addressingStrategyLevel" onChange={addressingStrategyLevelChange} />
              <Select name="addStrategyLocationLimit" />
              <C7nFormItemSort name="descendantStrategy" itemWidth={['60%', '40%']}>
                <Lov name="descendantStrategy" onChange={descendantStrategyChange} />
                <Select
                  name="areaAddressingTrigger"
                  dropdownMatchSelectWidth={false}
                  placeholder={intl.get(`${modelPrompt}.areaAddressingTriggerTime`).d('触发时间点')}
                />
              </C7nFormItemSort>
              <Switch style={{ paddingTop: 2 }} name="enableFlag" />
            </Form>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.strategyRange`).d('寻址范围')}
            key="strategyRange"
            dataSet={formDs}
          >
            {addressingStrategyLevel === 'MAIN' && (
              <Form
                disabled={!canEdit}
                dataSet={formDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <Lov name="locator" onChange={locatorChange} />
              </Form>
            )}
            {addressingStrategyLevel === 'MAIN' && (
              <Form
                disabled={!canEdit}
                dataSet={formDs}
                columns={1}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <SelectBox
                  name="rangeType"
                  mode="button"
                  className={styles['select-box-address']}
                >
                  <Option onClick={rangeTypeChange} value="LOCATOR_TYPE">
                    {intl.get(`${modelPrompt}.locatorLevel`).d('库位层级')}
                  </Option>
                  <Option disabled={!locatorId} onClick={rangeTypeChange} value="COORDINATE">
                    {intl.get(`${modelPrompt}.coordinateSystem`).d('坐标系')}
                  </Option>
                </SelectBox>
              </Form>
            )}
            <Row>
              {rangeType === 'LOCATOR_TYPE' && addressingStrategyLevel === 'MAIN' && (
                <Col span={16}>
                  <Form
                    disabled={!canEdit}
                    dataSet={formDs}
                    columns={1}
                    labelLayout="horizontal"
                    labelWidth={110}
                  >
                    <Select name="locatorTypeValueRange" />
                  </Form>
                </Col>
              )}
              {rangeType === 'COORDINATE' && (
                <Col span={8}>
                  <SliderComponents
                    ref={sliderComponentsRef}
                    canEdit={canEdit}
                    coordinateList={coordinateList}
                  />
                </Col>
              )}
            </Row>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.screenRule`).d('筛选条件')}
            key="screenRule"
            dataSet={formDs}
          >
            <Form
              disabled={!canEdit}
              dataSet={formDs}
              columns={1}
              labelLayout="horizontal"
              labelWidth={110}
            >
              <SelectBox
                className={styles['select-box-address']}
                name="screeningConditionSwitch"
                mode="button"
                onChange={screeningConditionSwitchChange}
              />
            </Form>
            {screeningConditionSwitch && screeningConditionSwitch === 'b' && (
              <Form
                disabled={!canEdit}
                dataSet={formDs}
                columns={1}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <SelectBox className={styles['strategy-select-box']} name="approximateLimit" />
              </Form>
            )}
          </Panel>
          <Panel
            header={
              <>
                {intl.get(`${modelPrompt}.retrievalOrder`).d('检索次序')}
                <Popover
                  placement="topLeft"
                  content={intl
                    .get(`${modelPrompt}.notice`)
                    .d(
                      '检索次序条件可通过上下拖拽提高优先级，其中坐标优先级X、Y、Z可通过左右拖拽提高相应坐标优先级，点击上三角和下三角选择升序或降序。',
                    )}
                >
                  <Icon
                    style={{ fontSize: 16, color: '#8c8c8c', marginBottom: 2, marginLeft: 2 }}
                    type="help"
                  />
                </Popover>
              </>
            }
            key="retrievalOrder"
            dataSet={formDs}
          >
            <Form
              disabled={!canEdit}
              dataSet={formDs}
              columns={1}
              labelLayout="horizontal"
              labelWidth={110}
            >
              <SelectBox
                name="processingOrderSwitch"
                mode="button"
                className={styles['select-box-address']}
              >
                <Option onClick={processingOrderSwitchChange} value="LOCATOR_CAPACITY">
                  {intl.get(`${modelPrompt}.locatorCapacity`).d('库存存量')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="IN_SITE_TIME">
                  {intl.get(`${modelPrompt}.inSiteTime`).d('入库时间')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="IN_LOCATOR_TIME">
                  {intl.get(`${modelPrompt}.inLocatorTime`).d('入站时间')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="PRODUCTION_DATE">
                  {intl.get(`${modelPrompt}.productionDate`).d('生产日期')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="EXPIRATION_DATE">
                  {intl.get(`${modelPrompt}.expirationDate`).d('到期日期')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="LOT_CODE">
                  {intl.get(`${modelPrompt}.lotCode`).d('批次')}
                </Option>
                <Option
                  disabled={!locatorId && addressingStrategyLevel !== 'PARTIAL'}
                  onClick={processingOrderSwitchChange}
                  value="COORDINATE"
                >
                  {intl.get(`${modelPrompt}.coordinateOrder`).d('坐标优先级')}
                </Option>
              </SelectBox>
            </Form>
            <DndProvider backend={HTMLBackend}>
              <DragComponentsRow
                list={retrievalOrderList}
                ref={retrievalOrderRef}
                canEdit={canEdit}
              />
            </DndProvider>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.judgmentCondition`).d('判定条件')}
            key="judgmentCondition"
            dataSet={formDs}
          >
            <Form
              disabled={!canEdit}
              dataSet={formDs}
              columns={1}
              labelLayout="horizontal"
              labelWidth={110}
            >
              <SelectBox className={styles['strategy-select-box']} name="judgmentCondition" />
            </Form>
          </Panel>
        </Collapse>
        }
        {addressingStrategyType && addressingStrategyType !== 'STORE' && <Collapse
          bordered={false}
          defaultActiveKey={[
            'basicAttributes',
            'strategyRange',
            'screenRule',
            'retrievalOrder',
            'judgmentCondition',
          ]}
        >
          <Panel
            header={intl.get(`${modelPrompt}.basicAttributes`).d('基本属性')}
            key="basicAttributes"
            dataSet={formDs}
          >
            <Form
              disabled={!canEdit}
              dataSet={formDs}
              columns={3}
              labelLayout="horizontal"
              labelWidth={110}
            >
              <TextField name="addressingStrategyCode" />
              <Select name="addressingStrategyLevel" onChange={addressingStrategyLevelChange} />
              <Select name="addStrategyLocationLimit" />
              <C7nFormItemSort name="descendantStrategy" itemWidth={['60%', '40%']}>
                <Lov name="descendantStrategy" onChange={descendantStrategyChange} />
                <Select
                  name="areaAddressingTrigger"
                  dropdownMatchSelectWidth={false}
                  placeholder={intl.get(`${modelPrompt}.areaAddressingTriggerTime`).d('触发时间点')}
                />
              </C7nFormItemSort>
              <Switch style={{ paddingTop: 2 }} name="enableFlag" />
            </Form>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.strategyRange`).d('寻址范围')}
            key="strategyRange"
            dataSet={formDs}
          >
            {addressingStrategyLevel === 'MAIN' && (
              <Form
                disabled={!canEdit}
                dataSet={formDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <Lov name="locator" onChange={locatorChange} />
              </Form>
            )}
            {addressingStrategyLevel === 'MAIN' && (
              <Form
                disabled={!canEdit}
                dataSet={formDs}
                columns={1}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <SelectBox
                  name="rangeType"
                  mode="button"
                  className={styles['select-box-address']}
                >
                  <Option onClick={rangeTypeChange} value="LOCATOR_TYPE">
                    {intl.get(`${modelPrompt}.locatorLevel`).d('库位层级')}
                  </Option>
                  <Option disabled={!locatorId} onClick={rangeTypeChange} value="COORDINATE">
                    {intl.get(`${modelPrompt}.coordinateSystem`).d('坐标系')}
                  </Option>
                </SelectBox>
              </Form>
            )}
            <Row>
              {rangeType === 'LOCATOR_TYPE' && addressingStrategyLevel === 'MAIN' && (
                <Col span={16}>
                  <Form
                    disabled={!canEdit}
                    dataSet={formDs}
                    columns={1}
                    labelLayout="horizontal"
                    labelWidth={110}
                  >
                    <Select name="locatorTypeValueRange" />
                  </Form>
                </Col>
              )}
              {rangeType === 'COORDINATE' && (
                <Col span={8}>
                  <SliderComponents
                    ref={sliderComponentsRef}
                    canEdit={canEdit}
                    coordinateList={coordinateList}
                  />
                </Col>
              )}
            </Row>
          </Panel>
          <Panel
            header={
              <>
                {intl.get(`${modelPrompt}.retrievalOrder`).d('检索次序')}
                <Popover
                  placement="topLeft"
                  content={intl
                    .get(`${modelPrompt}.notice`)
                    .d(
                      '检索次序条件可通过上下拖拽提高优先级，其中坐标优先级X、Y、Z可通过左右拖拽提高相应坐标优先级，点击上三角和下三角选择升序或降序。',
                    )}
                >
                  <Icon
                    style={{ fontSize: 16, color: '#8c8c8c', marginBottom: 2, marginLeft: 2 }}
                    type="help"
                  />
                </Popover>
              </>
            }
            key="retrievalOrder"
            dataSet={formDs}
          >
            <Form
              disabled={!canEdit}
              dataSet={formDs}
              columns={1}
              labelLayout="horizontal"
              labelWidth={110}
            >
              <SelectBox
                name="processingOrderSwitch"
                mode="button"
                className={styles['select-box-address']}
              >
                <Option onClick={processingOrderSwitchChange} value="LOCATOR_CAPACITY">
                  {intl.get(`${modelPrompt}.locatorCapacity`).d('库存存量')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="IN_SITE_TIME">
                  {intl.get(`${modelPrompt}.inSiteTime`).d('入库时间')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="IN_LOCATOR_TIME">
                  {intl.get(`${modelPrompt}.inLocatorTime`).d('入站时间')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="PRODUCTION_DATE">
                  {intl.get(`${modelPrompt}.productionDate`).d('生产日期')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="EXPIRATION_DATE">
                  {intl.get(`${modelPrompt}.expirationDate`).d('到期日期')}
                </Option>
                <Option onClick={processingOrderSwitchChange} value="LOT_CODE">
                  {intl.get(`${modelPrompt}.lotCode`).d('批次')}
                </Option>
                <Option
                  disabled={!locatorId && addressingStrategyLevel !== 'PARTIAL'}
                  onClick={processingOrderSwitchChange}
                  value="COORDINATE"
                >
                  {intl.get(`${modelPrompt}.coordinateOrder`).d('坐标优先级')}
                </Option>
              </SelectBox>
            </Form>
            <DndProvider backend={HTMLBackend}>
              <DragComponentsRow
                list={retrievalOrderList}
                ref={retrievalOrderRef}
                canEdit={canEdit}
              />
            </DndProvider>
          </Panel>
        </Collapse>
        }
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.addressing.strategy', 'tarzan.common'],
})(StrategyDetail);
