import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.mes.event.creationOfScrapInventoryFormTrial';
const tenantId = getCurrentOrganizationId();
const BASE_SERVER = BASIC.TARZAN_REPORT;

export const infoDS: () => DataSetProps = () => ({
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'interceptionOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.interceptionOperationName`).d('拦截工艺'),
    },
    {
      name: 'statusValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusValue`).d('标记状态值'),
    },
    {
      name: 'typeValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.typeValue`).d('标记类型值'),
    },
    {
      name: 'markingContentValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.markingContentValue`).d('标记内容值'),
    },
    {
      name: 'enableFlagValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlagValue`).d('有效性值'),
    },
    {
      name: 'markingCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.markingCode`).d('标记编码'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('标记状态'),
    },
    {
      name: 'type',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.type`).d('标记类型'),
    },
    {
      name: 'markingContent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.markingContent`).d('标记内容'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'applyReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyReason`).d('申请原因'),
    },
    {
      name: 'interceptionDisposalWay',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.interceptionDisposalWay`).d('拦截处置方法'),
    },
    {
      name: 'disposalResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalResult`).d('处置结果'),
    },
    {
      name: 'attachments',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.attachments`).d('附件'),
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('申请人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('申请时间'),
      width: 160,
    },
    {
      name: 'updatedBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.updatedBy`).d('最后更新人'),
    },
    {
      name: 'updatedDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.updatedDate`).d('最后更新时间'),
      width: 160,
    },
  ],
  queryFields: [
    {
      name: 'dateObj',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.`).d('创建时间'),
      range: ['start', 'end'],
      ignore: FieldIgnore.always,
    },
    {
      name: 'creationDateFrom',
      bind: 'dateObj.start',
    },
    {
      name: 'creationDateTo',
      bind: 'dateObj.end',
    },
  ],
  transport: {
    read: () => ({
      url: `${BASE_SERVER}/v1/${tenantId}/hme-markings/his/query`,
      method: 'POST',
    }),
  },
});
