.componentDistribution {
  display: flex;
  justify-content: space-between;
  height: 100%;

  .leftWrapper {
    width: 360px;
    min-height: 340px;
    height: auto;
    border: 1px solid rgba(0, 0, 0, 0.14901960784313725);
    border-radius: 2px;

    .stepHeader {
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      padding: 0 2px 0 16px;
      line-height: 50px;
      border-bottom: 2px solid rgba(0, 0, 0, 0.06);

      .stepTitleDropdown {
        :global {
          .ant-select-selection {
            border: none;
            //color: @primary-color;
          }

          .ant-select-arrow {
            right: 14px;
            color: #0840f8;
          }

          .ant-select-selection-selected-value {
            color: #0840f8;
          }

          .ant-select-dropdown {
            font-size: 16px;
          }

          .ant-select-focused .ant-select-selection,
          .ant-select-selection:active,
          .ant-select-selection:focus {
            border: none;
            box-shadow: none;
          }
        }
      }
    }

    .stepBottom {
      padding-top: 12px;
      height: calc(100% - 52px);

      .searchInput {
        width: 100%;
        padding-left: 16px;
      }

      .boxWrapper {
        margin-top: 8px;
        height: calc(100% - 36px);
        overflow: auto;

        .customBox {
          width: 358px;
          background: rgba(214, 255, 253, 1);

          .radioBox {
            display: block;
            height: 36px;
            line-height: 36px;
            padding-left: 16px;

            .containerItemIcon {
              position: absolute;
              top: 12px;
              right: 21px;
              color: #0840f8;
              font-weight: bold;
            }

            .imgWrapper {
              position: absolute;
              left: 239px;
              top: -1px;

              img {
                margin: 2px;
              }
            }

            .imgCheckWrapper {
              position: absolute;
              left: 239px;
              line-height: 35px;

              img {
                margin: 2px;
              }
            }

          }
        }

        :global {
          .ant-checkbox-wrapper + .ant-checkbox-wrapper {
            margin-left: 0;
          }
        }
      }
    }
  }

  .rightWrapper {
    width: 600px;
    min-height: 340px;
    height: auto;
    border: 1px solid rgba(0, 0, 0, 0.14901960784313725);
    border-radius: 2px;

    .componentHeader {
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      padding: 0 2px 0 16px;
      line-height: 50px;
      border-bottom: 2px solid rgba(0, 0, 0, 0.06);

      .searchInput {
        width: 100%;
        padding-left: 16px;
      }

      :global {
        .ant-checkbox-wrapper {
          font-size: 14px;
        }
      }

      .stepTitleDropdown {
        :global {
          .ant-select-selection {
            border: none;
            //color: @primary-color;
          }

          .ant-select-arrow {
            right: 14px;
            color: #0840f8;
          }

          .ant-select-selection-selected-value {
            color: #0840f8;
          }

          .ant-select-dropdown {
            font-size: 16px;
          }

          .ant-select-focused .ant-select-selection,
          .ant-select-selection:active,
          .ant-select-selection:focus {
            border: none;
            box-shadow: none;
          }
        }
      }
    }

    .componentBottom {
      padding-top: 12px;
      height: calc(100% - 52px);

      .searchInput {
        width: 100%;
        padding-left: 16px;
      }

      .boxComponentWrapper {
        height: calc(100% - 36px);
        overflow: auto;
        margin-top: 8px;

        :global {
          .ant-checkbox-group {
            height: 100%;
            min-width: 598px;
          }
          .ant-checkbox-wrapper + .ant-checkbox-wrapper {
            margin-left: 0;
          }
        }

        .containerComponentItem {
          height: 30px;
          line-height: 30px;
          display: inline-flex;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;

          .leftCheckBox {
            width: auto;
            padding-left: 16px;
          }

          .rightDescBox {
            width: auto;
            padding: 0 16px;
          }

          .deleteIcon {
            position: absolute;
            line-height: 30px;
            right: 28px;
            font-weight: bold;
            color: #0840f8;
            visibility: hidden;
            cursor: pointer;
          }
        }

        .containerComponentItem:hover {
          //background: #defefd;

          .deleteIcon {
            visibility: visible;
          }

          .containerItemIcon {
            position: absolute;
            right: 30px;
            top: 10px;
            color: #0840f8;
            transform: scale3d(1.4, 1.4, 1.4);
            cursor: pointer;
            display: inline-block;
          }
        }

        .radioBox {
          display: block;
          height: 36px;
          line-height: 36px;

          .imgWrapper {
            position: absolute;
            left: 239px;
            top: -1px;

            img {
              margin: 2px;
            }
          }

          .imgCheckWrapper {
            position: absolute;
            left: 239px;
            line-height: 35px;

            img {
              margin: 2px;
            }
          }

        }
      }
    }
  }

  .iconWrapper {
    width: 60px;
    height: calc(100% - 40px);
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
  }
}
