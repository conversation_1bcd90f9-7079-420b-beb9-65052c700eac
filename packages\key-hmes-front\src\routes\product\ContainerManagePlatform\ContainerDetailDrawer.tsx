/**
 * @Description: 容器管理平台-容器新建/编辑页面
 * @Author: <<EMAIL>>
 * @Date: 2022-04-07 16:08:05
 * @LastEditTime: 2023-05-18 16:08:18
 * @LastEditors: <<EMAIL>>
 */

import React, { useState } from 'react';
import {
  Form,
  TextField,
  DateTimePicker,
  Lov,
  Select,
  NumberField,
  Switch,
} from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';

function filterInitialStatus(dataSet) {
  const containerId = dataSet.current.get('containerId');
  const status = dataSet.current.get('status');
  if (!containerId) {
    // 新建
    return '';
  }
  return status;

}

export default ({ canEdit, ds, createFlag, customizeForm ,correctEdit}) => {
  // 只有状态为可用时，才能修改预留标识
  const [statusFlag, setStatusFlag] = useState(ds.current.get('status') === 'CANRELEASE');
  const [ownerTypeFlag, setOwnerTypeFlag] = useState(ds.current.get('status') === 'NEW');
  const [initialStatus] = useState(filterInitialStatus(ds));

  const statusOptionsFilter = record => {
    if (initialStatus === 'NEW') {
      // 新建只可改为可用和废弃
      return ['NEW', 'CANRELEASE', 'ABANDON'].includes(record.get('statusCode'));
    } if (initialStatus === 'CANRELEASE') {
      // 可用只可改为保留和废弃
      return ['CANRELEASE', 'HOLD', 'ABANDON'].includes(record.get('statusCode'));
    } if (initialStatus === 'HOLD') {
      // 保留只可改为废弃
      return ['HOLD', 'ABANDON'].includes(record.get('statusCode'));
    } if (initialStatus === 'ABANDON') {
      // 废弃不可修改
      return ['CANRELEASE', 'HOLD', 'ABANDON'].includes(record.get('statusCode'));
    }
    return true;
  };

  const handleChangeStatus = value => {
    setStatusFlag(false);
    setOwnerTypeFlag(false);
    if (value === 'CANRELEASE') {
      setStatusFlag(true);
    } else if (value === 'NEW') {
      setOwnerTypeFlag(true);
    }
    ds.current.set('ownerType', '');
    ds.current.set('ownerLov', {});
    ds.current.set('reservedFlag', 'N');
    ds.current.set('reservedObjectType', '');
    ds.current.set('reservedObjectLov', {});
  };

  const handleChangeSite = () => {
    ds!.current!.set('locatorLov', null);
  };

  const handleChangeOwnerType = () => {
    ds!.current!.set('ownerLov', {});
  };

  const handleChangeReservedFlag = () => {
    ds!.current!.set('reservedObjectType', '');
    ds!.current!.set('reservedObjectLov', {});
  };

  const handleChangeReservedObjectType = () => {
    ds!.current!.set('reservedObjectLov', {});
  };

  return (
    customizeForm(
      {
        code: `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.CREATE`,
      },
      <Form labelWidth={112} dataSet={ds} columns={2} disabled={!canEdit}>
        {!createFlag && <TextField name="identification" disabled />}
        <Lov name="containerTypeLov" disabled={!(createFlag||correctEdit)} />
        <Select
          name="status"
          optionsFilter={statusOptionsFilter}
          onChange={handleChangeStatus}
          clearButton={false}
          disabled={!(initialStatus !== 'ABANDON'||correctEdit)}
        />
        <Lov name="siteLov" onChange={handleChangeSite} disabled={!(createFlag||correctEdit)} />
        <Lov name="locatorLov" disabled={!(createFlag||correctEdit)} />
        <Select
          name="ownerType"
          onChange={handleChangeOwnerType}
          disabled={!(ownerTypeFlag||correctEdit)}
          clearButton={false}
        />
        <Lov name="ownerLov" disabled={!(ownerTypeFlag||correctEdit)} />
        {createFlag && <NumberField name="createCount" />}
        <Switch name="reservedFlag" onChange={handleChangeReservedFlag} disabled={!(statusFlag||correctEdit)} />
        <Select name="reservedObjectType" onChange={handleChangeReservedObjectType} disabled={!correctEdit}/>
        <Lov name="reservedObjectLov" disabled={!correctEdit}/>
        {!createFlag && (
          <>
            <Lov name="currentContainerCode" disabled={!correctEdit} />
            <Lov name="topContainerCode"  disabled={!correctEdit} />
            <TextField name="containerCode" disabled />
            <TextField name="containerName" disabled={!correctEdit} />
            <TextField name="description"  disabled={!correctEdit} />
            <TextField name="printTimes" disabled />
            <TextField name="createdByName" disabled />
            <DateTimePicker name="creationDate" disabled />
            <TextField name="lastUpdatedByName" disabled />
            <DateTimePicker name="lastUpdateDate" disabled />
          </>
        )}
      </Form>,
    )
  );
};
