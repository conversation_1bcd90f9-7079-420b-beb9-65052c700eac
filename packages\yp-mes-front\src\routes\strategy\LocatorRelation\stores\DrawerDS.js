/**
 * @feature 物料库位关系维护-抽屉新建和编辑的DS
 * @date 2021-12-14
 * <AUTHOR>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';

const modelPrompt = 'tarzan.strategy.locatorRelation';
const tenantId = getCurrentOrganizationId();

// 所有者类型下拉框数据源
const ownerTypeOptionDs = () =>
  new DataSet({
    autoQuery: true,
    dataKey: 'rows',
    paging: false,
    lang: getCurrentLanguage(),
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/limit-group/type`,
          method: 'POST',
          data: { typeGroup: 'OWNER_TYPE', module: 'GENERAL', tenantId },
          transformResponse: val => {
            const data = JSON.parse(val);
            data.rows.push({
              description: intl.get(`${modelPrompt}.owner`).d('自有'),
              typeCode: 'OWNER',
              typeGroup: 'OWNER_TYPE',
            });
            return {
              ...data,
            };
          },
        };
      },
    },
  });

const drawerDS = () => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'siteObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      valueField: 'siteId',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObject.siteCode',
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteObject.siteId',
    },
    {
      name: 'materialObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      textField: 'materialCode',
      valueField: 'materialId',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialObject.materialCode',
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialObject.materialId',
    },
    {
      name: 'locatorObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: `MT.MES.LOCATOR_INVENTORY_LOCATION`,
      textField: 'locatorCode',
      valueField: 'locatorId',
      noCache: true,
      ignore: 'always',
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'locatorObject.locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'locatorObject.locatorCode',
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
      options: ownerTypeOptionDs(),
      textField: 'description',
      valueField: 'typeCode',
      disabled: true,
      dynamicProps: {
        disabled({ record }) {
          return !record.get('materialObject');
        },
      },
    },
    {
      name: 'ownerObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      textField: 'ownerCode',
      valueField: 'ownerId',
      cascadeMap: {
        ownerType: 'ownerType',
      },
      dynamicProps: {
        lovCode({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'MT.MODEL.CUSTOMER';
          } if (
            record.get('ownerType') === 'SI' ||
            record.get('ownerType') === 'IIS' ||
            record.get('ownerType') === 'OD'
          ) {
            return 'MT.MODEL.SUPPLIER';
          } if (record.get('ownerType') === 'OI') {
            return `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`;
          }
          return null;

        },
        textField({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'customerCode';
          } if (
            record.get('ownerType') === 'SI' ||
            record.get('ownerType') === 'IIS' ||
            record.get('ownerType') === 'OD'
          ) {
            return 'supplierCode';
          } if (record.get('ownerType') === 'OI') {
            return 'soNumContent';
          }
          return '';

        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI', 'OD'].includes(record.get('ownerType'));
        },
        required({ record }) {
          return ['CI', 'IIC', 'SI', 'IIS', 'OI', 'OD'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerCode.customerId',
      dynamicProps: {
        bind({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'ownerObject.customerId';
          }

          if (
            record.get('ownerType') === 'SI' ||
            record.get('ownerType') === 'IIS' ||
            record.get('ownerType') === 'OD'
          ) {
            return 'ownerObject.supplierId';
          }

          if (record.get('ownerType') === 'OI') {
            return 'ownerObject.soLineId';
          }

          return '';
        },
      },
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerName`).d('所有者描述'),
      bind: 'ownerCode.customerCode',
      dynamicProps: {
        bind({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'ownerObject.customerCode';
          }

          if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'ownerObject.supplierCode';
          }

          if (record.get('ownerType') === 'OI') {
            return 'ownerObject.soNumContent';
          }

          return '';
        },
      },
    },
    {
      name: 'ownerName',
      label: intl.get(`${modelPrompt}.ownerName`).d('所有者描述'),
      type: FieldType.string,
      disabled: true,
      dynamicProps: {
        bind({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'ownerObject.customerName';
          }

          if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'ownerObject.supplierName';
          }

          if (record.get('ownerType') === 'OI') {
            return 'ownerObject.soNumName';
          }
          return '';
        },
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
});
export { drawerDS };
