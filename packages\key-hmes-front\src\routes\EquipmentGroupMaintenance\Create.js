import React, { useState, useMemo, useEffect } from 'react';
import {
  DataSet,
  Table,
  Button,
  Form,
  TextField,
  Modal,
  Lov,
  Switch,
  NumberField,
  Select,
  Spin,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Collapse, Popconfirm, Badge, Tabs } from 'choerodon-ui';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import myInstance from 'hcm-components-front/lib/utils/myAxios';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { observer } from 'mobx-react';
import { BASIC } from '@utils/config';
import {
  detailTableDS,
  formDS,
  detailFormDS,
  singleAssObjectDS,
  assObjectsDS,
} from './stores/EquipmentGroupMaintenanceDs';
import detailHistoryAssignFactory from './stores/detailHistoryAssignDs';
import detailHistoryFactory from './stores/detailHistoryDs';
import { useDataSet, } from 'utils/hooks';
import AssociatedObjectsTab from './AssociatedObjectsTab';

const modelPrompt = 'tarzan.hmes.EquipmentGroupMaintenance';

// const BASIC = `/tarzan-mes-29210`;

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();

const { TabPane } = Tabs;
let materialList = [];
let lovData = {};

const Create = observer(props => {
  const {
    match: {
      params: { id },
    },
  } = props;
  const [loading, setLoading] = useState(false);
  const [disabledFlag, setDisabledFlag] = useState(true);
  const [flag, setFlag] = useState('');
  const [originTableList, setOriginTableList] = useState([]);
  const [originAssociatList, setOriginAssociatList] = useState([]);
  const [deleteFlag, setDeleteFlag] = useState(false);
  const [buttonVisible, setButtonVisible] = useState(true);

  const formDs = useMemo(() => new DataSet(formDS()), []);
  const singleAssObjectDs = useMemo(() => new DataSet(singleAssObjectDS(formDs)), []);
  const detailTableDs = useMemo(() => new DataSet(detailTableDS(formDs)), []);
  const detailFormDs = useMemo(() => new DataSet(detailFormDS(formDs)), []);
  const assObjectsDs = useMemo(() => new DataSet(assObjectsDS()), []);

  const detailHistoryDs = useDataSet(detailHistoryFactory, 'detailHistoryFactory');
  const detailHistoryAssignDs = useDataSet(detailHistoryAssignFactory, 'detailHistoryAssignFactory');


  useEffect(() => {
    if (id === 'create') {
      queryBasicData();
      setDisabledFlag(true);
      setFlag('NEW');
    } else {
      setDisabledFlag(false);
      setFlag('EDIT');
      handleSearchTable(id);
    }
  }, []);

  // 查询站点
  const queryBasicData = async () => {
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-points/get/user/def/site`;
    const res = await myInstance.get(url);
    if (res && !res.failed) {
      formDs.loadData([
        {
          siteCode: res.data.siteCode,
          siteId: res.data.siteId,
          enableFlag: 'Y',
        },
      ]);
    } else {
      notification.error({ message: res.message });
    }
  };

  // 查询头，行
  const handleSearchTable = assembleGroupId => {
    setLoading(true);
    request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-groups/detail/ui?assembleGroupId=${assembleGroupId}`,
      {
        method: 'GET',
      },
    )
      .then(res => {
        setLoading(false);
        if (res && res.failed) {
          notification.error({ message: res.message });
        } else {
          formDs.loadData([res]);
          // setTimeout(() => {
          setOriginTableList(res.assignList || []);
          setOriginAssociatList(res.objectList || []);
          detailTableDs.loadData(res.assignList || []);
          assObjectsDs.loadData(res.objectList || []);
          // }, 1000);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 打开弹框
  const openModal = () => {
    const modalKey = Modal.key();
    Modal.open({
      key: modalKey,
      // drawer: true,
      width: 400,
      title: intl.get(`${modelPrompt}.pointMantin`).d('装配点维护'),
      closable: true,
      children: (
        <Form dataSet={detailFormDs} columns={1}>
          <Lov name="assemblePointObj" onChange={lovRecord => handleMaterial(lovRecord)} />
          <TextField name="description" disabled />
        </Form>
      ),
      onOk: () => createLine(),
      onCancel: () => cancelLine(),
      // afterClose: () => detailFormDs.loadData([]),
    });
  };
  const handleMaterial = lovRecord => {
    lovData = lovRecord;
    // request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-groups/assemble/material/query`, {
    //   method: 'GET',
    //   query: {
    //     assembleGroupId: formDs.current?.get('assembleGroupId'),
    //     assemblePointId: lovRecord.assemblePointId,
    //   },
    // }).then(res => {
    //   if (res && !res.failed) {
    //     const list = res.map(item => {
    //       return {
    //         ...item,
    //         assembleGroupAssignId: null,
    //       };
    //     });
    //     materialList = list;
    //   } else {
    //     notification.error({ message: res.message });
    //   }
    // });
  };

  // 取消新增
  const cancelLine = () => {
    detailFormDs.loadData([]);
  };

  // 确定新增
  const createLine = async () => {
    let newLineNumber;
    const result = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-groups/assemble/material/query`,
      {
        method: 'GET',
        query: {
          assembleGroupId: formDs.current?.get('assembleGroupId'),
          assemblePointId: detailFormDs.current?.get('assemblePointId'),
        },
      },
    );
    const list = result.map(item => {
      return {
        ...item,
        assembleGroupAssignId: null,
      };
    });
    materialList = list;
    // setTimeout(() => {
    const list2 = detailTableDs.toData();
    if (materialList.length > 0) {
      for (let i = 0; i < materialList.length; i++) {
        for (let j = 0; j < list2.length; j++) {
          if (
            materialList[i].assemblePointId === list2[j].assemblePointId &&
            (materialList[i].materialId || null) === (list2[j].materialId || null) &&
            (materialList[i].revisionCode || null) === (list2[j].revisionCode || null)
          ) {
            notification.warning({
              message: intl
                .get(`${modelPrompt}.error.createdataExit`)
                .d(`新增数据已存在，请检查后再维护！`)
            });
            return false;
          }
        }
      }
      if (detailTableDs.length > 0) {
        newLineNumber =
          (detailTableDs.toData().sort((a, b) => b.serialNumber - a.serialNumber)[0].serialNumber ||
            0) + 10;
      } else {
        newLineNumber = 10;
      }
      const materialListNumber = materialList.map((item, index) => {
        if (detailTableDs.length > 0 && index === 0) {
          newLineNumber =
            (detailTableDs.toData().sort((a, b) => b.serialNumber - a.serialNumber)[0]
              .serialNumber || 0) + 10;
          return {
            ...item,
            serialNumber: newLineNumber,
          };
        }
        if (detailTableDs.length === 0 && index === 0) {
          newLineNumber = 10;
          return {
            ...item,
            serialNumber: newLineNumber,
          };
        }
        newLineNumber += 10;
        return {
          ...item,
          serialNumber: newLineNumber,
        };
      });
      materialListNumber.sort((a, b) => {
        return a.serialNumber > b.serialNumber ? -1 : 1;
      });
      const finalList = materialListNumber.concat(list2);
      detailTableDs.loadData(finalList);
      detailFormDs.loadData([]);
    } else {
      if (
        list2.filter(
          i =>
            i.assemblePointId === lovData.assemblePointId &&
            (i.materialId || null) === (lovData.materialId || null) &&
            (i.revisionCode || null) === (lovData.revisionCode || null),
        ).length > 0
      ) {
        notification.warning({
          message: intl
            .get(`${modelPrompt}.error.createdataExit`)
            .d(`新增数据已存在，请检查后再维护！`)
        });
        return false;
      }
      if (detailTableDs.length > 0) {
        newLineNumber =
          (detailTableDs.toData().sort((a, b) => b.serialNumber - a.serialNumber)[0].serialNumber ||
            0) + 10;
      } else {
        newLineNumber = 10;
      }
      detailTableDs.create({
        serialNumber: Number(newLineNumber),
        enableFlag: 'Y',
        assemblePointCode: lovData.assemblePointCode,
        assemblePointId: lovData.assemblePointId,
        description: lovData.description,
      });
    }
    // }, 500);
  };
  // 拼数据
  const collaborateData = data => {
    return data.map(item => {
      const obj = {
        ...item,
        assembleGroupObjectId: item.assembleGroupObjectId,
      };
      (item.objectList || []).forEach(it => {
        if (it.objectType === 'MATERIAL') {
          obj.materialId = it.objectId;
          obj.materialCode = it.objectCode;
          obj.materialName = it.objectDecs;
          obj.revisionCode = it.objectRevision;
        } else if (it.objectType === 'MATERIAL_CATEGORY') {
          obj.materialCategoryId = it.objectId;
        } else if (it.objectType === 'OPERATION') {
          obj.operationId = it.objectId;
          obj.operationName = it.objectCode;
          obj.operationDesc = it.objectDecs;
        } else if (it.objectType === 'WORKCELL') {
          obj.workcellId = it.objectId;
          obj.workcellCode = it.objectCode;
          obj.workcellName = it.objectDecs;
        } else if (it.objectType === 'EQUIPMENT') {
          obj.equipmentId = it.objectId;
          obj.equipmentCode = it.objectCode;
          obj.equipmentName = it.objectDecs;
        } else if (it.objectType === 'EQUIPMENT_CATEGORY') {
          obj.equipmentCategory = it.objectCode;
        }
      });
      return obj;
    });
  };

  // 保存
  const handelSave = async () => {
    setLoading(true);
    const validate = await formDs.validate(false, true);
    if (!validate) {
      setLoading(false);
      return;
    }
    await detailTableDs.validate().then(valiResult => {
      if (valiResult) {
        let params = {};
        let assignList = [];
        let objectList = [];
        const formObj = formDs.toData();
        const tableList = detailTableDs.toData();
        const assObjectsData = assObjectsDs.toData();
        if (tableList.length > 0) {
          const filterList = tableList.filter(item => !item.serialNumber);
          if (filterList.length > 0) {
            return notification.warning({
              message: intl
                .get(`${modelPrompt}.error.required`)
                .d(`请填写必填项`),
              placement: 'bottomRight',
            });
          }
        }
        const tagGroupObjectOriginList = collaborateData(originAssociatList);
        const tagGroupObjectList = collaborateData(assObjectsData);
        if (tableList.length) {
          if (originTableList.length && deleteFlag) {
            const tempList = originTableList.map(item => {
              return {
                ...item,
                deleteFlag: 'Y',
              };
            });
            for (let i = 0; i < tableList.length; i++) {
              for (let j = 0; j < tempList.length; j++) {
                if (tableList[i].assembleGroupAssignId === tempList[j].assembleGroupAssignId) {
                  tempList.splice(j, 1);
                }
              }
            }
            const finalList = tableList.concat(tempList);
            assignList = finalList;
          } else {
            assignList = tableList;
          }
        }
        if (assObjectsData.length) {
          if (originAssociatList.length && deleteFlag) {
            const tempList = tagGroupObjectOriginList.map(item => {
              return {
                ...item,
                deleteFlag: 'Y',
              };
            });
            for (let i = 0; i < tagGroupObjectList.length; i++) {
              for (let j = 0; j < tempList.length; j++) {
                if (
                  tagGroupObjectList[i].assembleGroupObjectId === tempList[j].assembleGroupObjectId
                ) {
                  tempList.splice(j, 1);
                }
              }
            }
            const finalList = tagGroupObjectList.concat(tempList);
            objectList = finalList;
          } else {
            objectList = tagGroupObjectList;
          }
        }
        if (tableList.length === 0) {
          if (deleteFlag && originTableList.length) {
            const tempList = originTableList.map(item => {
              return {
                ...item,
                deleteFlag: 'Y',
              };
            });
            assignList = tempList;
          } else {
            assignList = [];
          }
        }
        if (assObjectsData.length === 0) {
          if (deleteFlag && originAssociatList.length) {
            const tempListTwo = tagGroupObjectOriginList.map(item => {
              return {
                ...item,
                deleteFlag: 'Y',
              };
            });
            objectList = tempListTwo;
          } else {
            objectList = [];
          }
        }
        params = {
          ...formObj[0],
          assignList,
          objectList,
        };
        request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-groups/save/ui`, {
          method: 'post',
          body: { ...params },
        }).then(res => {
          setLoading(false);
          if (res && !res.failed) {
            notification.success();
            if (id === 'create') {
              props.history.push({
                pathname: `/hmes/equipment-group-maintenance/${res}`,
              });
            } else {
              handleSearchTable(res);
            }
            setDisabledFlag(false);
            setFlag('EDIT');
          } else {
            notification.error({ message: res.message });
          }
        });
      } else {
        setLoading(false);
        return notification.warning({
          message: intl
            .get(`${modelPrompt}.error.required`)
            .d(`请填写必填项`),
          placement: 'bottomRight',
        });
      }
    });
  };

  // 编辑按钮
  const handelEdit = () => {
    setDisabledFlag(true);
  };

  // 取消按钮
  const handelCancel = () => {
    const { history } = props;
    if (flag === 'NEW') {
      history.push(`/hmes/equipment-group-maintenance/list`);
    } else {
      handleSearchTable(id);
      setDisabledFlag(false);
    }
  };

  // 更改tab页按钮控制
  const handleKey = key => {
    if (key === '1') {
      setButtonVisible(true);
    } else {
      setButtonVisible(false);
    }
  };

  // lov变化事件
  const changeObject = (lovRecords, record) => {
    if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
      record.getField('revisionCode').set('required', true);
      record.set('revisionFlag', 'Y');
      record.set('revisionCode', lovRecords.revisionCode);
    } else {
      record.getField('revisionCode').set('required', false);
      record.set('revisionCode', '');
      record.set('revisionFlag', 'N');
    }
  };

  // 版本下拉框变化事件
  const changeVersion = record => {
    if (record.data.revisionFlag === 'Y') {
      record.getField('revisionCode').set('required', true);
    }
  };

  // 增量同步
  const handleIncrementalSynchronization = () => {
    setLoading(true);
    request(
      `${BASIC.HMES_BASIC
      }/v1/${tenantId}/hme-assemble-groups/incremental/synchronization?assembleGroupId=${formDs.current?.get(
        'assembleGroupId',
      )}`,
      {
        method: 'GET',
      },
    ).then(res => {
      setLoading(false);
      if (res && !res.failed) {
        notification.success();
        const list = detailTableDs.toData();
        const finalList = list.concat(res);
        detailTableDs.loadData(finalList);
        setDisabledFlag(true);
        setFlag('EDIT');
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  // 全量同步
  const handleFullSynchronization = () => {
    setLoading(true);
    request(
      `${BASIC.HMES_BASIC
      }/v1/${tenantId}/hme-assemble-groups/full/synchronization?assembleGroupId=${formDs.current?.get(
        'assembleGroupId',
      )}`,
      {
        method: 'GET',
      },
    ).then(res => {
      setLoading(false);
      setDeleteFlag(true);
      if (res && !res.failed) {
        notification.success();
        detailTableDs.loadData(res);
        setDisabledFlag(true);
        setFlag('EDIT');
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  const handleDeleteAssociat = () => {
    setDeleteFlag(true);
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!disabledFlag}
          onClick={() => openModal(detailTableDs.current)}
          funcType="flat"
          // shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl
            .get(`${modelPrompt}.error.delete`)
            .d(`是否确认删除？`)}
          onConfirm={() => {
            detailTableDs.remove(record);
            setDeleteFlag(true);
          }}
        >
          <Button
            funcType="flat"
            icon="remove"
            shape="circle"
            size="small"
            disabled={!disabledFlag}
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    // 序号
    {
      name: 'serialNumber',
      align: 'left',
      editor: disabledFlag && <NumberField dataSet={detailTableDs} name="serialNumber" required />,
    },
    // 装配点编码
    {
      name: 'assemblePointCode',
      align: 'left',
      renderer: ({ record }) => record.get('assemblePointCode'),
    },
    // 装配点描述
    {
      name: 'description',
      align: 'left',
      renderer: ({ record }) => record.get('description'),
    },
    // 物料编码
    {
      name: 'materialObj',
      align: 'left',
      renderer: ({ record }) => record.get('materialCode'),
      editor: record => {
        return (
          disabledFlag && (
            <Lov
              dataSet={detailTableDs}
              name="materialObj"
              onChange={lovRecords => changeObject(lovRecords, record)}
            />
          )
        );
      },
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
    },
    // 单位
    {
      name: 'uomName',
      align: 'left',
    },
    // 物料版本
    {
      name: 'revisionCode',
      align: 'left',
      editor: record => {
        return (
          disabledFlag &&
          record.get('revisionFlag') === 'Y' && (
            <Select
              name="revisionCode"
              dataSet={detailTableDs}
              onChange={() => changeVersion(record)}
            />
          )
        );
      },
    },
    // 最大装载量
    {
      name: 'maxQty',
      align: 'left',
      editor: disabledFlag && <NumberField dataSet={detailTableDs} name="maxQty" />,
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      editor: disabledFlag && <Select dataSet={detailTableDs} name="enableFlag" />,
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '有效' : '无效'}>
          { }
        </Badge>
      ),
    },
  ];

  const columnHistory = [
    {
      name: 'assemblePointCode',
    },
    {
      name: 'description',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'uomName',
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'maxQty',
    },
    {
      name: 'enableFlag',
    },
    {
      name: 'creationDate',
      width: 150
    },
    {
      name: 'createdByRealName',
    },
  ]

  const columnAssignHistory = [
    {
      name: 'objectType',
    },
    {
      name: 'objectCode',
    },
    {
      name: 'objectDesc',
    },
    {
      name: 'objectRevision',
    },
    {
      name: 'revisionFlag',
    },
    {
      name: 'enableFlag',
    },
    {
      name: 'creationDate',
      width: 150
    },
    {
      name: 'createdByRealName',
    },
  ]

  const handleHistory = () => {
    detailHistoryDs.setQueryParameter('assembleGroupIds', [id])
    detailHistoryDs.query()
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history`).d('历史查询'),
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table
        dataSet={detailHistoryDs}
        columns={columnHistory}
      />,
    });
  }

  const handleHistoryAssign = () => {
    detailHistoryAssignDs.setQueryParameter('assembleGroupIds', [id])
    detailHistoryAssignDs.query()
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history`).d('历史查询'),
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table
        dataSet={detailHistoryAssignDs}
        columns={columnAssignHistory}
      />,
    });
  }

  const buttonsAssign = [
    <div>
      <Button onClick={handleHistoryAssign} color="primary" >
        历史查询
      </Button>
    </div>
  ]

  const HeaderButtons = () => {
    return (
      <>
        {buttonVisible && (
          <div>
            <Button
              onClick={handleIncrementalSynchronization}
              color="primary"
              disabled={disabledFlag}
            >
              增量同步
            </Button>
            <Button onClick={handleFullSynchronization} color="primary" disabled={disabledFlag}>
              全量同步
            </Button>
            <Button onClick={handleHistory} color="primary" >
              历史查询
            </Button>
          </div>
        )}
      </>
    );
  };

  const buttons = [<HeaderButtons />];

  return (
    <React.Fragment>
      <Header
        title={id === 'create' ? intl.get(`${modelPrompt}.title`).d('新建装配组') : intl.get(`${modelPrompt}.title`).d('编辑装配组')}
        backPath="/hmes/equipment-group-maintenance/list"
      >
        {!disabledFlag && (
          <Button onClick={handelEdit} style={{ marginRight: 15 }} icon="edit" color="primary">
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        )}
        {disabledFlag && (
          <>
            <Button onClick={handelSave} style={{ marginRight: 15 }} icon="save" color="primary">
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button onClick={handelCancel}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Collapse bordered={false} defaultActiveKey={['1']}>
            <Panel header={intl.get(`${modelPrompt}.basic`).d('基本属性')} key="1">
              <Form dataSet={formDs} columns={3} labelWidth="200">
                <Lov
                  name="siteObj"
                  disabled={!disabledFlag || detailTableDs.length || assObjectsDs.length}
                />
                <TextField name="assembleGroupCode" disabled={id !== 'create'} />
                <TextField name="description" disabled={!disabledFlag} />
                <Switch name="enableFlag" disabled={!disabledFlag} />
                <Select name="groupType" disabled={!disabledFlag} />
              </Form>
            </Panel>
          </Collapse>
          <Tabs defaultActiveKey="1" onChange={handleKey}>
            <TabPane tab={intl.get(`${modelPrompt}.point`).d('装配点信息')} key="1">
              <Table
                dataSet={detailTableDs}
                buttons={buttons}
                columns={columns}
                style={{ height: 400 }}
              // dragRow
              />
            </TabPane>
            <TabPane tab={intl.get(`${modelPrompt}.associatedObject`).d('关联对象')} key="2">
              <AssociatedObjectsTab
                assObjectsDs={assObjectsDs}
                buttons={buttonsAssign}
                singleAssObjectDs={singleAssObjectDs}
                detailDs={formDs}
                canEdit={disabledFlag}
                handleDeleteAssociat={handleDeleteAssociat}
              />
            </TabPane>
          </Tabs>
        </Spin>
      </Content>
    </React.Fragment>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.EquipmentGroupMaintenance', 'tarzan.common'],
})(Create);
