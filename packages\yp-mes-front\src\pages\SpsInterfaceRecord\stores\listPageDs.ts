import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';
import moment from 'moment';

const modelPrompt = 'tarzan.hmes.spsInterfaceRecord';

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'lampStatusId',
    selection: false,
    paging: true,
    pageSize: 10,
    autoQuery: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'interfaceName',
          label: intl.get(`${modelPrompt}.form.interfaceName`).d('接口名称'),
          type: FieldType.string,
        },
        {
          name: 'startTime',
          type: FieldType.dateTime,
          dynamicProps: {
            min: ({ record }) => {
              if (record?.get('endTime')) {
                return moment(record?.get('endTime')).subtract(6, 'months');
              }
            },
          },
          label: intl.get(`${modelPrompt}.form.startTime`).d('开始时间'),
          defaultValue: moment(
            moment()
              .subtract(1, 'weeks')
              .format('YYYY-MM-DD HH:mm:ss'),
          ),
        },
        {
          name: 'endTime',
          type: FieldType.dateTime,
          dynamicProps: {
            max: ({ record }) => {
              if (record?.get('startTime')) {
                return moment(record?.get('startTime')).add(6, 'months');
              }
            },
          },
          min: 'startTime',
          label: intl.get(`${modelPrompt}.form.endTime`).d('结束时间'),
          defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
        },
        {
          name: 'responseTime',
          type: FieldType.number,
          label: intl.get(`${modelPrompt}.form.responseTime`).d('响应耗时'),
        },
        {
          name: 'requestStatus',
          lookupCode: 'YP_MES.MES.INVOKE_STATUS',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.requestStatus`).d('状态'),
        },
        {
          name: 'requestBodyParameter',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.requestBodyParameter`).d('请求body参数'),
        },
      ],
    }),
    fields: [
      {
        name: 'interfaceName',
        label: intl.get(`${modelPrompt}.form.interfaceName`).d('接口名称'),
        type: FieldType.string,
      },
      {
        name: 'requestBodyParameter',
        label: intl.get(`${modelPrompt}.form.requestBodyParameter`).d('请求报文'),
        type: FieldType.string,
      },
      {
        name: 'requestMethod',
        label: intl.get(`${modelPrompt}.form.requestMethod`).d('请求方式'),
        type: FieldType.string,
      },
      {
        name: 'requestTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.requestTime`).d('请求时间'),
      },
      {
        name: 'responseTime',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.form.responseTime`).d('响应耗时'),
      },
      {
        name: 'requestStatus',
        type: FieldType.string,
        lookupCode: 'YP_MES.MES.INVOKE_STATUS',
        label: intl.get(`${modelPrompt}.form.requestStatus`).d('状态'),
      },
      {
        name: 'action',
        label: intl.get(`${modelPrompt}.form.action`).d('操作'),
      },
      {
        name: 'responseContent',
        label: intl.get(`${modelPrompt}.form.responseContent`).d('响应报文'),
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'GET',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-invoke-outbounds/list`,
        };
      },
    },
  });

export default listPageFactory;
