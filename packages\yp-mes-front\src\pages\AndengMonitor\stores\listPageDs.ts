import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';
import moment from 'moment'

const modelPrompt = 'tarzan.hmes.andengMonitor';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'lampStatusId',
    selection: false,
    paging: true,
    pageSize: 10,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'equipmentLov',
          label: intl.get(`${modelPrompt}.form.equipmentLov`).d('设备编码'),
          ignore: FieldIgnore.always,
          required:true,
          type: FieldType.object,
          lovCode: 'MT.MODEL.EQUIPMENT',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'equipmentId',
          bind: 'equipmentLov.equipmentId',
        },
        {
          name: 'statusCode',
          type: FieldType.string,
          lookupCode: 'HME.LAMP_CALL_STATUS',
          label: intl.get(`${modelPrompt}.form.statusCode`).d('安灯状态'),
        },
        {
          name: 'positionCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.positionCode`).d('定位编码'),
        },
        {
          name: 'startTimeFrom',
          type: FieldType.dateTime,
          required:true,
          defaultValue: moment().subtract(3, 'days'),
          label: intl.get(`${modelPrompt}.form.startTimeFrom`).d('开始时间从'),
        },
        {
          name: 'startTimeTo',
          required:true,
          type: FieldType.dateTime,
          min:'startTimeFrom', 
          defaultValue:new Date(),
          label: intl.get(`${modelPrompt}.form.startTimeTo`).d('开始时间至'),
        },
      ],
    }),
    fields: [
      {
        name: 'equipmentCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.equipmentName`).d('设备名称'),
      },
      {
        name: 'statusCode',
        type: FieldType.string,
        lookupCode: 'HME.LAMP_CALL_STATUS',
        label: intl.get(`${modelPrompt}.form.statusCode`).d('安灯状态'),
      },
      {
        name: 'positionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.positionCode`).d('定位编码'),
      },
      {
        name: 'equipmentLocationName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.equipmentLocationName`).d('设备位置'),
      },
      {
        name: 'startTime',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.form.startTime`).d('开始时间'),
      },
      {
        name: 'endTime',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.form.endTime`).d('结束时间'),
      },
      {
        name: 'continueTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.continueTime`).d('状态持续时长'),
      },
      {
        name: 'processBarcode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.processBarcode`).d('产品条码'),
      },
      {
        name: 'loginName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.loginName`).d('操作员'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method:'GET',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-lamp-status-watch/watch/ui`,
        };
      },
    },
  });

export default listPageFactory;
