/**
 * @Description: 物料批管理平台-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-03-01 10:47:34
 * @LastEditTime: 2023-05-18 16:06:29
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useEffect } from 'react';
import {
  Button,
  Form,
  TextField,
  NumberField,
  Switch,
  DataSet,
  Lov,
  Select,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Spin, Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import moment from 'moment';
import notification from 'utils/notification';
import { C7nFormItemSort, AttributeDrawer } from '@components/tarzan-ui';
import { getUserRole } from '@services/api';
import { getResponse } from '@utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { detailDS } from '../stores/DetailDS';
import { FetchMaterialLotDetail, SaveMaterialLotDetail, FetchExpirationDate } from '../services';
import styles from './index.modules.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.product.materialLotTrance';

const MaterialLotTraceDetail = props => {
  const { history } = props;
  const { id } = props.match.params;
  const {
    match: { path },
    customizeForm,
    custConfig,
  } = props;
  const _updateFlag = id !== 'create';

  const [canEdit, setCanEdit] = useState(false);
  const [createNumber, setCreateNumber] = useState(1);
  const [userRoleFlag, setUserRoleFlag] = useState(false);
  const fetchMaterialLotDetail = useRequest(FetchMaterialLotDetail(), {
    manual: true,
  });
  const fetchExpirationDate = useRequest(FetchExpirationDate(), {
    manual: true,
  });
  const saveMaterialLotDetail = useRequest(SaveMaterialLotDetail(), {
    manual: true,
  });

  const detailDs = useMemo(() => new DataSet({ ...detailDS() }), []);

  useEffect(() => {
    getUserRole().then(res => {
      const result = getResponse(res);
      if (result) {
        setUserRoleFlag(result.rows === 'Y');
      }
    });
  }, []);

  useEffect(() => {
    if (props.match.params.id === 'create') {
      setCanEdit(true);
      detailDs.current!.set('productionDate', moment().format('YYYY-MM-DD HH:mm:ss'));
      return;
    }
    initPageData();
  }, [props.match.params.id]);

  const initPageData = () => {
    detailDs.query();
    fetchMaterialLotDetail.run({
      params: {
        materialLotId: props.match.params.id,
      },
      onSuccess: res => {
        if (!res.secondaryUomId && res.secondaryUomCode) {
          res.secondaryUomCode = '';
        }
        detailDs.loadData([res]);
        // 根据materialLov下的secondaryUomType，来判断物料是否启用辅单位
        detailDs!.current!.set('materialLov', {
          ...detailDs!.current!.get('materialLov'),
          secondaryUomType: res.secondaryUomType,
        });
      },
    });
  };

  const handleCancel = () => {
    if (id === 'create') {
      history.push('/hmes/product/material-lot-traceability/list');
    } else {
      setCanEdit(prev => !prev);
      initPageData();
    }
  };

  const handleSave = async () => {
    const validate = await detailDs.validate();
    if (!validate) {
      return;
    }
    saveMaterialLotDetail.run({
      params: {
        ...detailDs!.current!.toData(),
        createNumber,
      },
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        if (res.length === 1) {
          history.push(`/hmes/product/material-lot-traceability/detail/${res[0]}`);
        } else {
          history.push(`/hmes/product/material-lot-traceability`);
        }
      },
    });
  };

  const handleChangeSite = () => {
    detailDs!.current!.init('materialLov', undefined);
    detailDs!.current!.init('primaryUomLov', undefined);
    detailDs!.current!.init('secondaryUomLov', undefined);
    detailDs!.current!.init('locatorLov', undefined);
    handleInitUomQty();
    handleInitExpirationDate();
  };

  const handleInitUomQty = () => {
    if (detailDs!.current!.get('createReason') === 'INVENTORY') {
      detailDs!.current!.init('primaryUomQty', 0);
      if (detailDs!.current!.get('secondaryUomType')) {
        detailDs!.current!.init('secondaryUomQty', 0);
      } else {
        detailDs!.current!.init('secondaryUomQty', null);
      }
    } else {
      detailDs.current!.init('primaryUomQty', null);
      detailDs.current!.init('secondaryUomQty', null);
    }
  };

  /*
   当到期日期是默认带出的时，相关字段变化之后到期日期要做到实时更新
   当到期日期是用户手选的时，相关字段变化之后到期日期不实时更新
   当站点、物料、库位清空时，到期日期清空
   */
  const handleInitExpirationDate = () => {
    const {
      siteId,
      materialId,
      productionDate,
      locatorId,
    } = detailDs.current?.toData();
    if (siteId && materialId && !detailDs.getField('expirationDate')!.isDirty(detailDs.current)) {
      fetchExpirationDate.run({
        params: {
          siteId,
          materialId,
          productionDate,
          organizationType: 'LOCATOR',
          organizationId: locatorId || undefined,
        },
        onSuccess: res => {
          detailDs.current!.init('expirationDate', res);
        },
      });
    } else if (!(siteId && materialId)) {
      detailDs.current!.init('expirationDate', null);
    }
  }

  const handleChangeEnableFlag = () => {
    if (!detailDs.current!.get('locatorId')) {
      detailDs!.current!.init('locatorLov', undefined);
    }
  };

  const handleChangeReservedFlag = () => {
    detailDs!.current!.set('reservedObjectType', '');
    detailDs!.current!.init('reservedObjectLov', undefined);
  };

  const handleChangeMaterial = val => {
    detailDs.current!.init('revisionCode', null);
    handleInitUomQty();
    handleInitExpirationDate();
    if (val) {
      // 选择了物料
      detailDs!.current!.init('primaryUomLov', {
        uomId: val.uomId,
        uomCode: val.primaryUomCode,
      });
      if (val.secondaryUomId) {
        detailDs!.current!.init('secondaryUomLov', {
          uomId: val.secondaryUomId,
          uomCode: val.secondaryUomCode,
        });
      } else {
        detailDs!.current!.init('secondaryUomLov', undefined);
      }
    } else {
      // 清空物料
      detailDs!.current!.init('primaryUomLov', undefined);
      detailDs!.current!.init('secondaryUomLov', undefined);
    }
  };

  const handleChangePrimaryUom = () => {
    if (detailDs!.current!.get('createReason') === 'INVENTORY') {
      detailDs!.current!.init('primaryUomQty', 0);
    } else {
      detailDs!.current!.init('primaryUomQty', null);
    }
  };

  const handleChangeSecondaryUom = () => {
    if (
      detailDs!.current!.get('createReason') === 'INVENTORY' &&
      detailDs!.current!.get('secondaryUomType')
    ) {
      detailDs!.current!.init('secondaryUomQty', 0);
    } else {
      detailDs!.current!.init('secondaryUomQty', null);
    }
  };

  const handleChangeLocator = values => {
    handleInitExpirationDate();
    if (values) {
      detailDs!.current!.set('wareHouse', values.wareHouse || values.locatorCode);
    } else {
      detailDs!.current!.set('wareHouse', '');
    }
  };

  const handleChangeCurrentContainer = values => {
    if (values) {
      detailDs!.current!.set('topContainerCode', values.topContainerCode || values.containerCode);
    } else {
      detailDs!.current!.set('topContainerCode', '');
    }
  };

  const handleChangeAssembleTool = () => {
    detailDs!.current!.init('assemblePointLov', null);
  };

  const handleChangeOwnerType = () => {
    detailDs!.current!.init('ownerLov', undefined);
  };

  const handleChangeSupplier = () => {
    detailDs!.current!.init('supplierSiteLov', undefined);
  };

  const handleChangeInstructionDoc = () => {
    detailDs!.current!.init('instructionLov', undefined);
  };

  const handleChangeReservedObjectType = () => {
    detailDs!.current!.init('reservedObjectLov', undefined);
  };

  const handleChangeCustomer = () => {
    detailDs!.current!.init('customerSiteLov', undefined);
  };

  // 头上输入框
  const onChangeNum = (val) => {
    setCreateNumber(val)
    detailDs!.current!.set('createMultiCodeNum', val);
    if (val >= 2) {
      detailDs!.current!.set('materialLotCode', '');
      detailDs!.current!.set('identification', '');
    }
  };

  return (
    <div className="hmes-style" style={{height: '100%', overflow: 'auto'}}>
      <Spin spinning={fetchMaterialLotDetail.loading || saveMaterialLotDetail.loading}>
        <Header
          title={intl.get(`${modelPrompt}.materialLotMaintenance`).d('物料批查询')}
          backPath="/hmes/product/material-lot-traceability/list"
        >
          {canEdit && (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && (
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              onClick={() => {
                setCanEdit(prev => !prev);
              }}
              disabled={detailDs!.current!.get('initialFlag') === 'Y' && !userRoleFlag}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.modify`).d('修正')}
            </PermissionButton>
          )}
          {!_updateFlag && <NumberField prefix={intl.get(`${modelPrompt}.createNum`).d("创建条码个数:")} min={1} precision={0} step={1} className={styles['create-num']} onChange={onChangeNum} />}
          <AttributeDrawer
            className='org.tarzan.mes.domain.entity.MtMaterialLot'
            kid={id}
            canEdit={canEdit}
            disabled={id === 'create'}
            serverCode={BASIC.HMES_BASIC}
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_DETAIL.BUTTON`}
            custConfig={custConfig}
          />
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'baseInfo',
              'physicalInfo',
              'positionInfo',
              'ownershipAndSourceInfo',
              'customerAndReservedInfo',
              'operationInfo',
            ]}
          >
            <Panel
              header={intl.get(`${modelPrompt}.baseInfo`).d('基础属性')}
              key="baseInfo"
              dataSet={detailDs}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_DETAIL.BASIC`,
                },
                <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={3}>
                  <TextField name="materialLotCode" disabled={_updateFlag} />
                  <Lov name="siteLov" onChange={handleChangeSite} disabled={_updateFlag} />
                  <Select
                    name="createReason"
                    disabled={_updateFlag}
                    onChange={() => handleInitUomQty()}
                  />
                  <TextField name="identification" disabled={_updateFlag} />
                  <Select name="qualityStatus" />
                  <Select name="materialLotStatus" />
                  <TextField name="lot" />
                  <TextField name="supplierLot" />
                  <NumberField name="extendedShelfLifeTimes" />
                  <DateTimePicker name="productionDate" onChange={handleInitExpirationDate} />
                  <DateTimePicker name="expirationDate" />
                  <Switch
                    name="enableFlag"
                    disabled={!_updateFlag}
                    onChange={handleChangeEnableFlag}
                  />
                  <Switch name="reservedFlag" onChange={handleChangeReservedFlag} />
                  <Switch name="freezeFlag" />
                  <Switch name="stocktakeFlag" />
                  <Switch name="overOrderInterceptionFlag" disabled />
                  <Lov name="equipmentLov"/>
                </Form>,
              )}
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.physicalInfo`).d('实物信息')}
              key="physicalInfo"
              dataSet={detailDs}
            >
              <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={3}>
                <C7nFormItemSort name="materialLov" itemWidth={['70%', '30%']}>
                  <Lov name="materialLov" onChange={handleChangeMaterial} />
                  <Select
                    name="revisionCode"
                  // dropdownMatchSelectWidth={false}
                  // dropdownMenuStyle={{ width: '200px' }}
                  />
                </C7nFormItemSort>
                <TextField name="materialDesc" />
                <NumberField name="secondaryUomQty" />
                <NumberField name="primaryUomQty" />
                <Lov name="primaryUomLov" onChange={handleChangePrimaryUom} />
                <Lov name="secondaryUomLov" onChange={handleChangeSecondaryUom} />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.positionInfo`).d('位置信息')}
              key="positionInfo"
              dataSet={detailDs}
            >
              <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={3}>
                <Lov name="locatorLov" onChange={handleChangeLocator} />
                <TextField name="wareHouse" />
                <DateTimePicker name="inSiteTime" />
                <Lov
                  name="currentContainerLov"
                  onChange={handleChangeCurrentContainer}
                  disabled={!_updateFlag}
                />
                <TextField name="topContainerCode" />
                <DateTimePicker name="inLocatorTime" />
                <Lov
                  name="assembleToolLov"
                  onChange={handleChangeAssembleTool}
                  disabled={!_updateFlag}
                />
                <Lov name="assemblePointLov" disabled={!_updateFlag} />
                <DateTimePicker name="loadTime" />
                <DateTimePicker name="unloadTime" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.ownershipAndSourceInfo`).d('所有权及来源信息')}
              key="ownershipAndSourceInfo"
              dataSet={detailDs}
            >
              <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={3}>
                <Select
                  name="ownerType"
                  onChange={handleChangeOwnerType}
                  className={styles['un-clear-select']}
                />
                <Lov name="ownerLov" />
                <TextField name="ownerDesc" />
                <Lov name="supplierLov" onChange={handleChangeSupplier} />
                <TextField name="supplierDesc" />
                <Lov name="eoLov" />
                <Lov name="supplierSiteLov" />
                <TextField name="supplierSiteDesc" />
                <TextField name="ovenNumber" />
                <Lov
                  name="instructionDocLov"
                  onChange={handleChangeInstructionDoc}
                  disabled={!_updateFlag}
                />
                <Lov name="instructionLov" disabled={!_updateFlag} />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.customerAndReservedInfo`).d('客户及预留信息')}
              key="customerAndReservedInfo"
              dataSet={detailDs}
            >
              <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={3}>
                <Select name="reservedObjectType" onChange={handleChangeReservedObjectType} />
                <Lov name="reservedObjectLov" />
                <Lov name="customerLov" onChange={handleChangeCustomer} />
                <Lov name="customerSiteLov" />
                <TextField name="customerSiteDesc" />
                <TextField name="customerDesc" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.operationInfo`).d('操作信息')}
              key="operationInfo"
              dataSet={detailDs}
            >
              <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={3}>
                <TextField name="creationDate" />
                <TextField name="createdUsername" />
                <TextField name="lastUpdateDate" />
                <TextField name="lastUpdatedUsername" />
                <TextField name="printTimes" />
              </Form>
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.product.materialLotTrance', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_DETAIL.BUTTON`],
  // @ts-ignore
})(MaterialLotTraceDetail));
