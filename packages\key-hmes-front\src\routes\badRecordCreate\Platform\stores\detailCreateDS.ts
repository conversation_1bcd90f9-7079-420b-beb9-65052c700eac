import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment/moment';

const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';
const tenantId = getCurrentOrganizationId();

const formDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  events:{
    update({name,record}) {
      if(name==='rootCauseWorkcellLov'){
        record.set('rootCauseEquipmentCode',undefined)
        record.set('rootCauseOperationCode',undefined)
      }
    }
  },
  fields: [
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码'),
      lovCode: 'MT.NC_CODE',
      lovPara: { tenantId },
      required: true,
      multiple: true,
      ignore: FieldIgnore.always,
      textField: 'description',
    },
    {
      name: 'ncCodeId',
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCodeDesc',
      bind: 'ncCodeLov.description',
    },
    {
      name: 'rootCauseWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.rootCauseWorkcellName`).d('不良产生工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => ({
          tenantId,
          workcellType: 'STATION',
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'rootCauseWorkcellName',
      bind: 'rootCauseWorkcellLov.workcellName',
    },
    {
      name: 'rootCauseWorkcellId',
      bind: 'rootCauseWorkcellLov.workcellId',
    },
    {
      name: 'rootCauseEquipmentCode',
      disabled: true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootCauseEquipmentCode`).d('不良产生设备'),
    },
    {
      name: 'rootCauseEquipmentId',
    },
    {
      name: 'rootCauseOperationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootCauseOperationCode`).d('不良产生工艺'),
      disabled: true,
    },
    {
      name: 'rootCauseOperationId',
    },
    {
      name: 'responsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('不良责任人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: { tenantId },
    },
    {
      name: 'responsibleUserId',
      bind: 'responsibleUserLov.userId',
    },
    {
      name: 'responsibleUserName',
      bind: 'responsibleUserLov.realName',
    },
    {
      name: 'responsibleApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleApartment`).d('不良责任部门'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        data: {
          ...data,
          shiftDate: data.shiftDate ? moment(data.shiftDate).format('yyyy-MM-DD') : undefined,
        },
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-nc-record/platform/head/query/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.LIST`,
        method: 'GET',
      };
    },
  },
});

export { formDS };
