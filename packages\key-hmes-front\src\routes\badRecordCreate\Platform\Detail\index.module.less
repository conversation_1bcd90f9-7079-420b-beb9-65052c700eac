.components-select-item {
  height: 450px;
}

.searchTable {
  :global {
    .c7n-pro-table-filter-buttons,
    .c7n-pro-table-filter-menu-query {
      display: none;
    }
  }
}

.searchButton {
  position: absolute;
  top: 61px;
  right: 28px;

  :global {
    .c7n-pro-btn.c7n-pro-btn.c7n-pro-btn {
      height: 26px;
    }
  }
}

.button-group {
  display: flex;
  flex-direction: row-reverse;
}

.card-item {
  display: flex;
  align-items: center;
  padding-left: 0.2rem;
  margin-bottom: 0.2rem;
}

.card-item-from {
  width: 97%;
  border: 1px solid rgba(229, 229, 229, 1);
  border-radius: 2px;
  margin-left: 0.1rem;
  padding: 0.1rem;
}

.assemble-load-row {
  :global {
    .c7n-col-6,
    .c7n-col-3 {
      line-height: 0.28rem;
    }
  }
}
