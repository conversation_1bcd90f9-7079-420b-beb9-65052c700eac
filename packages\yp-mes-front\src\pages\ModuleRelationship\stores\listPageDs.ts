import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.hmes.moduleRelationship';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'modulePositionRelId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'packMaterialLov',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          label: intl.get(`${modelPrompt}.packMaterial`).d('PACK物料编码'),
          lovCode: 'MT.MATERIAL.PERMISSION',
          multiple: true,
          lovPara: {
            tenantId,
          }
        },
        {
          name: 'packMaterialIds',
          bind: 'packMaterialLov.materialId',
        },
        {
          name: 'moduleMaterialLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.moduleMaterial`).d('模组物料编码'),
          lovCode: 'MT.MATERIAL.PERMISSION',
          ignore: FieldIgnore.always,
          multiple: true,
          lovPara: {
            tenantId,
          }
        },
        {
          name: 'moduleMaterialIds',
          bind: 'moduleMaterialLov.materialId',
        },
      ]
    }),
    fields: [
      {
        name: 'packMaterialLov',
        type: FieldType.object,
        required: true,
        label: intl.get(`${modelPrompt}.packMaterial`).d('PACK物料编码'),
        lovCode: 'MT.MATERIAL.PERMISSION',
        lovPara: {
          tenantId,
        }
      },
      {
        name: 'packMaterialId',
        bind: 'packMaterialLov.materialId',
      },
      {
        name: 'packMaterialCode',
        bind: 'packMaterialLov.materialCode',
      },
      {
        name: 'packMaterialName',
        type: FieldType.string,
        bind: 'packMaterialLov.materialName',
        label: intl.get(`${modelPrompt}.packMaterialName`).d('PACK物料名称'),
      },
      {
        name: 'moduleMaterialLov',
        type: FieldType.object,
        required: true,
        label: intl.get(`${modelPrompt}.moduleMaterial`).d('模组物料编码'),
        lovCode: 'MT.MATERIAL.PERMISSION',
        lovPara: {
          tenantId,
        }
      },
      {
        name: 'moduleMaterialId',
        bind: 'moduleMaterialLov.materialId',
      },
      {
        name: 'moduleMaterialCode',
        bind: 'moduleMaterialLov.materialCode',
      },
      {
        name: 'moduleMaterialName',
        type: FieldType.string,
        bind: 'moduleMaterialLov.materialName',
        label: intl.get(`${modelPrompt}.moduleMaterialName`).d('模组物料名称'),
      },
      {
        name: 'modulePosition',
        required: true,
        step: 1,
        min: 0,
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.moduleMaterialName`).d('模组位置'),
      }
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'POST',
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-module-position-rels/query`,
        };
      },
    },
  });

export default listPageFactory;
