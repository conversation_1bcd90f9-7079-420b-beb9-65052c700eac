import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.cassetteMaterial';

const detailPageFactory = () =>
  new DataSet({
    primaryKey: 'lampStatusId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
     
        {
          name: 'boxCodes',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.boxCode`).d('料盒编码'),
        },
        {
          name: 'identifications',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.identification`).d('产品条码'),
        },
        {
          name: 'materialLotCodes',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.materialLotCode`).d('物料批'),
        },
      ],
    }),
    fields: [
      {
        name: 'boxCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.boxCode`).d('料盒编码'),
      },
      {
        name: 'materialLotCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialLotCode`).d('物料批条码'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
      },
      {
        name: 'primaryUomQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.primaryUomQty`).d('剩余数量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.uomCode`).d('主单位'),
      },
      {
        name: 'loadTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.loadTime`).d('装载时间'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'POST',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-box-binding/material/box/ui`,
        };
      },
    },
  });

export default detailPageFactory;
