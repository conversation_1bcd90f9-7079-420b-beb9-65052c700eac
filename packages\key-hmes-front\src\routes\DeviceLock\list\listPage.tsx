import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, Modal, Form, TextArea, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { FieldType } from 'choerodon-ui/dataset/data-set/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import notification from 'utils/notification'
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import listPageFactory from '../stores/listPageDs';
import axios from 'axios';

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.ass.deviceLock';

const ListPageComponent: FC<ListPageProps> = ({ listDs, history }) => {

  const [submitFlag, setSubmitFlag] = useState(true)

  const [historyFlag, setHistoryFlag] = useState(true)


  useEffect(() => {
    listDs.query();
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'equipmentCode',
      width: 150,
    },
    {
      name: 'equipmentName',
      width: 180,
    },
    {
      name: 'workcellCode',
      width: 180,
    },
    {
      name: 'lockStatus',
    },
    {
      name: 'lockReasonMeaning',
    },
    {
      name: 'lockTime',
      width: 150,
    },
    {
      name: 'inspectRequestCode',
      width: 180,
    },
    {
      name: 'inspectDocCode',
      width: 180,
    },
    {
      name: 'inspectBusinessType',
      width: 150,
    },
    {
      name: 'inspectReqUserRealName',
    },
    {
      name: 'inspectorRealName',
    },
    {
      name: 'unlockReason',
    },
    {
      name: 'unlockTime',
      width: 150,
    },
    {
      name: 'status',
    },
  ]


  useDataSetEvent(listDs, 'select', () => {
    selectStatus()
  });

  useDataSetEvent(listDs, 'selectAll', () => {
    selectStatus()
  });

  useDataSetEvent(listDs, 'unSelectAll', () => {
    selectStatus()
  });

  useDataSetEvent(listDs, 'unselect', () => {
    selectStatus()
  });

  const selectStatus = () => {
    if (listDs.selected.length > 0) {
      setHistoryFlag(false)
      const flag = listDs.selected.every(item => (item.get('status') === 'NEW' || item.get('status') === 'REJECTED'))
      setSubmitFlag(!flag)
    } else {
      setSubmitFlag(true)
      setHistoryFlag(true)
    }
  }

  const lockDs = new DataSet({
    forceValidate: true,
    fields: [
      {
        name: 'unlockReason',
        type: FieldType.string,
        required: true,
        label: intl.get(`${modelPrompt}.form.unlockReason`).d('解锁原因'),
      },
    ]
  })

  const handleUnlock = async () => {
    lockDs.create({}, 0)
    Modal.open({
      title: intl.get(`${modelPrompt}.title.unlockRemark`).d('解锁原因'),
      destroyOnClose: true,
      drawer: false,
      closable: true,
      keyboardClosable: true,
      onCancel: () => {
        lockDs.loadData([])
      },
      className: 'hmes-style-modal',
      children: <Form dataSet={lockDs} columns={1} labelWidth={112}>
        <TextArea name="unlockReason" />
      </Form>,
      onOk: async () => {
        const validate = await lockDs.validate()
        if (validate) {
          const ids = listDs.selected.map(item => item.get('equipmentLockId'))
          const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-equipment-locks/submit`
          const res: any = await axios.post(url, { equipmentLockIdList: ids, unlockReason: lockDs.toData()[0].unlockReason, })
          if (res && res.success) {
            notification.success({})
            listDs.query();
          } else {
            notification.error({
              message: res.message
            });
            return false
          }
        } else {
          return false
        }
      }
    })
  }

  const handleHistory = () => {
    const ids = listDs.selected.map(item => item.get('equipmentLockId'))
    console.log(ids)
    history.push(`/mes/deviceLock/detail/${ids}`)
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.deviceLock`).d('设备锁定管理')}>
        <Button disabled={historyFlag} color={ButtonColor.primary} onClick={handleHistory}>{intl.get(`${modelPrompt}.title.history`).d('历史')}</Button>
        <Button disabled={submitFlag} onClick={handleUnlock}>{intl.get(`${modelPrompt}.title.unLock`).d('解锁')}</Button>
      </Header>
      <Content >
        <Table
          dataSet={listDs}
          columns={columns}
          key="deviceLock"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={2} // 头部显示的查询字段的数量
          searchCode="deviceLock" // 动态筛选条后端接口唯一编码
          customizedCode="deviceLock" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.ass.deviceLock'],
})(ListPage);
