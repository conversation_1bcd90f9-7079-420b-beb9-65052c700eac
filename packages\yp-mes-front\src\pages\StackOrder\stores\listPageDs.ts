import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentTenantId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.hmes.stackOrder';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'stackSequenceId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'materialCode',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
          lovCode: 'MT.MATERIAL.PERMISSION',
          ignore: FieldIgnore.always,
        },
        {
          name: 'materialId',
          bind: 'materialCode.materialId',
        },
      ]
    }),
    fields: [
      {
        name: 'materialCode',
        required:true,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        lovCode: 'MT.MATERIAL.PERMISSION',
        ignore: FieldIgnore.always,
      },
      {
        name: 'materialId',
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('materialCode') && record.get('materialCode').materialId) {
              return 'materialCode.materialId';
            }
          },
        },
      },
      {
        name: 'materialName',
        disabled:true,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料名称'),
        type: FieldType.string,
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('materialCode') && record.get('materialCode').materialName) {
              return 'materialCode.materialName';
            }
          },
        },
      },
      {
        name: 'scanSequence',
        required:true,
        label: intl.get(`${modelPrompt}.form.scanSequence`).d('扫描顺序'),
        type: FieldType.string,
      },
      {
        name: 'stackSequence',
        required:true,
        label: intl.get(`${modelPrompt}.form.stackSequence`).d('堆叠顺序'),
        type: FieldType.string,
      },

    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'GET',
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-stack-sequences/query`,
        };
      },
    },
  });

export default listPageFactory;
