import React from 'react';
import {
  Modal,
  Table,
  Row,
  Col,
  Form,
  TextField,
} from 'choerodon-ui/pro';
import { isEmpty } from 'lodash';

const BatchBarCodeModal = ({ inputLovDS, inputLovTitle, targetDS, inputLovFlag, submit }) => {
  const handleToChangeInputLov = (value) => {
    if (!isEmpty(value)) {
      const data = value.split(/[\s\n]/).filter(item => item !== '');
      inputLovDS.data = (data || []).map((item) => ({
        code: item,
      }));
      inputLovDS.selectAll();
    }
  };
  const renderInputLovBar = () => {
    const { queryDataSet } = inputLovDS;
    return (
      <>
        <Row>
          <Col span={20}>
            <Form dataSet={queryDataSet} labelWidth={100}>
              <TextField name="code" onChange={handleToChangeInputLov} />
            </Form>
          </Col>
        </Row>
      </>
    );
  };
  Modal.open({
    maskClosable: false,
    destroyOnClose: true,
    style: {
      width: '600px',
    },
    children: (
      <>
        <Table
          dataSet={inputLovDS}
          queryBar={renderInputLovBar}
          style={{ height: '3rem' }}
          columns={[
            {
              header: inputLovTitle,
              name: 'code',
              align: 'center',
            },
          ]}
        />
      </>
    ),
    drawer: false,
    onOk: async() => {
      if (targetDS && inputLovFlag) {
        let data = inputLovDS.selected;
        if (data.length < 1) {
          data = inputLovDS.records;
        }
        if(data.length < 1) return false
        let str = '';
        for (let i = 0; i < data.length; i++) {
          if (i !== data.length - 1) {
            str += `${data[i].get('code')},`;
          } else {
            str += data[i].get('code');
          }
        }
        targetDS.current.set(inputLovFlag, str);
        submit(inputLovFlag, str);
        return true;
      }
      return false
    },
  });
};

export default BatchBarCodeModal;
