import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';

const Host = `${BASIC.HMES_BASIC}`
const modelPrompt = 'tarzan.mes.event.accountProcessing';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  paging: false,
  autoCreate: true,
  selection: false,
  primaryKey: 'ncIncidentId',
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-finance-process/detail/query/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows = {}, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          return {
            ...rows,
            // disposalList,
          };
        },
      };
    },
  },
  fields: [
    {
      name: 'ncIncidentId',
      type: FieldType.number,
    },
    {
      name: 'ncIncidentNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncIncidentNum`).d('单据编码'),
      disabled: true,
    },
    {
      name: 'ncRecordType',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('单据类型'),
      lookupCode: 'HME_TRANS_DEC',
      lovPara: { tenantId },
      valueField: 'value',
      textField: 'description',
      // defaultValue: '001',
      dynamicProps: {
        disabled: ({ record }) => record.get('ncIncidentId'),
      },
    },
    {
      name: 'ncIncidentStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncIncidentStatus`).d('单据状态'),
      disabled: true,
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'NEW',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_RECORD_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      ignore: FieldIgnore.always,
      required: true,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => record.get('ncIncidentId'),
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});


const scanFormDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'materialLots',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      lovCode: 'YP_MES.MES.RECORD_MATERIAL_LOT',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        // required: ({record}) => record?.get('ncRecordType') === 'RM_NC'&&!record?.get('materialLots'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          ncRecordType: record?.get('ncRecordType'),
          materialId: record?.get('materialId'),
          revisionCode: record?.get('revisionCode'),
        }),
      },
    },
    {
      name: 'materialLotId',
      bind: 'materialLotLov.materialLotId',
    },
    {
      name: 'ncRecordIdMaterialLot',
      bind: 'materialLotLov.ncRecordId',
    },
    {
      name: 'ncRecordIdEo',
      bind: 'eoLov.ncRecordId',
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      bind: 'materialLotLov.materialLotCode',
      multiple: true,
    },
    {
      name: 'eos',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      dynamicProps: {
        // required: ({record}) => record?.get('ncRecordType') === 'EO_ALL_NC'&&record?.get('eoId')&&record?.get('eoId').length===0,
      },
    },
    {
      name: 'eoLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      lovCode: 'YP_MES.MES.RECORD_EO',
      textField: 'eoNum',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        // disabled: ({ record }) => !record?.get('siteId'),
        // required: ({record}) => record?.get('ncRecordType') === 'EO_ALL_NC'&&!record?.get('eos'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          ncRecordType: record?.get('ncRecordType'),
          operationId: record?.get('operationId'),
          materialId: record?.get('materialId'),
          revisionCode: record?.get('revisionCode'),
        }),
      },
    },
    {
      name: 'eoId',
      bind: 'eoLov.eoId',
    },
    {
      name: 'eoNum',
      bind: 'eoLov.eoNum',
    },
  ],
});

const formDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  selection: false,
  key: 'tagId',
  fields: [
    {
      name: 'scrapping',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapping`).d('原因'),
      textField: 'description',
      lookupCode: 'HME_TRANS_REASON',
      required: true,
    },
  ],
});

export { detailDS, scanFormDS, formDS };
