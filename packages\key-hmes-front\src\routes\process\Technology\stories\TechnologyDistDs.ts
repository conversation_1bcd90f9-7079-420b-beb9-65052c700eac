/**
 * @Description: 工艺维护详情-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 11:03:31
 * @LastEditTime: 2022-11-08 14:54:52
 * @LastEditors: <<EMAIL>>
 */

import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.process.technology.model.technology';

const formDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  selection: false,
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.method.domain.entity.MtOperation';
      return {
        data: { operationId: record.get('operationId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_METHOD}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
      required: true,
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
      required: true,
    },
    {
      name: 'currentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'operationStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationStatus`).d('状态'),
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'NEW',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ROUTER&statusGroup=OPERATION_STATUS&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      name: 'operationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationType`).d('工艺类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ROUTER&stateType=typeList&typeGroup=OPERATION_TYPE&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      name: 'completeInconformityFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completeInconformityFlag`).d('完工不一致标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.description`).d('工艺描述'),
    },
    {
      name: 'workcellType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellType`).d('工作单元类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&stateType=workCellList&typeGroup=WORKCELL_TYPE&tenantId=${tenantId}`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'workCellObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defaultWorkcellCode`).d('默认工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('workcellType');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            workcellType: record.get('workcellType'),
          };
        },
      },
    },
    {
      name: 'workcellId',
      bind: 'workCellObject.workcellId',
    },
    {
      name: 'workcellCode',
      bind: 'workCellObject.workcellCode',
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间从'),
      required: true,
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('生效时间至'),
    },
    {
      name: 'standardReqdTimeInProcess',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.standardReqdTimeInProcess`).d('工艺过程时间'),
      step: 1,
      min: 0,
      nonStrictStep: true,
      precision: 6,
    },
    {
      name: 'standardSpecialIntroduction',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.standardSpecialIntroduction`).d('特殊指令'),
    },
    {
      name: 'standardMaxLoop',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.standardMaxLoop`).d('最大循环次数'),
      step: 1,
      min: 0,
    },
    {
      name: 'skipFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.skipFlag`).d('允许跳站'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
});

const tableDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  pageSize: 10,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation-substep/list/ui`,
        method: 'GET',
        transformResponse: val => {
          const data = JSON.parse(val);
          if (data?.rows?.content && data?.rows?.content.length > 0)
            data.rows.content = data.rows.content.map(item => {
              return {
                ...item,
                description: item.substepDescription,
              };
            });
          return {
            ...data,
          };
        },
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.method.domain.entity.MtOperation';
      return {
        data: { operationId: record.get('operationId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_METHOD}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
      required: true,
      step: 1,
      min: 0,
    },
    {
      name: 'substepObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.substepName`).d('子步骤编码'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.METHOD.SUBSTEP',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'substepId',
      bind: 'substepObject.substepId',
    },
    {
      name: 'substepName',
      bind: 'substepObject.substepName',
    },
    {
      name: 'description',
      label: intl.get(`${modelPrompt}.description`).d('子步骤描述'),
      bind: 'substepObject.description',
    },
    {
      name: 'extendAttrs',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.extendAttrs`).d('扩展属性'),
    },
    {
      name: 'operator',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.action').d('操作'),
    },
  ],
});

export { formDS, tableDS };
