import React, { useEffect, } from 'react';
import {
  Table,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { useDataSet, useDataSetClear } from 'utils/hooks';
import { RouteComponentProps } from 'react-router';
import historyPageFactory from '../stories/historyDs';
import { TableQueryBarType } from 'choerodon-ui/pro/es/table/enum';

const modelPrompt = 'tarzan.ass.barcodeWorkstationBinding';
interface RouterId {
  id: string;
}

const DetailPage: React.FC<RouteComponentProps<RouterId>> = () => {

  const historyDs = useDataSet(historyPageFactory, 'barcodeWorkHistory');

  useEffect(() => {
    return () => {
      useDataSetClear('barcodeWorkHistory')
    }
  }, []);

  const columns = [
    {
      name: 'equipmentCode',
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'type',
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'operationTime',
      width: 150,
    },
    {
      name: 'userName',
    },
  ]

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title`).d('条码与工位绑定历史')}
        backPath="/hmes/barcode-workstation-binding-report/list"
      >
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={historyDs}
          columns={columns}
          searchCode="barcodeWorkHistory"
          customizedCode="barcodeWorkHistory"
        />
      </Content>
    </div>
  );
};

export default DetailPage;
