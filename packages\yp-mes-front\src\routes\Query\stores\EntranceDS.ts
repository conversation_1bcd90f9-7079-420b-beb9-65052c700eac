/**
 * @Description: 库存查询 - 入口页DS
 * @Author: <EMAIL>
 * @Date: 2022/7/6 15:39
 * @LastEditTime: 2022-11-21 13:37:27
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import uuid from 'uuid/v4';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import { getCurrentSiteInfo } from '@utils/utils';

const modelPrompt = 'tarzan.inventory.query.model.query';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  paging: 'server',
  pageSize: 10,
  primaryKey: 'uuid',
  parentField: 'parentUuid',
  idField: 'uuid',
  expandField: 'expand',
  modifiedCheck: false,
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        defaultValue: () => {
          const siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo }
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.queryLocator`).d('库位查询'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')],
            type: 'LOCATOR',
            queryLocatorCategoryList: ['AREA'],
            locatorCategoryList: ['AREA', 'INVENTORY'],
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        required: ({ record }) => !(record.get('lotCodes').length || (record.get('materialLov') || []).length)
      },
    },
    {
      name: 'locatorIds',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorCode',
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialId`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            enableFlag: 'Y',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        required: ({ record }) => !(record.get('lotCodes').length || (record.get('locatorLov') || []).length)
      },
    },
    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'revisionCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      multiple: true,
    },
    {
      name: 'lotCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次号'),
      multiple: true,
      dynamicProps: {
        required: ({ record }) => !((record.get('materialLov') || []).length || (record.get('locatorLov') || []).length)
      },
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'holdFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.holdFlag`).d('预留库存'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
      textField: 'description',
      valueField: 'typeCode',
      options: new DataSet({
        autoQuery: true,
        dataKey: 'rows',
        paging: false,
        transport: {
          read: () => {
            return {
              url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
              method: 'GET',
              params: { tenantId },
              transformResponse: val => {
                const data = JSON.parse(val);
                data.rows.push({
                  description: intl.get(`tarzan.common.ownerType`).d('自有'),
                  typeCode: 'ALL',
                  typeGroup: 'OWNER_TYPE',
                });
                return {
                  ...data,
                };
              },
            };
          },
        },
      }),
    },
    {
      name: 'ownerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerId`).d('所有者查询'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'MT.MODEL.CUSTOMER';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'MT.MODEL.SUPPLIER';
            case 'OI':
              return `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`;
            default:
              return 'MT.MES.EMPTY';
          }
        },
        textField({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'customerCode';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'supplierCode';
            case 'OI':
              return 'soNumContent';
            default:
              return 'noData';
          }
        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerLov.customerId',
      dynamicProps: {
        bind({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'ownerLov.customerId';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'ownerLov.supplierId';
            case 'OI':
              return 'ownerLov.soLineId';
            default:
              return 'ownerLov.customerId';
          }
        },
      },
    },
    {
      name: 'zeroFilterFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.zeroFilterFlag`).d('是否展示0库存'),
      lookupCode: 'MT.FLAG'
    },
  ],
  fields: [
    {
      name: 'uuid',
      type: FieldType.string,
    },
    {
      name: 'parentUuid',
      type: FieldType.string,
    },
    {
      name: 'expand',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorDesc`).d('库位描述'),
    },
    {
      name: 'locatorCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCategory`).d('库位类别'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'lotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'onhandQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.onhandQty`).d('库存'),
    },
    {
      name: 'availableQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.availableQty`).d('可用库存'),
    },
    {
      name: 'holdQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.holdQty`).d('预留库存'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uom`).d('单位'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
    },
    {
      name: 'ownerId',
      type: FieldType.string,
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-inv-onhand-quantity/query/ui`,
        method: 'POST',
        transformResponse: val => {
          const data = JSON.parse(val);
          const { content } = data.rows;
          content.forEach(item => {
            item.uuid = uuid();
          });
          return {
            ...data,
          };
        },
      };
    },
  },
});

export { entranceDS };
