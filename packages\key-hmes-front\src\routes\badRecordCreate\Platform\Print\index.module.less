.ncRecord-print{
  .card-green {
    :global(.c7n-card-bordered) {
      background-color: rgb(112,172,71) !important;
    }
    :global(.c7n-card-head) {
      background-color: rgb(112,172,71) !important;
    }
    :global(.c7n-card-body) {
      background-color: rgb(112,172,71) !important;
    }
  }

  .card-yellow {
    :global(.c7n-card-bordered) {
      background-color: rgb(254,253,54) !important;
    }
    :global(.c7n-card-head) {
      background-color: rgb(254,253,54) !important;
    }
    :global(.c7n-card-body) {
      background-color: rgb(254,253,54) !important;
    }
  }

  .card-red {
    :global(.c7n-card-bordered) {
      background-color: rgb(188,45,0) !important;
    }
    :global(.c7n-card-head) {
      background-color: rgb(188,45,0) !important;
    }
    :global(.c7n-card-body) {
      background-color: rgb(188,45,0) !important;
    }
  }

  .c7n-card.c7n-card.c7n-card.c7n-card .c7n-card-head,
  .c7n-card.c7n-card.c7n-card.c7n-card .c7n-card-body{
    :global{
      margin: 0 !important;
    }
  }
  .c7n-card.c7n-card.c7n-card.c7n-card .c7n-card-head::before{
    :global{
      background: none !important;
    }
  }
  .c7n-card-head-title{
    :global{
      text-align: center !important;
      font-size: 20px !important;
    }
  }
  .c7n-card-head{
    :global{
      min-height: 0.2rem !important;
    }
  }
  .c7n-pro-field-label.c7n-pro-field-label.c7n-pro-field-label{
    :global{
      color: #000 !important;
    }
  }
}

