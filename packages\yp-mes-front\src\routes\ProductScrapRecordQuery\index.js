import React, { useState, useMemo } from 'react';
import {
  DataSet,
  Table,
  Button,
  Row,
  Col,
  TextField,
  Form,
  Icon,
  Lov,
  DateTimePicker,
  Modal,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import { observer } from 'mobx-react';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { Header, Content } from 'components/Page';
import ExcelExportPro from 'components/ExcelExportPro';
import formatterCollections from 'utils/intl/formatterCollections';
import { TARZAN_REPORT} from '@/utils/config';
import { tableDS, drawerDS } from './stores';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.productScrapRecordQuery';
const tenantId = getCurrentOrganizationId();

const ProductScrapRecordQuery = observer(() => {
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  const drawerDs = useMemo(() => new DataSet(drawerDS()), []); // 复制ds

  const openNcDrawer = async record => {
    drawerDs.setQueryParameter('ncRecordId', record.data.ncRecordId);
    drawerDs.setQueryParameter('identification', record.data.identification);
    await drawerDs.query();
    Modal.open({
      closable: true,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.ncDetail`).d('不良明细'),
      drawer: true,
      style: {
        width: 720,
      },
      children: (
        <>
          <Table dataSet={drawerDs} columns={drawerColumns} style={{ height: 400 }} />
        </>
      ),
    });
  };

  const drawerColumns = [
    {
      name: 'identification',
      width: 200,
      lock: 'left',
    },
    {
      name: 'ncCode',
      width: 150,
    },
    {
      name: 'operationName',
      width: 150,
    },
    {
      name: 'ncStatusDesc',
      width: 150,
    },
    {
      name: 'ncRecordTime',
      width: 150,
    },
    {
      name: 'ncUserName',
      width: 150,
    },
    {
      name: 'lastUpdateTime',
      width: 150,
    },
  ];

  const columns = [
    {
      header: intl.get(`${modelPrompt}.ncDetail`).d('不良明细'),
      align: 'center',
      width: 240,
      lock: 'left',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => {
                openNcDrawer(record);
              }}
            >
              {intl.get(`${modelPrompt}.loadDetail`).d('明细')}
            </a>
          </span>
        );
      },
    },
    {
      name: 'siteCode',
      width: 200,
    },
    {
      name: 'organizationCode',
      width: 200,
    },
    {
      name: 'equipmentCode',
      width: 200,
    },
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'eoNum',
      width: 150,
    },
    {
      name: 'materialLotCode',
      width: 150,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'ncCode',
      width: 150,
    },
    {
      name: 'ncDescription',
      width: 150,
    },
    {
      name: 'operationName',
      width: 150,
    },
    {
      name: 'qualityStatusDesc',
      width: 150,
    },
    {
      name: 'workOrderNum',
      width: 150,
    },
    {
      name: 'disposalFunctionDescription',
    },
    {
      name: 'disposalTime',
    },
    {
      name: 'scrapQty',
    },
    {
      name: 'realName',
    },
  ];
  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Col span={18}>
            <Form columns={4} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'identifications', '条码号', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <Lov name="prodLineLov" />
              <Lov name="equipmentLov" />
              <Lov name="materialLov" />
              {expandForm && (
                <>
                  <Lov name="defaultNcCodeLov" />
                  <TextField
                    name="workOrderNumstr"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          onClick={() =>
                            onOpenInputModal(true, 'workOrderNumstr', '生产指令', queryDataSet)
                          }
                        />
                      </div>
                    }
                  />
                  <DateTimePicker name="startTime" />
                  <DateTimePicker name="endTime" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                funcType="link"
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleSearch = async () => {
    const {
      identifications,
      materialIds,
      ncCodeIds,
      workOrderNumstr,
      startTime,
      endTime,
      prodLineIds,
      equipmentIds,
    } = tableDs?.queryDataSet?.toJSONData()[0];
    console.log(tableDs?.queryDataSet?.toJSONData()[0]);
    if (
      !identifications &&
      !materialIds.length &&
      !ncCodeIds.length &&
      !workOrderNumstr &&
      !startTime &&
      !endTime &&
      !prodLineIds.length &&
      !equipmentIds.length
    ) {
      notification.error({ message: intl.get(`${modelPrompt}.queryField`).d('请输入查询条件') });
      return;
    }

    if (materialIds.length || ncCodeIds.length || prodLineIds.length || equipmentIds.length) {
      if (!startTime && !endTime) {
        notification.error({ message: intl.get(`${modelPrompt}.queryDate`).d('请输入时间查询！') });
        return;
      }
    }
    if ((startTime && !endTime) || (!startTime && endTime)) {
      notification.error({
        message: intl.get(`${modelPrompt}.dateValidate`).d('开始时间和结束时间必须同时输入！'),
      });
      return;
    }
    tableDs.query();
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleSearch();
    }
  };

  useDataSetEvent(drawerDs, 'load', () => {
    if (drawerDs.records.length) {
      drawerDs.records.forEach(record => {
        record.init('identification', drawerDs.getQueryParameter('identification'));
      });
    }
  });

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const getExportDisabledFlag = dataSet => {
    const queryParmas = dataSet.current?.toData() || {};
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i]) || i === '__dirty') {
        delete queryParmas[i];
      }
    });
    const queryDataLength = Object.keys(queryParmas)?.length;
    return !queryDataLength;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('产品报废记录查询报表')}>
        {getExportDisabledFlag(tableDs.queryDataSet) ? (
          <Button disabled>{intl.get(`${modelPrompt}.export`).d('导出')}</Button>
        ) : (
          <ExcelExportPro
            method="POST"
            exportAsync
            allBody
            requestUrl={`${TARZAN_REPORT}/v1/${tenantId}/hme-product-report/scrap/record/export`}
            queryParams={getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        )}
      </Header>
      <Content>
        <Table
          searchCode="ProductScrapRecordQuery"
          customizedCode="ProductScrapRecordQuery"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.productScrapRecordQuery', 'tarzan.common'],
})(ProductScrapRecordQuery);
