import React, { Component } from 'react';
import { connect } from 'dva';
import { Form, Row, Col, Select } from 'hzero-ui';
import {
  FORM_COL_3_LAYOUT,
  SEARCH_FORM_ROW_LAYOUT,
  DRAWER_FORM_ITEM_LAYOUT,
} from '@utils/constants';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';

@connect(({ maintainNumber }) => ({
  maintainNumber,
}))
@Form.create({ fieldNameProp: null })
export default class DateForm extends Component {
  constructor(props) {
    super(props);
    props.onRef(this);
  }

  handleChange = value => {
    const { dataSource, setUsingRuleDetail } = this.props;
    const usingRuleDetail = {
      ...dataSource,
      dateFormat: value,
    };
    setUsingRuleDetail(usingRuleDetail);
  };

  render() {
    const {
      form,
      canEdit,
      dataSource,
      maintainNumber: { dateFormatList = [], maintainNumberDetail = {}, userRole = 'N' },
    } = this.props;
    const { initialFlag = 'N' } = maintainNumberDetail;
    const { getFieldDecorator } = form;
    const { dateFormat } = dataSource;
    return (
      <Row {...SEARCH_FORM_ROW_LAYOUT}>
        <Col {...FORM_COL_3_LAYOUT}>
          <Form.Item
            {...DRAWER_FORM_ITEM_LAYOUT}
            label={intl.get(`${modelPrompt}.dateFormat`).d('日期格式')}
          >
            {getFieldDecorator('dateFormat', {
              initialValue: dateFormat,
              rules: [
                {
                  required: true,
                  message: intl.get('hzero.common.validation.notNull', {
                    name: intl.get(`${modelPrompt}.dateFormat`).d('日期格式'),
                  }),
                },
              ],
            })(
              <Select
                allowClear
                style={{ width: '100%' }}
                disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                onChange={this.handleChange}
              >
                {dateFormatList instanceof Array &&
                  dateFormatList.length !== 0 &&
                  dateFormatList.map(item => {
                    return (
                      <Select.Option value={item.typeCode} key={item.typeCode}>
                        {`${item.typeCode}(${item.description})`}
                      </Select.Option>
                    );
                  })}
              </Select>,
            )}
          </Form.Item>
        </Col>
      </Row>
    );
  }
}
