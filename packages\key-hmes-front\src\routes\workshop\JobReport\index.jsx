/**
 * @feature 班次作业实绩报表
 * @date 2021-5-10
 * <AUTHOR> <<EMAIL>>
 */

import React, { useEffect, useMemo } from 'react';
import { Table, DataSet, Button, Modal } from 'choerodon-ui/pro';
import { Badge } from 'hzero-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import intl from 'utils/intl';
import { LocaleProvider } from '@components/tarzan-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { entranceDS, detailDS } from './stories/EntranceDs';
import './index.module.less';

const modelPrompt = 'tarzan.workshop.jobReport.model';

const entrance = props => {
  const detailDs = useMemo(() => new DataSet(detailDS()), []);

  const columnsData = [
    {
      name: 'prodLineCode',
      align: 'left',
      width: 150,
    },
    {
      name: 'prodLineName',
      align: 'left',
      width: 150,
    },
    {
      name: 'operationName',
      align: 'left',
      width: 150,
    },
    {
      name: 'operationDesc',
      width: 150,
      align: 'left',
    },
    {
      name: 'workcellCode',
      width: 150,
      align: 'left',
    },
    {
      name: 'workcellName',
      width: 150,
      align: 'left',
    },
    {
      name: 'shiftDate',
      width: 150,
      align: 'center',
    },
    {
      name: 'shiftCode',
      width: 100,
      align: 'left',
    },
    {
      name: 'materialCode',
      align: 'left',
      width: 180,
    },
    {
      name: 'eoMaterialRevisionCode',
      align: 'left',
      width: 130,
    },
    { name: 'assignQty', align: 'right', width: 100 },
    { name: 'queueQty', align: 'right', width: 100 },
    { name: 'workingQty', align: 'right', width: 100 },
    { name: 'completePendingQty', align: 'right', width: 100 },
    { name: 'completedQty', align: 'right', width: 100 },
    { name: 'scrappedQty', align: 'right', width: 100 },
    { name: 'scrappedConfirmQty', align: 'right', width: 100 },
    {
      width: 100,
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: 'center',
      lock: 'right',
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => {
              handleCopyDrawerShow(record);
            }}
          >
            {intl.get(`${modelPrompt}.detail`).d('详情')}
          </a>
        );
      },
    },
  ];
  const columnsDetailData = [
    {
      name: 'eoNum',
      align: 'left',
      width: 150,
    },
    {
      name: 'workOrderNum',
      align: 'left',
      width: 150,
    },
    {
      name: 'dispatchFlag',
      align: 'center',
      width: 100,
      renderer: ({ record }) => (
        <Badge
          status={record.get('dispatchFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('dispatchFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'movingFlag',
      align: 'center',
      width: 100,
      renderer: ({ record }) => (
        <Badge
          status={record.get('movingFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('movingFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'assembleFlag',
      align: 'center',
      width: 100,
      renderer: ({ record }) => (
        <Badge
          status={record.get('assembleFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('assembleFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'assignQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'queueQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'workingQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'completePendingQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'completedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'scrappedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'scrappedConfirmQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'routerEntryStepFlag',
      align: 'center',
      width: 100,
      renderer: ({ record }) => (
        <Badge
          status={record.get('routerEntryStepFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('routerEntryStepFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'routerDoneStepFlag',
      align: 'center',
      renderer: ({ record }) => (
        <Badge
          status={record.get('routerDoneStepFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('routerDoneStepFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
      width: 100,
    },
  ];

  useEffect(() => {
    props.dataSet.query(props.dataSet.currentPage);
  }, []);

  useEffect(() => {
    function processDataSetListener(flag) {
      if (props.dataSet.queryDataSet) {
        const handler = flag
          ? props.dataSet.queryDataSet.addEventListener
          : props.dataSet.queryDataSet.removeEventListener;
        handler.call(props.dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
      }
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (!data.calendarShiftDateFrom && !data.calendarShiftDateTo) {
      record.set('shiftCode', '');
    }
  };

  let copyDrawer;
  const handleCopyDrawerShow = record => {
    detailDs.setQueryParameter('workcellId', record.get('workcellId'));
    detailDs.setQueryParameter('shiftDate', record.get('shiftDate'));
    detailDs.setQueryParameter('shiftCode', record.get('shiftCode'));
    detailDs.setQueryParameter('prodLineId', record.get('prodLineId'));
    detailDs.setQueryParameter('operationId', record.get('operationId'));
    detailDs.setQueryParameter('eoMaterialId', record.get('eoMaterialId'));
    detailDs.setQueryParameter('eoMaterialRevisionCode', record.get('eoMaterialRevisionCode'));
    detailDs.query();
    copyDrawer = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.detail`).d('班次作业实绩详情'),
      drawer: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal copy-drawer-modal',
      children: (
        <>
          <LocaleProvider>
            <Table dataSet={detailDs} columns={columnsDetailData} />
          </LocaleProvider>
        </>
      ),
      footer: (
        <>
          <div style={{ float: 'right' }}>
            <Button
              onClick={() => {
                copyDrawer.close();
              }}
            >
              {intl.get(`${modelPrompt}.back`).d('返回')}
            </Button>
          </div>
        </>
      ),
    });
  };

  return (
    <div className="hmes-style">
      <PageHeaderWrapper title={intl.get(`${modelPrompt}.title.list`).d('班次作业实绩报表')}>
        <Table
          queryBar='filterBar'
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={props.dataSet}
          columns={columnsData}
          searchCode="JobReport"
          customizedCode="JobReport"
        />
      </PageHeaderWrapper>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.workshop.jobReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...entranceDS(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(entrance),
);
