/**
 * @Description: radiofly
 * @Author: <<EMAIL>>
 * @Date: 2022-06-27 15:48:04
 * @LastEditTime: 2022-06-28 09:26:46
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 删除行
export function DeleteRows() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag-assign-channel/tag-assign-channel/delete/ui`,
    method: 'POST',
  };
}
// 保存表格
export function SaveTable() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag-assign-channel/tag-assign-channel/save/ui`,
    method: 'POST',
  };
}
// 分发渠道
export function GetChannelType() {
  return {
    url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=CHANNEL_TYPE`,
    method: 'GET',
  };
}
