/**
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-22
 * @description 不良记录平台新建form表详情及处置意见保存接口
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from 'hcm-components-front/lib/utils/config';

const tenantId = getCurrentOrganizationId();
// const Host = '/yp-mes-20000'
const Host = `${BASIC.HMES_BASIC}`

// 缓存标签数据
export function Cache(): object {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-record/data-set/cache`,
    method: 'POST',
  };
}
export function PrintView(): object {
  return {
    url: `${BASIC.HRPT_COMMON}/v1/${tenantId}/label-prints/view/html`,
    method: 'GET',
  };
}

/**
 * 保存不良记录表
 * @function SaveNcRecord
 * @returns {object} fetch Promise
 */
export function SaveNcRecord(): object {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-record/platform/save/ui`,
    method: 'POST',
  };
}

export function CancelNcRecord(): object {
  return {
    url: `${Host}/v1/${tenantId}/mt-nc-record/platform/cancel/ui`,
    method: 'POST',
  };
}

/**
 * 获取用户站点列表
 * @function FetchUserSiteList
 * @returns {object} fetch Promise
 */
export function FetchUserSiteList() {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/user/site/list/ui`,
    method: 'GET',
  };
}

/**
 * 获取物料批关联信息
 * @function FetchMaterialLotRelatedInfo
 * @returns {object} fetch Promise
 */
export function FetchMaterialLotRelatedInfo() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-record/material/lot/related`,
    method: 'POST',
  };
}
// 物料批扫描
export function ScanMaterialLotRelatedInfo() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-record/material/lot/related/scan`,
    method: 'POST',
  };
}


/**
 * 获取EO关联信息
 * @function FetchEoRelatedInfo
 * @returns {object} fetch Promise
 */
export function FetchEoRelatedInfo() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-record/eo/related`,
    method: 'POST',
  };
}
export function ScanEoRelatedInfo() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-record/eo/related/scan`,
    method: 'GET',
  };
}
export function ScanCodePrint() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-record/label/scan`,
    method: 'GET',
  };
}

/**
 * 根据行的步骤和头上的执行作业查询不良产生步骤加工次数
 * @function FetchTimesProcessed
 * @returns {object} fetch Promise
 */
export function FetchTimesProcessed() {
  return {
    url: `${Host}/v1/${tenantId}/mt-nc-record/times/processed/get/for/ui`,
    method: 'GET',
  };
}


// 明细查询
export function FetchDetailLine() {
  return {
    url: `${Host}/v1/${tenantId}/mt-nc-record/platform/line/query/ui`,
    method: 'GET',
  };
}

export function FetchOperation() {
  return {
    url: `${Host}/v1/${tenantId}/hme-nc-record/equipment/operation/query`,
    method: 'GET',
  };
}
