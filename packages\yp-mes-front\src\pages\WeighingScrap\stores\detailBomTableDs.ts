import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.inOutStorage';

const detailTableFactory = () =>
  new DataSet({
    primaryKey: 'bomComponentId',
    selection: DataSetSelection.multiple,
    paging: false,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
      },
      {
        name: 'qty',
        label: intl.get(`${modelPrompt}.form.qty`).d('单位用量'),
        type: FieldType.number,
      },
      {
        name: 'scarpQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.form.scarpQty`).d('理论报废数量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.uomCode`).d('主单位'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/query/bom`,
        };
      },
    },
  });

export default detailTableFactory;
