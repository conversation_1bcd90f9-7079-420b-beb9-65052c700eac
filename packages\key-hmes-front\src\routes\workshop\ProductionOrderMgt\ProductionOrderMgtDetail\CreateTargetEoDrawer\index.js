/**
 * @Description: 指定物料执行作业创建抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-10-11 18:12:49
 * @LastEditTime: 2022-11-10 15:45:05
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Form, NumberField, Select, Table, Lov } from 'choerodon-ui/pro';
import { Tabs, Tooltip, Icon } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useRequest } from '@components/tarzan-hooks';

import { TarzanDrawer } from '@components/tarzan-ui';
import styles from '../../index.module.less';
import { eoDS, eoListDS } from '../../stores/ProductionOrderMgtDrawerCreateTargetDS';
import { getAssembleDetailConfig, saveAssembleListConfig } from '../../services';

const { TabPane } = Tabs;

const modelPrompt = 'tarzan.workshop.productionOrderMgt';

const ProductionOrderMgtCreateEoForm = props => {
  const getAssembleDetail = useRequest(getAssembleDetailConfig(), {
    manual: true,
    needPromise: true,
  });

  const saveAssemble = useRequest(saveAssembleListConfig(), { manual: true, needPromise: true });

  const { workOrderId, visible, handleRefresh = () => {}, handleCancel = () => {} } = props;

  const [drawerEdit, setDrawerEdit] = useState(false);
  const [createEoList, setCreateEoList] = useState([]);
  const eoDs = useMemo(() => {
    return new DataSet(eoDS());
  }, []);

  const eoListDs = useMemo(() => {
    return new DataSet(eoListDS());
  }, []);

  useEffect(() => {
    if (visible) {
      eoQuery();
    }
  }, [visible]);

  const eoQuery = () => {
    eoDs.loadData([{ workOrderId }]);
    eoListDs.queryParameter = {
      workOrderId,
    };
    eoListDs.query();
  };

  const resetData = () => {
    setDrawerEdit(false);
    setCreateEoList([]);
    eoDs.loadData([]);
    eoListDs.loadData([]);
  };

  const handleSave = async () => {
    eoDs.current.set({ nowDate: new Date().getTime() });
    const validate = await eoDs.validate(false, true);
    if (!validate) {
      return;
    }
    const eoDeta = eoDs.toData()[0];
    return saveAssemble.run({
      params: {
        ...eoDeta,
        createByMaterialId: eoDeta.materialId,
      },
      onSuccess: res => {
        setCreateEoList([...res]);
        eoQuery();
        handleRefresh('EOCREATE');
        setDrawerEdit(false);
      },
    });
  };

  const handleClose = async () => {
    handleCancel();
    resetData();
  };

  const materialChange = value => {
    if (value) {
      getWoDetail(value);
    } else {
      resetWoDetail();
    }
  };

  const getWoDetail = async value => {
    getAssembleDetail.run({
      params: {
        workOrderId,
        materialId: value.materialId,
        primaryUnitRatio: value.primaryUnitRatio,
      },
      onSuccess: res => {
        eoDs.current.set('canReleaseQty', res.canReleaseQty);
        eoDs.current.set('releasedQty', res.releasedQty);
        eoDs.current.set('qty', res.qty);
      },
    });
  };

  const resetWoDetail = () => {
    eoDs.reset();
  };

  const resetMantissaDeal = value => {
    if (!value) {
      eoDs.current.init('eoCount', undefined);
      if (eoDs.current.get('mantissaDeal')) {
        eoDs.current.init('mantissaDeal', undefined);
      }
    } else if (eoDs.current.get('totalQty') > 0 && eoDs.current.get('eoQty') > 0) {
      const newEoCount = Math.floor(eoDs.current.get('totalQty') / eoDs.current.get('eoQty'));
      if (eoDs.current.get('totalQty') % eoDs.current.get('eoQty') > 0) {
        if (eoDs.current.get('mantissaDeal') === 'LESS_AVERAGE') {
          eoDs.current.init('eoCount', newEoCount + 1);
        } else if (eoDs.current.get('mantissaDeal') === 'MORE_AVERAGE') {
          eoDs.current.init('eoCount', newEoCount);
        } else {
          eoDs.current.init('mantissaDeal', 'LESS_AVERAGE');
          eoDs.current.init('eoCount', newEoCount + 1);
        }
      } else {
        eoDs.current.init('mantissaDeal', undefined);
        eoDs.current.init('eoCount', newEoCount);
      }
    } else {
      eoDs.current.init('eoCount', undefined);
    }
  };

  const setQty = e => {
    const {
      target: { name, value },
    } = e;
    eoDs.current.set(name, value);
    resetMantissaDeal(value);
  };

  const orderDetail = id => {
    handleClose();
    props.history.push(`/hmes/workshop/execute-operation-management/detail/${id}`);
  };

  const columns = [
    {
      name: 'eoNum',
      lock: 'left',
      renderer: ({ record, value }) => {
        return (
          <>
            {createEoList.indexOf(record.data.eoId) > -1 && (
              <div className={styles['icon-new']}>
                <div className={styles['icon-new-inner']}>
                  <div className={styles['icon-new-inner-text']}>NEW</div>
                </div>
              </div>
            )}
            <a
              onClick={() => {
                orderDetail(record.data.eoId);
              }}
            >
              {value}
            </a>
          </>
        );
      },
      width: 140,
    },
    {
      name: 'materialCode',
      width: 160,
    },
    {
      name: 'targetQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'actualQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'status',
      width: 90,
      align: 'center',
    },
    {
      name: 'materialTypeDesc',
      width: 90,
      align: 'center',
    },
    {
      name: 'unStartedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'wipSumQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'wipScrappedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'completedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'confirmScrappedQty',
      width: 100,
      align: 'right',
    },
  ];

  return (
    <>
      <TarzanDrawer
        visible={visible}
        canEdit={false}
        onOk={handleClose}
        onClose={handleClose}
        width={1080}
        title={
          <div className={styles['modal-title-container']}>
            <div>
              {intl
                .get(`${modelPrompt}.model.productionOrderMgt.eoTargetCreate`)
                .d('指定物料执行作业创建')}
            </div>
            <div className={styles['modal-title-btn-box']}>
              {drawerEdit && (
                <>
                  <PermissionButton
                    type="c7n-pro"
                    onClick={() => {
                      setDrawerEdit(prev => !prev);
                      eoQuery();
                    }}
                  >
                    {intl.get('tarzan.common.button.cancel').d('取消')}
                  </PermissionButton>
                  <PermissionButton type="c7n-pro" color={ButtonColor.primary} onClick={handleSave}>
                    {intl.get('tarzan.common.button.save').d('保存')}
                  </PermissionButton>
                </>
              )}
              {!drawerEdit && (
                <PermissionButton
                  type="c7n-pro"
                  color={ButtonColor.primary}
                  onClick={() => {
                    setDrawerEdit(prev => !prev);
                  }}
                >
                  {intl.get(`tarzan.common.button.create`).d('新建')}
                </PermissionButton>
              )}
            </div>
          </div>
        }
      >
        <Form
          disabled={!drawerEdit}
          dataSet={eoDs}
          columns={3}
          labelLayout="horizontal"
          labelWidth={110}
        >
          <Lov name="materialObject" onChange={materialChange} />
          <NumberField name="releasedQty" />
          <div name="canReleaseQty" className={styles['split-qty-box']}>
            <NumberField name="canReleaseQty" />
            <Tooltip
              placement="right"
              title={intl
                .get(`${modelPrompt}.canQtyNote`)
                .d('提示：可下达数量 = 生产指令数量 + 完工超量允差 - 已下达数量')}
            >
              <Icon
                className={styles['split-qty-icon']}
                type="help_outline"
                style={{
                  color: 'rgba(0,0,0,0.2)',
                  fontSize: 14,
                  cursor: 'pointer',
                }}
              />
            </Tooltip>
          </div>
          <NumberField name="totalQty" onChange={resetMantissaDeal} onInput={setQty} />
          <NumberField name="eoQty" onChange={resetMantissaDeal} onInput={setQty} />
          <Select name="mantissaDeal" onChange={resetMantissaDeal} />
          <NumberField name="eoCount" />
        </Form>
        <Tabs>
          <TabPane tab={intl.get(`${modelPrompt}.eoListTitle`).d('执行作业列表')}>
            <Table dataSet={eoListDs} columns={columns} />
          </TabPane>
        </Tabs>
      </TarzanDrawer>
    </>
  );
};

export default ProductionOrderMgtCreateEoForm;
