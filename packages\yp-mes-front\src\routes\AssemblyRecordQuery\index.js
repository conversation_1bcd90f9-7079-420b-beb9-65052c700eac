import React, { useMemo, useState, useEffect } from 'react';
import {
  DataSet,
  Table,
  Row,
  Col,
  Form,
  TextField,
  Lov,
  Icon,
  Button,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import ExcelExport from 'components/ExcelExportPro';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { isNil } from 'lodash';
import { BASIC } from '@/utils/config';
import { tableDS } from './stores/AssemblyRecordQueryDs';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

const modelPrompt = 'tarzan.hmes.assemblyRecordQuery';

const AssemblyRecordQuery = props => {
  const {
    location: { state },
    history,
  } = props;
  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  useEffect(() => {
    if (state?.identifications) {
      tableDs.queryDataSet?.loadData([{ identifications: state?.identifications }]);
      tableDs.query(tableDs.currentPage);
      history.replace({ ...history.location, state: undefined });
    }
  }, [history.location.state]);

  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'flex-start',
          }}
        >
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField name="workOrderNum" />
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'identifications', '条码号', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <Lov name="materialLov" />

              {expandForm && (
                <>
                  <Lov name="prodLineLov" />
                  <Lov name="operationLov" />
                  <Lov name="comMaterialLov" />
                  <TextField
                    name="materialLotCodes"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          onClick={() =>
                            onOpenInputModal(
                              true,
                              'materialLotCodes',
                              '组件物料批编码',
                              queryDataSet,
                            )
                          }
                        />
                      </div>
                    }
                  />
                  <DateTimePicker name="dateFrom" />
                  <DateTimePicker name="dateTo" />
                  <Lov name="workcellSiteLov" />
                  <TextField name="realName" />
                  <Lov name="equipmentLov" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                funcType="link"
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleSearch = async () => {
    if (await tableDs?.queryDataSet.validate()) {
      // const {
      //   materialId,
      // } = tableDs?.queryDataSet?.toJSONData()[0];
      // if(materialId){
      //   if(!startTime && !endTime){
      //     notification.error({message: intl.get(`${modelPrompt}.queryDate`).d('请输入时间查询！')});
      //     return
      //   }
      // }
      // if(startTime&&!endTime||!startTime&&endTime){
      //   notification.error({message: intl.get(`${modelPrompt}.dateValidate`).d('开始时间和结束时间必须同时输入！')});
      //   return
      // }
      tableDs.query();
    }
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const columns = [
    {
      name: 'identification',
      lock: 'left',
      width: 200,
    },
    {
      name: 'eoNum',
    },
    {
      name: 'eoStatusDesc',
    },
    {
      name: 'workOrderNum',
    },
    {
      name: 'qty',
    },
    {
      name: 'workOrderStatusDesc',
    },
    {
      name: 'productionLineCode',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'operationName',
    },
    {
      name: 'operationDesc',
    },
    {
      name: 'lineNumber',
    },
    {
      name: 'componentMaterialCode',
    },
    {
      name: 'componentRevisionCode',
    },
    {
      name: 'componentMaterialName',
    },
    {
      name: 'componentMaterialUomCode',
    },
    {
      name: 'componentMaterialUomName',
    },
    {
      name: 'sumAssembleQty',
    },
    {
      name: 'sumScrapQty',
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'assembleQty',
    },
    {
      name: 'scrapQty',
    },
    {
      name: 'referencePoint',
    },
    {
      name: 'eventId',
    },
    {
      name: 'eventTypeDesc',
      width: 120,
    },
    {
      name: 'eventRequestTypeDesc',
      width: 150,
    },
    {
      name: 'creationDate',
    },
    {
      name: 'workcellCode',
    },
    {
      name: 'workcellDescription',
    },
    {
      name: 'equipmentCode',
      width: 120,
    },
    {
      name: 'realName',
    },
  ];

  const getExportQueryParams = () => {
    const queryParams = tableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i]) || i.includes('Lov') || i === '__dirty') {
        delete queryParams[i];
      }
    });
    if (queryParams.identifications) {
      queryParams.identifications = queryParams.identifications.split(',');
    }
    if (queryParams.materialLotCodes) {
      queryParams.materialLotCodes = queryParams.materialLotCodes.split(',');
    }

    return queryParams;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.realName`).d('装配记录查询')}>
        <ExcelExport
          method="POST"
          allBody
          requestUrl={`${
            BASIC.TARZAN_REPORT
          }/v1/${getCurrentOrganizationId()}/hme-assemble-record-query/export/info`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          modalProps={{ drawer: false }}
        />
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          queryBar={renderQueryBar}
          searchCode="AssemblyRecordQuery"
          customizedCode="AssemblyRecordQuery"
        />
      </Content>
      <LovModal {...lovModalProps} />
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.assemblyRecordQuery', 'tarzan.common'],
})(AssemblyRecordQuery);
