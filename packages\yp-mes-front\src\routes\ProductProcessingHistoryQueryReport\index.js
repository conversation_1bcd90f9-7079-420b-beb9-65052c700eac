import React, { useState, useMemo, useEffect } from 'react';
import {
  DataSet,
  Table,
  Button,
  Row,
  Col,
  TextField,
  Form,
  Icon,
  Select,
  DateTimePicker,
  DatePicker,
  Lov,
} from 'choerodon-ui/pro';
import notification from 'utils/notification';
import ExcelExportPro from 'components/ExcelExportPro';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { tableDS } from './stores';
import { Host, API_HOST, TARZAN_REPORT } from '@/utils/config';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';
const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;

const modelPrompt = 'tarzan.hmes.productProcessingHistoryQueryReport';

const ProductProcessingHistoryQueryReport = observer(props => {
  const {
    location: { state },
    history,
  } = props;

  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds

  useEffect(() => {
    if (state?.identifications) {
      tableDs.queryDataSet?.loadData([{ identifications: state?.identifications }]);
      tableDs.query(tableDs.currentPage);
      history.replace({ ...history.location, state: undefined });
    }
  }, [history.location.state]);

  const columns = [
    {
      name: 'identification',
      lock: 'left',
      width: 150,
    },
    {
      name: 'eoQty',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'workOrderCode',
    },
    {
      name: 'sequence',
    },
    {
      name: 'operationName',
    },
    {
      name: 'operationDesc',
    },
    {
      name: 'wipStatusDesc',
    },
    {
      name: 'qty',
    },
    {
      name: 'equipmentCode',
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'workcellCode',
    },
    {
      name: 'workcellName',
    },
    {
      name: 'reworkFlag',
    },
    {
      name: 'shiftCodeDesc',
    },
    {
      name: 'shiftDate',
    },
    {
      name: 'createdByRealName',
    },
    {
      name: 'outboundRepairFlag',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'prodLineCode',
    },
    {
      name: 'prodLineName',
    },
  ];
  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'flex-start',
          }}
        >
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <DateTimePicker name="dateFrom" />
              <DateTimePicker name="dateTo" />
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'identifications', '产品条码', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <Lov name="prodLineLov" />
              {expandForm && (
                <>
                  <Lov name="materialLov" />
                  <Select name="moveType" />
                  <Lov name="operationLov" />
                  <TextField name="workOrderCode" />
                  <Select name="enableFlag" />
                  <DatePicker name="timeInfo" />
                  <Select name="shiftCode" />
                  <Lov name="workcellLov" />
                  <Lov name="equipmentLov" />
                  <TextField name="loginName" />
                  <Select name="outboundRepairFlag" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                funcType="link"
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleSearch = async () => {
    const { __id, _status, ...otherParams } = tableDs?.queryDataSet?.toJSONData()[0];
    console.log(otherParams,'otherParams');
    
    if (!Object.keys(otherParams).length) {
      notification.error({
        message: '请输入查询条件',
      });
      return;
    }
    tableDs.query();
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      // handleSearch()
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };
  const getExportQueryParams = () => {
    return {
      ...tableDs.queryDataSet.toJSONData()[0],
      traceRelIds: tableDs.selected.map(item => item.get('traceRelId')),
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('产品加工履历查询报表')}>
        <ExcelExportPro
          method="POST"
          allBody
          exportAsync={false}
          otherButtonProps={{ disabled: !tableDs.toData().length }}
          queryParams={getExportQueryParams}
          requestUrl={`${API_HOST}${TARZAN_REPORT}/v1/${tenantId}/hme-product-step-actual/export/ui`}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="ScrapBarcodeGeneration"
          customizedCode="ScrapBarcodeGeneration"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.productProcessingHistoryQueryReport', 'tarzan.common'],
})(ProductProcessingHistoryQueryReport);
