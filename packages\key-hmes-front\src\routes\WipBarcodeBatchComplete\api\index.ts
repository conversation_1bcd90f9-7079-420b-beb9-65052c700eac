/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-06-23 10:29:52
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-06-23 10:39:32
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\WipBarcodeBatchComplete\api\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 出站
export function ExecuteOutbound() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-working-eo-batch-complete/execute`,
    method: 'POST',
  };
}
