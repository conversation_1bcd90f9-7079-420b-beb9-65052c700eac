import React, { useState, useMemo, useEffect } from 'react';
import { Table, Button, Lov, Select, Switch, Form } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
// import notification from 'utils/notification';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hmes.EquipmentGroupMaintenance';

export default ({ singleAssObjectDs, record: propsRecord, canEdit, enabledDs }) => {
  const [selected, setSelected] = useState({});
  const selectedProxy = useMemo(() => ({}), []);
  selectedProxy.selected = selected;
  const typeName = {
    MATERIAL: intl.get(`${modelPrompt}.MATERIAL`).d('物料'),
    MATERIAL_CATEGORY: intl.get(`${modelPrompt}.MATERIAL_CATEGORY`).d('物料类别'),
    OPERATION: intl.get(`${modelPrompt}.OPERATION`).d('工艺'),
    WORKCELL: intl.get(`${modelPrompt}.WORKCELL`).d('工作单元'),
    EQUIPMENT: intl.get(`${modelPrompt}.AREA`).d('设备'),
    // EQUIPMENT_CATEGORY: intl.get(`${modelPrompt}.PROD_LINE`).d('设备类别'),
  };

  useEffect(() => {
    const cacheObjectList = propsRecord.get('objectList') || [];
    const cacheSelected = {};
    cacheObjectList.forEach(item => {
      cacheSelected[item.objectType] = true;
    });
    setSelected(cacheSelected);
    singleAssObjectDs.loadData(
      cacheObjectList.map(item => {
        return { ...item, uuid: uuid() };
      }),
    );
  }, []);

  const selectObjTypeDiv = type => {
    if (!canEdit) {
      return;
    }
    if (selected[type]) {
      // 已选过的类型，再次点击为删除行
      const newList = singleAssObjectDs.filter(record => record.get('objectType') !== type);
      const newSelected = {
        ...selectedProxy.selected,
        [type]: false,
      };
      singleAssObjectDs.loadData(newList);
      setSelected(newSelected);
    } else {
      // 新增行
      const newRow = { uuid: uuid(), objectType: type };
      // if (
      //   ['AREA', 'PROD_LINE', 'WORKCELL'].includes(type) &&
      //   (selected.AREA || selected.PROD_LINE || selected.WORKCELL)
      // ) {
      //   notification.error({
      //     message: '关联对象区域/产线/工作单元只能三选一, 请检查',
      //   });
      //   return;
      // }
      // if (
      //   ['MATERIAL', 'MATERIAL_CATEGORY'].includes(type) &&
      //   (selected.MATERIAL || selected.MATERIAL_CATEGORY)
      // ) {
      //   notification.error({
      //     message: '成品物料与成品物料类别只能二选一, 请检查',
      //   });
      //   return;
      // }
      // if (
      //   ['COM_MATERIAL', 'COM_MATERIAL_CATEGORY'].includes(type) &&
      //   (selected.COM_MATERIAL || selected.COM_MATERIAL_CATEGORY)
      // ) {
      //   notification.error({
      //     message: '组件物料与组件物料类别只能二选一, 请检查',
      //   });
      //   return;
      // }
      // if (
      //   ['MATERIAL', 'COM_MATERIAL_CATEGORY'].includes(type) &&
      //   (selected.MATERIAL || selected.COM_MATERIAL_CATEGORY)
      // ) {
      //   notification.error({
      //     message: '成品物料与组件物料类别只能二选一, 请检查',
      //   });
      //   return;
      // }
      // if (
      //   ['COM_MATERIAL', 'MATERIAL_CATEGORY'].includes(type) &&
      //   (selected.COM_MATERIAL || selected.MATERIAL_CATEGORY)
      // ) {
      //   notification.error({
      //     message: '组件物料与成品物料类别只能二选一, 请检查',
      //   });
      //   return;
      // }
      singleAssObjectDs.create(newRow);
      setSelected({
        ...selectedProxy.selected,
        [type]: true,
      });
    }
  };

  const changeObject = async (lovRecords, record) => {
    if (typeName[record.get('objectType')] === '物料') {
      if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
        record.getField('objectRevision').set('required', true);
        record.set('revisionFlag', 'Y');
        record.set('objectRevision', lovRecords.revisionCode);
      } else {
        record.set('objectRevision', '');
        record.set('revisionFlag', 'N');
        record.getField('objectRevision').set('required', false);
      }
    } else {
      record.set('objectRevision', lovRecords.revision);
    }
  };

  const columns = [
    {
      header: null,
      align: 'center',
      width: 60,
      renderer: ({ record }) => {
        const objType = record.get('objectType');
        return (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              selectObjTypeDiv(objType);
            }}
          >
            <Button disabled={!canEdit} funcType="flat" icon="remove" shape="circle" size="small" />
          </Popconfirm>
        );
      },
      lock: 'left',
    },
    {
      name: 'objectType',
      width: 120,
      renderer: ({ record }) => typeName[record.get('objectType')],
    },
    {
      name: 'objectLov',
      width: 180,
      renderer: ({ record }) => record.get('objectCode'),
      editor: record => {
        return (
          canEdit && (
            <Lov
              dataSet={singleAssObjectDs}
              name="objectLov"
              onChange={lovRecords => changeObject(lovRecords, record)}
            />
          )
        );
      },
    },
    {
      name: 'objectRevision',
      width: 120,
      editor: record => {
        return (
          canEdit &&
          record.get('revisionFlag') === 'Y' &&
          record.data.objectType === 'MATERIAL' && (
            <Select name="objectRevision" dataSet={singleAssObjectDs} />
          )
        );
      },
    },
    {
      name: 'objectDesc',
    },
  ];

  return (
    <>
      <div className={styles['card-select-wrapper']}>
        {Object.keys(typeName).map(item => {
          if (selected[item]) {
            return (
              <Popconfirm
                title={intl
                  .get(`${modelPrompt}.message.confirm.delete`, {
                    typeName: typeName[item],
                  })
                  .d(`是否确认删除关联对象类型为“${typeName[item]}”的数据?`)}
                onConfirm={() => {
                  selectObjTypeDiv(item);
                }}
              >
                <div key={item} className={styles['card-select']} data-selected disabled={!canEdit}>
                  {typeName[item]}
                </div>
              </Popconfirm>
            );
          }
          return (
            <div
              key={item}
              onClick={() => {
                selectObjTypeDiv(item);
              }}
              className={styles['card-select']}
              disabled={!canEdit}
            >
              {typeName[item]}
            </div>
          );
        })}
      </div>
      <Form dataSet={enabledDs} columns={1}>
        <Switch name="enableFlag" disabled={!canEdit} />
      </Form>
      <Table dataSet={singleAssObjectDs} columns={columns} />
    </>
  );
};
