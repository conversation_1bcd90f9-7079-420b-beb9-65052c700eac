import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.hmes.teamMaintenanceRadio';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'productionAreaId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    forceValidate: true,
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'areaCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.areaCode`).d('班组编码'),
        },
        {
          name: 'areaName',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.areaCode`).d('班组名称'),
        },
      ]
    }),
    fields: [
      {
        name: 'areaCode',
        required: true,
        maxLength: 255,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.areaCode`).d('班组编码'),
      },
      {
        name: 'areaName',
        maxLength: 255,
        required: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.areaName`).d('班组名称'),
      },
      {
        name: 'broadcastFlag',
        required: true,
        label: intl.get(`${modelPrompt}.broadcastFlag`).d('是否广播'),
        lookupCode: 'MT.YES_NO',
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
      {
        name: 'points',
        label: intl.get(`${modelPrompt}.form.points`).d('终端维护'),
        maxLength: 255,
        type: FieldType.string,
        dynamicProps: {
          required: ({ record }) => {
            return record.get('broadcastFlag') === 'Y'
          }
        }
      },
      {
        name: 'pointsName',
        label: intl.get(`${modelPrompt}.form.pointsName`).d('终端名称'),
        maxLength: 255,
        type: FieldType.string,
        dynamicProps: {
          required: ({ record }) => {
            return record.get('broadcastFlag') === 'Y'
          }
        }
      },
      {
        name: 'volume',
        label: intl.get(`${modelPrompt}.form.volume`).d('声量'),
        type: FieldType.number,
        step: 1,
        min: 0,
        max: 100,
        dynamicProps: {
          required: ({ record }) => {
            return record.get('broadcastFlag') === 'Y'
          }
        }
      },
      {
        name: 'repeatTime',
        label: intl.get(`${modelPrompt}.form.repeatTime`).d('重复次数'),
        type: FieldType.number,
        step: 1,
        dynamicProps: {
          required: ({ record }) => {
            return record.get('broadcastFlag') === 'Y'
          }
        }
      },
      {
        name: 'speed',
        label: intl.get(`${modelPrompt}.form.speed`).d('语速'),
        type: FieldType.number,
        step: 1,
        min: 0,
        max: 100,
        dynamicProps: {
          required: ({ record }) => {
            return record.get('broadcastFlag') === 'Y'
          }
        }
      },
      {
        name: 'noRepeatMinutes',
        label: intl.get(`${modelPrompt}.form.noRepeatMinutes`).d('抑制时间(分)'),
        step: 1,
        type: FieldType.number,
        dynamicProps: {
          required: ({ record }) => {
            return record.get('broadcastFlag') === 'Y'
          }
        }
      },
      {
        name: 'templateName',
        label: intl.get(`${modelPrompt}.form.templateName`).d('消息模板名称'),
        type: FieldType.object,
        textField: 'templateName',
        lovCode: 'HMSG.MESSAGE_TEMPLATE',
        lovPara: {
          tenantId,
        },
        dynamicProps: {
          required: ({ record }) => {
            return record.get('broadcastFlag') === 'Y'
          }
        }
      },
      {
        name: 'messageTemplateId',
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('templateName') && record.get('templateName').templateId) {
              return 'templateName.templateId'
            }
          }
        }
      },
      {
        name: 'messageName',
        type: FieldType.object,
        lovCode: 'IIOT.MSG_SEND_CONFIG',
        label: intl.get(`${modelPrompt}.form.messageName`).d('发送配置'),
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'tempServerId',
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('messageName') && record.get('messageName').tempServerId) {
              return 'messageName.tempServerId'
            }
          }
        }
      },
      {
        name: 'typeName',
        type: FieldType.object,
        lovCode: 'IIOT.RECEIVER_GROUP',
        label: intl.get(`${modelPrompt}.form.typeName`).d('接收配置'),
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'receiverTypeId',
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('typeName') && record.get('typeName').receiverTypeId) {
              return 'typeName.receiverTypeId'
            }
          }
        }
      },
      {
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.form.enableFflag`).d('是否启用'),
        type: FieldType.string,
        lookupCode: 'MT.YES_NO',
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
      {
        name: 'mediaStreamUrls',
        label: intl.get(`${modelPrompt}.form.mediaStreamUrls`).d('媒体http链接'),
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'GET',
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-production-areas/query/list`,
        };
      },
    },
  });

export default listPageFactory;
