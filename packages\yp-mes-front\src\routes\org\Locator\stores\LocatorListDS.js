/**
 * @Description: 新库位维护汇总
 * @Author: <<EMAIL>>
 * @Date: 2021-03-05 14:14:48
 * @LastEditTime: 2022-11-09 14:37:53
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.model.org.locator';

const LocatorListDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: ({ data }) => {
      const { siteIds, ...others } = data;
      if (siteIds && siteIds.length > 0) {
        others.siteIds = siteIds.join(',');
      }
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}/mt-mod-locator/list/ui`,
        data: others,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'locatorId',
  fields: [
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'locatorType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorType`).d('库位类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=LOCATOR_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'locatorCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCategory`).d('库位类别'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=LOCATOR_CATEGORY`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'parentLocatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.parentLocatorLov`).d('上层库位'),
    },
    {
      name: 'siteNameList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteIds`).d('分配站点'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('库位标识'),
    },
    {
      name: 'coordinateCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.location`).d('位置坐标系'),
    },
    {
      name: 'xValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locationValue`).d('位置坐标值'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
    {
      name: 'negativeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.negativeFlag`).d('是否允许负库存'),
    },
  ],
  queryFields: [
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'locatorCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCategory`).d('库位类别'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=LOCATOR_CATEGORY`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'parentLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.parentLocatorLov`).d('上层库位'),
      lovCode: 'MT.MODEL.PARENT_LOCATOR',
      ignore: 'always',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId: getCurrentOrganizationId(),
          };
        },
      },
    },
    {
      name: 'parentLocatorId',
      type: FieldType.number,
      bind: 'parentLocatorLov.locatorId',
    },
    {
      name: 'parentLocatorCode',
      type: FieldType.string,
      bind: 'parentLocatorLov.locatorCode',
    },
    {
      name: 'siteIds',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.siteIds`).d('分配站点'),
      textField: 'siteName',
      valueField: 'siteId',
      multiple: true,
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${
        BASIC.TARZAN_MODEL
      }/v1/${getCurrentOrganizationId()}/mt-user-organization/user/site/list/ui`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'locatorType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorType`).d('库位类型'),
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=LOCATOR_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('库位标识'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'negativeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.negativeFlag`).d('是否允许负库存'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

export { LocatorListDS };
