import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { TARZAN_TZNI } from '@/utils/config';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor, } from 'choerodon-ui/pro/lib/button/enum';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import listPageFactory from '../stores/listPageDs';
import axios from 'axios';

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.traceabilityPlatform';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {

  const [returnFlag, setReturnFlag] = useState(true)

  useEffect(() => {
    listDs.query();
  }, []);

  useDataSetEvent(listDs, 'query', () => {
    setReturnFlag(true)
  });

  useDataSetEvent(listDs, 'select', () => {
    setStatus()
  });

  useDataSetEvent(listDs, 'unSelect', () => {
    setStatus()
  });

  useDataSetEvent(listDs, 'selectAll', () => {
    setStatus()
  });

  useDataSetEvent(listDs, 'unSelectAll', () => {
    setStatus()
  });

  const setStatus = () => {
    if (listDs.selected.length) {
      const falg = listDs.selected.every(item => item.get('status') === 'E')
      setReturnFlag(!falg)
    } else {
      setReturnFlag(true)
    }
  }

  const columns: ColumnProps[] = [
    {
      name: 'userCode',
    },
    {
      name: 'batchCode',
    },
    {
      name: 'monomerCode',
    },
    {
      name: 'monomerFacspecCode',
      width: 150
    },
    {
      name: 'moduleCode',
    },
    {
      name: 'moduleFacspecCode',
      width: 150
    },
    {
      name: 'packFlow',
      width: 150
    },
    {
      name: 'packCode',
      width: 150
    },
    {
      name: 'packFacspecCode',
      width: 150
    },
    {
      name: 'status',
    },
    {
      name: 'msg',
      width: 180
    },
    {
      name: 'lastUpdateDate',
      width: 150
    },
  ];

  const onChangeReturn = async () => {
    const ids = listDs.selected.map(item => item.get('ifaceId'))
    const url = `${TARZAN_TZNI}/v1/${getCurrentOrganizationId()}/hme-pack-module-trace-ifaces/trans/back/ui`;
    const res: any = await axios.post(url, ids)
    if (res && res.success) {
      await listDs.query();
    } else {
      notification.error({ message: res.message })
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.traceabilityPlatform`).d('追溯平台对接管理')}>
        <Button
          color={ButtonColor.primary}
          disabled={returnFlag}
          onClick={onChangeReturn}
        >
          {intl.get('tarzan.common.button.return').d('手动回传')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="traceabilityPlatform"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={11} // 头部显示的查询字段的数量
          searchCode="traceabilityPlatform" // 动态筛选条后端接口唯一编码
          customizedCode="traceabilityPlatform" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.traceabilityPlatform'],
})(ListPage);
