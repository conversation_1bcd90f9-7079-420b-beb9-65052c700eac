/**
 * @Description: 给组件提供删除
 * @Author: <<EMAIL>>
 * @Date: 2021-04-28 10:01:05
 * @LastEditTime: 2021-04-28 10:20:18
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { Icon } from 'choerodon-ui';
import './Close.less';

const CloseWapper = props => {
  const { onClick } = props;
  return (
    <div className="hcm-components-close-wrapper">
      {props.children}
      <Icon className="hcm-components-close" type="close" onClick={onClick} />
    </div>
  );
};

export default CloseWapper;
