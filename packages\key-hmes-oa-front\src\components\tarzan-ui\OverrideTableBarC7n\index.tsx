/**
 * @Description: 覆盖C7N表格的搜索表单
 * @Author: <<EMAIL>>
 * @Date: 2021-01-12 17:08:54
 * @LastEditTime: 2021-04-26 09:50:48
 * @LastEditors: <<EMAIL>>
 *
 * @example
 *  import { overrideTableBar } from '@/components/tarzan-ui'
 *  <Table dataSet={tableDs} columns={columns} queryBar={overrideTableBar} />
 */

import React, { useMemo, useState } from 'react';
import intl from 'utils/intl';
import { Button, Form, Row, Col } from 'choerodon-ui/pro';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import { TableQueryBarHook, TableQueryBarHookProps } from 'choerodon-ui/pro/lib/table/Table';
import './index.modules.less';

const buttonColumn = {
  lineHeight: '40px',
  paddingLeft: '16px',
};

const rowStyle = {
  marginBottom: '11px',
};

const RenderTableSearchBar: React.FC<TableQueryBarHookProps> = ({
  queryFields,
  queryDataSet,
  dataSet,
  buttons,
}) => {
  const [hidden, setVisible] = useState(true);
  const firstFlowFidlds = useMemo(() => {
    return queryFields.slice(0, 3);
  }, [queryFields]);
  const otherFidlds = useMemo(() => {
    return queryFields.slice(3);
  }, [queryFields, hidden]);

  const keyDownClick = async e => {
    if (e.keyCode === 13) {
      await dataSet.query();
    }
  };

  const clickHandler = async () => {
    await dataSet.query();
  };

  const loadEmptyData = () => {
    queryDataSet!.loadData([{}]);
  };

  return (
    <>
      <Row style={rowStyle}>
        <Col span={18}>
          {/* @ts-ignore */}
          <Form columns={3} labelWidth={121} onKeyDown={keyDownClick} dataSet={queryDataSet}>
            {firstFlowFidlds}
            {!hidden && otherFidlds}
          </Form>
        </Col>
        <Col span={6} style={buttonColumn}>
          {queryFields.length > 3 && (
            <Button
              onClick={() => {
                setVisible(prev => !prev);
              }}
            >
              {hidden
                ? intl.get('tarzan.common.button.moreQueries').d('更多查询')
                : intl.get('tarzan.common.button.lessQueries').d('收起查询')}
            </Button>
          )}
          <Button onClick={loadEmptyData}>
            {intl.get('tarzan.common.button.reset').d('重置')}
          </Button>
          <Button onClick={clickHandler} color={ButtonColor.primary} type={ButtonType.submit}>
            {intl.get('tarzan.common.button.search').d('查询')}
          </Button>
        </Col>
      </Row>
      {buttons && <div className="c7n-pro-table-professional-toolbar">{buttons}</div>}
    </>
  );
};

const overrideTableBar: TableQueryBarHook = (props: TableQueryBarHookProps) => (
  <RenderTableSearchBar {...props} />
);

export default overrideTableBar;
