import { getCurrentOrganizationId } from 'utils/utils';
import { Host } from '@/utils/config';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.assemblyRecordQuery';
// const Host = `/mes-41300`;

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'eoId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: 'multiple',
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      },
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      },

      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'concessiveInterceptionFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.concessiveInterceptionFlag`).d('拦截标识'),
      },
      {
        name: 'operationDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDesc`).d('拦截工艺'),
      },
      {
        name: 'qualityStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      },
      {
        name: 'routerStepDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.routerStepDesc`).d('当前工艺'),
      },
    ],
    queryFields: [
      {
        name: 'identifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.identifications`).d('条码号'),
      },
      {
        name: 'materialLov',
        type: 'object',
        lovCode: 'MT.MATERIAL',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
      },
      {
        name: 'materialId',
        bind: 'materialLov.materialId',
      },
      {
        name: 'materialCode',
        bind: 'materialLov.materialCode',
      },
      {
        name: 'operationLov',
        type: 'object',
        lovCode: 'MT.OPERATION',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.operationLov`).d('拦截工艺'),
      },
      {
        name: 'operationId',
        bind: 'operationLov.operationId',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-eo-batch-dealing/head/query`,
          method: 'GET',
        };
      },
    },
  };
};


export { tableDS };
