import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentTenantId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.hmes.equipmentMaintenance';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'transRoutesId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'userLov',
          label: intl.get(`${modelPrompt}.form.loginName`).d('用户名称'),
          ignore: FieldIgnore.always,
          type: FieldType.object,
          multiple: true,
          textField: 'loginName',
          lovCode: "MT.USER.ORG",
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'userIds',
          bind: 'userLov.id',
        },
        {
          name: 'equipmentCode',
          label: intl.get(`${modelPrompt}.form.equipmentCode`).d('设备编码'),
          ignore: FieldIgnore.always,
          type: FieldType.object,
          multiple: true,
          lovCode: 'MT.MODEL.EQUIPMENT',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'equipmentIds',
          bind: 'equipmentCode.equipmentId',
        },
        {
          name: 'cardNums',
          multiple:true,
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.cardNums`).d('卡号'),
        },
      ],
    }),
    fields: [
      {
        name: 'userName',
        required: true,
        label: intl.get(`${modelPrompt}.form.userName`).d('用户名称'),
        ignore: FieldIgnore.always,
        type: FieldType.object,
        textField: 'loginName',
        lovCode: "MT.USER.ORG",
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'userId',
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('userName') && record.get('userName').id) {
              return 'userName.id';
            }
          },
        },
      },
      {
        name: 'equipmentCode',
        required: true,
        label: intl.get(`${modelPrompt}.form.equipmentCode`).d('设备编码'),
        ignore: FieldIgnore.always,
        type: FieldType.object,
        lovCode: 'MT.MODEL.EQUIPMENT',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'equipmentId',
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('equipmentCode') && record.get('equipmentCode').equipmentId) {
              return 'equipmentCode.equipmentId';
            }
          },
        },
      },
      {
        name: 'equipmentName',
        label: intl.get(`${modelPrompt}.form.equipmentName`).d('设备名称'),
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('equipmentCode') && record.get('equipmentCode').equipmentName) {
              return 'equipmentCode.equipmentName';
            }
          },
        },
      },
      {
        name: 'levelInfo',
        label: intl.get(`${modelPrompt}.form.levelInfo`).d('等级'),
        type: FieldType.string,
      },
      {
        name: 'cardNum',
        label: intl.get(`${modelPrompt}.form.cardNum`).d('卡号'),
        type: FieldType.string,
      },
      {
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.form.enableFlag`).d('有效性'),
        type: FieldType.string,
        lookupCode: 'MT.APS.YES_NO',
        defaultValue: 'Y',
        trueValue: 'Y',
        falseValue: 'N'
      },

    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-employee-equipment-levels/query/info`,
        };
      },
    },
  });

export default listPageFactory;
