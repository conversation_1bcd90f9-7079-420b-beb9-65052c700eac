/**
 * @Description: 执行作业管理列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:40:22
 * @LastEditTime: 2023-05-18 14:42:31
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { Badge, Tag } from 'choerodon-ui';
import { DataSet, Table, Button, Modal, TextField, Lov, Icon, DateTimePicker, Row, Col, Form, ModalProvider, useModal, Select } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import { getResponse } from '@utils/utils';
import request from 'utils/request';
// import { BASIC } from '@utils/config';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { BASIC } from '../../../../utils/config';
import styles from '../index.module.less';
import HistoryDrawer from './HistoryDrawer';
import { tableDS, historyDS } from '../stores/ExecuteListDS';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import LovModal from '@/components/BatchInput/LovModal';


const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.workshop.execute';

const ExecuteList = observer(props => {
  const {
    dataSet,
    historyDs,
    match: { path },
    customizeTable,
  } = props;

  const inputLovDS = new DataSet(InputLovDS());
  // 选中列表项执行作业状态 (选中条状态不同时为空字符)
  // const [status, setStatus] = useState('');
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [expandForm, setExpandForm] = useState(false);
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);



  // 选中列表项执行作业状态 (选中条状态不同时为空字符)
  // const [selectIds, setSelectIds] = useState([]);

  const columns = [
    {
      name: 'eoNum',
      lock: 'left',
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              orderDetail(record.data.eoId);
            }}
          >
            {value}
          </a>
        );
      },
      width: 220,
    },
    {
      name: 'statusDesc',
      width: 150,
    },
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'nowOperationName',
      width: 150,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'revisionCode',
      width: 120,
    },
    {
      name: 'materialName',
      width: 200,
    },
    {
      name: 'qualityStatusDesc',
      width: 110,
      align: 'center',
      renderer: ({ record, value }) => {
        if (record.data.qualityStatus === 'OK') {
          return <Tag color="green">{value}</Tag>;
        }
        if (record.data.qualityStatus === 'NG') {
          return <Tag color="red">{value}</Tag>;
        }
        if (record.data.qualityStatus === 'PENDING') {
          return <Tag color="blue">{value}</Tag>;
        }
      },
    },
    {
      name: 'qty',
      width: 150,
      align: 'right',
    },
    {
      name: 'completedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'scrappedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'reworkFlag',
      width: 150,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'concessiveInterceptionFlag',
      width: 150,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'overOrderInterceptionFlag',
      width: 150,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'dischargeFlag',
      width: 150,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'degradeFlag',
      width: 150,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'workOrderNum',
      width: 200,
    },
    {
      name: 'planStartTime',
      width: 150,
      align: 'center',
    },
    {
      name: 'planEndTime',
      width: 150,
      align: 'center',
    },
    {
      name: 'eoTypeDesc',
      width: 150,
    },
    {
      name: 'productionLineCode',
      width: 200,
    },
    // {
    //   name: 'productionLineName',
    //   width: 200,
    // },
    {
      name: 'siteCode',
      width: 120,
    },
  ];

  useEffect(() => {
    dataSet.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.EO.LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.EO.LIST.TABLE`);
    // dataSet.query(props.dataSet.currentPage);
    // handleDataSetSelectUpdate();
  }, []);

  const orderDetail = id => {
    props.history.push(`/hmes/workshop/execute-operation-management/detail/${id}`);
  };

  const clickMenu = async ({ key }) => {
    setLoading(true);
    const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/status/update/ui`, {
      method: 'POST',
      body: {
        eoIds: dataSet.selected.map(item => item.get('eoId')),
        operationType: key,
      },
    });
    setLoading(false);

    const res = getResponse(response);
    if (res) {
      if (dataSet) {
        dataSet.batchUnSelect(dataSet.selected.map(item => item.id));
        // setStatus('');
        // setSelectIds([]);
      }
      notification.success();
      dataSet.query(props.dataSet.currentPage);
    }
  };
  const handleCancel = () => {
    setEditing(false);
    dataSet.clearCachedSelected();
    dataSet.query(props.dataSet.currentPage);
  }
  const handleEdit = () => {
    setEditing(true);
  }
  const handleSave = async () => {
    await dataSet.submit();
    setEditing(false);
    dataSet.query(props.dataSet.currentPage);
  }

  const handleQueryHistory = () => {
    historyDs.setQueryParameter('eoIdList', dataSet.selected.map(item => item.get('eoId')));
    historyDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}</div>
        </div>
      ),
      destroyOnClose: true,
      children: (
        <HistoryDrawer ds={historyDs} />
      ),
    });
  };

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
      // handleSearch()
    }
  }
  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: dataSet,
    onOpenInputModal,
  };
  const toggleForm = () => {
    setExpandForm(!expandForm);
  }
  const handleSearch = async () => {
    dataSet.query();
  };
  const renderQueryBar = ({ buttons, queryDataSet, queryFields, dataSet }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={19}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              {/* <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identifications', '物料批标识')}
                    />
                  </div>
                }
              />
              <TextField
                name="materialLotCodes"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'materialLotCodes', '物料批编码')}
                    />
                  </div>
                }
              /> */}
              <TextField
                name="eoNums"
                style={{ maxHeight: '200px', overflow: 'auto' }}
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'eoNums', '执行作业编码')}
                    />
                  </div>
                }
              />
              <TextField
                style={{ maxHeight: '200px', overflow: 'auto' }}
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identifications', '执行作业标识')}
                    />
                  </div>
                }
              />
              <Lov name="material" />
              {expandForm && (
                <>
                  <Lov name="site" />
                  <Select name="eoType" />
                  <Select name="status" />
                  <Lov name="productionLine" />
                  <Lov name="workOrder" />
                  <DateTimePicker name="startTimeFrom" />
                  <DateTimePicker name="startTimeTo" />
                  <DateTimePicker name="endTimeFrom" />
                  <DateTimePicker name="endTimeTo" />
                </>
              )}
            </Form>
          </Col>
          <Col span={5}>
            <div>
              <Button funcType='link' icon={
                expandForm ? 'expand_less' : 'expand_more'
              } onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button onClick={() => {
                queryDataSet.current.reset();
                dataSet.fireEvent('queryBarReset', {
                  dataSet,
                  queryFields,
                });
              }}>
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button onClick={handleSearch} color='primary'>
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('执行作业管理')}>
        {editing ? (
          <>
            <Button onClick={handleSave} color='primary'>{intl.get(`tarzan.common.button.save`).d('保存')}</Button>
            <Button onClick={handleCancel} color='primary'>{intl.get(`tarzan.common.button.cancel`).d('取消')}</Button></>
        ) : (<Button onClick={() => handleEdit()} color='primary'>{intl.get(`${modelPrompt}.head.button.title`).d('扩展属性修改')}</Button>)}
        <Button
          disabled={!dataSet.selected.length}
          onClick={handleQueryHistory}
        >
          {intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}
        </Button>
      </Header>
      <Content>
        {customizeTable(
          {
            // filterCode: `${BASIC.CUSZ_CODE_BEFORE}.EO.LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.EO.LIST.TABLE`,
            columnEditorRender: () => editing,
          },
          <Table
            queryBar={renderQueryBar}
            dataSet={dataSet}
            columns={columns}
            searchCode="ExecuteList"
            customizedCode="ExecuteList"
          />,
        )}
      </Content>
      <LovModal {...lovModalProps} />

    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.workshop.execute', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...tableDS() });
      const historyDs = new DataSet({ ...historyDS() });
      return {
        dataSet,
        historyDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.EO.LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.EO.LIST.TABLE`],
    })(ExecuteList),
  ),
);
