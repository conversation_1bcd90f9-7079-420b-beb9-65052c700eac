/**
 * @Description: 数据项信息-Tab页
 * @Author: <EMAIL>
 * @Date: 2023-03-14 10:19:56
 */

import React, { useState, useEffect } from 'react';
import { Dropdown, Icon, Menu, Form, NumberField, Select, Switch } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Icon as HzeroIcon } from 'hzero-ui';
import { queryMapIdpValue } from '../../../services/api';
import styles from './index.module.less';

const MenuItem = Menu.Item;
const modelPrompt = 'tarzan.hmes.materialPreventError';

const DataItemInfoTab = props => {
  const {
    canEdit,
    dataItemDs1, // 生效时长
    dataItemDs2, // 预处理前静置时间
    dataItemDs3, // 预处理时间
    dataItemDs4, // 预处理后静置时间
    dataItemDs5, // 状态维度
    agingDemension,
    setAgingDemension,
    statusDemension,
    setStatusDemension,
  } = props;
  const [proofingDemension, setProofingDemension] = useState([]); // 配置维度值集
  const [expandForm, setExpandForm] = useState(true); // 展开收起标识

  useEffect(() => {
    // 查询维度
    queryMapIdpValue({
      proofingDemensionList: 'HME.ERR_PROOFING_DEMENSION',
    }).then(res => {
      if (res && !res.failed) {
        setProofingDemension(res.proofingDemensionList);
      }
    });
  }, []);

  // 切换配置维度
  const changeQueryMenu = ({ keyPath }) => {
    if (canEdit) {
      if (keyPath[0] === 'AGING_DIMENSION') {
        queryMapIdpValue({
          agingDemensionList: 'AGING_DIMENSION',
        }).then(res => {
          if (res && !res.failed) {
            const newAgingDemensionList = [];
            res.agingDemensionList.forEach(item => {
              newAgingDemensionList.push(item.meaning);
            });
            setAgingDemension(newAgingDemensionList);
          }
        });
      }
      if (keyPath[0] === 'STATUS_DIMENSION') {
        queryMapIdpValue({
          statusDemensionList: 'STATUS_DIMENSION',
        }).then(res => {
          if (res && !res.failed) {
            const newStatusDemensionList = [];
            res.statusDemensionList.forEach(item => {
              newStatusDemensionList.push(item.meaning);
            });
            setStatusDemension(newStatusDemensionList);
          }
        });
      }
    }
  };

  // 维度菜单
  const menu = (
    <Menu onClick={changeQueryMenu} mode="vertical">
      {proofingDemension &&
        proofingDemension.map(item => {
          return <MenuItem key={item.value}>{item.meaning}</MenuItem>;
        })}
    </Menu>
  );

  // 删除维度
  const removeDimension = dimension => {
    if (canEdit) {
      if (dimension === 'timeDimension') {
        // dataItemDs1.reset();
        // dataItemDs2.reset();
        // dataItemDs3.reset();
        // dataItemDs4.reset();
        dataItemDs1.loadData([]);
        dataItemDs2.loadData([]);
        dataItemDs3.loadData([]);
        dataItemDs4.loadData([]);
        setAgingDemension([]);
      } else if (dimension === 'statusDimension' || dimension === '预处理状态') {
        setStatusDemension([]);
      } else {
        if (dimension === '生效时长') {
          dataItemDs1.loadData([]);
          // dataItemDs1.reset();
        }
        if (dimension === '预处理前静置时间') {
          // dataItemDs2.reset();
          dataItemDs2.loadData([]);
        }
        if (dimension === '预处理时间') {
          // dataItemDs3.reset();
          dataItemDs3.loadData([]);
        }
        if (dimension === '预处理后静置时间') {
          // dataItemDs4.reset();
          dataItemDs4.loadData([]);
        }
        const newAgingDemension = agingDemension.filter(ele => ele !== dimension); // 获取常规列数据
        setAgingDemension(newAgingDemension);
      }
    }
  };

  const changeDataItemDs = (value, dataSet, flag) => {
    if (value) {
      if (flag !== 'dataItemDs1') {
        dataSet.getField('relation')?.set('required', true);
      }
      dataSet.getField('detailValue')?.set('required', true);
      dataSet.getField('uomId')?.set('required', true);
    } else if (
      !(
        dataSet.toData()[0]?.relation ||
        dataSet.toData()[0]?.detailValue ||
        dataSet.toData()[0]?.uomId
      )
    ) {
      dataSet.getField('relation')?.set('required', false);
      dataSet.getField('detailValue')?.set('required', false);
      dataSet.getField('uomId')?.set('required', false);
      dataSet.validate();
    }
  };

  return (
    <div className="hmes-style tarzan-ui-remove">
      <div className={styles['query-header-wrapper']}>
        <div className={styles['query-header-title']}>
          <div>
            <span className={styles['query-header-title-dimension']}>
              {intl.get(`${modelPrompt}.queryDimension`).d('查询维度')}
            </span>
            <Dropdown
              overlay={menu}
              getPopupContainer={() => document.getElementById('hpro-menu-container')}
            >
              <Icon type="control_point" className={styles['add-icon']} />
            </Dropdown>
            <a
              style={{
                marginLeft: '16px',
                fontSize: '12px',
              }}
              onClick={() => setExpandForm(!expandForm)}
            >
              {expandForm
                ? intl.get(`tarzan.common.button.retract`).d('收起')
                : intl.get('tarzan.common.button.open').d('展开')}
              <HzeroIcon type={expandForm ? 'up' : 'down'} />
            </a>
          </div>
        </div>
        <div style={{ display: expandForm ? '' : 'none' }}>
          <div className={styles['customer-divider']} />
          {/* 时效维度 */}
          <div
            className={styles['query-header-row']}
            style={{ display: agingDemension.length > 0 ? '' : 'none' }}
          >
            <div className={styles['query-header-row-header']}>
              <Icon
                type="remove_circle_outline"
                className={styles['remove-icon']}
                onClick={() => removeDimension('timeDimension')}
              />
              {intl.get(`${modelPrompt}.timeDimension`).d('时效维度')}
            </div>
            <div className={styles['query-header-row-form']}>
              <Form
                labelLayout="placeholder"
                showValidation="tooltip"
                disabled={!canEdit}
                columns={3}
              >
                {agingDemension.includes('生效时长') && (
                  <div>
                    <Icon
                      type="remove_circle_outline"
                      className={styles['remove-icon']}
                      onClick={() => removeDimension('生效时长')}
                    />
                    <NumberField
                      dataSet={dataItemDs1}
                      label="生效时长"
                      name="detailValue"
                      style={{ width: '30%', marginLeft: '10px' }}
                      onChange={value => changeDataItemDs(value, dataItemDs1, 'dataItemDs1')}
                    />
                    <Select
                      dataSet={dataItemDs1}
                      name="uomId"
                      style={{ width: '50%' }}
                      onChange={value => changeDataItemDs(value, dataItemDs1, 'dataItemDs1')}
                    />
                  </div>
                )}
                {agingDemension.includes('预处理前静置时间') && (
                  <div newLine>
                    <Icon
                      type="remove_circle_outline"
                      className={styles['remove-icon']}
                      onClick={() => removeDimension('预处理前静置时间')}
                    />
                    <Select
                      dataSet={dataItemDs2}
                      name="relation"
                      style={{ width: '30%', marginLeft: '10px' }}
                      onChange={value => changeDataItemDs(value, dataItemDs2)}
                    />
                    <NumberField
                      dataSet={dataItemDs2}
                      label="预处理前静置时间"
                      name="detailValue"
                      style={{ width: '30%' }}
                      onChange={value => changeDataItemDs(value, dataItemDs2)}
                    />
                    <Select
                      dataSet={dataItemDs2}
                      name="uomId"
                      style={{ width: '20%' }}
                      onChange={value => changeDataItemDs(value, dataItemDs2)}
                    />
                  </div>
                )}
                {agingDemension.includes('预处理时间') && (
                  <div>
                    <Icon
                      type="remove_circle_outline"
                      className={styles['remove-icon']}
                      onClick={() => removeDimension('预处理时间')}
                    />
                    <Select
                      dataSet={dataItemDs3}
                      name="relation"
                      style={{ width: '30%', marginLeft: '10px' }}
                      onChange={value => changeDataItemDs(value, dataItemDs3)}
                    />
                    <NumberField
                      dataSet={dataItemDs3}
                      label="预处理时间"
                      name="detailValue"
                      style={{ width: '30%' }}
                      onChange={value => changeDataItemDs(value, dataItemDs3)}
                    />
                    <Select
                      dataSet={dataItemDs3}
                      name="uomId"
                      style={{ width: '20%' }}
                      onChange={value => changeDataItemDs(value, dataItemDs3)}
                    />
                  </div>
                )}
                {agingDemension.includes('预处理后静置时间') && (
                  <div>
                    <Icon
                      type="remove_circle_outline"
                      className={styles['remove-icon']}
                      onClick={() => removeDimension('预处理后静置时间')}
                    />
                    <Select
                      dataSet={dataItemDs4}
                      name="relation"
                      style={{ width: '30%', marginLeft: '10px' }}
                      onChange={value => changeDataItemDs(value, dataItemDs4)}
                    />
                    <NumberField
                      dataSet={dataItemDs4}
                      label="预处理后静置时间"
                      name="detailValue"
                      style={{ width: '30%' }}
                      onChange={value => changeDataItemDs(value, dataItemDs4)}
                    />
                    <Select
                      dataSet={dataItemDs4}
                      name="uomId"
                      style={{ width: '20%' }}
                      onChange={value => changeDataItemDs(value, dataItemDs4)}
                    />
                  </div>
                )}
              </Form>
            </div>
          </div>
          {/* 状态维度 */}
          <div
            className={styles['query-header-row']}
            style={{ display: statusDemension.length > 0 ? '' : 'none', marginTop: '30px' }}
          >
            <div className={styles['query-header-row-header']}>
              <Icon
                type="remove_circle_outline"
                className={styles['remove-icon']}
                onClick={() => removeDimension('statusDimension')}
              />
              {intl.get(`${modelPrompt}.statusDimension`).d('状态维度')}
            </div>
            <div className={styles['query-header-row-form']}>
              <Form
                style={{ marginTop: '-10px' }}
                disabled={!canEdit}
                dataSet={dataItemDs5}
                columns={3}
              >
                {statusDemension.includes('预处理状态') && (
                  <div>
                    <Icon
                      type="remove_circle_outline"
                      className={styles['remove-icon']}
                      onClick={() => removeDimension('预处理状态')}
                    />
                    <span style={{ marginLeft: '10px', marginRight: '8px' }}>预处理状态:</span>
                    <Switch name="pretreatmentStatus" />
                  </div>
                )}
              </Form>
            </div>
          </div>
        </div>
        {!expandForm && <div className={styles['customer-divider']} />}
      </div>
      <div id="hpro-menu-container" />
    </div>
  );
};

export default DataItemInfoTab;
