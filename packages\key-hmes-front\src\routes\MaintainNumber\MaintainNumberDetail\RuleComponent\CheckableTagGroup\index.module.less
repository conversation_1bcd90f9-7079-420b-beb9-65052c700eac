:global {
  .CheckableTagGroup {
    position: relative;

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      z-index: 100;
    }

    .ant-tag-checkable {
      border: 1px solid #d9d9d9;
      background: #fafafa;
      display: inline-flex;
      align-items: center;
      transition: none;

      &:hover {
        .content {
          color: #333;
        }

        i {
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .content:hover {
        color: #29bece;
      }

      i {
        transform: scale(calc(10 / 12));
        color: rgba(0, 0, 0, 0.45);
        margin: 2px 0 0 4px;

        &:hover {
          color: rgba(0, 0, 0, 1);
        }
      }
    }

    .ant-tag-checkable-checked {
      border-color: transparent;
      background-color: #29bece;
      transition: none;

      &:hover {
        .content {
          color: #fff;
        }
      }
    }

    .usedTag {
      pointer-events: none;
      background: #f5f5f5;
      color: #aaa;

      i {
        display: none;
      }
    }
  }

  .CannotEdit {
    .ant-tag-checkable {
      background: #f5f5f5;
      color: #aaa;
    }

    .ant-tag-checkable-checked {
      border-color: transparent;
      background-color: #29bece;
      color: #fff;
    }
  }
}

.inputTag {
  border-style: dashed;
  background: #fff;

  &:hover {
    border: 1px dashed #29bece;
    color: #29bece;
  }
}
