import React, { Component } from 'react';
import { connect } from 'dva';
import { Form, Row, Col } from 'hzero-ui';
import {
  FORM_COL_3_LAYOUT,
  SEARCH_FORM_ROW_LAYOUT,
  DRAWER_FORM_ITEM_LAYOUT,
} from '@utils/constants';
import intl from 'utils/intl';
import CheckableTagGroup from './CheckableTagGroup';

const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';
@connect(({ maintainNumber }) => ({
  maintainNumber,
}))
@Form.create({ fieldNameProp: null })
export default class StandardObjectCode extends Component {
  constructor(props) {
    super(props);
    props.onRef(this);
  }

  handleChange = item => {
    const { dataSource, setUsingRuleDetail } = this.props;
    const usingRuleDetail = {
      ...dataSource,
      callStandardObject: item,
    };
    setUsingRuleDetail(usingRuleDetail);
  };

  deleteTag = (item, e) => {
    e.stopPropagation();
    const {
      dispatch,
      maintainNumber: { maintainNumberDetail = {} },
    } = this.props;
    const { objectColumnCodeList = [] } = maintainNumberDetail;
    const newList = objectColumnCodeList.filter(
      object => object.objectColumnCode !== item.objectColumnCode,
    );
    dispatch({
      type: 'maintainNumber/updateState',
      payload: {
        maintainNumberDetail: {
          ...maintainNumberDetail,
          objectColumnCodeList: newList,
        },
      },
    });
  };

  saveObjTypeCodeList = inputValue => {
    const {
      dispatch,
      maintainNumber: { maintainNumberDetail = {} },
    } = this.props;
    const { objectColumnCodeList = [] } = maintainNumberDetail;
    const newList = objectColumnCodeList;
    if (inputValue && !objectColumnCodeList.some(item => item.objectColumnCode === inputValue)) {
      newList.push({ objectColumnCode: inputValue, initialFlag: 'N' });
      dispatch({
        type: 'maintainNumber/updateState',
        payload: {
          maintainNumberDetail: {
            ...maintainNumberDetail,
            objectColumnCodeList: newList,
          },
        },
      });
    }
  };

  changeUsedTag = val => {
    const {
      dispatch,
      maintainNumber: { usingObjectColumnCodeArray = [] },
      focusInputKey,
    } = this.props;
    usingObjectColumnCodeArray[focusInputKey] = val;
    dispatch({
      type: 'maintainNumber/updateState',
      payload: {
        usingObjectColumnCodeArray,
      },
    });
  };

  render() {
    const {
      form,
      canEdit,
      dataSource,
      maintainNumber: {
        maintainNumberDetail: { objectColumnCodeList = [] },
        usingObjectColumnCodeArray = [],
      },
    } = this.props;
    const { getFieldDecorator } = form;
    const { callStandardObject = [] } = dataSource;
    return (
      <Row {...SEARCH_FORM_ROW_LAYOUT}>
        <Col {...FORM_COL_3_LAYOUT}>
          <Form.Item
            {...DRAWER_FORM_ITEM_LAYOUT}
            label={intl.get(`${modelPrompt}.callStandardObject`).d('编码对象属性')}
          >
            {getFieldDecorator('callStandardObject', {
              initialValue: callStandardObject,
              rules: [
                {
                  required: true,
                  message: intl
                    .get('tarzan.mes.maintainNumber.validation.callStandardObject')
                    .d('至少选择一个编码对象属性标签'),
                },
              ],
            })(
              <CheckableTagGroup
                style={{ width: '400%' }}
                dataSource={objectColumnCodeList}
                usingTagArray={usingObjectColumnCodeArray}
                saveObjTypeCodeList={this.saveObjTypeCodeList}
                onChange={this.handleChange}
                canEdit={canEdit}
                deleteTag={this.deleteTag}
                changeUsedTag={this.changeUsedTag}
              />,
            )}
          </Form.Item>
        </Col>
      </Row>
    );
  }
}
