import Record from 'choerodon-ui/pro/lib/data-set/Record';
import { isArray, isEmpty } from 'lodash';


export const searchCopy = (
    types: Array<string>,
    currentType: string,
    record: Record,
    value: string[],
) => {
    if (types.includes(currentType) && isArray(value)) {
        const newValue = value.reduce((prev: string[], next: string) => {
            const currentMiddle = next.replace(/\s+/g, ',');
            const current = currentMiddle.split(',');
            return [...prev, ...current];
        }, []);
        const result = newValue.filter(child => !isEmpty(child));
        record.set(currentType, result);
    }
};