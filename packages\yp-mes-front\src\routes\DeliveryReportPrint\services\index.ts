/*
 * @Description: 发货报告打印平台-services
 * @Author: <<EMAIL>>
 * @Date: 2024-03-07 15:09:02
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-07 15:09:39
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存物料计划属性
 * @function QueryPrintInfo
 * @returns {object} fetch Promise
 */
export function QueryPrintInfo(): object {
  return {
    url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-shipment-print/detail/ui`,
    method: 'POST',
  };
}