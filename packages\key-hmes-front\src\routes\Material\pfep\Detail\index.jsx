// 物料PFEP属性维护
import {
  Select,
  Button,
  DataSet,
  Form,
  Table,
  Switch,
  Spin,
  TextField,
  Lov,
  NumberField,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm, Badge } from 'choerodon-ui';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { getCurrentSiteInfo } from '@utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import request from 'utils/request';
import React, { useState, useMemo, useEffect } from 'react';
import { AttributeDrawer } from '@components/tarzan-ui';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { detailDS, orgListDS } from '../stories/detailDs';

const modelPrompt = 'tarzan.material-pfep-manager.model.dataItem';
const tenantId = getCurrentOrganizationId();
const siteInfo = getCurrentSiteInfo();
const { Panel } = Collapse;

const detail = props => {
  const {
    match: { path, params },
    customizeForm,
    custConfig,
  } = props;
  const { id } = params;
  const isCreate = useMemo(() => id === 'create', [id]);
  const [loading, setLoading] = useState(false);
  const [changeFlag, setChangeFlag] = useState(false);
  const [canEdit, setCanEdit] = useState(id === 'create');

  const baseInfoDs = useMemo(() => new DataSet(detailDS()), []);
  const orgListDs = useMemo(() => new DataSet(orgListDS(baseInfoDs)), []);

  useEffect(() => {
    if (id === 'create') {
      baseInfoDs.loadData([
        {
          siteId: siteInfo.siteId,
          siteCode: siteInfo.siteCode,
        },
      ]);
      return;
    }
    handleQuery(id);
  }, [id]);


  useEffect(() => {
    baseInfoDs.addEventListener('update', handleUpdate);
    orgListDs.addEventListener('update', handleUpdate);
    return () => {
      baseInfoDs.removeEventListener('update', handleUpdate);
      orgListDs.addEventListener('update', handleUpdate);
    };
  }, []);

  const handleUpdate = () => {
    setChangeFlag(true);
  }

  const handleQuery = async () => {
    // id = '3213973001,15812001'
    const [materialSiteId, pfepInventoryId] = id.split(',');
    baseInfoDs.setQueryParameter('materialSiteId', materialSiteId);
    baseInfoDs.setQueryParameter('pfepInventoryId', pfepInventoryId);
    baseInfoDs.query().then(res => {
      orgListDs.loadData(res.lineList || []);
      if (res.expirationDateFlag === "N") {
        baseInfoDs.current?.set('expirationDateFlag', 'N');
      } else {
        baseInfoDs.current?.set('expirationDateFlag', 'Y');
      }
    });
  };
  // 新增组件行的回调
  const handleAddLine = () => {
    orgListDs.create({}, 0);
  };

  // 删除表格某一行的回调
  const deleteRecord = record => {
    orgListDs.remove(record);
  };

  const columns = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleAddLine}
          funcType="flat"
          shape="circle"
          size="small"
          disabled={!canEdit}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      name: 'editColumn',
      align: 'center',
      lock: 'left',
      width: 80,
      hideable: false,
      renderer: ({ record }) => {
        if (record.status !== 'add') return null;
        return (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => deleteRecord(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <PermissionButton
              type="c7n-pro"
              icon="remove"
              funcType="flat"
              shape="circle"
              size="small"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            />
          </Popconfirm>
        );
      },
    },
    {
      name: 'organizationIdObj',
      editor: record => {
        if (record.status !== 'add') return false;
        return canEdit && <Lov />;
      },
    },
    { name: 'shelfLife', editor: canEdit && <NumberField /> },
    {
      name: 'extendedShelfLife',
      editor: canEdit && <NumberField />,
    },
    {
      name: 'dullPeriod',
      editor: canEdit && <NumberField />,
    },
    {
      name: 'dullPeriodSecondary',
      editor: canEdit && <NumberField />,
    },
    {
      name: 'earlyWarningLeadTime',
      editor: canEdit && <NumberField />,
    },
    {
      name: 'shelfLifeUomIdObj',
      width: 114,
      editor: canEdit && <Lov />,
    },
    {
      name: 'minStockQty',
      width: 114,
      editor: canEdit && <NumberField />,
    },
    {
      name: 'maxStockQty',
      width: 114,
      editor: canEdit && <NumberField />,
    },
    {
      name: 'enableFlag',
      editor: canEdit && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  const handleSave = async () => {
    const [validate, listValidate] = await Promise.all([
      baseInfoDs.validate(),
      orgListDs.validate(),
    ]);
    if (!validate || !listValidate) {
      return;
    }
    const formObj = baseInfoDs.toData();
    const tableList = orgListDs.toData();
    const lineList = tableList.map(item => {
      return {
        ...item,
      };
    });
    const params = {
      ...formObj[0],
      lineList,
    };
    setLoading(true);
    request(`${BASIC.TARZAN_METHOD}/v1/${tenantId}/wms-mrp-inventorys`, {
      method: 'post',
      body: { ...params },
    }).then(res => {
      setLoading(false);
      if (res && !res.failed) {
        notification.success();
        if (isCreate) {
          const materialSiteId = res.materialSiteId;
          const pfepInventoryId = res.pfepInventoryId;
          props.history.push(
            `/hmes/product/material-pfep-manager/detail/${materialSiteId},${pfepInventoryId}`,
          );
          return;
        }
        handleQuery();
        setCanEdit(false);
        setChangeFlag(false);
      } else {
        notification.error({ message: res.message });
      }
    });
    // const { success, resultId } = await baseInfoDs.submit();
    // if (success ) {
    //   setCanEdit(prev => !prev);
    //   if(isCreate) props.history.push(`/hmes/product/material-pfep-manager/detail/${resultId}`);
    //   handleQuery()
    // }
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/product/material-pfep-manager/list');
    } else {
      setCanEdit(prev => !prev);
      handleQuery();
    }
  };

  const renderAttributeDrawer = code => {
    const [materialSiteId, pfepInventoryId] = id.split(',');
    let paramCode = '',kid='';
    if (code === 'MT_MATERIAL_SITE_CA') {
      paramCode = 'MtMaterialSite';
      kid=materialSiteId
    } else {
      paramCode = 'MtPfepInventory';
      kid=pfepInventoryId
    }
    return (
      <div onClick={e => e.stopPropagation()}>
        <AttributeDrawer
          // onClick={handleExtra}
          serverCode={BASIC.TARZAN_METHOD}
          className={`org.tarzan.method.domain.entity.${paramCode}`}
          kid={kid}
          disabled={id === 'create' || changeFlag}
          canEdit={canEdit}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.${code}`}
          custConfig={custConfig}
          onSubmit={handleAttrSubmit}
        />
      </div>
    );
  };

  const handleExtra = () => {
    if (changeFlag) {
      notification.error({ message: intl
        .get(`${modelPrompt}.error.unSave.savefirst`)
        .d(`当前模块下存在未保存的数据，请先保存！`) });
    }
  };

  const handleAttrSubmit = () => {
    handleQuery();
  };

  return (
    <React.Fragment>
      <Header
        title={intl.get('tarzan.material-pfep-manager.title.detail').d('物料PFEP属性维护')}
        backPath="/hmes/product/material-pfep-manager/list"
      >
        {canEdit ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Collapse bordered={false} defaultActiveKey={['1', '2', '3', '4']}>
            <Panel
              header={intl.get(`${modelPrompt}.material`).d('物料站点属性')}
              key="1"
              dataSet={baseInfoDs}
              // extra={renderAttributeDrawer('MT_MATERIAL_SITE_CA')}
            >
              {customizeForm(
                {
                  code: `MT_MATERIAL_SITE_CA`,
                },
                <Form
                  disabled={!canEdit}
                  dataSet={baseInfoDs}
                  columns={3}
                  labelLayout="horizontal"
                  labelWidth={112}
                >
                  <Lov name="siteIdObj" disabled={!isCreate} />
                  <Lov name="materialIdObj" disabled={!isCreate} />
                  <TextField name="materialName" disabled />
                  {/* <TextField name="businessSector" /> */}
                  <Select name="makeBuyCode" />
                  <Lov name="containerTypeObj" />
                  <TextField name="jitFlag" />
                  <NumberField name='minPackageQty'/>
                  {/* <NumberField name='moq'/> */}
                  {/* <NumberField name='perCompletedQty' /> */}
                  {/* <TextField name='receiptDate' /> */}
                  <TextField name='specialPurchaseType' />
                  <NumberField name='standardPackageQty'/>
                  <TextField name='receiptDate' />
                  <TextField name='aPointSupplier' />
                  <Switch name="poLineCreationFlag" />
                  {/* <Switch name="singleFlag" /> */}
                  <Switch name="highPriceFlag" />
                  <Switch name="coaApproveGlag" />
                  <Switch name="pickFlag" />
                  <Switch name="deliverGroup" />
                  <Switch name="receivedGroup" />
                  <Switch name="lotControlFlag" />
                  <Switch name="snpControlFlag" />
                  <Switch name="mmsEnableFlag" />
                  {/* <Switch name="wkcBoudingFlag" /> */}
                </Form>,
              )}
            </Panel>
            <Panel
              disabled={!canEdit}
              header={intl.get(`${modelPrompt}.inventory`).d('物料存储属性')}
              key="2"
              dataSet={baseInfoDs}
              // extra={renderAttributeDrawer('MT_PFEP_INVENTORY_CA')}
            >
              {customizeForm(
                {
                  code: `MT_PFEP_INVENTORY_CA`,
                },
                <Form
                  disabled={!canEdit}
                  dataSet={baseInfoDs}
                  columns={3}
                  labelLayout="horizontal"
                  labelWidth={112}
                >
                  {/*<NumberField name="minRemainShelfLife" />*/}
                  <NumberField name="shelfLife" />
                  <NumberField name="extendedShelfLife" />
                  <NumberField name="dullPeriod" />
                  <NumberField name="dullPeriodSecondary" />
                  <NumberField name="earlyWarningLeadTime" />
                  <Lov name="shelfLifeUomIdObj" />
                  <NumberField name="minStockQty" />
                  <NumberField name="maxStockQty" />
                  <NumberField name="minPackageQty" />
                  <NumberField name="packMinQty" />
                  <NumberField name="packageWeight" />
                  <Lov name="weightUomIdObj" />
                  <NumberField name="packageLength" />
                  <NumberField name="packageWidth" />
                  <NumberField name="packageHeight" />
                  <Lov name="packageSizeUomIdObj" />
                  <NumberField name="loadingArea" />
                  <NumberField name="loadingFloor" />
                  <TextField name="packageType" />
                  <NumberField name="singleBoxes" />
                  <TextField name='freezeDate' />
                  <Select name='mareqPackType' />
                  <Switch name="mpiEnableFlag" />
                  <Switch name="mixLotFlag" />
                  <Switch name="expirationDateFlag" />
                </Form>,
              )}
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.mrp`).d('MRP属性')}
              key="3"
              dataSet={baseInfoDs}
              // extra={renderAttributeDrawer('MT_MATERIAL_SITE_CA')}
            >
              <Form
                disabled={!canEdit}
                dataSet={baseInfoDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={112}
              >
                <Select name="mrpType" />
                <Select name="mrpController" />
                <Select name="batchProgram" />
                <NumberField name="maxBatch" />
                <NumberField name="minBatch" />
                <NumberField name="fixedBatch" />
                <NumberField name="safetyInventory" />
                <Lov name="receiveLocatorIdObj" />
                <NumberField name="seifmadeProductionTime" />
                <NumberField name="receiveProcessTime" />
                <NumberField name="procureAdvance" />
                <Switch name="longCycleFlag" />
              </Form>
            </Panel>
            <Panel header={intl.get(`${modelPrompt}.locator`).d('仓库存储属性')} key="4">
              <Table dataSet={orgListDs} columns={columns} />
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['tarzan.material-pfep-manager', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`MT_MATERIAL_SITE_CA`, `MT_PFEP_INVENTORY_CA`],
  })(detail),
);
