import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { getResponse } from '@utils/utils';

const modelPrompt = 'tarzan.model.org.enterprise';

const tenantId = getCurrentOrganizationId();

const EnterpriseDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  paging: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'enterpriseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterpriseCode`).d('企业编码'),
      min: 'dateFrom',
    },
    {
      name: 'enterpriseName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterpriseName`).d('企业名称'),
    },
    {
      name: 'enterpriseShortName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enterpriseShortName`).d('企业简称'),
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-enterprise/query/ui`,
        method: 'GET',
        transformResponse: val => {
          const data = JSON.parse(val);
          if (data.rows.content.length) {
            return data.rows.content[0];
          } 
          return null;
          
        },
      };
    },
    submit: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-enterprise/save/ui`,
        method: 'POST',
        data: data[0],
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
});

export { EnterpriseDS };
