import React, { useEffect, useMemo, useState } from 'react';
import { Table, DataSet, Button, Modal, Switch } from 'choerodon-ui/pro';
import { Badge } from 'hzero-ui';
import { observer } from 'mobx-react';
import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { TableQueryBarType, ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { HeaderDS, LineDS, HeadHistoryDS, LineHistoryDS } from './stores/tableDS';

import HistoryDrawer from './HistoryDrawer';

const modelPrompt = 'hmes.traceabilityDataTemplateMaintenance';
const tenantId = getCurrentOrganizationId();

const TraceabilityDataTemplateMaintenance = observer(() => {
  const lineDs = useMemo(() => new DataSet({ ...LineDS() }), []);
  const headerDs = useMemo(() => {
    const ds = new DataSet({ ...HeaderDS() });
    // 将lineDs挂载到headerDs上，以便在submit时访问
    (ds as any).lineDs = lineDs;
    return ds;
  }, [lineDs]);
  const headHistoryDs = useMemo(() => new DataSet({ ...HeadHistoryDS() }), []);
  const lineHistoryDs = useMemo(() => new DataSet({ ...LineHistoryDS() }), []);

  const [disabledFlag, setDisabledFlag] = useState(false);

  useEffect(() => {
    headerDs.addEventListener('load', handleHeaderLoad);
    return () => {
      headerDs.removeEventListener('load', handleHeaderLoad);
    };
  }, [headerDs]);

  useEffect(() => {
    headerDs.query();
  }, []);

  const handleHeaderLoad = () => {
    if (headerDs.current) {
      lineDs.setQueryParameter('headId', headerDs.current.get('headId'));
      lineDs.query().then(res => {
        if (res && res.success) {
          const processedData = res?.rows?.content?.map((item: any) => {
            const { newsType, ...resetData } = item;
            return { ...resetData, paraTypeObj: newsType };
          }) || [];
          lineDs.loadData(processedData);
        }
      });
    } else {
      lineDs.data = [];
    }
  };

  const handleRowClick = (record: any) => {
    return {
      onClick: () => {
        if (record) {
          // 设置当前选中的头记录
          headerDs.current = record;
          lineDs.setQueryParameter('headId', record.get('headId'));
          lineDs.query().then(res => {
            if (res && res.success) {
              const processedData = res?.rows?.content?.map((item: any) => {
                const { newsType, ...resetData } = item;
                return { ...resetData, paraTypeObj: newsType };
              }) || [];
              lineDs.loadData(processedData);
            }
          });
        }
      },
    };
  };

  const handleHistory = () => {
    const selectedRecords = headerDs.selected;
    if (selectedRecords.length === 0) {
      return;
    }

    const headId = selectedRecords[0].get('headId');
    if (!headId) {
      return;
    }

    // 设置查询参数并查询历史数据
    headHistoryDs.setQueryParameter('headId', headId);
    lineHistoryDs.setQueryParameter('headId', headId);
    headHistoryDs.query();
    lineHistoryDs.query();

    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history`).d('历史查询'),
      drawer: true,
      style: {
        width: 1200,
      },
      children: <HistoryDrawer headHistoryDs={headHistoryDs} lineHistoryDs={lineHistoryDs} />,
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      cancelButton: true,
    });
  };

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/HME.TRACE_TEMPLATE`,
      title: '导入',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  // 保存
  const handleSave = async () => {
    await headerDs.validate().then(headerValid => {
      lineDs.validate().then(lineValid => {
        if (headerValid && lineValid) {
          const headerRecord = headerDs.current;
          if (!headerRecord) {
            notification.warning({
              message: intl.get(`${modelPrompt}.error.check.head`).d('请先选择头数据'),
              placement: 'bottomRight',
            });
            return;
          }

          // 手动提取需要的字段，过滤掉ignore字段和空值
          const headerData: any = {};
          const headerFields = [
            'headId', 'siteId', 'siteCode', 'materialId', 'materialCode',
            'materialName', 'revisionCode', 'customerId', 'customerCode',
            'customerName', 'enableFlag', 'createdBy', 'creationDate',
          ];

          headerFields.forEach(field => {
            const value = headerRecord.get(field);
            if (value !== null && value !== undefined && value !== '') {
              headerData[field] = value;
            }
          });

          // 处理行数据，过滤ignore字段、空值并处理newsType
          const processedLineData = lineDs.records.map((record: any) => {
            // 注意：前端存储的是paraTypeObj，但后端需要的是newsType
            const paraTypeObj = record.get('paraTypeObj');
            let processedNewsType: string | null = null;

            if (Array.isArray(paraTypeObj) && paraTypeObj.length > 0) {
              processedNewsType = paraTypeObj.join(',');
            } else if (typeof paraTypeObj === 'string' && paraTypeObj.trim() !== '') {
              processedNewsType = paraTypeObj.trim();
            }

            const params: any = {};
            const lineFields = [
              'headId', 'paraType', 'paraName', 'paraValue', 'operationId',
              'operationName', 'operationDescription', 'tagId', 'tagCode',
              'tagDescription', 'upperLimitValue', 'lowerLimitValue', 'enableFlag',
            ];

            // 只传递有值的字段
            lineFields.forEach(field => {
              const value = record.get(field);
              if (value !== null && value !== undefined && value !== '') {
                params[field] = value;
              }
            });

            // 处理newsType，只有有值时才传递
            if (processedNewsType !== null) {
              params.newsType = processedNewsType;
            }

            // 如果不是新增记录，添加lineId
            if (record.get('status') !== 'add') {
              const lineId = record.get('lineId');
              if (lineId !== null && lineId !== undefined && lineId !== '') {
                params.lineId = lineId;
              }
            }

            return params;
          });

          const listData = [
            {
              ...headerData,
              lineList: processedLineData,
            },
          ];

          request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-trace-templates/save`, {
            method: 'POST',
            body: listData,
          })
            .then(res => {
              if (res && res.success !== false) {
                notification.success({
                  message: intl.get('tarzan.common.operation.success').d('操作成功'),
                });
                setDisabledFlag(false);
                headerDs.query();
                lineDs.loadData([]);
              } else {
                notification.error({
                  message: res?.message || intl.get('tarzan.common.operation.failed').d('操作失败'),
                });
              }
            })
            .catch(error => {
              notification.error({
                message: `保存失败: ${error?.message || '未知错误'}`,
              });
            });
        } else {
          notification.warning({
            message: intl.get(`${modelPrompt}.error.required`).d('请填写必填项'),
            placement: 'bottomRight',
          });
        }
      });
    });
  };

  // 头新建
  const handleHeaderCreate = () => {
    headerDs.create({}, 0);
    lineDs.loadData([]);
    setDisabledFlag(true);
  };

  // 编辑按钮
  const handleEdit = () => {
    headerDs.records.forEach(item => {
      item.setState('editing', true);
    });
    lineDs.records.forEach(item => {
      item.setState('editing', true);
    });
    setDisabledFlag(true);
  };

  // 取消按钮
  const handleCancel = () => {
    headerDs.records.forEach(item => {
      if (item.status === 'add') {
        headerDs.remove(item);
      } else {
        item.reset();
        item.setState('editing', false);
      }
    });
    lineDs.records.forEach(item => {
      if (item.status === 'add') {
        lineDs.remove(item);
      } else {
        item.reset();
        item.setState('editing', false);
      }
    });
    setDisabledFlag(false);
  };

  // 新建行数据
  const handleLineCreate = () => {
    if (headerDs.current) {
      lineDs.create({
        headId: headerDs.current.get('headId'),
      });
      lineDs.current?.setState('editing', true);
      // 进入编辑模式，显示保存按钮
      setDisabledFlag(true);
    } else {
      notification.warning({
        message: intl.get(`${modelPrompt}.error.check.head`).d('请先勾选头数据'),
        placement: 'bottomRight',
      });
    }
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        header: (
          <Button
            icon="add"
            onClick={() => handleHeaderCreate()}
            funcType={FuncType.flat}
            disabled={disabledFlag}
          />
        ),
        align: ColumnAlign.center,
        width: 60,
        lock: ColumnLock.left,
        renderer: ({ record }) => {
          if (!record) return null;
          return (
            <Button
              funcType={FuncType.flat}
              icon="remove"
              disabled={!disabledFlag}
              onClick={() => {
                if (record?.status === 'add') {
                  headerDs.remove(record);
                  lineDs.loadData([]);
                }
              }}
            />
          );
        },
      },
      { name: 'siteObj', editor: record => record.getState('editing') || record.status === 'add' },
      {
        name: 'materialObj',
        editor: record => record.getState('editing') || record.status === 'add',
      },
      {
        name: 'revisionCode',
        editor: record => record.getState('editing') || record.status === 'add',
      },
      { name: 'materialName' },
      {
        name: 'customerObj',
        editor: record => record.getState('editing') || record.status === 'add',
      },
      { name: 'customerName' },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        width: 100,
        editor: record =>
          record?.getState('editing') || record.status === 'add' ? <Switch /> : false,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('enableFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('enableFlag') === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      {
        name: 'createdByObj',
        editor: (record: any) => record.status === 'add',
      },
      { name: 'creationDate', editor: (record: any) => record.status === 'add' },
    ],
    [disabledFlag],
  );

  const lineColumns: ColumnProps[] = useMemo(
    () => [
      {
        header: (
          <Button
            icon="add"
            onClick={() => handleLineCreate()}
            funcType={FuncType.flat}
            disabled={!disabledFlag}
          />
        ),
        align: ColumnAlign.center,
        width: 60,
        lock: ColumnLock.left,
        renderer: ({ record }) => {
          if (!record) return null;
          return (
            <Button
              funcType={FuncType.flat}
              icon="remove"
              disabled={!disabledFlag}
              onClick={() => {
                if (record.status === 'add') {
                  lineDs.remove(record);
                } else {
                  // 如果有删除接口，在这里调用
                  // 目前没有删除接口，所以只能删除未保存的数据
                  notification.warning({
                    message: intl
                      .get(`${modelPrompt}.error.delete.saved`)
                      .d('已保存的数据无法删除'),
                  });
                }
              }}
            />
          );
        },
      },
      {
        name: 'paraTypeObj',
        editor: record => record?.getState('editing') || record.status === 'add',
      },
      {
        name: 'paraName',
        editor: record => record?.getState('editing') || record.status === 'add',
      },
      {
        name: 'paraValue',
        editor: record => record?.getState('editing') || record.status === 'add',
      },
      {
        name: 'operationObj',
        editor: record => record?.getState('editing') || record.status === 'add',
      },
      { name: 'operationDescription' },
      { name: 'tagObj', editor: record => record?.getState('editing') || record.status === 'add' },
      { name: 'tagDescription' },
      {
        name: 'upperLimitValue',
        editor: record => record?.getState('editing') || record.status === 'add',
      },
      {
        name: 'lowerLimitValue',
        editor: record => record?.getState('editing') || record.status === 'add',
      },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        width: 100,
        editor: record =>
          record?.getState('editing') || record.status === 'add' ? <Switch /> : false,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('enableFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('enableFlag') === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      { name: 'createdByObj', editor: (record: any) => record.status === 'add' },
      { name: 'creationDate', editor: (record: any) => record.status === 'add' },
    ],
    [disabledFlag],
  );

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('追溯数据模板维护')}>
        {!disabledFlag && (
          <>
            <Button
              onClick={handleEdit}
              style={{ marginRight: 15 }}
              icon="edit"
              color={ButtonColor.primary}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Button icon="history" onClick={handleHistory} disabled={!headerDs.selected.length}>
              {intl.get('tarzan.common.button.history').d('历史查询')}
            </Button>
            <Button icon="cloud_upload-o" onClick={handleImport}>
              {intl.get('tarzan.common.button.import').d('导入')}
            </Button>
          </>
        )}
        {disabledFlag && (
          <>
            <Button onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button
              onClick={handleSave}
              style={{ marginRight: 15 }}
              icon="save"
              color={ButtonColor.primary}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Table
          queryFieldsLimit={3}
          dataSet={headerDs}
          columns={columns}
          onRow={({ record }) => handleRowClick(record)}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
        />
        <Table
          queryFieldsLimit={3}
          dataSet={lineDs}
          columns={lineColumns}
          queryBar={TableQueryBarType.filterBar}
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['hmes.traceabilityDataTemplateMaintenance', 'tarzan.common'],
})(TraceabilityDataTemplateMaintenance);
