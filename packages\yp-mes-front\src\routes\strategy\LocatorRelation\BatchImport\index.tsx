/**
 * @feature 物料库位关系维护-批量导入入口页面
 * @date 2021-12-15
 * <AUTHOR>
 */
import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import { getCurrentSiteId } from '@utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { overrideTableBar } from '@components/tarzan-ui';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { batchDS } from '../stores/BatchDS';
import { CommonButton, CommonButtonProps } from './CommonButton';

const modelPrompt = 'tarzan.strategy.locatorRelation';

const BatchImport = ({ match: { path } }) => {
  const batchDs: DataSet = useMemo(() => new DataSet(batchDS()), []);
  const [deleteFlag, setDeleteFlag] = useState(true); // 批量导入页面“删除”按钮的禁用标识
  const [siteId] = useState(getCurrentSiteId());

  // 监听C7N pro列表选中操作
  useEffect(() => {
    function processDataSetListener(flag) {
      const handler = flag ? batchDs?.addEventListener : batchDs?.removeEventListener;
      // @ts-ignore
      handler.call(batchDs, 'select', handleDataSetSelectUpdate);
      handler.call(batchDs, 'unSelect', handleDataSetSelectUpdate);
      handler.call(batchDs, 'selectAll', handleDataSetSelectUpdate);
      handler.call(batchDs, 'unSelectAll', handleDataSetSelectUpdate);
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  // 处理选中条状态
  const handleDataSetSelectUpdate = () => {
    setDeleteFlag((batchDs.selected || []).length === 0);
  };

  const columns: ColumnProps[] = [
    { name: 'lineNumber', width: 100 },
    {
      name: 'status',
      align: ColumnAlign.center,
      renderer: ({ value }) => {
        return (
          (value === 'Y' && intl.get('tarzan.common.label.yes').d('是')) ||
          (value === 'N' && intl.get('tarzan.common.label.no').d('否'))
        );
      },
    },
    { name: 'importMsg', width: 250 },
    { name: 'siteCode', width: 200 },
    { name: 'materialCode', width: 200 },
    { name: 'locatorCode', width: 200 },
    { name: 'ownerType', width: 200 },
    { name: 'ownerCode', width: 200 },
  ];
  const commonButtonProps: CommonButtonProps = {
    dataSet: batchDs,
    path,
    siteId,
    deleteFlag,
    setDeleteFlag,
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.locator-relation`).d('物料库位关系维护')}
        backPath="/hmes/strategy/locator-relation/list"
      >
        <CommonButton {...commonButtonProps} />
      </Header>
      <Content>
        <Table
          dataSet={batchDs}
          columns={columns}
          queryFieldsLimit={3}
          queryBar={overrideTableBar}
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.strategy.locatorRelation', 'tarzan.common'],
})(BatchImport);
