import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, Row, Col, Form, TextField, Icon, Tabs } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import { useDataSet, useDataSetEvent } from 'utils/hooks';
import { isNil } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import listPageFactory from '../stores/listPageDs';
import { BASIC } from '@utils/config';
import InputLovDS from '../stores/inputLovDs';
import request from 'utils/request';
import LovModal from './lovModal';
import axios from 'axios'
import TabPane from 'choerodon-ui/lib/tabs/TabPane';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/es/table/enum';

interface ListPageProps extends RouteComponentProps {
    listDs: DataSet;
}
const lightMap = new Map([['GRAY', 'gray'], ['GREEN', 'green'], ['YELLOW', 'yellow']])
const modelPrompt = 'tarzan.inventory.marking';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {

    const inputLovDS = useDataSet(InputLovDS, 'inputLovDS');

    const [inputLovFlag, setInputLovFlag] = useState('');
    const [inputLovTitle, setInputLovTitle] = useState('');
    const [inputLovVisible, setInputLovVisible] = useState(false);

    const [isExport, setIsExport] = useState(true);
    const [panelTableColumns, setPanelTableColumns] = useState<ColumnProps[]>([]);

    const [markingCode, setMarkingCode] = useState()
    const [markings, setMarkings] = useState([])

    useDataSetEvent(listDs, 'load', () => {
        setColumn()
    });

    useEffect(() => {
        setColumn()
    }, [markingCode]);

    const setColumn = () => {
        const dataAll = listDs.toData();
        const titleList = dataAll.length > 0 ? dataAll[0].columnsList : [];
        const columnsList = dataAll.length > 0 ? dataAll[0].titleList : [];
        if (dataAll.length > 0 && titleList && titleList.length > 0) {
            const dyComlumsTemp: ColumnProps[] = [];
            titleList.forEach((item, index) => {
                dyComlumsTemp.push({
                    name: columnsList[index], renderer: ({ record }) => {
                        if (columnsList[index] === 'light') {
                            return record!.get(columnsList[index]) && <Icon type="brightness_1" style={{ color: lightMap.get(record!.get(columnsList[index])), fontSize: '20px' }} />
                        } else {
                            return record!.get(columnsList[index])
                        }
                    },
                    align: ColumnAlign.center,
                    onCell: ({ record }) => ({
                        style: {
                            background: record!.toData().markingList[index] ? 'yellow' : '',
                        },
                    }),
                    width: 190,
                });
                listDs.addField(columnsList[index], {
                    name: columnsList[index],
                    label: item,
                });
            });
            setPanelTableColumns(dyComlumsTemp);
        } else {
            setPanelTableColumns([
            ]);
        }
    }

    // 根据条码/marking获取对应的条件
    const getQueryParams = async () => {
        const { processBarcodeListStr, markingCodeListStr }: any = listDs?.queryDataSet?.toData()[0]
        if (processBarcodeListStr || markingCodeListStr) {
            if (processBarcodeListStr) {
                listDs?.queryDataSet?.current!.set('markingCodeList', []);
            }
            if (markingCodeListStr) {
                listDs?.queryDataSet?.current!.set('processBarcodeList', []);
            }
            const url = `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-marking-report/relate/query`
            const res: any = await axios.post(url, { processBarcodeList: processBarcodeListStr ? processBarcodeListStr.split(',') : [], markingCodeList: markingCodeListStr ? markingCodeListStr.split(',') : [], })
            if (res && res.success) {
                const { markingCodeList, processBarcodeList } = res.rows
                setMarkings(markingCodeList)
                listDs?.queryDataSet?.current!.set('processBarcodeList', processBarcodeList);
                listDs?.queryDataSet?.current!.set('markingCodeList', markingCodeList);
                if (processBarcodeListStr) {
                    listDs?.queryDataSet?.current!.set('markingCodeListStr', markingCodeList.join(','));
                }
                if (markingCodeListStr) {
                    listDs?.queryDataSet?.current!.set('processBarcodeListStr', processBarcodeList.join(','));
                }
                getQueryLevel(processBarcodeList)
            } else {
                notification.error({
                    message: res.message
                })
            }
        }
    }

    const getQueryLevel = async (processBarcodeList) => {
        const url = `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-marking-report/level/query`
        const res: any = await axios.post(url, { processBarcodeList, })
        if (res && res.success) {
            const { level, } = res.rows
            listDs?.queryDataSet?.current!.set('level', level);
        } else {
            // notification.error({
            //     message: res.message
            // })
        }
    }

    // 产品条码
    const onChangeBarCode = async (value) => {
        if (!value) {
            listDs?.queryDataSet?.current!.set('processBarcodeList', []);
            listDs?.queryDataSet?.current!.set('processBarcodeListStr', null);
        }
    };
    // marking
    const onChangeMarking = async (value) => {
        if (!value) {
            listDs?.queryDataSet?.current!.set('markingCodeList', []);
            listDs?.queryDataSet?.current!.set('markingCodeListStr', null);
        }
    }

    const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
        if (inputLovFlag === 'markingCodeListStr' || inputLovFlag === 'processBarcodeListStr') {
            listDs?.queryDataSet?.current!.set('processBarcodeList', []);
            listDs?.queryDataSet?.current!.set('processBarcodeListStr', null);
            listDs?.queryDataSet?.current!.set('markingCodeList', []);
            listDs?.queryDataSet?.current!.set('markingCodeListStr', null);
        }
        setInputLovFlag(inputLovFlag);
        setInputLovTitle(inputLovTitle);
        setInputLovVisible(inputLovVisible);
        if (inputLovVisible) {
            inputLovDS!.queryDataSet!.current!.getField('code')!.set('label', inputLovTitle);
        } else {
            inputLovDS!.queryDataSet!.current!.set('code', '');
            inputLovDS.data = [];
        }
        getQueryParams()
    };

    const handleSearch = async () => {
        const {
            processBarcodeListStr,
            markingCodeListStr,
        }: any = listDs?.queryDataSet?.toJSONData()[0];
        if (!processBarcodeListStr || !markingCodeListStr) {
            notification.error({
                message: intl.get(`${modelPrompt}.pleaseEnterQueryCriteria`).d('请输入查询条件'),
            });
            return;
        }
        const markingCodeList = markingCodeListStr.split(',')
        listDs?.queryDataSet?.current!.set('processBarcodeList', processBarcodeListStr.split(','));
        listDs?.queryDataSet?.current!.set('markingCodeList', markingCodeListStr.split(','));
        setMarkings(markingCodeList)
        listDs?.queryDataSet?.current!.set('markingCode', markingCode || markingCodeList.length > 0 ? markingCodeList[0] : null);
        listDs.query();
        setIsExport(false)
    };

    const lovModalProps = {
        inputLovDS,
        inputLovFlag,
        inputLovTitle,
        inputLovVisible,
        targetDS: listDs,
        onOpenInputModal,
    };

    const getExportQueryParams = () => {
        const queryParams = listDs?.queryDataSet?.current?.toData();
        Object.keys(queryParams).forEach(i => {
            if (isNil(queryParams[i])) {
                delete queryParams[i];
            }
        });
        const url = `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-marking-report/export/ui`;
        request(url, {
            method: 'POST',
            responseType: 'blob',
            body: { ...queryParams, traceLevel: null },
        }).then(res => {
            const file = new Blob([res], { type: 'application/vnd.ms-excel' }); // 解析文件
            const fileURL = URL.createObjectURL(file); // 生成文件地址
            const fileName = 'MARKING报表.xls';
            const elink = document.createElement('a'); // 生成a标签
            elink.download = fileName; // a标签下载名
            elink.style.display = 'none'; // 设置a标签隐藏，不在页面展示
            elink.href = fileURL; // 设置a标签下载地址
            document.body.appendChild(elink); // 在body中添加a标签
            elink.click();  // 手动调用a标签点击下载属性
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink); // 在body中删除a标签
        });
    };

    const onHandleTabs = (key) => {
        setMarkingCode(key)
        listDs.queryDataSet?.current?.set('markingCode', key)
        listDs.query()
        setIsExport(false)
    }

    return (
        <div className="hmes-style">
            <Header title={intl.get(`${modelPrompt}.title.marking`).d('MARKING报表')}>
                <Button onClick={getExportQueryParams}
                    disabled={isExport} color={ButtonColor.primary} >{intl.get(`${modelPrompt}.title.export`).d('导出')}</Button>
            </Header>
            <Content>
                <Row
                    gutter={24}
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                    }}
                >
                    <Col span={18}>
                        <Form columns={4} dataSet={listDs.queryDataSet} labelWidth={120}>
                            <TextField
                                name="processBarcodeListStr"
                                onChange={onChangeBarCode}
                                suffix={
                                    <div className="c7n-pro-select-suffix">
                                        <Icon
                                            type="search"
                                            onClick={() =>
                                                onOpenInputModal(true, 'processBarcodeListStr', '原材料条码')
                                            }
                                        />
                                    </div>
                                }
                            />
                            <TextField name="markingCodeListStr" onChange={onChangeMarking}
                                suffix={
                                    <div className="c7n-pro-select-suffix">
                                        <Icon
                                            type="search"
                                            onClick={() =>
                                                onOpenInputModal(true, 'markingCodeListStr', '标记')
                                            }
                                        />
                                    </div>
                                }
                            />
                            <TextField name="level" disabled />
                        </Form>
                    </Col>
                    <Col span={6}>
                        <div>
                            <Button
                                onClick={() => {
                                    listDs?.queryDataSet?.current?.reset();
                                    setIsExport(true)
                                }}
                            >
                                {intl.get('hzero.common.button.reset').d('重置')}
                            </Button>
                            <Button onClick={handleSearch} color={ButtonColor.primary}>
                                {intl.get('hzero.common.button.search').d('查询')}
                            </Button>
                        </div>
                    </Col>
                </Row>
                <Tabs defaultActiveKey="1" onChange={onHandleTabs}>
                    {markings.map(item => {
                        return <TabPane tab={item} key={item}>
                        </TabPane>
                    })}
                </Tabs>
                <Table
                    dataSet={listDs}
                    columns={panelTableColumns}
                    queryBar={TableQueryBarType.none}
                    key="marking"
                    queryBarProps={{
                        fuzzyQuery: false, // 是否开启模糊查询
                    }}
                    searchCode="marking" // 动态筛选条后端接口唯一编码
                    customizedCode="marking" // 个性化编码
                />
            </Content>
            <LovModal {...lovModalProps} />
        </div>
    );
};

const ListPage = withProps(
    () => {
        const listDs = listPageFactory();
        return {
            listDs,
        };
    },
    { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
    code: ['tarzan.ass.marking'],
})(ListPage);
