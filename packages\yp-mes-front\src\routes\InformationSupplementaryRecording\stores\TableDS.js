import { BASIC, Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/yp-mes-38510`;
const modelPrompt = 'tarzan.hmes.InformationSupplementaryRecording';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'lineNumber',
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'lineNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineNumber`).d('序号'),
      },
      {
        name: 'status',
        type: 'string',
        label: intl.get(`${modelPrompt}.status`).d('是否成功'),
      },
      {
        name: 'message',
        type: 'string',
        label: intl.get(`${modelPrompt}.message`).d('导入信息'),
      },
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'workcellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
      },
      {
        name: 'processBarcode',
        type: 'string',
        label: intl.get(`${modelPrompt}.processBarcode`).d('产品条码'),
      },
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('条码数量'),
      },
      {
        name: 'gbCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.gbCode`).d('国标码'),
      },
      {
        name: 'containerCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
      },
      {
        name: 'row',
        type: 'string',
        label: intl.get(`${modelPrompt}.row`).d('容器行'),
      },
      {
        name: 'column',
        type: 'string',
        label: intl.get(`${modelPrompt}.column`).d('容器列'),
      },
      {
        name: 'rawMaterialBarcode',
        type: 'string',
        label: intl.get(`${modelPrompt}.rawMaterialBarcode`).d('原材料条码数组'),
      },
      {
        name: 'assemblyPosition',
        type: 'string',
        label: intl.get(`${modelPrompt}.assemblyPosition`).d('电芯/模组装配位置'),
      },
      {
        name: 'inputQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.inputQty`).d('投入数量'),
      },
      {
        name: 'assemblyPoint',
        type:'string',
        label: intl.get(`${modelPrompt}.assemblyPoint`).d('装配点'),
      },
      {
        name: 'assemblyGroup',
        type:'string',
        label: intl.get(`${modelPrompt}.assemblyGroup`).d('装配组'),
      },
      {
        name: 'userName',
        type: 'string',
        label: intl.get(`${modelPrompt}.userName`).d('账号'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'wipStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.wipStatus`).d('在制品状态'),
      },
      {
        name: 'currentWorkcellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.currentWorkcellCode`).d('当前加工工位'),
      },
      {
        name: 'currentProcessCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.currentProcessCode`).d('当前加工工艺'),
      },
      {
        name: 'currentStepStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.currentStepStatus`).d('当前步骤状态'),
      },
      {
        name: 'qualityStatusMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatusMeaning`).d('质量状态'),
      },
      {
        name: 'splitQuantity',
        type: 'string',
        label: intl.get(`${modelPrompt}.splitQuantity`).d('分切数量'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('顶盖码'),
      },
    ],
    queryFields: [
      {
        name: 'processBarcodes',
        type: 'string',
        label: intl.get(`${modelPrompt}.processBarcodes`).d('产品条码'),
        // multiple: true,
      },
      {
        name: 'workCellLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.barCode`).d('当前加工工位'),
        lovCode: 'MT.MODEL.WORKCELL',
        ignore: 'always',
      },
      {
        name: 'currentWorkcellCode',
        type: 'string',
        bind: 'workCellLov.workcellName',
      },
      {
        name: 'operationLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.currentProcessCode`).d('当前加工工艺'),
        lovCode: 'MT.OPERATION',
        ignore: 'always',
      },
      {
        name: 'currentProcessCode',
        type: 'string',
        bind: 'operationLov.description',
      },
      {
        name: 'currentStepStatusCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.currentStepStatusCode`).d('当前步骤状态'),
        textField: 'description',
        valueField: 'statusCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=EO_STEP_STATUS`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
    ],
    transport: {
      read: ({data}) => {
        const temp = {
          ...data,
          processBarcodes: data.processBarcodes?.split(','),
        }
        return {
          url: `${Host}/v1/${tenantId}/hme-entry-exit-info/select/import/ui`,
          method: 'GET',
          data: temp,
          transformResponse: (val) => {
            const data = JSON.parse(val);
            return {
              ...data.rows,
            };
          },
        };
      },
    },
  };
};


export { tableDS };
