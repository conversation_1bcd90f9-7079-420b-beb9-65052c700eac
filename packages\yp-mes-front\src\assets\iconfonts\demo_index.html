<!DOCTYPE html lang="zh">
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://img.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1571835" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">居中对齐-05</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">居中对齐-04</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">拓展</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">更换背景颜色-19</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">更换背景颜色-18</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63a;</span>
                <div class="name">更换背景颜色-20</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f2;</span>
                <div class="name">左箭头</div>
                <div class="code-name">&amp;#xe7f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">左箭头</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f1;</span>
                <div class="name">左箭头</div>
                <div class="code-name">&amp;#xe7f1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">web__比特币右箭头</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">方向-右</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">方向-左</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">数据采集-01</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">退回</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">报废</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">合并</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dd;</span>
                <div class="name">恢复/重置</div>
                <div class="code-name">&amp;#xe6dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe675;</span>
                <div class="name">解锁</div>
                <div class="code-name">&amp;#xe675;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe676;</span>
                <div class="name">锁定</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74f;</span>
                <div class="name">回收</div>
                <div class="code-name">&amp;#xe74f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">分页</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">右 右</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">左 左</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">控制台</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">隐藏</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">隐藏</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe698;</span>
                <div class="name">结果管理</div>
                <div class="code-name">&amp;#xe698;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe708;</span>
                <div class="name">树形</div>
                <div class="code-name">&amp;#xe708;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe781;</span>
                <div class="name">plus-circle</div>
                <div class="code-name">&amp;#xe781;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d8;</span>
                <div class="name">验证</div>
                <div class="code-name">&amp;#xe6d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fa;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe6fa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ef;</span>
                <div class="name">upload</div>
                <div class="code-name">&amp;#xe7ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f0;</span>
                <div class="name">download</div>
                <div class="code-name">&amp;#xe7f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cc;</span>
                <div class="name">必填</div>
                <div class="code-name">&amp;#xe7cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66d;</span>
                <div class="name">租户报表分配</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont iconjushangduiqi"></span>
            <div class="name">
              居中对齐-05
            </div>
            <div class="code-name">.iconjushangduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjuzhongduiqi"></span>
            <div class="name">
              居中对齐-04
            </div>
            <div class="code-name">.iconjuzhongduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontuozhan"></span>
            <div class="name">
              拓展
            </div>
            <div class="code-name">.icontuozhan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondone-step-icon"></span>
            <div class="name">
              更换背景颜色-19
            </div>
            <div class="code-name">.icondone-step-icon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconentry-step-icon"></span>
            <div class="name">
              更换背景颜色-18
            </div>
            <div class="code-name">.iconentry-step-icon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconreturn-step-icon"></span>
            <div class="name">
              更换背景颜色-20
            </div>
            <div class="code-name">.iconreturn-step-icon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontop-arrow-icon"></span>
            <div class="name">
              左箭头
            </div>
            <div class="code-name">.icontop-arrow-icon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondanseshixintubiao-"></span>
            <div class="name">
              左箭头
            </div>
            <div class="code-name">.icondanseshixintubiao-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbottpm-arrow-icon"></span>
            <div class="name">
              左箭头
            </div>
            <div class="code-name">.iconbottpm-arrow-icon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconweb_youjiantoucomponent"></span>
            <div class="name">
              web__比特币右箭头
            </div>
            <div class="code-name">.iconweb_youjiantoucomponent
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconic_image"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.iconic_image
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyou"></span>
            <div class="name">
              方向-右
            </div>
            <div class="code-name">.iconyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzuo"></span>
            <div class="name">
              方向-左
            </div>
            <div class="code-name">.iconzuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshujucaiji-"></span>
            <div class="name">
              数据采集-01
            </div>
            <div class="code-name">.iconshujucaiji-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontuihui"></span>
            <div class="name">
              退回
            </div>
            <div class="code-name">.icontuihui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbaofei"></span>
            <div class="name">
              报废
            </div>
            <div class="code-name">.iconbaofei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhebing"></span>
            <div class="name">
              合并
            </div>
            <div class="code-name">.iconhebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-"></span>
            <div class="name">
              恢复/重置
            </div>
            <div class="code-name">.iconicon-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiesuo"></span>
            <div class="name">
              解锁
            </div>
            <div class="code-name">.iconjiesuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconsuoding"></span>
            <div class="name">
              锁定
            </div>
            <div class="code-name">.iconsuoding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhuishou"></span>
            <div class="name">
              回收
            </div>
            <div class="code-name">.iconhuishou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfenye"></span>
            <div class="name">
              分页
            </div>
            <div class="code-name">.iconfenye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyouyou-"></span>
            <div class="name">
              右 右
            </div>
            <div class="code-name">.iconyouyou-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzuozuo-"></span>
            <div class="name">
              左 左
            </div>
            <div class="code-name">.iconzuozuo-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconnormal"></span>
            <div class="name">
              控制台
            </div>
            <div class="code-name">.iconnormal
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyincang"></span>
            <div class="name">
              隐藏
            </div>
            <div class="code-name">.iconyincang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyincang1"></span>
            <div class="name">
              隐藏
            </div>
            <div class="code-name">.iconyincang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjieguoguanli"></span>
            <div class="name">
              结果管理
            </div>
            <div class="code-name">.iconjieguoguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshuxing"></span>
            <div class="name">
              树形
            </div>
            <div class="code-name">.iconshuxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconplus-circle"></span>
            <div class="name">
              plus-circle
            </div>
            <div class="code-name">.iconplus-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyanzheng"></span>
            <div class="name">
              验证
            </div>
            <div class="code-name">.iconyanzheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlianjie"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.iconlianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlianjie1"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.iconlianjie1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlianjie2"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.iconlianjie2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconupload"></span>
            <div class="name">
              upload
            </div>
            <div class="code-name">.iconupload
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondownload"></span>
            <div class="name">
              download
            </div>
            <div class="code-name">.icondownload
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbitian"></span>
            <div class="name">
              必填
            </div>
            <div class="code-name">.iconbitian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzuhubaobiaofenpei"></span>
            <div class="name">
              租户报表分配
            </div>
            <div class="code-name">.iconzuhubaobiaofenpei
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjushangduiqi"></use>
                </svg>
                <div class="name">居中对齐-05</div>
                <div class="code-name">#iconjushangduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjuzhongduiqi"></use>
                </svg>
                <div class="name">居中对齐-04</div>
                <div class="code-name">#iconjuzhongduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontuozhan"></use>
                </svg>
                <div class="name">拓展</div>
                <div class="code-name">#icontuozhan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondone-step-icon"></use>
                </svg>
                <div class="name">更换背景颜色-19</div>
                <div class="code-name">#icondone-step-icon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconentry-step-icon"></use>
                </svg>
                <div class="name">更换背景颜色-18</div>
                <div class="code-name">#iconentry-step-icon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconreturn-step-icon"></use>
                </svg>
                <div class="name">更换背景颜色-20</div>
                <div class="code-name">#iconreturn-step-icon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontop-arrow-icon"></use>
                </svg>
                <div class="name">左箭头</div>
                <div class="code-name">#icontop-arrow-icon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondanseshixintubiao-"></use>
                </svg>
                <div class="name">左箭头</div>
                <div class="code-name">#icondanseshixintubiao-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbottpm-arrow-icon"></use>
                </svg>
                <div class="name">左箭头</div>
                <div class="code-name">#iconbottpm-arrow-icon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconweb_youjiantoucomponent"></use>
                </svg>
                <div class="name">web__比特币右箭头</div>
                <div class="code-name">#iconweb_youjiantoucomponent</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconic_image"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#iconic_image</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyou"></use>
                </svg>
                <div class="name">方向-右</div>
                <div class="code-name">#iconyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzuo"></use>
                </svg>
                <div class="name">方向-左</div>
                <div class="code-name">#iconzuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshujucaiji-"></use>
                </svg>
                <div class="name">数据采集-01</div>
                <div class="code-name">#iconshujucaiji-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontuihui"></use>
                </svg>
                <div class="name">退回</div>
                <div class="code-name">#icontuihui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbaofei"></use>
                </svg>
                <div class="name">报废</div>
                <div class="code-name">#iconbaofei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhebing"></use>
                </svg>
                <div class="name">合并</div>
                <div class="code-name">#iconhebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-"></use>
                </svg>
                <div class="name">恢复/重置</div>
                <div class="code-name">#iconicon-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiesuo"></use>
                </svg>
                <div class="name">解锁</div>
                <div class="code-name">#iconjiesuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconsuoding"></use>
                </svg>
                <div class="name">锁定</div>
                <div class="code-name">#iconsuoding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhuishou"></use>
                </svg>
                <div class="name">回收</div>
                <div class="code-name">#iconhuishou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfenye"></use>
                </svg>
                <div class="name">分页</div>
                <div class="code-name">#iconfenye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyouyou-"></use>
                </svg>
                <div class="name">右 右</div>
                <div class="code-name">#iconyouyou-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzuozuo-"></use>
                </svg>
                <div class="name">左 左</div>
                <div class="code-name">#iconzuozuo-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconnormal"></use>
                </svg>
                <div class="name">控制台</div>
                <div class="code-name">#iconnormal</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyincang"></use>
                </svg>
                <div class="name">隐藏</div>
                <div class="code-name">#iconyincang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyincang1"></use>
                </svg>
                <div class="name">隐藏</div>
                <div class="code-name">#iconyincang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjieguoguanli"></use>
                </svg>
                <div class="name">结果管理</div>
                <div class="code-name">#iconjieguoguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshuxing"></use>
                </svg>
                <div class="name">树形</div>
                <div class="code-name">#iconshuxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconplus-circle"></use>
                </svg>
                <div class="name">plus-circle</div>
                <div class="code-name">#iconplus-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyanzheng"></use>
                </svg>
                <div class="name">验证</div>
                <div class="code-name">#iconyanzheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlianjie"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#iconlianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlianjie1"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#iconlianjie1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlianjie2"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#iconlianjie2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconupload"></use>
                </svg>
                <div class="name">upload</div>
                <div class="code-name">#iconupload</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondownload"></use>
                </svg>
                <div class="name">download</div>
                <div class="code-name">#icondownload</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbitian"></use>
                </svg>
                <div class="name">必填</div>
                <div class="code-name">#iconbitian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzuhubaobiaofenpei"></use>
                </svg>
                <div class="name">租户报表分配</div>
                <div class="code-name">#iconzuhubaobiaofenpei</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
