import React, { Component } from 'react';
import { connect } from 'dva';
import { Radio, Divider } from 'hzero-ui';
import intl from 'utils/intl';
import FixedValueForm from './RuleComponent/FixedValueForm';
import DateForm from './RuleComponent/DateForm';
import TimeForm from './RuleComponent/TimeForm';
import StandardObjectCode from './RuleComponent/StandardObjectCode';
import ExternalValueForm from './RuleComponent/ExternalValueForm';
import SerialNumberForm from './RuleComponent/SerialNumberForm';

const RadioGroup = Radio.Group;
const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';
/**
 * 表单数据展示
 * @extends {Component} - React.Component
 * @return React.element
 */

@connect(({ maintainNumber }) => ({
  maintainNumber,
}))
export default class RulesBlock extends Component {
  ruleComponent;

  constructor(props) {
    super(props);
    props.onRef(this);
  }

  //  正在使用的表单的ref
  onRefRuleComponent = (ref = {}) => {
    this.ruleComponent = (ref.props || {}).form;
  };

  //  根据选择的类型返回表单
  renderRuleFrom = numRule => {
    const {
      canEdit,
      dataSource,
      setUsingRuleDetail,
      rulesList = [],
      ruleId,
      focusInputKey,
    } = this.props;
    const initProps = {
      ruleId,
      focusInputKey,
      canEdit,
      dataSource,
      rulesList,
      setUsingRuleDetail,
      onRef: this.onRefRuleComponent,
    };
    switch (numRule) {
      case '1': //  固定输入值
        return <FixedValueForm key={focusInputKey} {...initProps} />;
      case '2': //  序列号段
        return <SerialNumberForm key={focusInputKey} {...initProps} />;
      case '3': //  日期格式
        return <DateForm key={focusInputKey} {...initProps} />;
      case '4': //  时间格式
        return <TimeForm key={focusInputKey} {...initProps} />;
      case '5': //  对象属性值
        return <StandardObjectCode key={focusInputKey} {...initProps} />;
      case '6': //  外部输入值
        return <ExternalValueForm key={focusInputKey} {...initProps} />;
      default:
        return null;
    }
  };

  onChange = e => {
    const {
      dataSource,
      setUsingRuleDetail,
      focusInputKey,
      dispatch,
      maintainNumber: { usingObjectColumnCodeArray = [] },
    } = this.props;
    let displayFlag;
    if (e.target.value === '5') {
      if (dataSource.displayFlag === 'N') {
        displayFlag = 'N';
      } else {
        displayFlag = 'Y';
      }
    }
    const usingRuleDetail = {
      numrangeRuleId: dataSource.numrangeRuleId || undefined,
      numRule: e.target.value,
      displayFlag,
      _backUpRule: dataSource._backUpRule,
    };
    usingObjectColumnCodeArray[focusInputKey] = null;
    dispatch({
      type: 'maintainNumber/updateState',
      payload: {
        usingObjectColumnCodeArray,
      },
    });
    setUsingRuleDetail(usingRuleDetail);
  };

  /**
   * render
   * @returns React.element
   */
  render() {
    const {
      dataSource = {},
      rulesList = [],
      canEdit,
      maintainNumber: { maintainNumberDetail = {}, userRole = 'N' },
    } = this.props;
    const { initialFlag = 'N' } = maintainNumberDetail;
    const { numRule = undefined } = dataSource;
    let alreadyUsedFlag = false;
    rulesList.forEach(item => {
      if (item && item.numRule && item.numRule === '2') {
        alreadyUsedFlag = true;
      }
    });
    const disabledFlag = alreadyUsedFlag && numRule !== '2';

    return (
      <>
        <RadioGroup
          disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
          onChange={this.onChange}
          value={numRule}
          style={{ marginLeft: '6%' }}
        >
          <Radio value="1">{intl.get(`${modelPrompt}.ono`).d('固定输入值')}</Radio>
          <Radio value="3">{intl.get(`${modelPrompt}.dateFormat`).d('日期格式')}</Radio>
          <Radio value="4">{intl.get(`${modelPrompt}.timeFormat`).d('时间格式')}</Radio>
          <Radio value="5">{intl.get(`${modelPrompt}.callObject`).d('对象属性值')}</Radio>
          <Radio value="6">{intl.get(`${modelPrompt}.outInput`).d('外部输入值')}</Radio>
          <Radio value="2" disabled={disabledFlag}>
            {intl.get(`${modelPrompt}.numSeg`).d('序列号段')}
          </Radio>
        </RadioGroup>
        <Divider style={{ marginBottom: 16 }} />
        {this.renderRuleFrom(numRule)}
      </>
    );
  }
}
