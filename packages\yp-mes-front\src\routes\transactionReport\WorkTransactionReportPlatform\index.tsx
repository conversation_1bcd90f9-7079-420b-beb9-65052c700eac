/**
 * @Description: 事务报表平台
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-25 18:09:22
 * @LastEditors: <<EMAIL>>
 */

import React, { useCallback, useMemo } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { isNil } from 'lodash';
import { useDataSetEvent } from 'utils/hooks';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from './stories';
import { lineTableDS } from './stories/lineTableDS';
import { TransDelete, TransBack } from './services';
import './index.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.event.materialTransIface';

// const prefix = '/mes-42638'
// const prefix = '/mes-42638'

// 手工回传按钮
// const EchoBtn = observer(({ ds }: { ds: DataSet }) => {
//   const selectedRows = ds.selected;

//   const { run: runTransBack, loading } = useRequest(TransBack(), { manual: true });

//   const handleEcho = () => {
//     const transBackList = selectedRows.map(item => {
//       return item.toData();
//     });
//     runTransBack({
//       params: transBackList,
//       onSuccess: () => {
//         const currentDate = new Date();
//         currentDate.setMinutes(currentDate.getMinutes() + 1);
//         ds!.queryDataSet!.current!.set('lastUpdateDateTo', currentDate);
//       },
//     });
//   };

//   return (
//     <Button
//       onClick={handleEcho}
//       loading={loading}
//       disabled={!selectedRows.length}
//     >
//       {intl.get(`${modelPrompt}.echoButton`).d('手工回传')}
//     </Button>
//   );
// });

const TransactionReportPlatform = observer(props => {
  console.log('ddd')
  const { tableDs, history } = props;
  const { run: transDelete, loading: transDeleteLoading } = useRequest(TransDelete(), {
    manual: true,
    needPromise: true,
  });
  const { run: transBack, loading: transBackLoading } = useRequest(TransBack(), {
    manual: true,
    needPromise: true,
  });
  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialId':
        record.set('revisionCode', null);
        break;
      case 'sourceOrderType':
        record.set('sourceOrderLov', null);
        break;
      case 'sourceOrderLov':
        record.set('sourceOrderLineLov', null);
        break;
      default:
        break;
    }
  });

  useDataSetEvent(tableDs, 'indexChange', ({ record }) => {
    if (record) {
      lineTableDs.setQueryParameter('woReportTransIfaceIds', record.get('woReportTransIfaceId'));
      lineTableDs.query();
    }
  });

  useDataSetEvent(tableDs, 'query', () => {
    lineTableDs.setQueryParameter('woReportTransIfaceIds', null);
    lineTableDs.loadData([]);
  });

  const lineTableDs = useMemo(() => new DataSet(lineTableDS()), []);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'woReportTransIfaceId',
        lock: ColumnLock.left,
        width: 120,
      },
      {
        name: 'transTypeCode',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'status',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'message',
        width: 150,
      },
      {
        name: 'transferTimes',
        width: 90,
      },
      {
        name: 'batchId',
        width: 150,
      },
      {
        name: 'transTime',
        width: 150,
      },
      {
        name: 'accountTime',
        width: 150,
      },
      {
        name: 'plantCode',
        width: 180,
      },
      {
        name: 'workOrderNum',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'revisionCode',
        width: 150,
      },
      {
        name: 'qty',
        width: 150,
      },
      {
        name: 'scrapQty',
        width: 150,
      },
      {
        name: 'reworkQty',
        width: 150,
      },
      {
        name: 'uomCode',
        width: 150,
      },
      {
        name: 'cancelRueck',
        width: 150,
      },
      {
        name: 'cancelRmzhl',
        width: 150,
      },
      {
        name: 'cancelFlag',
        width: 150,
      },
      {
        name: 'workCenter',
        width: 150,
      },
      {
        name: 'operationSeqNum',
        width: 150,
      },
      {
        name: 'operationStep',
        width: 150,
      },
      {
        name: 'sourceOperationSeqNum',
        width: 150,
      },
      {
        name: 'sourceOperationStep',
        width: 150,
      },
      {
        name: 'manualDuration',
        width: 150,
      },
      {
        name: 'manualDurationUomCode',
        width: 150,
      },
      {
        name: 'machineDuration',
        width: 150,
      },
      {
        name: 'machineDurationUomCode',
        width: 150,
      },
      {
        name: 'otherDuration',
        width: 150,
      },
      {
        name: 'otherDurationUomCode',
        width: 150,
      },
      {
        name: 'transAccount',
        width: 150,
      },
      {
        name: 'transReasonCode',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
      {
        name: 'rueck',
        width: 150,
      },
      {
        name: 'rmzhl',
        width: 150,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'createdByName',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 150,
      },
      {
        name: 'attribute1',
        width: 150,
      },
      {
        name: 'attribute2',
        width: 150,
      },
      {
        name: 'attribute3',
        width: 150,
      },
      {
        name: 'attribute4',
        width: 150,
      },
      {
        name: 'attribute5',
        width: 150,
      },
    ];
  }, []);

  const listTableColumns: ColumnProps[] = useMemo(() => {
    return [
      // {
      //   name: 'transTypeCode',
      //   lock: ColumnLock.left,
      //   width: 150,
      // },
      {
        name: 'status',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'message',
        width: 150,
      },
      // {
      //   name: 'woReportTransIfaceId',
      //   lock: ColumnLock.left,
      //   width: 120,
      // },
      {
        name: 'eventId',
        width: 120,
      },
      // {
      //   name: 'transCode',
      //   lock: ColumnLock.left,
      //   width: 150,
      // },
      // {
      //   name: 'sumFlagDesc',
      //   lock: ColumnLock.left,
      //   width: 150,
      // },
      {
        name: 'eventTypeCode',
        width: 120,
      },
      {
        name: 'eventTypeCodeDesc',
        width: 120,
      },
      // {
      //   name: 'businessTypeCodeDesc',
      //   width: 120,
      // },
      {
        name: 'transTime',
        width: 150,
      },
      {
        name: 'accountTime',
        width: 150,
      },
      {
        name: 'plantCode',
        width: 150,
      },
      // {
      //   name: 'siteCode',
      //   width: 150,
      // },
      {
        name: 'workOrderNum',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'revisionCode',
        width: 150,
      },
      {
        name: 'qty',
        width: 150,
      },
      {
        name: 'scrapQty',
        width: 150,
      },
      {
        name: 'reworkQty',
        width: 150,
      },
      {
        name: 'uomCode',
        width: 150,
      },
      {
        name: 'cancelRueck',
        width: 150,
      },
      {
        name: 'cancelRmzhl',
        width: 150,
      },
      {
        name: 'cancelFlag',
        width: 150,
      },
      {
        name: 'workCenter',
        width: 150,
      },
      {
        name: 'operationSeqNum',
        width: 150,
      },
      {
        name: 'operationStep',
        width: 150,
      },
      {
        name: 'sourceOperationSeqNum',
        width: 150,
      },
      {
        name: 'sourceOperationStep',
        width: 150,
      },
      {
        name: 'manualDuration',
        width: 150,
      },
      {
        name: 'manualDurationUomCode',
        width: 150,
      },
      {
        name: 'machineDuration',
        width: 150,
      },
      {
        name: 'machineDurationUomCode',
        width: 150,
      },
      {
        name: 'otherDuration',
        width: 150,
      },
      {
        name: 'otherDurationUomCode',
        width: 150,
      },
      {
        name: 'eventTime',
        width: 150,
      },
      {
        name: 'transAccount',
        width: 150,
      },
      {
        name: 'transReasonCode',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'createdByName',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 150,
      },
      {
        name: 'attribute1',
        width: 150,
      },
      {
        name: 'attribute2',
        width: 150,
      },
      {
        name: 'attribute3',
        width: 150,
      },
      {
        name: 'attribute4',
        width: 150,
      },
      {
        name: 'attribute5',
        width: 150,
      },
    ];
  }, []);

  const handleJumpToTransactionDetailReport = useCallback(() => {
    history.push(`/hmes/work-transaction-report/transaction-detail-report`);
  }, []);

  const handleJumpToMobileEventDetailReport = useCallback(() => {
    history.push(`/hmes/work-transaction-report/mobile-event-detail-report`);
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  const handleManualAbolition = () => {
    transDelete({
      params: tableDs.selected.map(item => item.data),
    }).then(res => {
      if (res && res.success) {
        const currentDate = new Date();
        currentDate.setMinutes(currentDate.getMinutes() + 1);
        tableDs!.queryDataSet!.current!.set('lastUpdateDateTo', currentDate);
        tableDs.query();
      }
    });
  };
  const handleManualReturn = () => {
    transBack({
      params: tableDs.selected.map(item => item.data),
    }).then(res => {
      if (res && res.success) {
        const currentDate = new Date();
        currentDate.setMinutes(currentDate.getMinutes() + 1);
        tableDs!.queryDataSet!.current!.set('lastUpdateDateTo', currentDate);
        tableDs.query();
      }
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('报工事务报表平台')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-wo-report-trans-ifaces/export/ui`}
          // requestUrl={`${prefix}/v1/${tenantId}/mt-wo-report-trans-ifaces/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        {/* <EchoBtn ds={tableDs} /> */}
        <Button
          loading={transDeleteLoading}
          disabled={
            !tableDs.selected.length || tableDs.selected.some(item => item.get('status') !== 'E')
          }
          onClick={handleManualAbolition}
        >
          {intl.get(`${modelPrompt}.manualAbolition`).d('手工废除')}
        </Button>
        <Button
          loading={transBackLoading}
          disabled={
            !tableDs.selected.length || tableDs.selected.some(item => item.get('status') === 'S')
          }
          onClick={handleManualReturn}
        >
          {intl.get(`${modelPrompt}.manualReturn`).d('手工回传')}
        </Button>
        <Button onClick={handleJumpToTransactionDetailReport}>
          {intl.get(`${modelPrompt}.jumpToTransactionDetailReport`).d('事务明细查看')}
        </Button>
        <Button onClick={handleJumpToMobileEventDetailReport}>
          {intl.get(`${modelPrompt}.jumpToMobileEventDetailReport`).d('移动事件明细查看')}
        </Button>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          style={{height: 400}}
          searchCode="TransactionReportPlatform"
          customizedCode="TransactionReportPlatform"
        />
        <Collapse bordered={false} defaultActiveKey={['transDetail']}>
          <Panel
            header={intl
              .get('tarzan.mes.event.materialTransIface.title.transDetail')
              .d('事务明细信息')}
            key="transDetail"
          >
            <Table style={{height: 400}} dataSet={lineTableDs} columns={listTableColumns} />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.mes.event.materialTransIface', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(TransactionReportPlatform),
);
