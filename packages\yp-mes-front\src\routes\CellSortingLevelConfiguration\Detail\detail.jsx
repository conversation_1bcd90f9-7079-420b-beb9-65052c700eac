import React, { forwardRef, useEffect, useImperativeHandle, useMemo } from 'react';
import { Form, Lov, Select, Switch, TextField, DataSet, TextArea } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import NumberComponent from '../components/NumberComponent';
import { baseInfoDS, numberListDS } from '../stories/BaseInfoDs';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.cellSortingLevelConfiguration';

const Detail = (props, ref) => {
  const { canEdit, id, type } = props;
  useEffect(() => {
    if (id !== 'create') {
      detailQuery(id);
    }
  }, [id, type]);
  const baseInfoDs = useMemo(
    () =>
      new DataSet({
        ...baseInfoDS(),
      }),
    [],
  );
  const capacityDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  const voltageDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  const dcrDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  const acrDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  const k1Ds = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  const k2Ds = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  const voltageFormationEndDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  const capacityFormationEndDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  const chargeCapacitySupplementDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );

  const detailQuery = paramsId => {
    baseInfoDs.setQueryParameter('levelConfigId', paramsId);
    baseInfoDs.query().then(res => {
      if (res.capacity && res.capacity.length) {
        capacityDs.loadData(res.capacity);
      }
      if (res.voltage && res.voltage.length) {
        voltageDs.loadData(res.voltage);
      }
      if (res.dcr && res.dcr.length) {
        dcrDs.loadData(res.dcr);
      }
      if (res.acr && res.acr.length) {
        acrDs.loadData(res.acr);
      }
      if (res.k1 && res.k1.length) {
        k1Ds.loadData(res.k1);
      }
      if (res.k2 && res.k2.length) {
        k2Ds.loadData(res.k2);
      }
      if (res.voltageFormationEnd && res.voltageFormationEnd.length) {
        voltageFormationEndDs.loadData(res.voltageFormationEnd);
      }
      if (res.capacityFormationEnd && res.capacityFormationEnd.length) {
        capacityFormationEndDs.loadData(res.capacityFormationEnd);
      }
      if (res.chargeCapacitySupplement && res.chargeCapacitySupplement.length) {
        chargeCapacitySupplementDs.loadData(res.chargeCapacitySupplement);
      }
    });
  };

  const validateGrading = () => {
    const v1 = capacityDs.toData()[0]?.multipleValue
    const v2 = voltageDs.toData()[0]?.multipleValue
    const v3 = dcrDs.toData()[0]?.multipleValue
    const v4 = acrDs.toData()[0]?.multipleValue
    const v5 = k1Ds.toData()[0]?.multipleValue
    const v6 = k2Ds.toData()[0]?.multipleValue
    const v7 = voltageFormationEndDs.toData()[0]?.multipleValue
    const v8 = capacityFormationEndDs.toData()[0]?.multipleValue
    const v9 = chargeCapacitySupplementDs.toData()[0]?.multipleValue
    if (!v1 || isNaN(v1.leftValue) || isNaN(v1.rightValue)) {
      notification.error({ message: intl.get(`${modelPrompt}.v1`).d('请输入容量上下限') })
      return true
    } else if (!v2 || isNaN(v2.leftValue) || isNaN(v2.rightValue)) {
      notification.error({ message: intl.get(`${modelPrompt}.v2`).d('请输入电压上下限') })
      return true
    } else if (!v3 || isNaN(v3.leftValue) || isNaN(v3.rightValue)) {
      notification.error({ message: intl.get(`${modelPrompt}.v3`).d('请输入DCR上下限') })
      return true
    } else if (!v4 || isNaN(v4.leftValue) || isNaN(v4.rightValue)) {
      notification.error({ message: intl.get(`${modelPrompt}.v4`).d('请输入ACR上下限') })
      return true
    } else if (!v5 || isNaN(v5.leftValue) || isNaN(v5.rightValue)) {
      notification.error({ message: intl.get(`${modelPrompt}.v5`).d('请输入K1值上下限') })
      return true
    } else if (!v6 || isNaN(v6.leftValue) || isNaN(v6.rightValue)) {
      notification.error({ message: intl.get(`${modelPrompt}.v6`).d('请输入K2值上下限') })
      return true
    } else if (!v7 || isNaN(v7.leftValue) || isNaN(v7.rightValue)) {
      notification.error({ message: intl.get(`${modelPrompt}.v7`).d('请输入化成结束电压上下限') })
      return true
    } else if (!v8 || isNaN(v8.leftValue) || isNaN(v8.rightValue)) {
      notification.error({ message: intl.get(`${modelPrompt}.v8`).d('请输入化成结束容量上下限') })
      return true
    } else if (!v9 || isNaN(v9.leftValue) || isNaN(v9.rightValue)) {
      notification.error({ message: intl.get(`${modelPrompt}.v9`).d('请输入补电容量上下限') })
      return true
    } else {
      return false
    }
  }

  useImperativeHandle(ref, () => ({
    detailQuery,
    // 暴露给父组件的方法
    submit: async () => {
      const validate = await baseInfoDs.validate();
      let success = false;
      if (validate) {
        let levelConfigId = '';
        const flag = validateGrading()
        if (flag) return true
        const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-specified-level-configs/createAndUpdate`;
        const res = await request(url, {
          method: 'POST',
          body: {
            ...baseInfoDs.toData()[0],
            capacity: capacityDs.toData(),
            voltage: voltageDs.toData(),
            dcr: dcrDs.toData(),
            acr: acrDs.toData(),
            k1: k1Ds.toData(),
            k2: k2Ds.toData(),
            voltageFormationEnd: voltageFormationEndDs.toData(),
            capacityFormationEnd: capacityFormationEndDs.toData(),
            chargeCapacitySupplement: chargeCapacitySupplementDs.toData(),
          },
        });
        const result = getResponse(res);
        if (result && result.levelConfigId) {
          notification.success({});
          success = true;
          levelConfigId = result.levelConfigId;
        } else {
          notification.error({
            message: result.message,
          });
        }
        return { success, levelConfigId };
      }
      return { success };
    },
    reset: () => {
      detailQuery(id);
    },
  }));

  return (
    <>
      <Collapse bordered={false} defaultActiveKey={['basicInfo', 'parameter']}>
        <Panel
          header={intl.get(`${modelPrompt}.baseInfo`).d('基础信息')}
          key="basicInfo"
          dataSet={baseInfoDs}
        >
          <Form disabled={!canEdit} dataSet={baseInfoDs} columns={3} labelLayout="horizontal">
            {/* 站点和物料不可编辑 */}
            <Lov name="siteLov" disabled={id !== 'create'} />
            <TextField readOnly name="siteName" />
            <Lov name="materialLov" disabled={id !== 'create'} />
            <TextField readOnly name="materialName" />
            <Select name="levelType" />
            <TextField name="levelCode" />
            <Switch name="enableFlag" />
            <TextArea name="remark" newLine colSpan={3} />
          </Form>
          ,
        </Panel>

        <Panel
          header={intl.get(`${modelPrompt}.gradingParameters`).d('分档参数维护')}
          key="parameter"
          dataSet={baseInfoDs}
        >
          <Form
            disabled={!canEdit}
            dataSet={baseInfoDs}
            columns={3}
            labelLayout="horizontal"
            labelWidth={112}
          >
            <NumberComponent
              showStandard
              name="capacity"
              parentDs={baseInfoDs}
              dataSet={capacityDs}
              // disabled={trueValueDisabled}
              canEdit={canEdit}
            />
            <NumberComponent
              name="voltage"
              parentDs={baseInfoDs}
              dataSet={voltageDs}
              // disabled={falseValueDisabled}
              canEdit={canEdit}
            />
            <NumberComponent
              name="dcr"
              parentDs={baseInfoDs}
              dataSet={dcrDs}
              // disabled={falseValueDisabled}
              canEdit={canEdit}
            />
            <NumberComponent
              name="acr"
              parentDs={baseInfoDs}
              dataSet={acrDs}
              // disabled={falseValueDisabled}
              canEdit={canEdit}
            />
            <NumberComponent
              name="k1"
              parentDs={baseInfoDs}
              dataSet={k1Ds}
              // disabled={falseValueDisabled}、
              canEdit={canEdit}
            />
            <NumberComponent
              name="k2"
              parentDs={baseInfoDs}
              dataSet={k2Ds}
              // disabled={falseValueDisabled}
              canEdit={canEdit}
            />
            <NumberComponent
              name="voltageFormationEnd"
              parentDs={baseInfoDs}
              dataSet={voltageFormationEndDs}
              canEdit={canEdit}
            />
            <NumberComponent
              name="capacityFormationEnd"
              parentDs={baseInfoDs}
              dataSet={capacityFormationEndDs}
              canEdit={canEdit}
            />
            <NumberComponent
              name="chargeCapacitySupplement"
              parentDs={baseInfoDs}
              dataSet={chargeCapacitySupplementDs}
              canEdit={canEdit}
            />
          </Form>
        </Panel>
      </Collapse>
    </>
  );
};

export default forwardRef(Detail);
