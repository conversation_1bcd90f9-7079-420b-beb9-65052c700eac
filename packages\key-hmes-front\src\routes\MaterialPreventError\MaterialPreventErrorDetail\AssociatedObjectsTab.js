/**
 * @Description: 物料防呆防错配置-关联对象
 * @Author: <EMAIL>
 * @Date: 2023-03-14 10:19:56
 */

import React, { useMemo } from 'react';
import { Table, Button, Modal, DataSet } from 'choerodon-ui/pro';
import { Tag, Popconfirm } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import AssociatedObjectsDrawer from './AssociatedObjectsDrawer';
import { singleAssObjectDS } from '../stores/MaterialPreventErrorDS';

const modelPrompt = 'tarzan.hmes.materialPreventError';

const AssociatedObjectsTab = props => {
  const { canEdit, assObjectsDs, tableHeight, detailDs } = props;
  const singleAssObjectDs = useMemo(() => new DataSet(singleAssObjectDS()), []);

  let _dataItemDrawer;
  const typeName = {
    MATERIAL: intl.get(`${modelPrompt}.MATERIAL`).d('成品物料'),
    MATERIAL_CATEGORY: intl.get(`${modelPrompt}.MATERIAL_CATEGORY`).d('成品物料类别'),
    COM_MATERIAL: intl.get(`${modelPrompt}.COM_MATERIAL`).d('组件物料'),
    COM_MATERIAL_CATEGORY: intl.get(`${modelPrompt}.COM_MATERIAL_CATEGORY`).d('组件物料类别'),
    OPERATION: intl.get(`${modelPrompt}.OPERATION`).d('工艺'),
    AREA: intl.get(`${modelPrompt}.AREA`).d('区域'),
    PROD_LINE: intl.get(`${modelPrompt}.PROD_LINE`).d('产线'),
    WORKCELL: intl.get(`${modelPrompt}.WORKCELL`).d('工作单元'),
  };

  const associatedObjectsDrawerClose = record => {
    if (!record) {
      assObjectsDs.remove(assObjectsDs.current);
    }
    _dataItemDrawer.close();
  };

  const associatedObjectsDrawerSubmit = async record => {
    const validate = await singleAssObjectDs.validate(false, true);
    if (!validate) {
      return;
    }
    if (singleAssObjectDs.toData().length === 0) {
      _dataItemDrawer.close();
      assObjectsDs.remove(assObjectsDs.current);
      return;
    }
    if (!record.get('lineNumber')) {
      const newLineNumber =
        (assObjectsDs.toData().sort((a, b) => b.lineNumber - a.lineNumber)[0].lineNumber || 0) + 1;
      record.set('lineNumber', newLineNumber);
    }
    record.set('objectShowList', singleAssObjectDs.toData());
    _dataItemDrawer.close();
  };

  const associatedObjectsModify = record => {
    if (!record) {
      assObjectsDs.create({ tagGroupObjectId: -Date.parse(new Date()) });
    }
    _dataItemDrawer = Modal.open({
      key: Modal.key(),
      title: record
        ? intl.get(`${modelPrompt}.associatedObjectsEdit`).d('关联对象组合编辑')
        : intl.get(`${modelPrompt}.associatedObjectsCreate`).d('新建关联对象组合'),
      drawer: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <AssociatedObjectsDrawer
          singleAssObjectDs={singleAssObjectDs}
          canEdit={canEdit}
          record={record || assObjectsDs.current}
          detailDs={detailDs}
        // 用get方法取，他是ObservableArrayAdministration，还需要spile
        />
      ),
      footer: !canEdit ? (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              associatedObjectsDrawerClose(record);
            }}
          >
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        </div>
      ) : (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              associatedObjectsDrawerClose(record);
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            type="submit"
            onClick={() => {
              associatedObjectsDrawerSubmit(record || assObjectsDs.current);
            }}
            color={ButtonColor.primary}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!canEdit}
          onClick={() => associatedObjectsModify()}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => {
            assObjectsDs.remove(record);
          }}
        >
          <Button disabled={!canEdit} funcType="flat" icon="remove" shape="circle" size="small" />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'lineNumber',
      align: 'left',
      width: 120,
      renderer: ({ value, record }) => (
        <a
          style={{ minWidth: '100%', display: 'inline-block' }}
          onClick={() => {
            associatedObjectsModify(record);
          }}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'objectShowList',
      renderer: ({ record }) => {
        return (record.get('objectShowList') || []).length && (record.get('objectShowList') || []).map(item => (
          <Tag key={item.objectId} color="blue">
            {`${typeName[item.objectType]}：${item.objectDesc ? item.objectDesc : item.objectCode}`}
          </Tag>
        ));
      },
    },
  ];

  return <Table dataSet={assObjectsDs} columns={columns} virtual style={{ height: tableHeight }} />;
};

export default AssociatedObjectsTab;
