/**
 * @Description: 盘点工作台-详情页DS
 * @Author: <<EMAIL>>
 * @Date: 2022-02-10 20:13:45
 * @LastEditTime: 2022-03-11 10:39:28
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  fields: [
    {
      name: 'stocktakeNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeNum`).d('盘点单编码'),
      disabled: true,
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeIdentification`).d('盘点单标识'),
      disabled: true,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('盘点单说明'),
    },
    {
      name: 'openFlag',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.openFlag`).d('盘点类型'),
      lookupCode: 'MT.MES.STOCKTAKE_TYPE',
      required: true,
    },
    {
      name: 'stocktakeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeStatus`).d('状态'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=STOCKTAKE_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('stocktakeId');
        },
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('stocktakeId');
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'areaLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库'),
      lovCode: 'WMS.CONTAINER_REGISTER_LOCATOR',
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            // locatorCategories: 'AREA',
          };
        },
        disabled: ({ record }) => {
          return (
            record.get('stocktakeId') ||
            !record.get('siteId') ||
            (record.get('locatorIds') && record.get('locatorIds').length)
          );
        },
      },
    },
    {
      name: 'areaLocatorId',
      bind: 'areaLocatorLov.locatorId',
    },
    {
      name: 'areaLocatorCode',
      bind: 'areaLocatorLov.locatorCode',
    },
    // 用于Lov按钮
    {
      // 新增库位
      name: 'tableLocatorRangeLov',
      type: FieldType.object,
      multiple: true,
      lovCode: 'MT.MODEL.LOCATOR',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategories: 'INVENTORY',
            areaLocatorId: record.get('areaLocatorId') || null,
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'locatorIds',
      bind: 'tableLocatorRangeLov.locatorId',
    },
    {
      // 新增物料
      name: 'tableMaterialRangeLov',
      type: FieldType.object,
      multiple: true,
      lovCode: 'MT.METHOD.MATERIAL',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            (!record.get('areaLocatorId') && !record.get('locatorIds').length)
          );
        },
      },
    },
    {
      name: 'materialIds',
      bind: 'tableMaterialRangeLov.materialId',
    },
  ],
});

const locatorRangeTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'locatorTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorTypeDesc`).d('库位类型'),
    },
  ],
});

const materialRangeTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'locatorId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'userLov',
      type: FieldType.object,
      required: true,
      lovCode: "HME.INVENTORY_USER",
      // lovPara: { tenantId },
      textField: 'realName',
      valueField: 'id',
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.userLov`).d('盘点人'),
      dynamicProps: {
        lovPara: ({ record }) => {
          console.log(record)
          return {
            tenantId,
            areaLocatorId: record?.get('locatorId'),
          };
        },
      },
    },
    {
      name: 'userId',
      bind: 'userLov.id',
    },{
      name: 'realName',
      bind: 'userLov.realName',
    },
  ],
});

const stocktakeDetailTableDS: () => DataSetProps = () => ({
  // selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  dataKey: 'rows',
  forceValidate: true,
  queryFields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
  ],
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'sumQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumQuantity`).d('账面数量'),
    },
    {
      name: 'adjustQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.adjustQty`).d('调整数量'),
    },
    {
      name: 'firstCountQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.firstCountQty`).d('盘点数量'),
    },
    {
      name: 'firstCountDiffQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.firstCountDiffQty`).d('盘点差异'),
    },
    {
      name: 'reCountQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleCountQty`).d('抽盘数量'),
    },
    {
      name: 'reCountDiffQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleCountDiffQty`).d('抽盘差异'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位编码'),
    },
    {
      name: 'contrastReason',
      type: FieldType.string,
      required: true,
      lookupCode: 'WMS.STOCKTAKE_CONTRAST_REASON',
      label: intl.get(`${modelPrompt}.contrastReason`).d('差异原因'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const newData = {
        ...data,
        locatorRangeIds:
          data.locatorRangeIds && data.locatorRangeIds.length
            ? (data.locatorRangeIds || []).join(',')
            : null,
        materialRangeIds:
          data.materialRangeIds && data.materialRangeIds.length
            ? (data.materialRangeIds || []).join(',')
            : null,
      };
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/details/ui`,
        method: 'POST',
        body: newData,
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && !datas.success) {
            notification.error({ message: datas.message });
            return;
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
});

const stocktakeBarcodeDetailDS: () => DataSetProps = () => ({
  // selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次编码'),
    },
    {
      name: 'diffFlag',
      type: FieldType.string,
      lookupCode: 'MT.YES_NO',
      label: intl.get(`${modelPrompt}.diffFlag`).d('是否有差异'),
    },
    {
      name: 'adjustFlag',
      type: FieldType.string,
      lookupCode: 'MT.YES_NO',
      label: intl.get(`${modelPrompt}.adjustFlag`).d('是否已调整'),
    },
    {
      name: 'materialIds',
      labelWidth: '20',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      textField: 'materialCode',
      valueField: 'materialId',
      type: FieldType.string,
      multiple: true,
    },
    {
      name: 'locatorIds',
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      textField: 'locatorCode',
      valueField: 'locatorId',
      type: FieldType.string,
      multiple: true,
    },
  ],
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'diffFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.diffFlag`).d('是否有差异'),
    },
    {
      name: 'adjustFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.adjustFlag`).d('是否已调整'),
    },
    {
      name: 'currentQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.currentQuantity`).d('账面数量'),
    },
    {
      name: 'firstCountQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.firstCountQty`).d('盘点数量'),
    },
    {
      name: 'firstCountDiffQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.firstCountDiffQty`).d('盘点差异'),
    },
    {
      name: 'reCountQty',
      type: FieldType.number,
      required: true,
      min: 0,
      label: intl.get(`${modelPrompt}sampleeCountQty`).d('抽盘数量'),
    },
    {
      name: 'reCountDiffQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sampleCountDiffQty`).d('抽盘差异'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位编码'),
    },
    {
      name: 'firstLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstLocatorCode`).d('盘点货位编码'),
    },
    {
      name: 'firstLocatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstLocatorName`).d('盘点货位描述'),
    },
    {
      name: 'reLocatorCodeLov',
      type: FieldType.object,
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      required: true,
      textField: 'locatorCode',
      valueField: 'locatorId',
      label: intl.get(`${modelPrompt}.sampleLocatorCode`).d('抽盘货位编码'),
    },
    {
      name: 'reLocatorCode',
      bind: 'reLocatorCodeLov.locatorCode',
    },
    {
      name: 'reLocatorId',
      bind: 'reLocatorCodeLov.locatorId',
    },
    {
      name: 'reLocatorName',
      type: FieldType.string,
      bind: 'reLocatorCodeLov.locatorName',
      label: intl.get(`${modelPrompt}.sampleLocatorName`).d('抽盘货位描述'),
    },
    {
      name: 'firstCountRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstCountRemark`).d('盘点备注'),
    },
    {
      name: 'firstCountByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.firstCountByName`).d('盘点人'),
    },
    {
      name: 'firstCountDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.firstCountDate`).d('盘点时间'),
    },
    {
      name: 'reCountRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleCountRemark`).d('抽盘备注'),
    },
    {
      name: 'reCountByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sampleCountByName`).d('抽盘人'),
    },
    {
      name: 'reCountDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.sampleCountDate`).d('抽盘时间'),
    },
    {
      name: 'adjustQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.adjustQty`).d('调整数量'),
    },
    {
      name: 'adjustCountByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.adjustCountByName`).d('调整人'),
    },
  ],
  record: {
    dynamicProps: {
      // 有差异的才可以选择
      selectable: record => record.get('diffFlag') === 'Y' && record.get('adjustFlag') === 'N',
    },
  },
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/bar-code/details/ui`,
        method: 'POST',
      };
    },
  },
});

export {
  detailDS,
  locatorRangeTableDS,
  materialRangeTableDS,
  stocktakeDetailTableDS,
  stocktakeBarcodeDetailDS,
};
