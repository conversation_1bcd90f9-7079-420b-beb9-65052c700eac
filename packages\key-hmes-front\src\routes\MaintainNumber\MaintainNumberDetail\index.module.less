// 主要颜色 用于导航栏按钮 或者 其他需要强调的 文字 按钮 操作等
@hzero-primary-color: #1e3255;

// 次级主要颜色 用于页面内部的主要按钮等
@hzero-primary-color-2: #29bece;

.rightDiv {
  flex: 1 1;
  .ruleInput {
    width: calc(20% - 12px); //  四分之一比例
    margin: 0 6px;

    :global {
      .ant-input-suffix {
        right: 8px;
      }
    }
  }

  .ruleInput > input,
  i {
    font-size: 12px;

    &:hover {
      cursor: pointer;
    }
  }

  .ruleInput:nth-child(1) {
    margin-left: 0;
  }

  .ruleInput:last-child {
    margin-right: 0;
  }

  .focusRuleInput > input {
    border-color: @hzero-primary-color-2;
    box-shadow: 0 0 3px @hzero-primary-color-2;
    transition: border-color 1s;
  }

  .ruleDemo {
    float: left;
    margin-top: 6px;
    width: 40%;
  }

  .ruleDemoLength {
    float: left;
    margin: 0 4px 0;
  }
}
