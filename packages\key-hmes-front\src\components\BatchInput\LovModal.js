/**
 * @Description: 中盒码打印模板
 * @author: ZCL<<EMAIL>>
 * @date: 2021-05-10 10:59:52
 * @version: 0.0.1
 * @copyright: Copyright (c) 2021, Hand
 */

import React, { Component } from 'react';
import { Modal as ModalUI } from 'choerodon-ui';
import { Table, Row, Col, Form, TextField, Button } from 'choerodon-ui/pro';
import { isEmpty } from 'lodash';

export default class LovModal extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  // 输入选择弹窗确定
  handleToOkInputLov = () => {
    const { inputLovDS, targetDS, inputLovFlag, onOpenInputModal } = this.props;
    if (targetDS && inputLovFlag) {
      let data = inputLovDS.selected;
      if (data.length < 1) {
        data = inputLovDS.records;
      }
      let str = '';
      for (let i = 0; i < data.length; i++) {
        if (i !== data.length - 1) {
          str += `${data[i].get('code')},`;
        } else {
          str += data[i].get('code');
        }
      }
      targetDS.queryDataSet.current.set(inputLovFlag, str);
      onOpenInputModal(false, '', '');
    }
  };

  handleToChangeInputLov = (value) => {
    if (!isEmpty(value)) {
      const data = value.split(/[\s\n]/).filter(item => item !== '');
      this.props.inputLovDS.data = (data || []).map((item) => ({
        code: item,
      }));
    }
  };

  // 选择弹窗的查询列
  renderInputLovBar = () => {
    const { inputLovDS } = this.props;
    const { queryDataSet } = inputLovDS;
    return (
      <>
        <div
          style={{
            display: 'flex',
            marginBottom: '0.1rem',
            alignItems: 'center',
            marginTop: '0.1rem',
          }}
        >
          <div >
            <Row>
              <Col span={24}>
                <Form dataSet={queryDataSet} labelWidth={100}>
                  <TextField name="code" onChange={this.handleToChangeInputLov} />
                </Form>
              </Col>
            </Row>
          </div>
          <div style={{ marginLeft: '20px' }}>
            <Button
              disabled={inputLovDS.data.length < 1}
              color="primary"
              onClick={this.handleToOkInputLov}
            >
              确定
            </Button>
          </div>
        </div>
      </>
    );
  };

  render() {
    const { inputLovDS, inputLovVisible, inputLovTitle, onOpenInputModal } = this.props;
    return (
      <ModalUI
        data-hcg_flag="InputLovModal"
        visible={inputLovVisible}
        closable
        onCancel={() => onOpenInputModal(false, '', '')}
        width="6rem"
        bodyStyle={{ width: '100%' }}
        zIndex={999}
        footer={[]}
      >
        <Table
          dataSet={inputLovDS}
          queryBar={this.renderInputLovBar}
          style={{ height: '4rem' }}
          columns={[
            {
              header: inputLovTitle,
              name: 'code',
              align: 'center',
            },
          ]}
        />
      </ModalUI>
    );
  }
}
