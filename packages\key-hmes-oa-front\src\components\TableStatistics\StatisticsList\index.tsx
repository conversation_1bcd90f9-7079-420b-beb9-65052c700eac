import React, { Component } from 'react';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import SumTable from './SumTable';

const { Panel } = Collapse;

interface sumTableData {
  collectFieldName: string | number;
  collectColumnMap: object;
}
interface StatisticsListProps {
  checkedList?: Array<sumTableData>;
  searchList?: Array<sumTableData>;
}

export default class StatisticsList extends Component<StatisticsListProps> {
  constructor(props) {
    super(props);
    this.changeCollapse = this.changeCollapse.bind(this);
  }
  state = {
    collapseKeys: ['1', '2'],
  };
  
  changeCollapse(key) {
    // 收起的时候，减少底部所占空间
    this.setState({
      collapseKeys: key,
    });
  }

  renderCollapse(isExpanded) {
    return (
      <span style={{ fontSize: 12, paddingLeft: 20 }}>
        <a>
          {isExpanded
            ? intl.get(`hzero.common.button.up`).d('收起')
            : intl.get(`hzero.common.button.expand`).d('展开')}
        </a>
      </span>
    );
  }

  render() {
    const { collapseKeys } = this.state;
    const { checkedList, searchList } = this.props;
    return (
      <React.Fragment>
        <Collapse className="form-collapse" activeKey={collapseKeys} onChange={this.changeCollapse}>
          <Panel
            key="1"
            showArrow={false}
            header={
              <h3>
                {intl.get('tarzan.aps.statistics.checked').d('勾选统计')}
                {this.renderCollapse(collapseKeys.includes('1'))}
              </h3>
            }
          >
            <SumTable data={checkedList} />
          </Panel>
          <Panel
            key="2"
            showArrow={false}
            header={
              <h3>
                {intl.get('tarzan.aps.statistics.search').d('搜索统计')}
                {this.renderCollapse(collapseKeys.includes('2'))}
              </h3>
            }
          >
            <SumTable data={searchList} />
          </Panel>
        </Collapse>
      </React.Fragment>
    );
  }
}
