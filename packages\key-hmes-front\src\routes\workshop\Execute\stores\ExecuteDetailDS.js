/**
 * @Description: 执行作业管理详情页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2023-05-18 14:43:12
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.execute';
const tenantId = getCurrentOrganizationId();

const formDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-eo/detail/ui`,
        method: 'GET',
      };
    },
    submit: ({ dataSet }) => {
      const {
        material,
        revisionFlag,
        productionLine,
        bom,
        router,
        ...data
      } = dataSet.current.toData();
      delete data.productionVersion;
      delete data.productionVersionDisable;
      delete data.productionVersionRequire;
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.EO.DETAIL.BASIC`,
        method: 'POST',
        data,
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
  fields: [
    // 基本属性
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoNum`).d('执行作业编码'),
      disabled: true,
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      valueField: 'siteId',
      noCache: true,
      ignore: 'always',
      disabled: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'site.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.siteName`).d('站点名称'),
      ignore: 'always',
      bind: 'site.siteName',
      disabled: true,
    },
    {
      name: 'eoType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoType`).d('执行作业类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      disabled: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=EO_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.status`).d('执行作业状态'),
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'NEW',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
      disabled: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.workOrderNum`).d('生产指令编码'),
      disabled: true,
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.identification`).d('执行作业标识'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.qualityStatusDesc`).d('质量状态'),
      disabled: true,
    },
    // 需求属性
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.BOM_MATERIAL',
      textField: 'materialCode',
      valueField: 'materialId',
      noCache: true,
      disabled: true,
      ignore: 'always',
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId,
            siteIds: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'material.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'material.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      disabled: true,
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialName`).d('物料名称'),
      bind: 'material.materialName',
      disabled: true,
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.execute.qty`).d('数量'),
      min: 0,
      precision: 2,
      dynamicProps: {
        disabled({ record }) {
          const middle = ['NEW', 'HOLD', 'ABANDON'];
          return !middle.some(ele => ele === record.get('status'));
        },
      },
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.uom.uomCode`).d('单位编码'),
      bind: 'material.uomCode',
      disabled: true,
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.uom.uomName`).d('单位描述'),
      bind: 'material.uomName',
      disabled: true,
    },
    // 生产属性
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.planStartTime`).d('计划开始时间'),
      required: true,
      max: 'planEndTime',
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.planEndTime`).d('计划结束时间	'),
      required: true,
      min: 'planStartTime',
    },
    {
      name: 'productionLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.productionLineCode`).d('生产线编码'),
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineCode',
      valueField: 'prodLineId',
      required: true,
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId,
            siteIds: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'productionLineCode',
      type: FieldType.string,
      ignore: 'always',
      bind: 'productionLine.prodLineCode',
    },
    {
      name: 'productionLineId',
      type: FieldType.number,
      bind: 'productionLine.prodLineId',
    },
    {
      name: 'productionLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionLineName`).d('生产线描述'),
      disabled: true,
      ignore: 'always',
      bind: 'productionLine.prodLineName',
    },
    {
      name: 'bom',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.eoBomNameRevision`).d('装配清单/版本'),
      textField: 'bomName',
      valueField: 'bomId',
      disabled: true,
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        help({ record }) {
          return record.get('bomType') !== 'EO' ? intl.get(`${modelPrompt}.bomMessage`).d('保存时会生成EO类型的装配清单') : '';
        },
        lovCode({ record }) {
          return record.get('bomType') ? 'MT.MES.BOM_BASIC' : 'MT.METHOD.BOM_BASIC';
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteIds: record.get('siteId'),
            materialId: record.get('materialId'),
            bomTypes: record.get('bomType') ? 'EO' : undefined,
          };
        },
      },
    },
    {
      name: 'eoBomName',
      type: FieldType.string,
      bind: 'bom.bomName',
      label: intl.get(`${modelPrompt}.model.execute.eoBomNameRevision`).d('装配清单/版本'),
      disabled: true,
    },
    {
      name: 'eoBomId',
      type: FieldType.number,
      bind: 'bom.bomId',
    },
    {
      name: 'eoBomRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'bom.revision',
    },
    // {
    //   name: 'bomType',
    //   type: FieldType.string,
    //   bind: 'bom.bomType',
    // },
    {
      name: 'router',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.eoRouterNameRevision`).d('工艺路线/版本'),
      // lovCode: 'MT.MES.ROUTER',
      textField: 'routerName',
      valueField: 'routerId',
      disabled: true,
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        help({ record }) {
          return record.get('routerType') !== 'EO' ? intl.get(`${modelPrompt}.routerMessage`).d('保存时会生成EO类型的工艺路线') : '';
        },
        lovCode({ record }) {
          return record.get('routerType') ? 'MT.MES.ROUTER_SITE' : 'MT.METHOD.ROUTER_SITE';
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteIds: record.get('siteId'),
            // eoId: record.get('eoId'),
            materialId: record.get('materialId'),
            routerTypes: record.get('routerType') ? 'EO' : undefined,
          };
        },
      },
    },
    {
      name: 'eoRouterName',
      type: FieldType.string,
      bind: 'router.routerName',
      label: intl.get(`${modelPrompt}.model.execute.eoRouterNameRevision`).d('工艺路线/版本'),
      disabled: true,
    },
    {
      name: 'eoRouterId',
      type: FieldType.number,
      bind: 'router.routerId',
    },
    {
      name: 'eoRouterRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'router.revision',
    },
    // {
    //   name: 'routerType',
    //   type: FieldType.string,
    //   bind: 'router.routerType',
    // },
    {
      name: 'productionVersion',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionVersion`).d('生产版本'),
      lovCode: 'MT.METHOD.MATERIAL.SITE.PROD-VERSION',
      textField: 'productionVersionCode',
      valueField: 'productionVersionId',
      noCache: true,
      ignore: 'always',
      disabled: true,
      dynamicProps: {
        // disabled({ record }) {
        //   return record.get('productionVersionDisable') !== 'Y';
        // },
        required({ record }) {
          return record.get('productionVersionRequire') === 'Y';
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
            revisionCode: record.get('revisionCode'),
            currentFlag: 'Y',
          };
        },
      },
    },
    {
      name: 'productionVersionId',
      type: FieldType.number,
      bind: 'productionVersion.productionVersionId',
    },
    {
      name: 'productionVersionCode',
      type: FieldType.string,
      bind: 'productionVersion.productionVersionCode',
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionVersion`).d('生产版本'),
      disabled: true,
    },
    {
      name: 'productionVersionRequire',
      type: FieldType.string,
      defaultValue: false,
      ignore: 'always',
    },
    {
      name: 'productionVersionDisable',
      type: FieldType.string,
      defaultValue: false,
      ignore: 'always',
    },
    {
      name: 'reworkFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkFlag`).d('返修标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      disabled: true,
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'concessiveInterceptionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.concessiveInterceptionFlag`).d('让步拦截标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      disabled: true,
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'overOrderInterceptionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overOrderInterceptionFlag`).d('跨工单拦截标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      disabled: true,
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'dischargeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dischargeFlag`).d('在制品排出标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      disabled: true,
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'degradeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeFlag`).d('降级标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      disabled: true,
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

const defaultBomRouterMaterialDS = () => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/default/bom/router`,
        method: 'GET',
      };
    },
  },
});

const bomAndRouterDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'selectType',
      type: FieldType.string,
      defaultValue: 'designChange',
    },
    {
      name: 'designProductionVersion',
      type: FieldType.object,
    },
    {
      name: 'designProductionVersionCode',
      type: FieldType.string,
      bind: 'designProductionVersion.productionVersionCode',
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionVersion`).d('生产版本'),
      disabled: true,
    },
    {
      name: 'designBom',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.eoBomNameRevision`).d('装配清单/版本'),
    },
    {
      name: 'designBomName',
      type: FieldType.string,
      bind: 'designBom.bomName',
      disabled: true,
    },
    {
      name: 'designBomRevision',
      type: FieldType.string,
      bind: 'designBom.revision',
      disabled: true,
    },
    {
      name: 'designRouter',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.eoRouterNameRevision`).d('工艺路线/版本'),
    },
    {
      name: 'designRouterId',
      type: FieldType.number,
      bind: 'designRouter.routerId',
    },
    {
      name: 'designRouterName',
      type: FieldType.string,
      bind: 'designRouter.routerName',
      disabled: true,
    },
    {
      name: 'designRouterRevision',
      type: FieldType.string,
      bind: 'designRouter.revision',
      disabled: true,
    },
    {
      name: 'ownProductionVersion',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'productionVersion',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionVersion`).d('生产版本'),
      lovCode: 'MT.METHOD.MATERIAL.SITE.PROD-VERSION',
      textField: 'productionVersionCode',
      valueField: 'productionVersionId',
      noCache: true,
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
            revisionCode: record.get('revisionCode'),
          };
        },
        required({ record }) {
          return record.get('ownProductionVersion') && record.get('selectType') === 'textMaterial';
        },
      },
    },
    {
      name: 'bom',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.eoBomNameRevision`).d('装配清单/版本'),
      // lovCode: 'MT.METHOD.PROD-VERSION.BOM', // 设计变更
      // lovCode: 'MT.BOM_BASIC',
      textField: 'bomName',
      valueField: 'bomId',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovCode({ record }) {
          let queryLovCode;
          // if (record.get('selectType') === 'designChange') {
          //   queryLovCode = 'MT.METHOD.PROD-VERSION.BOM';
          // }
          if (record.get('selectType') === 'textMaterial') {
            queryLovCode = 'MT.METHOD.PROD-VERSION.BOM';
            // queryLovCode = 'MT.METHOD.BOM_BASIC';
          }
          if (record.get('selectType') === 'textOrder') {
            queryLovCode = 'MT.MES.BOM_BASIC';
          }
          return queryLovCode;
        },
        lovPara({ record }) {
          const queryParams = {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
          };
          if (record.get('selectType') === 'textMaterial') {
            queryParams.revisionCode = record.get('revisionCode');
            queryParams.productionVersionCode = record.get('productionVersionCode');
          }
          return queryParams;
        },
        disabled({ record }) {
          // 有生产版本的话，只能通过选择生产版本带出来
          if (record.get('selectType') === 'textMaterial') {
            return record.get('ownProductionVersion');
          }
          return false;
        },
      },
    },
    {
      name: 'bomName',
      type: FieldType.string,
      bind: 'bom.bomName',
    },
    {
      name: 'bomId',
      type: FieldType.number,
      bind: 'bom.bomId',
    },
    {
      name: 'bomRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'bom.revision',
    },
    {
      name: 'bomType',
      type: FieldType.string,
      bind: 'bom.bomType',
    },
    {
      name: 'router',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.eoRouterNameRevision`).d('工艺路线/版本'),
      // lovCode: 'MT.METHOD.PROD-VERSION.ROUTER', // 设计变更
      textField: 'routerName',
      valueField: 'routerId',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovCode({ record }) {
          let queryLovCode;
          // if (record.get('selectType') === 'designChange') {
          //   queryLovCode = 'MT.METHOD.PROD-VERSION.ROUTER';
          // }
          if (record.get('selectType') === 'textMaterial') {
            // queryLovCode = 'MT.METHOD.ROUTER_SITE';
            queryLovCode = 'MT.METHOD.PROD-VERSION.ROUTER';
          }
          if (record.get('selectType') === 'textOrder') {
            queryLovCode = 'MT.MES.ROUTER_SITE';
          }
          return queryLovCode;
        },
        lovPara({ record }) {
          const queryParams = {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
          };
          if (record.get('selectType') === 'textMaterial') {
            queryParams.revisionCode = record.get('revisionCode');
            queryParams.productionVersionCode = record.get('productionVersionCode');
          }
          return queryParams;
        },
        disabled({ record }) {
          // 有生产版本的话，只能通过选择生产版本带出来
          if (record.get('selectType') === 'textMaterial') {
            return record.get('ownProductionVersion');
          }
          return false;
        },
      },
    },
    {
      name: 'routerName',
      type: FieldType.string,
      bind: 'router.routerName',
    },
    {
      name: 'routerId',
      type: FieldType.number,
      bind: 'router.routerId',
    },
    {
      name: 'routerRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'router.revision',
    },
    {
      name: 'routerType',
      type: FieldType.string,
      bind: 'router.routerType',
    },
  ],
});

export { formDS, defaultBomRouterMaterialDS, bomAndRouterDS };
