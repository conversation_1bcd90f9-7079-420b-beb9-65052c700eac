/**
 * @feature 库存初始化-按钮相关
 * @date 2021-9-16
 * <AUTHOR>
 */
import React, { useState, useEffect } from 'react';
import { withRouter } from 'dva/router';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Upload } from 'choerodon-ui';
// import { Button } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId, getAccessToken, getResponse } from 'utils/utils';
import notification from 'utils/notification';
import request from 'utils/request';
import myInstance from '@utils/myAxios';
import { observer } from 'mobx-react';
import { BASIC, API_HOST } from '@utils/config';
// import { PrintButton } from '@components/tarzan-ui';

// import DownloadButton from './DownloadButton';

const tenantId = getCurrentOrganizationId();
// const prefix = '/mes-38546';
const modelPrompt = 'tarzan.inventory.initial.model';

const CommonButton = observer(props => {
  const [confirmStatus, changeConfirm] = useState(true);
  const [validateStatus, changeValidate] = useState(true);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  // const [validateLoading, setValidateLoading] = useState(false);
  // eslint-disable-next-line no-console
  console.log('validateStatus', validateStatus);

  const {
    match: { path },
  } = props;

  useEffect(() => {
    props.dataSet.query().then(res => {
      if (res.success) {
        changeValidate((res.rows || {}).totalElements === 0);
        changeConfirm((res.rows || {}).totalElements === 0);
      }
    });
  }, []);

  useDataSetEvent(props.dataSet, 'load', () => {
    changeValidate(props.dataSet.totalCount === 0);
    changeConfirm(props.dataSet.totalCount === 0);
  });

  const uploadFile = info => {
    setImportLoading(false);
    if (info.success) {
      props.dataSet.query().then(res => {
        if (res.success) {
          changeValidate((res.rows || {}).totalElements === 0);
        }
      });
    } else if (info.message) {
      notification.error({
        message: info.message,
        description: '',
      });
    }
  };

  const beforeUpload = file => {
    const conversionFileSize = file.size / 1024 / 1024;

    if (conversionFileSize > 4) {
      notification.error({
        message: intl.get(`${modelPrompt}.notification.file.too.large`).d('文件大小超过4MB限制'),
      });
      return false; // 阻止上传
    }

    if (!['.csv', '.xlsx', '.xls'].some(child => file.name.indexOf(child) >= 0)) {
      notification.warning({
        description: '',
        message: intl.get(`${modelPrompt}.notification.import.validate`).d('上传文本格式不正确'),
      });
      return false;
    }
    changeConfirm(true);
    setImportLoading(true);
    return true;
  };

  // 数据验证
  // const validateAction = () => {
  //   setValidateLoading(true);
  //   const validateUrl = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot/validate/excel/import/ui`;
  //   myInstance.post(validateUrl).then(res => {
  //     const { success } = res.data;
  //     if (success) {
  //       notification.success();
  //       changeConfirm(false);
  //       props.dataSet.query().then(ress => {
  //         if (ress.success && ress.rows) {
  //           changeConfirm((res.rows || {}).totalElements === 0);
  //         }
  //       });
  //     } else if (res.data.message) {
  //       notification.error({
  //         message: res.data.message,
  //         description: '',
  //       });
  //     }
  //     setValidateLoading(false);
  //   });
  // };

  // 数据导入
  const confirmDatas = () => {
    setConfirmLoading(true);
    const importUrl = `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-product-ps-pa-supps/save/excel/import/ui`;
    myInstance.post(importUrl, {}).then(res => {
      const { success } = res.data;
      if (success) {
        notification.success({
          message: intl.get(`${modelPrompt}.notification.import.success`).d('导入成功'),
        });
      } else if (res.data.message) {
        notification.error({
          message: res.data.message,
        });
        setConfirmLoading(false);
      }
      props.dataSet.query();
      changeConfirm(false);
      changeValidate(true);
      setConfirmLoading(false);
    });
  };

  // 删除
  const onDelete = async () => {
    const ress = await props.dataSet.delete(props.dataSet.selected);
    if (ress.success) {
      props.dataSet.query().then(res => {
        if (res.success) {
          changeValidate((res.rows || {}).totalElements === 0);
          changeConfirm((res.rows || {}).totalElements === 0);
        }
      });
      props.dataSet.clearCachedSelected();
      props.setState(true);
    }
  };

  const url = `${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/hme-product-ps-pa-supps/excel/import/ui`;
  const uploadProps = {
    name: 'file',
    beforeUpload,
    showUploadList: false,
    headers: {
      authorization: `Bearer ${getAccessToken()}`,
    },
    accept: ['.xlsx', '.xls'],
    action: url,
    onSuccess: uploadFile,
    showUploadBtn: false,
    data: {},
  };

  const handleExport = async () => {
    // 请求后台
    const len = props.dataSet.queryDataSet?.toData()?.length - 1 || 0;
    const queryData = props.dataSet.queryDataSet.toData()[len];
    if (queryData && queryData.siteId && queryData.materialId && queryData.equipmentId) {
      const params = {
        siteId: queryData.siteId,
        siteCode: queryData.siteCode,
        materialId: queryData.materialId,
        materialCode: queryData.materialCode,
        equipmentId: queryData.equipmentId,
        equipmentCode: queryData.equipmentCode,
        workcellId: queryData.workcellId,
        workcellCode: queryData.workcellCode,
      };
      const res = await request(
        `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-product-ps-pa-supps/excel/output/get`,
        {
          method: 'GET',
          query: params,
          responseType: 'blob',
        },
      );
      if (res) {
        if (res.type === 'application/json') {
          const fileReader = new FileReader();
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result);
            // 普通对象，读取信息
            getResponse(jsonData);
            notification.error({ message: jsonData.message });
          };
          fileReader.readAsText(res);
        } else {
          const file = new Blob([res], {
            type: 'vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          });
          const url = window.URL.createObjectURL(file);
          const a = document.createElement('a');
          a.href = url;
          a.download = decodeURIComponent('产品加工参数补录信息表.xlsx');
          document.body.appendChild(a);
          a.click();
          setTimeout(() => {
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
          }, 1000);
        }
      }
    } else {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.site.material.equip`)
          .d(`请选择查询条件的站点，物料，设备！`),
      });
    }
  };

  return (
    <>
      <span style={{ margin: '-4px 0 0 8px' }}>
        <Upload {...uploadProps}>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            loading={importLoading}
            permissionList={[
              {
                code: `${path}.button.upload`,
                type: 'button',
                meaning: '列表页-数据上传按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.data.import`).d('数据上传')}
          </PermissionButton>
        </Upload>
      </span>
      <PermissionButton
        type="c7n-pro"
        icon="get_app"
        onClick={handleExport}
        permissionList={[
          {
            code: `${path}.button.tempalte`,
            type: 'button',
            meaning: '列表页-模板下载按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.button.download.importTempalte`).d('导入模板获取')}
      </PermissionButton>
      {/* <DownloadButton
        dataSet={props.dataSet}
        // path={path}
        url={`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot/download/model-attr/ui`}
      /> */}
      {/* <PermissionButton
        type="c7n-pro"
        icon="spellcheck"
        onClick={validateAction}
        disabled={validateStatus}
        loading={validateLoading}
        permissionList={[
          {
            code: `${path}.button.validatas`,
            type: 'button',
            meaning: '列表页-数据验证按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.button.validatas`).d('数据验证')}
      </PermissionButton> */}
      <PermissionButton
        type="c7n-pro"
        icon="file_upload"
        onClick={confirmDatas}
        disabled={confirmStatus}
        loading={confirmLoading}
        permissionList={[
          {
            code: `${path}.button.confirm`,
            type: 'button',
            meaning: '列表页-数据导入按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.button.data.confirm`).d('数据导入')}
      </PermissionButton>
      <PermissionButton
        type="c7n-pro"
        icon="delete"
        onClick={onDelete}
        // disabled={props.deleteFlag}
        disabled={props.dataSet.selected.length < 1}
        permissionList={[
          {
            code: `${path}.button.edit`,
            type: 'button',
            meaning: '列表页-编辑新建删除复制按钮',
          },
        ]}
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
      {/* <PrintButton
        disabled={props.deleteFlag}
        path={path}
        permissionFlag
        dataSet={props.dataSet}
        labelTemplateCode="MT.MATERIAL_LOT_TEMP_001"
      /> */}
    </>
  );
});

export default withRouter(CommonButton);
