/*
 * @Description: 生产工艺折算系数维护-service
 * @Author: <<EMAIL>>
 * @Date: 2024-01-08 11:11:56
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-01-08 14:06:22
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 查询查询设备相关工艺
 * @function GetOperationByEquipment
 * @returns {object} fetch Promise
 */
export function GetOperationByEquipment(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-equipment-wkc-rel/equipment/wkc/rel/query`,
    method: 'POST',
  };
}