import { DataSetSelection, FieldType } from 'choerodon-ui/dataset/data-set/enum';
import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';

const InputLovDS = () => new DataSet({
    autoQuery: false,
    selection:DataSetSelection.multiple,
    paging: false,
    fields: [
        {
            type: FieldType.string,
            name: 'code',
            label: intl.get('Default.code').d('编码'),
        },
    ],
    queryFields: [
        {
            type: FieldType.string,
            name: 'code',
            label: intl.get('Default.code').d('编码'),
        },
    ],
});

export default InputLovDS;
