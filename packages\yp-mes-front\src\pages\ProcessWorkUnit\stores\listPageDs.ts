import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { DataSetSelection, FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.process.unitWork.model.unitWork';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    autoQuery: true,
    autoCreate: false,
    selection: DataSetSelection.multiple,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    primaryKey: 'id',
    queryFields: [
      {
        name: 'prodLineLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.prodLine`).d('生产线编码'),
        lovCode: 'MT.MODEL.PRODLINE',
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'prodLineId',
        type: FieldType.string,
        bind: 'prodLineLov.prodLineId',
      },
      {
        name: 'prodLineCode',
        bind: 'prodLineLov.prodLineCode',
      },
      {
        name: 'equipmentCodes',
        multiple: true,
        label: intl.get(`${modelPrompt}.equipmentCodes`).d('设备编码'),
        type: FieldType.string,
      },
      {
        name: 'operationName',
        label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
        type: FieldType.string,
      },
      {
        name: 'workcellLov',
        ignore: FieldIgnore.always,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.workcell`).d('工位级工作单元编码'),
        lovCode: 'MT.MODEL.WORKCELL',
        lovPara: {
          tenantId,
          workcellType: 'STATION'
        },
      },
      {
        name: 'workcellId',
        type: FieldType.string,
        bind: 'workcellLov.workcellId',
      },
      {
        name: 'workcellCode',
        type: FieldType.string,
        bind: 'workcellLov.workcellCode',
      },
      {
        name: 'opWorkcellLov',
        ignore: FieldIgnore.always,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.opWorkcell`).d('工序级工作单元编码'),
        lovCode: 'MT.MODEL.WORKCELL',
        lovPara: {
          tenantId,
          workcellType: 'PROCESS'
        },
      },
      {
        name: 'opWorkcellId',
        type: FieldType.string,
        bind: 'opWorkcellLov.workcellId',
      },
      {
        name: 'opWorkcellCode',
        type: FieldType.string,
        bind: 'opWorkcellLov.workcellCode',
      },
    ],
    fields: [
      {
        name: 'prodLineCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
      },
      {
        name: 'prodLineName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.prodLineName`).d('生产线描述'),
      },
      {
        name: 'equipmentLov',
        required: true,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.equipment`).d('设备编码'),
        lovCode: 'MT.MODEL.EQUIPMENT',
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'equipmentId',
        type: FieldType.string,
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'equipmentCode',
        type: FieldType.string,
        bind: 'equipmentLov.equipmentCode',
      },
      {
        name: 'equipmentName',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备描述'),
        bind: 'equipmentLov.equipmentName',
      },
      {
        name: 'operationLov',
        required: true,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.operation`).d('工艺编码'),
        lovCode: 'MT.METHOD.OPERATION',
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'operationId',
        bind: 'operationLov.operationId',
      },
      {
        name: 'operationName',
        type: FieldType.string,
        bind: 'operationLov.operationName',
      },
      {
        name: 'operationDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.description`).d('工艺描述'),
        bind: 'operationLov.description',
      },
      {
        name: 'workcellLov',
        required: true,
        ignore: FieldIgnore.always,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.workcell`).d('工位级工作单元编码'),
        lovCode: 'MT.MODEL.WORKCELL',
        lovPara: {
          tenantId,
          workcellType: 'STATION'
        },
      },
      {
        name: 'workcellId',
        type: FieldType.string,
        bind: 'workcellLov.workcellId',
      },
      {
        name: 'workcellCode',
        type: FieldType.string,
        bind: 'workcellLov.workcellCode',
      },
      {
        name: 'workcellName',
        label: intl.get(`${modelPrompt}.workcellName`).d('工位级工作单元描述'),
        type: FieldType.string,
        bind: 'workcellLov.workcellName',
      },
      {
        name: 'opWorkcellLov',
        required: true,
        ignore: FieldIgnore.always,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.opWorkcell`).d('工序级工作单元编码'),
        lovCode: 'MT.MODEL.WORKCELL',
        lovPara: {
          tenantId,
          workcellType: 'PROCESS'
        },
      },
      {
        name: 'opWorkcellId',
        bind: 'opWorkcellLov.workcellId',
      },
      {
        name: 'opWorkcellCode',
        type: FieldType.string,
        bind: 'opWorkcellLov.workcellCode',
      },
      {
        name: 'opWorkcellName',
        label: intl.get(`${modelPrompt}.opWorkcellName`).d('工序级工作单元描述'),
        type: FieldType.string,
        bind: 'opWorkcellLov.workcellName',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/hme-equipment-op-wkc/list/ui`,
          method: 'GET',
        };
      },
    },
  });
export default listPageFactory
