module.exports = [
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9I8vz+jRCvpV3P18DawhswQ==',
    id: 1580,
    code: 'hzero.hzero-front-demo',
    name: '演示菜单',
    quickIndex: 'yscd',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 0,
    icon: 'basic-data-management',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9VMGD3WMkPIoLifhOR3g9cw==',
        id: 1597,
        code: 'hzero.hzero-front-demo.data.preparation',
        name: 'Demo页面',
        quickIndex: '',
        level: 'site',
        parentId: 1580,
        type: 'menu',
        sort: 0,
        route: '/demo1/demo-page',
        parentName: '演示菜单',
        viewCode: 'data.preparation',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9O6Rx7BJRegGV3jXbhEGUuA==',
        id: 1593,
        code: 'hzero.hzero-front-demo.hzero.test.hello',
        name: '你好',
        quickIndex: '',
        level: 'site',
        parentId: 1580,
        type: 'menu',
        sort: 0,
        route: '/test-modules1/hello',
        parentName: '演示菜单',
        viewCode: 'hzero.test.hello',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9c+qV30dcHfQEkGCJI2jQFw==',
        id: 1595,
        code: 'hzero.hzero-front-demo.hzero.user.list',
        name: '用户列表',
        quickIndex: '',
        level: 'site',
        parentId: 1580,
        type: 'menu',
        sort: 0,
        route: '/hanhan/list',
        parentName: '演示菜单',
        viewCode: 'hzero.user.list',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9PcXhdkKzZY+kPI/Jv6l3/A==',
        id: 1587,
        code: 'hzero.hzero-front-demo.sample-demo-page',
        name: '页面3',
        quickIndex: '',
        level: 'site',
        parentId: 1580,
        type: 'menu',
        sort: 0,
        route: '/hzero-front-demo-demo1/test1',
        parentName: '演示菜单',
        viewCode: 'sample-demo-page',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9ZVp1JItX/5tuPPs6G0ZjzQ==',
        id: 1589,
        code: 'hzero.hzero-front-demo.test33',
        name: 'hzero-boot测试菜单',
        quickIndex: '',
        level: 'site',
        parentId: 1580,
        type: 'menu',
        sort: 0,
        route: '/hzero-boot-test-module1/hello',
        parentName: '演示菜单',
        viewCode: 'test33',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9aBC+LAfFw9no94Kx8kNc9Q==',
        id: 1581,
        code: 'hzero.hzero-front-demo.todo',
        name: '待办事项',
        quickIndex: '',
        level: 'site',
        parentId: 1580,
        type: 'menu',
        sort: 0,
        route: '/todo-module/todo-feature',
        parentName: '演示菜单',
        viewCode: 'todo',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9JfuCtlaS8r5cK5a8EjCnCA==',
        id: 1583,
        code: 'hzero.hzero-front-demo.todo-user-mgn',
        name: '用户管理',
        quickIndex: '',
        level: 'site',
        parentId: 1580,
        type: 'menu',
        sort: 0,
        route: '/todo/user',
        parentName: '演示菜单',
        viewCode: 'todo-user-mgn',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9phUrqaPx5dbVSIUlaV5bpg==',
        id: 1585,
        code: 'hzero.hzero-front-demo.vue-page-demo',
        name: 'Vue页面演示',
        quickIndex: '',
        level: 'site',
        parentId: 1580,
        type: 'menu',
        sort: 0,
        route: '/hzero-front-demo-demo1/vue-test',
        parentName: '演示菜单',
        viewCode: 'vue-page-demo',
      },
    ],
    viewCode: '',
  },
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVUKRUzZFbsowUAtDspcsq/8m9i/qNPyO+5m5iiqVp7Dg==',
    id: 473,
    code: 'hzero.site.sys',
    name: '系统管理',
    quickIndex: 'XTGL',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 10,
    icon: 'system-management',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXEh5oNyCYTubr1zeVDweuKmAPGtD09/n9T6F05Qv8NhA==',
        id: 792,
        code: 'hzero.site.sys.tenant',
        name: '租户管理',
        quickIndex: 'ZHWH',
        level: 'site',
        parentId: 473,
        type: 'dir',
        sort: 10,
        icon: 'data-platform',
        route: '',
        parentName: '系统管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXEh5oNyCYTubr1zeVDweuKHQmcSI3uA/b1hoOOoXazKw==',
            id: 797,
            code: 'hzero.site.sys.tenant.tenant-maintain',
            name: '租户维护',
            quickIndex: 'ZHWH',
            level: 'site',
            parentId: 792,
            type: 'menu',
            sort: 10,
            icon: 'workflow',
            route: '/hiam/tenants',
            parentName: '租户管理',
            viewCode: 'sys.tenant.tenant-maintain',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXEh5oNyCYTubr1zeVDweuK8m9i/qNPyO+5m5iiqVp7Dg==',
            id: 793,
            code: 'hzero.site.sys.tenant.tenant-init-config',
            name: '租户初始化处理配置',
            quickIndex: 'ZHCSHCLPZ',
            level: 'site',
            parentId: 792,
            type: 'menu',
            sort: 20,
            icon: 'business-settings',
            route: '/hiam/tenant-init-config',
            parentName: '租户管理',
            viewCode: 'sys.tenant.tenant-init-config',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXEh5oNyCYTubr1zeVDweuK0hIAldj2A541yaV+WRHmmg==',
            id: 795,
            code: 'hzero.site.sys.tenant.tenant-init-log',
            name: '租户初始化处理日志',
            quickIndex: 'ZHCSHCLRZ',
            level: 'site',
            parentId: 792,
            type: 'menu',
            sort: 30,
            icon: 'business-settings',
            route: '/hiam/tenant-init-log',
            parentName: '租户管理',
            viewCode: 'sys.tenant.tenant-init-log',
          },
        ],
        viewCode: 'sys.tenant',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHV/THeey/hlo1FWLyDzLo7Apx1UUwAWVaOW/AHajC/F/w==',
        id: 666,
        code: 'hzero.site.sys.role-management',
        name: '角色管理',
        quickIndex: 'JSGL',
        level: 'site',
        parentId: 473,
        type: 'dir',
        sort: 20,
        icon: 'service-governance',
        route: '',
        parentName: '系统管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHV/THeey/hlo1FWLyDzLo7AHQmcSI3uA/b1hoOOoXazKw==',
            id: 667,
            code: 'hzero.site.sys.role',
            name: '角色管理',
            quickIndex: 'JSGL',
            level: 'site',
            parentId: 666,
            type: 'menu',
            sort: 10,
            icon: 'expert-library',
            route: '/hiam/role',
            parentName: '角色管理',
            viewCode: 'sys.role',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHV/THeey/hlo1FWLyDzLo7AcJEocQhBDCuMVORPA12QnA==',
            id: 668,
            code: 'hzero.site.sys.role-management.role-tree',
            name: '角色管理树形',
            quickIndex: 'JSGLSX',
            level: 'site',
            parentId: 666,
            type: 'menu',
            sort: 20,
            icon: 'organization-management',
            route: '/hiam/role-tree',
            parentName: '角色管理',
            viewCode: 'sys.role-management.role-tree',
          },
        ],
        viewCode: 'sys.role-management',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHX+V+sf0LxqNMmL3d5dIOOopx1UUwAWVaOW/AHajC/F/w==',
        id: 626,
        code: 'hzero.site.sys.menu-manage',
        name: '菜单管理',
        quickIndex: 'CDGL',
        level: 'site',
        parentId: 473,
        type: 'dir',
        sort: 30,
        icon: 'cert',
        route: '',
        parentName: '系统管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHX+V+sf0LxqNMmL3d5dIOOoHQmcSI3uA/b1hoOOoXazKw==',
            id: 627,
            code: 'hzero.site.sys.menu',
            name: '菜单配置',
            quickIndex: 'CDPZ',
            level: 'site',
            parentId: 626,
            type: 'menu',
            sort: 10,
            icon: 'cert',
            route: '/hiam/menu',
            parentName: '菜单管理',
            viewCode: 'sys.menu',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHX+V+sf0LxqNMmL3d5dIOOocJEocQhBDCuMVORPA12QnA==',
            id: 628,
            code: 'hzero.site.sys.menu-manage.api-management',
            name: 'API管理',
            quickIndex: 'APIGL',
            level: 'site',
            parentId: 626,
            type: 'menu',
            sort: 20,
            route: '/hiam/api-management',
            parentName: '菜单管理',
            viewCode: 'sys.menu-manage.api-management',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHX4QjWQ6Bg2QXjXF5QwY3RFmAPGtD09/n9T6F05Qv8NhA==',
            id: 632,
            code: 'hzero.site.sys.menu-manage.tenant-menu-manage',
            name: '租户菜单管理',
            quickIndex: 'ZHCDGL',
            level: 'site',
            parentId: 626,
            type: 'menu',
            sort: 30,
            route: '/hiam/tenant-menu-manage',
            parentName: '菜单管理',
            viewCode: 'sys.menu-manage.tenant-menu-manage',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHX4QjWQ6Bg2QXjXF5QwY3RFS2kvAZjuXEI6uV7/bVWlwg==',
            id: 630,
            code: 'hzero.site.sys.menu-manage.miss-permission-control',
            name: '缺失权限管理',
            quickIndex: 'QXQSGL',
            level: 'site',
            parentId: 626,
            type: 'menu',
            sort: 50,
            route: '/hiam/miss-permission-record',
            parentName: '菜单管理',
            viewCode: 'sys.menu-manage.miss-permission-control',
          },
        ],
        viewCode: 'sys.menu-manage',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVA4aXJ5ILd/C5zYHZMcYhtfzraweJ1UiwIugDmaojZlw==',
        id: 804,
        code: 'hzero.site.sys.user',
        name: '用户管理',
        quickIndex: 'YHGL',
        level: 'site',
        parentId: 473,
        type: 'dir',
        sort: 40,
        icon: 'workflow',
        route: '',
        parentName: '系统管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXEo41NHTCuyLR0dkenuU6imAPGtD09/n9T6F05Qv8NhA==',
            id: 822,
            code: 'hzero.site.sys.user-group',
            name: '用户组管理',
            quickIndex: 'YHZGL',
            level: 'site',
            parentId: 804,
            type: 'menu',
            sort: 10,
            icon: 'icon-test8',
            route: '/hiam/user-group-management',
            parentName: '用户管理',
            viewCode: 'sys.user-group',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVA4aXJ5ILd/C5zYHZMcYht0hIAldj2A541yaV+WRHmmg==',
            id: 805,
            code: 'hzero.site.sys.account',
            name: '子账户管理',
            quickIndex: 'ZZHGL',
            level: 'site',
            parentId: 804,
            type: 'menu',
            sort: 20,
            icon: 'workflow',
            route: '/hiam/sub-account-site',
            parentName: '用户管理',
            viewCode: 'sys.account',
          },
        ],
        viewCode: 'sys.user',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVUKRUzZFbsowUAtDspcsq/fzraweJ1UiwIugDmaojZlw==',
        id: 474,
        code: 'hzero.site.sys.config',
        name: '配置管理',
        quickIndex: 'PZGL',
        level: 'site',
        parentId: 473,
        type: 'dir',
        sort: 60,
        icon: 'transaction-management',
        route: '',
        parentName: '系统管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHW/dpFXAJ5Xjk+mPngkwbYhpx1UUwAWVaOW/AHajC/F/w==',
            id: 526,
            code: 'hzero.site.sys.config-site',
            name: '系统配置',
            quickIndex: 'XTPZ',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 10,
            icon: 'document-management',
            route: '/hpfm/config',
            parentName: '配置管理',
            viewCode: 'sys.config-site',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUQbjHDbt3Mhyzr/Di/6y/EHQmcSI3uA/b1hoOOoXazKw==',
            id: 537,
            code: 'hzero.site.sys.data-permission',
            name: '数据权限规则',
            quickIndex: 'SJQXGZ',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 20,
            icon: 'data-platform',
            route: '/hpfm/permission',
            parentName: '配置管理',
            viewCode: 'sys.data-permission',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUlQfwMFdFPno2RbkrrlezQdlJauhGpw4+TUxM8tJXlUQ==',
            id: 509,
            code: 'hzero.site.sys.code-rule',
            name: '编码规则',
            quickIndex: 'BMGZ',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 30,
            icon: 'reporting-platform',
            route: '/hpfm/code-rule',
            parentName: '配置管理',
            viewCode: 'sys.code-rule',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXzBHq6j66j9+vTI3db2MAQdlJauhGpw4+TUxM8tJXlUQ==',
            id: 559,
            code: 'hzero.site.sys.profile',
            name: '配置维护',
            quickIndex: 'PZWH',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 40,
            icon: 'message-management',
            route: '/hpfm/profile',
            parentName: '配置管理',
            viewCode: 'sys.profile',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXzBHq6j66j9+vTI3db2MAQS2kvAZjuXEI6uV7/bVWlwg==',
            id: 550,
            code: 'hzero.site.sys.open-app',
            name: '三方应用管理',
            quickIndex: 'DSFYYGL',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 50,
            icon: 'quality-business',
            route: '/hiam/open-app',
            parentName: '配置管理',
            viewCode: 'sys.open-app',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUlQfwMFdFPno2RbkrrlezQ8m9i/qNPyO+5m5iiqVp7Dg==',
            id: 503,
            code: 'hzero.site.sys.client',
            name: '客户端',
            quickIndex: 'KHD',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 60,
            icon: 'portal-management',
            route: '/hiam/client',
            parentName: '配置管理',
            viewCode: 'sys.client',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUQbjHDbt3Mhyzr/Di/6y/EmAPGtD09/n9T6F05Qv8NhA==',
            id: 532,
            code: 'hzero.site.sys.data-hierarchies',
            name: '数据层级配置',
            quickIndex: 'SJCJPZ',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 70,
            icon: 'dispatch-platform',
            route: '/hpfm/data-hierarchies',
            parentName: '配置管理',
            viewCode: 'sys.data-hierarchies',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWJr+w8FYptI66fnrHg0OTcdlJauhGpw4+TUxM8tJXlUQ==',
            id: 569,
            code: 'hzero.site.sys.site.sys.sso-config',
            name: '域名配置',
            quickIndex: 'YMPZ',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 80,
            icon: 'workflow-setting',
            route: '/hiam/domain-config',
            parentName: '配置管理',
            viewCode: 'sys.site.sys.sso-config',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVUKRUzZFbsowUAtDspcsq/0hIAldj2A541yaV+WRHmmg==',
            id: 475,
            code: 'hzero.site.sys.card-manage',
            name: '卡片管理',
            quickIndex: 'KPGL',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 90,
            icon: 'offer',
            route: '/hpfm/card-manage',
            parentName: '配置管理',
            viewCode: 'sys.card-manage',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVGI7YdgVCTfUys9rLoXNHP0hIAldj2A541yaV+WRHmmg==',
            id: 485,
            code: 'hzero.site.sys.clause',
            name: '条目配置',
            quickIndex: 'TMPZ',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 100,
            icon: 'workflow',
            route: '/hpfm/dashboard-clause',
            parentName: '配置管理',
            viewCode: 'sys.clause',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWROCPnu3DWvALRJ2CFr+s08m9i/qNPyO+5m5iiqVp7Dg==',
            id: 583,
            code: 'hzero.site.sys.tpl-manage.templates',
            name: '内容模板',
            quickIndex: 'MBWH',
            level: 'site',
            parentId: 474,
            type: 'menu',
            sort: 110,
            icon: 'cloud-service',
            route: '/hpfm/templates',
            parentName: '配置管理',
            viewCode: 'sys.tpl-manage.templates',
          },
        ],
        viewCode: 'sys.config',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWDzr9a1a6XrOfAiCOA8UyWcJEocQhBDCuMVORPA12QnA==',
        id: 608,
        code: 'hzero.site.sys.doc-management',
        name: '单据权限管理',
        quickIndex: 'DJQXGL',
        level: 'site',
        parentId: 473,
        type: 'dir',
        sort: 70,
        icon: 'data-platform',
        route: '',
        parentName: '系统管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWDzr9a1a6XrOfAiCOA8UyWdlJauhGpw4+TUxM8tJXlUQ==',
            id: 609,
            code: 'hzero.site.sys.doc-management.doc-dimension',
            name: '单据维度',
            quickIndex: 'DJQXWDDY',
            level: 'site',
            parentId: 608,
            type: 'menu',
            sort: 0,
            icon: 'business-settings',
            route: '/hiam/doc-dimension',
            parentName: '单据权限管理',
            viewCode: 'sys.doc-management.doc-dimension',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUYeNymehirjkMptV7RKlEddfNGgRFSSRzaLY5PKq79Ag==',
            id: 611,
            code: 'hzero.site.sys.doc-type',
            name: '单据权限',
            quickIndex: 'DJQX',
            level: 'site',
            parentId: 608,
            type: 'menu',
            sort: 80,
            icon: 'edit',
            route: '/hiam/doc-type',
            parentName: '单据权限管理',
            viewCode: 'sys.doc-type',
          },
        ],
        viewCode: 'sys.doc-management',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHX+V+sf0LxqNMmL3d5dIOOoS2kvAZjuXEI6uV7/bVWlwg==',
        id: 620,
        code: 'hzero.site.sys.login-manage',
        name: '登录管理',
        quickIndex: 'DLGL',
        level: 'site',
        parentId: 473,
        type: 'dir',
        sort: 80,
        icon: 'dispatch-platform',
        route: '',
        parentName: '系统管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHX+V+sf0LxqNMmL3d5dIOOodfNGgRFSSRzaLY5PKq79Ag==',
            id: 621,
            code: 'hzero.site.sys.login-manage.login-log',
            name: '用户登录日志',
            quickIndex: 'YHDLRZ',
            level: 'site',
            parentId: 620,
            type: 'menu',
            sort: 10,
            icon: 'id-card',
            route: '/hpfm/platform-log',
            parentName: '登录管理',
            viewCode: 'sys.login-manage.login-log',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHX+V+sf0LxqNMmL3d5dIOOofzraweJ1UiwIugDmaojZlw==',
            id: 624,
            code: 'hzero.site.sys.login-manage.online-user',
            name: '在线用户',
            quickIndex: 'ZXYH',
            level: 'site',
            parentId: 620,
            type: 'menu',
            sort: 20,
            icon: 'cloud-management',
            route: '/hpfm/online',
            parentName: '登录管理',
            viewCode: 'sys.login-manage.online-user',
          },
        ],
        viewCode: 'sys.login-manage',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWROCPnu3DWvALRJ2CFr+s0cJEocQhBDCuMVORPA12QnA==',
        id: 588,
        code: 'hzero.site.sys.data-group',
        name: '数据组管理',
        quickIndex: 'SJZGL',
        level: 'site',
        parentId: 473,
        type: 'dir',
        sort: 90,
        icon: 'data-platform',
        route: '',
        parentName: '系统管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHV//TtPJ17Q0SA+J7cHB+dXcJEocQhBDCuMVORPA12QnA==',
            id: 598,
            code: 'hzero.site.sys.data-group.management',
            name: '数据组管理',
            quickIndex: 'SJZGL',
            level: 'site',
            parentId: 588,
            type: 'menu',
            sort: 10,
            icon: 'business-settings',
            route: '/hpfm/data-group',
            parentName: '数据组管理',
            viewCode: 'sys.data-group.management',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWROCPnu3DWvALRJ2CFr+s0dlJauhGpw4+TUxM8tJXlUQ==',
            id: 589,
            code: 'hzero.site.sys.data-group.dimension-config',
            name: '数据维度配置',
            quickIndex: 'SJWDPZ',
            level: 'site',
            parentId: 588,
            type: 'menu',
            sort: 20,
            icon: 'business-settings',
            route: '/hpfm/data-dimension-config',
            parentName: '数据组管理',
            viewCode: 'sys.data-group.dimension-config',
          },
        ],
        viewCode: 'sys.data-group',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWr6t64C0zB5iEQlCyzeYFBdfNGgRFSSRzaLY5PKq79Ag==',
        id: 771,
        code: 'hzero.site.sys.server-manage',
        name: '服务器管理',
        quickIndex: 'FFQGL',
        level: 'site',
        parentId: 473,
        type: 'dir',
        sort: 100,
        icon: 'service-governance',
        route: '',
        parentName: '系统管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWdMNb0CDU5gqtZWWPFCZi6mAPGtD09/n9T6F05Qv8NhA==',
            id: 782,
            code: 'hzero.site.sys.server-manage.server-define',
            name: '服务器定义',
            quickIndex: 'FFQDY',
            level: 'site',
            parentId: 771,
            type: 'menu',
            sort: 0,
            icon: 'cloud-management',
            route: '/hpfm/server-define',
            parentName: '服务器管理',
            viewCode: 'sys.server-manage.server-define',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWr6t64C0zB5iEQlCyzeYFBmAPGtD09/n9T6F05Qv8NhA==',
            id: 772,
            code: 'hzero.site.sys.server-manage.server-cluster',
            name: '服务器集群定义',
            quickIndex: 'FWQPZ',
            level: 'site',
            parentId: 771,
            type: 'menu',
            sort: 1,
            icon: 'business-settings',
            route: '/hpfm/server-cluster',
            parentName: '服务器管理',
            viewCode: 'sys.server-manage.server-cluster',
          },
        ],
        viewCode: 'sys.server-manage',
      },
    ],
    viewCode: 'sys',
  },
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWtzqb6/t7EDSRXbpRyQpDxfzraweJ1UiwIugDmaojZlw==',
    id: 184,
    code: 'hzero.site.dev',
    name: '开发管理',
    quickIndex: 'KFGL',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 20,
    icon: 'development-management',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVAF/QUjnMKiRPW68FNw1bdHQmcSI3uA/b1hoOOoXazKw==',
        id: 317,
        code: 'hzero.site.dev.rule-mgr',
        name: '规则管理',
        quickIndex: 'GZGL',
        level: 'site',
        parentId: 184,
        type: 'dir',
        sort: 0,
        icon: 'organization-management',
        route: '',
        parentName: '开发管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVQKmrSsAoms8Wj+5nk4/y7fzraweJ1UiwIugDmaojZlw==',
            id: 334,
            code: 'hzero.site.dev.rule-engine',
            name: '规则脚本',
            quickIndex: 'GZYQ',
            level: 'site',
            parentId: 317,
            type: 'menu',
            sort: 100,
            icon: 'commodity-management',
            route: '/hpfm/rule-engine',
            parentName: '规则管理',
            viewCode: 'dev.rule-engine',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVAF/QUjnMKiRPW68FNw1bdcJEocQhBDCuMVORPA12QnA==',
            id: 318,
            code: 'hzero.site.dev.event',
            name: '事件规则',
            quickIndex: 'SJGZ',
            level: 'site',
            parentId: 317,
            type: 'menu',
            sort: 110,
            icon: 'expert-library',
            route: '/hpfm/event',
            parentName: '规则管理',
            viewCode: 'dev.event',
          },
        ],
        viewCode: 'dev.rule-mgr',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVfKMWq/EXyy96/N6EFj0oZfzraweJ1UiwIugDmaojZlw==',
        id: 214,
        code: 'hzero.site.dev.lang-mgr',
        name: '多语言管理',
        quickIndex: 'DYYGL',
        level: 'site',
        parentId: 184,
        type: 'dir',
        sort: 10,
        icon: 'text',
        route: '',
        parentName: '开发管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVfKMWq/EXyy96/N6EFj0oZ0hIAldj2A541yaV+WRHmmg==',
            id: 215,
            code: 'hzero.site.dev.language',
            name: '语言维护',
            quickIndex: 'YYWH',
            level: 'site',
            parentId: 214,
            type: 'menu',
            sort: 20,
            icon: 'offer',
            route: '/hpfm/languages',
            parentName: '多语言管理',
            viewCode: 'dev.language',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHU5xwXh/oTVaWUaGyMDjHTGS2kvAZjuXEI6uV7/bVWlwg==',
            id: 230,
            code: 'hzero.site.dev.prompt',
            name: '平台多语言',
            quickIndex: 'PTDYY',
            level: 'site',
            parentId: 214,
            type: 'menu',
            sort: 30,
            icon: 'purchase',
            route: '/hpfm/prompt',
            parentName: '多语言管理',
            viewCode: 'dev.prompt',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVGe3oIxNfMB5WdpTug58T1dfNGgRFSSRzaLY5PKq79Ag==',
            id: 221,
            code: 'hzero.site.dev.message',
            name: '返回消息管理',
            quickIndex: 'FHXXGL',
            level: 'site',
            parentId: 214,
            type: 'menu',
            sort: 40,
            icon: 'quality-business',
            route: '/hpfm/message',
            parentName: '多语言管理',
            viewCode: 'dev.message',
          },
        ],
        viewCode: 'dev.lang-mgr',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWtzqb6/t7EDSRXbpRyQpDxHQmcSI3uA/b1hoOOoXazKw==',
        id: 187,
        code: 'hzero.site.dev.ds-mgr',
        name: '数据源管理',
        quickIndex: 'SJYGL',
        level: 'site',
        parentId: 184,
        type: 'dir',
        sort: 50,
        icon: 'basic-data-management',
        route: '',
        parentName: '开发管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVCoo4rKPoCctXXqNBZX0n38m9i/qNPyO+5m5iiqVp7Dg==',
            id: 203,
            code: 'hzero.site.dev.ds',
            name: '数据源设置',
            quickIndex: 'SJYSZ',
            level: 'site',
            parentId: 187,
            type: 'menu',
            sort: 80,
            icon: 'reporting-platform',
            route: '/hpfm/data-source',
            parentName: '数据源管理',
            viewCode: 'dev.ds',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWtzqb6/t7EDSRXbpRyQpDxcJEocQhBDCuMVORPA12QnA==',
            id: 188,
            code: 'hzero.site.dev.data-source-driver',
            name: '数据源驱动',
            quickIndex: 'SJYQD',
            level: 'site',
            parentId: 187,
            type: 'menu',
            sort: 81,
            icon: 'development-management',
            route: '/hpfm/data-source-driver',
            parentName: '数据源管理',
            viewCode: 'dev.data-source-driver',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXQ3943rIcBn7tn0VHEYkw/8m9i/qNPyO+5m5iiqVp7Dg==',
            id: 193,
            code: 'hzero.site.dev.db',
            name: '数据库设置',
            quickIndex: 'SJKSZ',
            level: 'site',
            parentId: 187,
            type: 'menu',
            sort: 90,
            icon: 'development-management',
            route: '/hpfm/database',
            parentName: '数据源管理',
            viewCode: 'dev.db',
          },
        ],
        viewCode: 'dev.ds-mgr',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXuMMmZuvSBw5nVqL/Alx7LdfNGgRFSSRzaLY5PKq79Ag==',
        id: 241,
        code: 'hzero.site.dev.lov-mgr',
        name: '值集管理',
        quickIndex: 'ZJGL',
        level: 'site',
        parentId: 184,
        type: 'dir',
        sort: 50,
        icon: 'file-text',
        route: '',
        parentName: '开发管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWh1QqynvRMTXOD97kmq16Z0hIAldj2A541yaV+WRHmmg==',
            id: 255,
            code: 'hzero.site.dev.value-list',
            name: '值集配置',
            quickIndex: 'ZJPZ',
            level: 'site',
            parentId: 241,
            type: 'menu',
            sort: 60,
            icon: 'store-management',
            route: '/hpfm/value-list',
            parentName: '值集管理',
            viewCode: 'dev.value-list',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXuMMmZuvSBw5nVqL/Alx7LmAPGtD09/n9T6F05Qv8NhA==',
            id: 242,
            code: 'hzero.site.dev.lov',
            name: '值集视图配置',
            quickIndex: 'ZJSTPZ',
            level: 'site',
            parentId: 241,
            type: 'menu',
            sort: 70,
            icon: 'finance',
            route: '/hpfm/lov-view',
            parentName: '值集管理',
            viewCode: 'dev.lov',
          },
        ],
        viewCode: 'dev.lov-mgr',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVvqJY23sq4+pGqqFWYhqFCcJEocQhBDCuMVORPA12QnA==',
        id: 268,
        code: 'hzero.site.dev.personality',
        name: '个性化管理',
        quickIndex: 'GXHGL',
        level: 'site',
        parentId: 184,
        type: 'dir',
        sort: 50,
        icon: 'workflow-setting',
        route: '',
        parentName: '开发管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXDWh5X/9NyaJ+p6Q1R/2khpx1UUwAWVaOW/AHajC/F/w==',
            id: 286,
            code: 'hzero.site.dev.personality.customize',
            name: 'API 个性化',
            quickIndex: 'APIGXH',
            level: 'site',
            parentId: 268,
            type: 'menu',
            sort: 0,
            icon: 'cloud-management',
            route: '/hpfm/customize',
            parentName: '个性化管理',
            viewCode: 'dev.personality.customize',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXDWh5X/9NyaJ+p6Q1R/2khfzraweJ1UiwIugDmaojZlw==',
            id: 284,
            code: 'hzero.site.dev.personality.customer-management',
            name: '客户化管理',
            quickIndex: 'KHHGL',
            level: 'site',
            parentId: 268,
            type: 'menu',
            sort: 15,
            icon: 'workflow',
            route: '/hiam/customer-management',
            parentName: '个性化管理',
            viewCode: 'dev.personality.customer-management',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWwIH9ilm3jLaBcyiY/IFScpx1UUwAWVaOW/AHajC/F/w==',
            id: 306,
            code: 'hzero.site.dev.personality.flex-model',
            name: '弹性域模型',
            quickIndex: 'DXYMX',
            level: 'site',
            parentId: 268,
            type: 'menu',
            sort: 20,
            icon: 'service-governance',
            route: '/hpfm/flex-model',
            parentName: '个性化管理',
            viewCode: 'dev.personality.flex-model',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVAF/QUjnMKiRPW68FNw1bdmAPGtD09/n9T6F05Qv8NhA==',
            id: 312,
            code: 'hzero.site.dev.personality.flex-rule',
            name: '弹性域规则',
            quickIndex: 'DXYGZ',
            level: 'site',
            parentId: 268,
            type: 'menu',
            sort: 30,
            icon: 'sourcing',
            route: '/hpfm/flex-rule',
            parentName: '个性化管理',
            viewCode: 'dev.personality.flex-rule',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVdJYbpLt+YtoxBj4yOPvUC0hIAldj2A541yaV+WRHmmg==',
            id: 275,
            code: 'hzero.site.dev.dynamic-form',
            name: '表单配置管理',
            quickIndex: 'BDPZGL',
            level: 'site',
            parentId: 268,
            type: 'menu',
            sort: 130,
            icon: 'business-settings',
            route: '/hpfm/dynamic-form',
            parentName: '个性化管理',
            viewCode: 'dev.dynamic-form',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVvqJY23sq4+pGqqFWYhqFCdlJauhGpw4+TUxM8tJXlUQ==',
            id: 269,
            code: 'hzero.site.dev.api-fields',
            name: '接口字段维护',
            quickIndex: 'JKZDWH',
            level: 'site',
            parentId: 268,
            type: 'menu',
            sort: 140,
            icon: 'development-management',
            route: '/hiam/api-field',
            parentName: '个性化管理',
            viewCode: 'dev.api-fields',
          },
        ],
        viewCode: 'dev.personality',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUkF54sXFsNBhwMNXuDXLPgpx1UUwAWVaOW/AHajC/F/w==',
        id: 346,
        code: 'hzero.site.dev.static-text',
        name: '静态文本管理',
        quickIndex: 'JTWBGL',
        level: 'site',
        parentId: 184,
        type: 'menu',
        sort: 60,
        icon: 'cloud-service',
        route: '/hpfm/static-text',
        parentName: '开发管理',
        viewCode: 'dev.static-text',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWtzqb6/t7EDSRXbpRyQpDx0hIAldj2A541yaV+WRHmmg==',
        id: 185,
        code: 'hzero.site.dev.auth.ca',
        name: 'CA证书管理',
        level: 'site',
        parentId: 184,
        type: 'menu',
        sort: 70,
        route: '/hpfm/ca-management',
        parentName: '开发管理',
        viewCode: 'dev.auth.ca',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUAzxy9bxBlUN1vhx9fE+mUHQmcSI3uA/b1hoOOoXazKw==',
        id: 357,
        code: 'hzero.site.dev.sys-tools',
        name: '系统工具',
        quickIndex: 'XTGJ',
        level: 'site',
        parentId: 184,
        type: 'menu',
        sort: 80,
        icon: 'yuanquyunwei',
        route: '/hpfm/sys-tools',
        parentName: '开发管理',
        viewCode: 'dev.sys-tools',
      },
    ],
    viewCode: 'dev',
  },
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUjkY//60Izjhf0aMxoKrYgF3jrNI6Empbhu6xSUrFyXw==',
    id: 1342,
    code: 'hzero.site.msg',
    name: '消息管理',
    quickIndex: 'XXGL',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 30,
    icon: 'message-management',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUjkY//60Izjhf0aMxoKrYgaBC+LAfFw9no94Kx8kNc9Q==',
        id: 1381,
        code: 'hzero.site.msg.notices',
        name: '公告管理',
        quickIndex: 'GGGL',
        level: 'site',
        parentId: 1342,
        type: 'menu',
        sort: 10,
        icon: 'notice',
        route: '/hmsg/notices',
        parentName: '消息管理',
        viewCode: 'msg.notices',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQTs5N8+evDqDrwYipi7Blxvg==',
        id: 1412,
        code: 'hzero.site.msg.server',
        name: '账户配置',
        quickIndex: 'ZHPZ',
        level: 'site',
        parentId: 1342,
        type: 'dir',
        sort: 20,
        icon: 'basic-data-management',
        route: '',
        parentName: '消息管理',
        subMenus: [
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQTmEE51Eepclhsjn8gFM181Q==',
            id: 1415,
            code: 'hzero.site.msg.email',
            name: '邮箱账户',
            quickIndex: 'YXZH',
            level: 'site',
            parentId: 1412,
            type: 'menu',
            sort: 10,
            icon: 'reporting-platform',
            route: '/hmsg/email',
            parentName: '账户配置',
            viewCode: 'msg.email',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQT7olTLEcTwQ4mbv1tf1HlVg==',
            id: 1432,
            code: 'hzero.site.msg.sms-config',
            name: '短信配置',
            quickIndex: 'DXPZ',
            level: 'site',
            parentId: 1412,
            type: 'menu',
            sort: 20,
            icon: 'cloud-service',
            route: '/hmsg/sms-config',
            parentName: '账户配置',
            viewCode: 'msg.sms-config',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQToaLOun/aehPawxwmRQ4R0w==',
            id: 1430,
            code: 'hzero.site.msg.official-acounts-config',
            name: '微信公众号配置',
            quickIndex: 'WXGZHPZ',
            level: 'site',
            parentId: 1412,
            type: 'menu',
            sort: 30,
            icon: 'workflow-setting',
            route: '/hmsg/official-accounts-config',
            parentName: '账户配置',
            viewCode: 'msg.official-acounts-config',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQTbEbrr3rYMB7t8dMADD6I9Q==',
            id: 1439,
            code: 'hzero.site.msg.wechat-config',
            name: '微信企业号配置',
            quickIndex: 'WXQYHPZ',
            level: 'site',
            parentId: 1412,
            type: 'menu',
            sort: 40,
            icon: 'document-management',
            route: '/hmsg/wechat-config',
            parentName: '账户配置',
            viewCode: 'msg.wechat-config',
          },
          {
            _token:
              'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQTSQOqw7MZLwHezz3G+ZbD3g==',
            id: 1413,
            code: 'hzero.site.msg.ding-talk-config',
            name: '钉钉配置',
            quickIndex: 'DDPZ',
            level: 'site',
            parentId: 1412,
            type: 'menu',
            sort: 50,
            icon: 'icon-test5',
            route: '/hmsg/ding-talk-config',
            parentName: '账户配置',
            viewCode: 'msg.ding-talk-config',
          },
        ],
        viewCode: 'msg.server',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUjkY//60Izjhf0aMxoKrYgQV35SWa6t7zJLirLw/khBQ==',
        id: 1355,
        code: 'hzero.site.msg.message-template',
        name: '消息模板',
        quickIndex: 'XXMB',
        level: 'site',
        parentId: 1342,
        type: 'menu',
        sort: 60,
        icon: 'offer',
        route: '/hmsg/message-template',
        parentName: '消息管理',
        viewCode: 'msg.message-template',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQTCEkuV+Dtsq4XZ/k/xkz7nw==',
        id: 1441,
        code: 'hzero.site.msg.template-service',
        name: '消息发送配置',
        quickIndex: 'XXFSPZ',
        level: 'site',
        parentId: 1342,
        type: 'menu',
        sort: 70,
        icon: 'notice',
        route: '/hmsg/send-config',
        parentName: '消息管理',
        viewCode: 'msg.template-service',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUjkY//60Izjhf0aMxoKrYgaOjDBGCYfgpJcBL1NOWdOg==',
        id: 1392,
        code: 'hzero.site.msg.receive-config',
        name: '消息接收配置',
        quickIndex: 'XXJSPZ',
        level: 'site',
        parentId: 1342,
        type: 'menu',
        sort: 80,
        icon: 'finance',
        route: '/hmsg/receive-config',
        parentName: '消息管理',
        viewCode: 'msg.receive-config',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQTc012kJ9FZgYGsapkFeRb8g==',
        id: 1403,
        code: 'hzero.site.msg.receiver-group',
        name: '接收组维护',
        quickIndex: 'JSZWH',
        level: 'site',
        parentId: 1342,
        type: 'menu',
        sort: 90,
        icon: 'quality-business',
        route: '/hmsg/receiver-type',
        parentName: '消息管理',
        viewCode: 'msg.receiver-group',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUjkY//60Izjhf0aMxoKrYgNzCaemnLXBi+VrdVLtVLSQ==',
        id: 1343,
        code: 'hzero.site.msg.message-query',
        name: '消息监控',
        quickIndex: 'XXJK',
        level: 'site',
        parentId: 1342,
        type: 'menu',
        sort: 100,
        icon: 'expert-library',
        route: '/hmsg/message-query',
        parentName: '消息管理',
        viewCode: 'msg.message-query',
      },
    ],
    viewCode: 'msg',
  },
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVMLt1uQnAe0h3CwlugD2uMmEE51Eepclhsjn8gFM181Q==',
    id: 1215,
    code: 'hzero.site.file',
    name: '文件管理',
    quickIndex: 'WJGL',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 40,
    icon: 'document-management',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVMLt1uQnAe0h3CwlugD2uMVxo88TE+DJ+jzG+53VtlGw==',
        id: 1244,
        code: 'hzero.site.file.storage',
        name: '文件存储配置',
        quickIndex: 'WJCCPZ',
        level: 'site',
        parentId: 1215,
        type: 'menu',
        sort: 10,
        icon: 'cloud-management',
        route: '/hfile/storage',
        parentName: '文件管理',
        viewCode: 'file.storage',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVMLt1uQnAe0h3CwlugD2uMTYvKXJw7MBVV5syqg6LMAQ==',
        id: 1225,
        code: 'hzero.site.file.file-upload',
        name: '文件上传配置',
        quickIndex: 'WJSCPZ',
        level: 'site',
        parentId: 1215,
        type: 'menu',
        sort: 20,
        icon: 'interface-management',
        route: '/hfile/file-upload',
        parentName: '文件管理',
        viewCode: 'file.file-upload',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVMLt1uQnAe0h3CwlugD2uM1uzJHDl/Vim4g8gY2A7hlA==',
        id: 1216,
        code: 'hzero.site.file.aggregate',
        name: '文件汇总查询',
        quickIndex: 'WJHZCX',
        level: 'site',
        parentId: 1215,
        type: 'menu',
        sort: 30,
        icon: 'expert-library',
        route: '/hfile/file-aggregate',
        parentName: '文件管理',
        viewCode: 'file.aggregate',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVMLt1uQnAe0h3CwlugD2uMF3jrNI6Empbhu6xSUrFyXw==',
        id: 1242,
        code: 'hzero.site.file.site.file.edit-log',
        name: '文件编辑日志',
        quickIndex: 'WJBJRZ',
        level: 'site',
        parentId: 1215,
        type: 'menu',
        sort: 40,
        icon: 'partner',
        route: '/hfile/edit-log',
        parentName: '文件管理',
        viewCode: 'file.site.file.edit-log',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVMLt1uQnAe0h3CwlugD2uMBpbgT/W0qqZWmhqiJXe7KQ==',
        id: 1234,
        code: 'hzero.site.file.server-upload',
        name: '服务器上传配置',
        quickIndex: 'FWQSCPZ',
        level: 'site',
        parentId: 1215,
        type: 'menu',
        sort: 50,
        icon: 'dispatch-platform',
        route: '/hfile/server-upload',
        parentName: '文件管理',
        viewCode: 'file.server-upload',
      },
    ],
    viewCode: 'file',
  },
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQTphUrqaPx5dbVSIUlaV5bpg==',
    id: 1485,
    code: 'hzero.site.schedule',
    name: '调度服务',
    quickIndex: 'DDFW',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 50,
    icon: 'scheduling-service',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9Qcb7t1bS1HyFNagtzfmKkQ==',
        id: 1523,
        code: 'hzero.site.schedule.job-group',
        name: '执行器管理',
        quickIndex: 'ZXQGL',
        level: 'site',
        parentId: 1485,
        type: 'menu',
        sort: 1,
        icon: 'transaction-management',
        route: '/hsdr/job-group',
        parentName: '调度服务',
        viewCode: 'schedule.job-group',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9BpbgT/W0qqZWmhqiJXe7KQ==',
        id: 1534,
        code: 'hzero.site.schedule.job-info',
        name: '调度任务',
        quickIndex: 'DDRW',
        level: 'site',
        parentId: 1485,
        type: 'menu',
        sort: 2,
        icon: 'development-management',
        route: '/hsdr/job-info',
        parentName: '调度服务',
        viewCode: 'schedule.job-info',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9wK0XWVlTn3jEK87nP0CnMA==',
        id: 1556,
        code: 'hzero.site.schedule.job-log',
        name: '调度日志',
        quickIndex: 'DDRZ',
        level: 'site',
        parentId: 1485,
        type: 'menu',
        sort: 3,
        icon: 'reporting-platform',
        route: '/hsdr/job-log',
        parentName: '调度服务',
        viewCode: 'schedule.job-log',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9mEE51Eepclhsjn8gFM181Q==',
        id: 1515,
        code: 'hzero.site.schedule.executable',
        name: '可执行定义',
        quickIndex: 'KZXDY',
        level: 'site',
        parentId: 1485,
        type: 'menu',
        sort: 4,
        icon: 'document-management',
        route: '/hsdr/executable',
        parentName: '调度服务',
        viewCode: 'schedule.executable',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9YJ+EyUAnucoo+O/7osfZKw==',
        id: 1501,
        code: 'hzero.site.schedule.concurrent',
        name: '请求定义',
        quickIndex: 'QQDY',
        level: 'site',
        parentId: 1485,
        type: 'menu',
        sort: 5,
        icon: 'partner',
        route: '/hsdr/concurrent',
        parentName: '调度服务',
        viewCode: 'schedule.concurrent',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXFWbqvd8jw3MJ8syet0mQT4YfdoH2XBOLDvLfS8rIVNQ==',
        id: 1486,
        code: 'hzero.site.schedule.conc-request',
        name: '并发请求',
        quickIndex: 'BFQQ',
        level: 'site',
        parentId: 1485,
        type: 'menu',
        sort: 60,
        icon: 'group',
        route: '/hsdr/conc-request',
        parentName: '调度服务',
        viewCode: 'schedule.conc-request',
      },
    ],
    viewCode: 'schedule',
  },
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXDiTDq+aZEurcx5ipH37tEHQmcSI3uA/b1hoOOoXazKw==',
    id: 377,
    code: 'hzero.site.mdm',
    name: '基础数据管理',
    quickIndex: 'JCSJGL',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 60,
    icon: 'basic-data-management',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXDiTDq+aZEurcx5ipH37tEcJEocQhBDCuMVORPA12QnA==',
        id: 378,
        code: 'hzero.site.mdm.bank',
        name: '银行定义',
        quickIndex: 'YHDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 1,
        icon: 'cloud-management',
        route: '/hpfm/mdm/bank',
        parentName: '基础数据管理',
        viewCode: 'mdm.bank',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWboj+E0KBAvWtv5yTk98dd0hIAldj2A541yaV+WRHmmg==',
        id: 395,
        code: 'hzero.site.mdm.country',
        name: '国家定义',
        quickIndex: 'GJDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 2,
        icon: 'group',
        route: '/hpfm/mdm/country',
        parentName: '基础数据管理',
        viewCode: 'mdm.country',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWS275ZeX/IO3GWGEakcsXKdlJauhGpw4+TUxM8tJXlUQ==',
        id: 409,
        code: 'hzero.site.mdm.currency',
        name: '币种定义',
        quickIndex: 'BZDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 3,
        icon: 'enterprise-certification',
        route: '/hpfm/mdm/currency',
        parentName: '基础数据管理',
        viewCode: 'mdm.currency',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVKUW+JY4VGk0XOWioacB56mAPGtD09/n9T6F05Qv8NhA==',
        id: 452,
        code: 'hzero.site.mdm.tax-rate',
        name: '税率定义',
        quickIndex: 'SLDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 4,
        icon: 'development-management',
        route: '/hpfm/mdm/tax-rate',
        parentName: '基础数据管理',
        viewCode: 'mdm.tax-rate',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUwrBXodpwTiZfXZcbahQ+vcJEocQhBDCuMVORPA12QnA==',
        id: 438,
        code: 'hzero.site.mdm.rate-type',
        name: '汇率类型定义',
        quickIndex: 'HLLXDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 5,
        icon: 'dispatch-platform',
        route: '/hpfm/mdm/rate-type',
        parentName: '基础数据管理',
        viewCode: 'mdm.rate-type',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUwrBXodpwTiZfXZcbahQ+vHQmcSI3uA/b1hoOOoXazKw==',
        id: 437,
        code: 'hzero.site.mdm.rate',
        name: '汇率定义',
        quickIndex: 'HLDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 6,
        icon: 'commodity-management',
        route: '/hpfm/mdm/rate',
        parentName: '基础数据管理',
        viewCode: 'mdm.rate',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHW/FDSIuPUUuD9N4vXtRB/NS2kvAZjuXEI6uV7/bVWlwg==',
        id: 460,
        code: 'hzero.site.mdm.uom-type',
        name: '计量单位类型定义',
        quickIndex: 'JLDWLXDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 7,
        icon: 'organization-management',
        route: '/hpfm/mdm/uom-type',
        parentName: '基础数据管理',
        viewCode: 'mdm.uom-type',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHVKUW+JY4VGk0XOWioacB56dlJauhGpw4+TUxM8tJXlUQ==',
        id: 459,
        code: 'hzero.site.mdm.uom',
        name: '计量单位定义',
        quickIndex: 'JLDWDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 8,
        icon: 'business-settings',
        route: '/hpfm/mdm/uom',
        parentName: '基础数据管理',
        viewCode: 'mdm.uom',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHXXPPVLtDInFhQN9VVKzpUSHQmcSI3uA/b1hoOOoXazKw==',
        id: 427,
        code: 'hzero.site.mdm.period',
        name: '期间定义',
        quickIndex: 'QJDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 9,
        icon: 'offer',
        route: '/hpfm/mdm/period',
        parentName: '基础数据管理',
        viewCode: 'mdm.period',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHWOpMfpWoywKFw40tMrcMex0hIAldj2A541yaV+WRHmmg==',
        id: 385,
        code: 'hzero.site.mdm.calendar',
        name: '日历定义',
        quickIndex: 'RLDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 10,
        icon: 'store-management',
        route: '/hpfm/mdm/calendar',
        parentName: '基础数据管理',
        viewCode: 'mdm.calendar',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHV0lCJlsdey0OQvuOHpFqZ2px1UUwAWVaOW/AHajC/F/w==',
        id: 416,
        code: 'hzero.site.mdm.industry-category',
        name: '国标品类定义',
        quickIndex: 'GBPLDY',
        level: 'site',
        parentId: 377,
        type: 'menu',
        sort: 11,
        icon: 'quality-business',
        route: '/hpfm/mdm/industry-category',
        parentName: '基础数据管理',
        viewCode: 'mdm.industry-category',
      },
    ],
    viewCode: 'mdm',
  },
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9P9nJ2BQqSS79THwVb3cgNQ==',
    id: 1574,
    code: 'hzero.site.himp',
    name: '通用导入',
    quickIndex: 'TYDR',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 70,
    icon: 'import',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHURKcmpH9O8BuYh9Jy9UAI9Fnrvl/BJXeasqnBvQSi1Mg==',
        id: 1575,
        code: 'hzero.site.himp.template',
        name: '导入模板管理',
        quickIndex: 'DRMBGL',
        level: 'site',
        parentId: 1574,
        type: 'menu',
        sort: 0,
        icon: 'import',
        route: '/himp/template',
        parentName: '通用导入',
        viewCode: 'himp.template',
      },
    ],
    viewCode: 'himp',
  },
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+PcXhdkKzZY+kPI/Jv6l3/A==',
    id: 1187,
    code: 'hzero.site.asgard',
    name: '事务管理',
    quickIndex: 'SWGL',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 130,
    icon: 'transaction-management',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+MvCsf63M6dq/+inmsL00OQ==',
        id: 1188,
        code: 'hzero.site.asgard.saga',
        name: '事务定义',
        quickIndex: 'SWDY',
        level: 'site',
        parentId: 1187,
        type: 'menu',
        sort: 0,
        icon: 'purchase',
        route: '/hagd/saga',
        parentName: '事务管理',
        viewCode: 'asgard.saga',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+ZVp1JItX/5tuPPs6G0ZjzQ==',
        id: 1189,
        code: 'hzero.site.asgard.saga-instance',
        name: '事务实例',
        quickIndex: 'SWSL',
        level: 'site',
        parentId: 1187,
        type: 'menu',
        sort: 10,
        icon: 'business-settings',
        route: '/hagd/saga-instance',
        parentName: '事务管理',
        viewCode: 'asgard.saga-instance',
      },
    ],
    viewCode: 'asgard',
  },
  {
    _token:
      'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+maWEx3R2yGeP7FiRpxTiWw==',
    id: 1152,
    code: 'hzero.site.config',
    name: '平台治理',
    quickIndex: 'PTZL',
    level: 'site',
    parentId: 0,
    type: 'root',
    sort: 150,
    icon: 'service-governance',
    route: '',
    subMenus: [
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+Fnrvl/BJXeasqnBvQSi1Mg==',
        id: 1175,
        code: 'hzero.site.config.service-manage',
        name: '服务管理',
        quickIndex: 'FWGL',
        level: 'site',
        parentId: 1152,
        type: 'menu',
        sort: 10,
        icon: 'service-governance',
        route: '/hadm/service',
        parentName: '平台治理',
        viewCode: 'config.service-manage',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+XpjplNjD70LWspM6+Rewhg==',
        id: 1170,
        code: 'hzero.site.config.service-config',
        name: '服务配置',
        quickIndex: 'FWPZ',
        level: 'site',
        parentId: 1152,
        type: 'menu',
        sort: 30,
        icon: 'workflow-setting',
        route: '/hadm/config',
        parentName: '平台治理',
        viewCode: 'config.service-config',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+5Gak6FG9po52fXiv+jPWHg==',
        id: 1160,
        code: 'hzero.site.config.hzero.zuul-limit',
        name: '限流规则',
        quickIndex: 'XLGZ',
        level: 'site',
        parentId: 1152,
        type: 'menu',
        sort: 40,
        icon: 'basic-data-management',
        route: '/hadm/rate-limit',
        parentName: '平台治理',
        viewCode: 'config.hzero.zuul-limit',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+QV35SWa6t7zJLirLw/khBQ==',
        id: 1155,
        code: 'hzero.site.config.hzero.hystrix',
        name: '熔断规则',
        quickIndex: 'RDGZ',
        level: 'site',
        parentId: 1152,
        type: 'menu',
        sort: 50,
        icon: 'portal-management',
        route: '/hadm/hystrix',
        parentName: '平台治理',
        viewCode: 'config.hzero.hystrix',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+K2XWcoRXl/rNCA7t9erYLg==',
        id: 1153,
        code: 'hzero.site.config.api-limit',
        name: 'API访问控制',
        quickIndex: 'APIGK',
        level: 'site',
        parentId: 1152,
        type: 'menu',
        sort: 60,
        route: '/hadm/api-limit',
        parentName: '平台治理',
        viewCode: 'config.api-limit',
      },
      {
        _token:
          'keUs24wdEJx8Lgl9KbyHaW3Pyg8anglhvllGobP5iHUXL7dD9oPk4h2rYImZ41u+EMDkzIpPuRhCpuv6f0KAXA==',
        id: 1168,
        code: 'hzero.site.config.inner.admin.console',
        name: '服务监控控制台',
        quickIndex: 'FWJKKZT',
        level: 'site',
        parentId: 1152,
        type: 'inner-link',
        sort: 70,
        icon: 'dispatch-platform',
        route: '/hadm',
        parentName: '平台治理',
        viewCode: 'hzero.site.config.inner.admin.console',
      },
    ],
    viewCode: 'config',
  },
];
