/**
 * FilterForm - 搜索框
 * @date: 2020-5-21
 * @author: jrq <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2019, Hand
 */
import React from 'react';
import { connect } from 'dva';
import { Form, Select, Row, Col, Button, Input } from 'hzero-ui';
import intl from 'utils/intl';
import {
  SEARCH_FORM_CLASSNAME,
  FORM_COL_4_LAYOUT,
  SEARCH_COL_CLASSNAME,
  SEARCH_FORM_ITEM_LAYOUT,
  SEARCH_FORM_ROW_LAYOUT,
} from 'utils/constants';

const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';

/**
 * 使用 Form.Item 组件
 */
const FormItem = Form.Item;

/**
 * 搜索框
 * @extends {Component} - React.Component
 * @reactProps {Object} siteList - 数据源
 * @reactProps {Object} form - 表单对象
 * @return React.element
 */
@connect(({ maintainNumber, loading }) => ({
  maintainNumber,
  fetchMessageLoading: loading.effects['maintainNumber/fetchMaintainNumberList'],
}))
@Form.create({ fieldNameProp: null })
export default class FilterForm extends React.Component {
  constructor(props) {
    super(props);
    props.onRef(this);
    this.state = {
      objectCode: undefined,
    };
  }

  state = {
    expandForm: false,
  };

  /**
   * 查询数据
   * @param {object} page 页面基本信息数据
   */
  fetchQueryList = pagination => {
    const {
      form,
      dispatch,
      maintainNumber: { maintainNumberPagination = {} },
    } = this.props;
    const { objectCode } = this.state;
    form.validateFields((err, fieldsValue) => {
      if (!err) {
        const { pageSize } = maintainNumberPagination;
        dispatch({
          type: 'maintainNumber/updateState',
          payload: {
            maintainNumberParams: {
              ...fieldsValue,
              objectCode,
            },
          },
        });
        dispatch({
          type: 'maintainNumber/fetchMaintainNumberList',
          payload: {
            ...fieldsValue,
            page: pagination || { pageSize },
          },
        });
      }
    });
  };

  /**
   * 重置form表单
   */
  handleFormReset = () => {
    const { form } = this.props;
    form.resetFields();
  };

  // 查询条件展开/收起
  toggleForm = () => {
    const { expandForm } = this.state;
    this.setState({ expandForm: !expandForm });
  };

  handleChange = (val, values) => {
    this.setState({
      objectCode: val ? values.objectCode : undefined,
    });
  };

  /**
   * 渲染方法
   * @returns
   */
  render() {
    const { expandForm } = this.state;
    const { form } = this.props;
    const { getFieldDecorator } = form;
    return (
      <Form className={SEARCH_FORM_CLASSNAME}>
        <Row {...SEARCH_FORM_ROW_LAYOUT}>
          <Col {...FORM_COL_4_LAYOUT}>
            <Form.Item
              {...SEARCH_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.objectId`).d('编码对象')}
            >
              {getFieldDecorator('objectCode')(<Input />)}
            </Form.Item>
          </Col>
          <Col {...FORM_COL_4_LAYOUT}>
            <Form.Item
              {...SEARCH_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.objectTypeList`).d('对象类型')}
            >
              {getFieldDecorator('objectTypeCodeList')(
                <Select
                  mode="tags"
                  style={{ width: '100%' }}
                  tokenSeparators={[',', '，']}
                  dropdownStyle={{ display: 'none' }}
                  allowClear
                />,
              )}
            </Form.Item>
          </Col>
          <Col {...FORM_COL_4_LAYOUT}>
            <Form.Item
              {...SEARCH_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.siteCodeList`).d('站点编码')}
            >
              {getFieldDecorator('siteCodeList')(
                <Select
                  mode="tags"
                  style={{ width: '100%' }}
                  tokenSeparators={[',', '，']}
                  dropdownStyle={{ display: 'none' }}
                  allowClear
                />,
              )}
            </Form.Item>
          </Col>
          <Col {...FORM_COL_4_LAYOUT} className={SEARCH_COL_CLASSNAME}>
            <FormItem>
              <Button onClick={this.toggleForm}>
                {expandForm
                  ? intl.get('tarzan.common.button.lessQueries').d('收起查询')
                  : intl.get(`tarzan.common.button.moreQueries`).d('更多查询')}
              </Button>
              <Button onClick={this.handleFormReset}>
                {intl.get('tarzan.common.button.reset').d('重置')}
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                onClick={() => {
                  this.fetchQueryList();
                }}
              >
                {intl.get('tarzan.common.button.search').d('查询')}
              </Button>
            </FormItem>
          </Col>
        </Row>
        <Row {...SEARCH_FORM_ROW_LAYOUT} style={{ display: expandForm ? 'block' : 'none' }}>
          <Col {...FORM_COL_4_LAYOUT}>
            <Form.Item
              {...SEARCH_FORM_ITEM_LAYOUT}
              label={intl.get('tarzan.common.label.enableFlag').d('启用状态')}
            >
              {getFieldDecorator('enableFlag')(
                <Select style={{ width: '100%' }} allowClear>
                  <Select.Option value="Y">
                    {intl.get('tarzan.common.label.enable').d('启用')}
                  </Select.Option>
                  <Select.Option value="N">
                    {intl.get('tarzan.common.label.disable').d('禁用')}
                  </Select.Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col {...FORM_COL_4_LAYOUT}>
            <Form.Item
              {...SEARCH_FORM_ITEM_LAYOUT}
              label={intl.get('tarzan.common.label.initialFlag').d('启用状态')}
            >
              {getFieldDecorator('initialFlag')(
                <Select style={{ width: '100%' }} allowClear>
                  <Select.Option value="Y">
                    {intl.get('tarzan.common.label.yes').d('是')}
                  </Select.Option>
                  <Select.Option value="N">
                    {intl.get('tarzan.common.label.no').d('否')}
                  </Select.Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  }
}
