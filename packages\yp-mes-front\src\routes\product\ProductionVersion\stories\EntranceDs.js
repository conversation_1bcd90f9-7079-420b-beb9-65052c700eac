/**
 * @feature 生产版本维护-列表页的DS
 * @date 2021-8-31
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.product.productionVersion';
const tenantId = getCurrentOrganizationId();

/**
 * 列表和详情页
 */
const entranceDS = () => ({
  primaryKey: 'tagCode',
  autoQuery: false,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  queryFields: [
    {
      name: 'productionVersionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionVersionCode`).d('生产版本编码'),
    },
    {
      name: 'productionVersionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionVersionDesc`).d('生产版本描述'),
    },
    {
      name: 'router',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.router`).d('工艺路线编码'),
      lovCode: 'MT.METHOD.USER_SITE_ROUTER',
      ignore: 'always',
      multiple: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'routerIds',
      bind: 'router.routerId',
    },
    {
      name: 'bom',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.bom`).d('装配清单编码'),
      lovCode: 'MT.METHOD.USER_SITE_BOM',
      ignore: 'always',
      multiple: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'bomIds',
      bind: 'bom.bomId',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('是否有效'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'MT.YES_NO',
      falseValue: 'N',
      trueValue: 'Y',
    },
  ],
  fields: [
    {
      name: 'productionVersionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionVersionCode`).d('生产版本编码'),
    },
    {
      name: 'productionVersionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionVersionDesc`).d('生产版本描述'),
    },
    {
      name: 'originalProversionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.originalProversionCode`).d('原始生产版本编码'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('装配清单编码/版本'),
    },
    {
      name: 'valueAllowMissing',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.valueAllowMissing`).d('工艺路线编码/版本'),
    },
    {
      name: 'dateFrom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
    },
    {
      name: 'dateTo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const newData = {
        ...data,
        routerIds: data.routerIds.length ? (data.routerIds || []).join(',') : undefined,
        bomIds: data.bomIds.length ? (data.bomIds || []).join(',') : undefined,
      };
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-production-version/limit-property/ui`,
        data: newData,
        method: 'GET',
      };
    },
  },
});

export { entranceDS };
