import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentTenantId } from 'utils/utils';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { searchCopy } from '@utils/utils';

const modelPrompt = 'tarzan.hmes.inventoryTransfer';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'invTransferDocId',
    selection: DataSetSelection.multiple,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    queryDataSet: new DataSet({
      events: {
        update({ record, name, value }) {
          searchCopy(
            [
              'invTransferDocNums',
              'materialLotCodes',
            ],
            name,
            record,
            value,
          );
        }
      },
      fields: [
        {
          name: 'invTransferDocNums',
          multiple: true,
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.invTransferDocNum`).d('单据编码'),
        },
        {
          name: 'materialLotCodes',
          multiple: true,
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.materialLotCodes`).d('条码号'),
        },
        {
          name: 'locatorLov',
          multiple: true,
          ignore: FieldIgnore.always,
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.locator`).d('目标库位'),
          lovCode: 'HME.USER_LOCATOR',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'locatorIds',
          bind: 'locatorLov.locatorId',
        },
        {
          name: 'transferDocStatus',
          multiple: true,
          lookupCode: 'HME.INV_TRANSFER_DOC_STATUS',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.transferDocStatus`).d('单据状态'),
        },
        {
          name: 'userLov',
          multiple: true,
          ignore: FieldIgnore.always,
          type: FieldType.object,
          textField: 'realName',
          label: intl.get(`${modelPrompt}.user`).d('创建人'),
          lovCode: 'LOV_USER',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'userIds',
          bind: 'userLov.id',
        },
        {
          name: 'approveLov',
          multiple: true,
          ignore: FieldIgnore.always,
          type: FieldType.object,
          textField: 'realName',
          label: intl.get(`${modelPrompt}.approve`).d('审批人'),
          lovCode: 'LOV_USER',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'approveIds',
          bind: 'approveLov.id',
        },
        {
          name: 'creationDateFrom',
          max: 'creationDateTo',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
        },
        {
          name: 'creationDateTo',
          min: 'creationDateFrom',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
        },

      ]
    }),
    fields: [
      {
        name: 'invTransferDocNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.invTransferDocNum`).d('单据编码'),
      },
      {
        name: 'transferDocStatus',
        lookupCode: 'HME.INV_TRANSFER_DOC_STATUS',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.transferDocStatus`).d('单据状态'),
      },
      {
        name: 'siteCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'createdByRealName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.createdByRealName`).d('创建人'),
      },
      {
        name: 'approveRealName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.approveRealName`).d('审批人'),
      },
      {
        name: 'creationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'lastUpdatedByRealName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lastUpdatedByRealName`).d('最后更新人'),
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'POST',
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-inv-transfer/query/ui`,
        };
      },
    },
  });

export default listPageFactory;
