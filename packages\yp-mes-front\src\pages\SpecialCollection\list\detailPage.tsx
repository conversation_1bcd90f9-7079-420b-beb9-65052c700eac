import React, { useEffect, useState } from 'react';
import {
  Form,
  Row,
  Col,
  TextField,
  Button,
  Table,
  Lov,
  Modal,
  DateTimePicker,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { useDataSet } from 'utils/hooks';
import { RouteComponentProps } from 'react-router';
import { Collapse, Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import detailPageFactory from '../stores/detailPageDs';
import detailTable from '../stores/detailTableDs';
import numberListFactory from '../stores/numberListDs';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import axios from 'axios';
import NumberComponent from '../components/NumberComponent';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
const { Panel } = Collapse;

const modelPrompt = 'tarzan.ass.specialCollection';
interface RouterId {
  id: string;
}

const DetailPage: React.FC<RouteComponentProps<RouterId>> = ({ match: { params }, history }) => {
  const [edit, setEdit] = useState(false);

  const detailDs = useDataSet(detailPageFactory, 'specialCollectionDetail');

  const detailTableDs = useDataSet(detailTable, 'specialCollectionTable');

  const trueNumberDs = useDataSet(numberListFactory, 'trueNumber');

  const falseNumberDs = useDataSet(numberListFactory, 'falseNumber');

  useEffect(() => {
    if (params.id === 'create') {
      detailDs.create({}, 0);
      setEdit(true);
    } else {
      detailDs.setQueryParameter('keyId', params.id);
      detailDs.query();
      detailTableDs.setQueryParameter('keyId', params.id);
      detailTableDs.query();
    }
    return () => {
      detailDs.loadData([]);
      detailTableDs.loadData([]);
    };
  }, [params]);

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={() => handleEditModel()}
          disabled={!edit}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      name: 'editColumn',
      align: ColumnAlign.center,
      lock: ColumnLock.left,
      width: 100,
      hideable: false,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            disabled={!(edit && !record?.get('instructionDocLineId'))}
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
    },
    {
      name: 'ordinalLine',
      renderer: ({ record }) => {
        return !edit ? (
          <a disabled={!edit}>{record?.get('ordinalLine')}</a>
        ) : (
          record?.get('ordinalLine')
        );
      },
    },
    {
      name: 'decide',
      renderer: ({ record }) => {
        return (
          <a
            disabled={!edit}
            onClick={() => {
              handleEditModel(record);
            }}
          >
            {record?.get('decide')}
          </a>
        );
      },
    },
    {
      name: 'trueValue',
      renderer: ({ record }) => {
        return !edit ? (
          <a disabled={!edit}>{record?.get('trueValue')}</a>
        ) : (
          record?.get('trueValue')
        );
      },
    },
  ];

  const handleEditModel = (record?) => {
    if (record) {
      trueNumberDs.loadData(record.toData().trueValueList || []);
      falseNumberDs.loadData(record.toData().decideList || []);
    } else {
      detailTableDs.create({ valueType: 'VALUE', status: 'add' }, 0);
      record = detailTableDs.current;
      trueNumberDs.reset();
      falseNumberDs.reset();
    }
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.dataItemEdit`).d('数据项编辑'),
      drawer: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal',
      children: (
        <Form record={record} columns={1}>
          <NumberComponent
            title={intl.get(`${modelPrompt}.decide`).d('条件')}
            name="decide"
            dataSet={falseNumberDs}
            disabled={false}
            canEdit={true}
          />
          <NumberComponent
            title={intl.get(`${modelPrompt}.synchronizeData`).d('符合值')}
            name="trueValue"
            dataSet={trueNumberDs}
            disabled={false}
            canEdit={true}
          />
        </Form>
      ),
      onOk: () => dataItemDrawerSubmit(record),
      onCancel: () => {
        if (record.status === 'add') {
          detailTableDs.remove(record);
        }
        trueNumberDs.loadData([]);
        falseNumberDs.loadData([]);
      },
    });
  };

  const dataItemDrawerSubmit = async record => {
    if (!(await falseNumberDs?.validate())) return;
    const trueValueList = trueNumberDs.toData();
    const decideList = falseNumberDs.toData();
    console.log('123', trueValueList, decideList);
    record.set('trueValueList', trueValueList);
    record.set('decideList', decideList);
    if (trueValueList.length > 0) {
      trueValueList.map(item => {
        record.set('trueValue', item.dataValue);
      });
    }
    if (decideList.length > 0) {
      decideList.map(item => {
        record.set('decide', item.dataValue);
      });
    }
    const validate = await record.validate(true);
    if (!validate) {
      return false;
    }
  };

  // 删除表格某一行的回调
  const deleteRecord = async record => {
    const lineId = record.get('lineId');
    if (lineId) {
      const url = `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/hme-look-process/line/delete?lineId=${lineId}`;
      const res: any = await axios.get(url);
      if (res && res.success) {
        notification.success({});
        detailTableDs.remove(record);
      } else {
        notification.error({
          message: res.message,
        });
      }
    } else {
      detailTableDs.remove(record);
    }
  };

  const onHandleSave = async () => {
    const validate = await detailDs.validate();
    if (detailTableDs.toData().length > 0) {
      const validateTable = await detailTableDs.validate();
      if (validate && validateTable) {
        const createList = detailTableDs.created.map(item => item.toData());
        const updateList = detailTableDs.updated.map(item => item.toData());
        const url = `${
          BASIC.HMES_BASIC
        }/v1/${getCurrentOrganizationId()}/hme-look-process/total/save`;
        const res: any = await axios.post(url, {
          ...detailDs.toData()[0],
          lineList: [...createList, ...updateList],
        });
        if (res && res.success) {
          if (params.id === 'create') {
            history.push(`/hmes/specialCollection/detail/${res.rows}`);
          } else {
            detailDs.setQueryParameter('keyId', res.rows);
            detailTableDs.setQueryParameter('keyId', res.rows);
            detailDs.query();
            detailTableDs.query();
          }
          notification.success({});
          setEdit(false);
        } else {
          notification.error({
            message: res.message,
          });
        }
      }
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.title.requireLine`).d('请至少输入一条行信息'),
      });
    }
  };
  const onHandleEdit = () => {
    setEdit(true);
  };
  const onHandleCancel = () => {
    if (params.id === 'create') {
      history.push(`/hmes/specialCollection/list`);
    } else {
      setEdit(false);
      detailDs.reset();
      detailTableDs.reset();
    }
  };
  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.specialCollection`).d('特殊收集项校验（K值）')}
        backPath="/hmes/specialCollection/list"
      >
        {edit ? (
          <div>
            <Button onClick={() => onHandleCancel()}>
              {intl.get(`${modelPrompt}.title.cancel`).d('取消')}
            </Button>
            <Button color={ButtonColor.primary} onClick={onHandleSave}>
              {intl.get(`${modelPrompt}.title.save`).d('保存')}
            </Button>
          </div>
        ) : (
          <>
            <Button color={ButtonColor.primary} onClick={() => onHandleEdit()}>
              {intl.get(`${modelPrompt}.title.edit`).d('编辑')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['1']}>
          <Panel key="1" header={intl.get(`${modelPrompt}.title.baseInfo`).d('头信息')}>
            <Row type="flex" align="top" justify="space-between">
              <Col span={20}>
                <Form dataSet={detailDs} columns={3} labelWidth={112}>
                  <Lov name="tagLov" disabled={!edit} />
                  <TextField name="tagName" disabled={!edit} />
                  <Lov name="equipmentLov" disabled={!edit} />
                  <Lov name="materialLov" disabled={!edit} />
                  <Lov name="missingNcCodeLov" disabled={!edit} />
                  <Lov name="defaultNcCodeLov" disabled={!edit} />
                  <TextField name="userName" disabled={!edit} />
                  <DateTimePicker name="lastUpdateDate" disabled={!edit} />
                  <TextField name="remark" disabled={!edit} />
                  <Lov name="conditionTagCodeLov" disabled={!edit} />
                  <TextField name="conditionTagName" disabled />
                </Form>
              </Col>
            </Row>
          </Panel>
        </Collapse>
        <Collapse bordered={false} defaultActiveKey={['2']}>
          <Panel key="2" header={intl.get(`${modelPrompt}.title.lineMessage`).d('行信息')}>
            <Table
              dataSet={detailTableDs}
              columns={columns}
              key="demandManageTable"
              rowHeight={35}
              queryBar={TableQueryBarType.none}
              queryFieldsLimit={4} // 头部显示的查询字段的数量
              searchCode="demandManageTable" // 动态筛选条后端接口唯一编码
              customizedCode="demandManageTable" // 个性化编码
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default DetailPage;
