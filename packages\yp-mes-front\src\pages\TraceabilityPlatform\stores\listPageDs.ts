import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { DataSetSelection, FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { TARZAN_TZNI } from '@/utils/config';

const modelPrompt = 'tarzan.process.unitWork.model.unitWork';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    autoQuery: true,
    autoCreate: false,
    selection: DataSetSelection.multiple,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    primaryKey: 'ifaceId',
    queryFields: [
      {
        name: 'batchCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.batchCode`).d('批次号'),

      },
      {
        name: 'monomerCode',
        label: intl.get(`${modelPrompt}.monomerCode`).d('单体编号'),
        type: FieldType.string,
      },
      {
        name: 'monomerFacspecCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.monomerFacspecCode`).d('单体厂商规格'),
      },
      {
        name: 'moduleCode',
        label: intl.get(`${modelPrompt}.moduleCode`).d('模组编号'),
        type: FieldType.string,
      },
      {
        name: 'moduleFacspecCode',
        label: intl.get(`${modelPrompt}.moduleFacspecCode`).d('模组厂商规格'),
        type: FieldType.string,
      },
      {
        name: 'packFlow',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.packFlow`).d('去向企业角色编码'),
      },
      {
        name: 'packCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.packCode`).d('pack编号'),
      },
      {
        name: 'packFacspecCode',
        label: intl.get(`${modelPrompt}.packFacspecCode`).d('pack厂商规格'),
        type: FieldType.string,
      },
      {
        name: 'status',
        lookupCode: 'MT.INTERFACE_STATUS',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.status`).d('状态'),
      },
      {
        name: 'updateDateFrom',
        label: intl.get(`${modelPrompt}.updateDateFrom`).d('最后更新时间从'),
        type: FieldType.dateTime,
      },
      {
        name: 'updateDateTo',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.updateDateTo`).d('最后更新时间至'),
      },
    ],
    fields: [
      {
        name: 'userCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.userCode`).d('用户登录码'),
      },
      {
        name: 'batchCode',
        label: intl.get(`${modelPrompt}.batchCode`).d('批次号'),
        type: FieldType.string,
      },
      {
        name: 'monomerCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.monomerCode`).d('单体编号'),
      },
      {
        name: 'monomerFacspecCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.monomerFacspecCode`).d('单体厂商规格'),
      },
      {
        name: 'moduleCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.moduleCode`).d('模组编号'),
      },
      {
        name: 'moduleFacspecCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.moduleFacspecCode`).d('模组厂商规格'),
      },
      {
        name: 'packFlow',
        label: intl.get(`${modelPrompt}.packFlow`).d('去向企业角色编码'),
        type: FieldType.string,
      },
      {
        name: 'packCode',
        label: intl.get(`${modelPrompt}.packCode`).d('pack编号'),
        type: FieldType.string,
      },
      {
        name: 'packFacspecCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.packFacspecCode`).d('pack厂商规格'),
      },
      {
        name: 'status',
        lookupCode: 'MT.INTERFACE_STATUS',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.status`).d('状态'),
      },
      {
        name: 'msg',
        label: intl.get(`${modelPrompt}.msg`).d('处理消息'),
        type: FieldType.string,
      },
      {
        name: 'lastUpdateDate',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
        type: FieldType.string,
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${TARZAN_TZNI}/v1/${tenantId}/hme-pack-module-trace-ifaces/query`,
          method: 'POST',
        };
      },
    },
  });
export default listPageFactory
