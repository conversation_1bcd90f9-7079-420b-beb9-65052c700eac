/**
 * @Description: 实绩信息列表组件
 * @Author: <<EMAIL>>
 * @Date: 2021-07-26 15:34:14
 * @LastEditTime: 2022-09-08 14:04:42
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import intl from 'utils/intl';
import { DataSet, Tabs, Table, Progress } from 'choerodon-ui/pro';
import { Radio } from 'choerodon-ui';
import { workingListDS, bomListDS } from '../stores/ExecuteDetailListDS';
import styles from '../index.module.less';

const modelPrompt = 'tarzan.workshop.execute';

const { TabPane } = Tabs;

const ProductionOrderMgtDetailList = (props, ref) => {
  const { id } = props;

  const workingListDs = useMemo(() => {
    return new DataSet(workingListDS());
  }, []);
  const bomListDs = useMemo(() => {
    return new DataSet(bomListDS());
  }, []);

  const [workingSwitch, setWorkingSwitch] = useState('N');
  const [bomSwitch, setBomSwitch] = useState('N');

  const [workingCombineArr, setWorkingCombineArr] = useState([]);
  const [bomCombineArr, setBomCombineArr] = useState([]);

  useEffect(() => {
    workingListDs.queryParameter = {
      eoId: id,
      keyStepFlag: 'N',
    };
    workingListDs.query();
    bomListDs.queryParameter = {
      eoId: id,
      keyStepFlag: 'N',
    };
    bomListDs.query();
  }, [id]);

  useEffect(() => {
    if (workingListDs) {
      workingListDs.addEventListener('beforeLoad', handleWorkingListBeforeLoad);
    }
    if (bomListDs) {
      bomListDs.addEventListener('beforeLoad', handleBomListBeforeLoad);
    }
    return () => {
      if (workingListDs) {
        workingListDs.removeEventListener('beforeLoad', handleWorkingListBeforeLoad);
      }
      if (bomListDs) {
        bomListDs.removeEventListener('beforeLoad', handleBomListBeforeLoad);
      }
    };
  });

  const handleWorkingListBeforeLoad = ({ data }) => {
    if (data && data.length > 0) {
      const rowCombineArr = [];
      let currentName = null;
      let repeatNum = 0;
      let repeatStart = 0;
      for (let i = 0; i < data.length; i++) {
        const record = data[i];
        const { routerStepId } = record;
        if (currentName === null) {
          currentName = routerStepId;
          repeatNum = 1;
          repeatStart = i;
          rowCombineArr[repeatStart] = 1;
        } else if (currentName === routerStepId) {
          rowCombineArr[i] = 0;
          repeatNum++;
        } else {
          currentName = null;
          rowCombineArr[repeatStart] = repeatNum;
          repeatNum = 0;
          i--;
        }
        if (i === data.length - 1) {
          rowCombineArr[repeatStart] = repeatNum;
        }
      }
      setWorkingCombineArr(rowCombineArr);
    } else {
      setWorkingCombineArr([]);
    }
  };

  const handleBomListBeforeLoad = ({ data }) => {
    if (data && data.length > 0) {
      const rowCombineArr = [];
      let currentName = null;
      let repeatNum = 0;
      let repeatStart = 0;
      for (let i = 0; i < data.length; i++) {
        const record = data[i];
        const { bomComponentId } = record;
        if (currentName === null) {
          currentName = bomComponentId;
          repeatNum = 1;
          repeatStart = i;
          rowCombineArr[repeatStart] = 1;
        } else if (currentName === bomComponentId) {
          rowCombineArr[i] = 0;
          repeatNum++;
        } else {
          currentName = null;
          rowCombineArr[repeatStart] = repeatNum;
          repeatNum = 0;
          i--;
        }
        if (i === data.length - 1) {
          rowCombineArr[repeatStart] = repeatNum;
        }
      }
      setBomCombineArr(rowCombineArr);
    } else {
      setBomCombineArr([]);
    }
  };

  const workingColumns = [
    {
      name: 'sequence',
      width: 60,
      align: 'left',
      onCell({ record }) {
        const { index } = record;
        const rowSpan = workingCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'operationNameRevision',
    },
    {
      name: 'stepNameCode',
    },
    {
      name: 'operationQueueQty',
      align: 'right',
      onCell({ record }) {
        const { index } = record;
        const rowSpan = workingCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'wipQty',
      align: 'right',
      renderer: ({ record }) => {
        const { wipQty, wipScrappedQty } = record.data;
        return `${wipQty}/${wipScrappedQty}`;
      },
    },
    {
      name: 'completedQty',
      width: 100,
      align: 'right',
      onCell({ record }) {
        const { index } = record;
        const rowSpan = workingCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'scrapConfirmQty',
      width: 100,
      align: 'right',
      onCell({ record }) {
        const { index } = record;
        const rowSpan = workingCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
  ];

  const bomColumns = [
    {
      name: 'lineNumber',
      align: 'left',
      width: 60,
      onCell({ record }) {
        const { index } = record;
        const rowSpan = bomCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'materialCode',
      align: 'left',
      onCell({ record }) {
        const { index } = record;
        const rowSpan = bomCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'revisionCode',
      onCell({ record }) {
        const { index } = record;
        const rowSpan = bomCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'materialName',
      onCell({ record }) {
        const { index } = record;
        const rowSpan = bomCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'demandQty',
      align: 'right',
      onCell({ record }) {
        const { index } = record;
        const rowSpan = bomCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'assembleProgress',
      align: 'center',
      width: 200,
      onCell({ record }) {
        const { index } = record;
        const rowSpan = bomCombineArr[index];
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
      renderer: ({ value }) => {
        return (
          <Progress
            className={styles['progress-item']}
            value={(value * 100).toFixed(3) - 0}
            status={value < 1 ? 'active' : 'success'}
            size="large"
          />
        );
      },
    },
    {
      name: 'assembleMaterialCode',
    },
    {
      name: 'assembleMaterialRevisionCode',
    },
    {
      name: 'assembleMaterialName',
      width: 200,
    },
    {
      name: 'assembleQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'scrappedQty',
      width: 100,
      align: 'right',
    },
  ];

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    queryWorking: () => {
      workingListDs.query();
    },
    queryBom: () => {
      bomListDs.query();
    },
  }));

  const handlePageChangeWorking = e => {
    setWorkingSwitch(e.target.value);
    workingListDs.queryParameter = {
      eoId: id,
      keyStepFlag: e.target.value,
    };
    workingListDs.query();
  };

  const handlePageChangeBom = e => {
    setBomSwitch(e.target.value);
    bomListDs.queryParameter = {
      eoId: id,
      keyStepFlag: e.target.value,
    };
    bomListDs.query();
  };

  return (
    <Tabs defaultActiveKey="1">
      <TabPane tab={intl.get(`${modelPrompt}.achieveWorking`).d('加工实绩')} key="1">
        <div>
          <Radio.Group
            onChange={handlePageChangeWorking}
            value={workingSwitch}
            style={{ marginBottom: 8 }}
          >
            <Radio.Button value="Y">
              {intl.get(`${modelPrompt}.keyWorking`).d('关键工艺')}
            </Radio.Button>
            <Radio.Button value="N">
              {intl.get(`${modelPrompt}.allWorking`).d('全部工艺')}
            </Radio.Button>
          </Radio.Group>
        </div>
        <Table dataSet={workingListDs} columns={workingColumns} />
      </TabPane>
      <TabPane tab={intl.get(`${modelPrompt}.achieveBom`).d('装配实绩')} key="2">
        <div>
          <Radio.Group onChange={handlePageChangeBom} value={bomSwitch} style={{ marginBottom: 8 }}>
            <Radio.Button value="Y">
              {intl.get(`${modelPrompt}.keyComponent`).d('关键组件')}
            </Radio.Button>
            <Radio.Button value="N">
              {intl.get(`${modelPrompt}.allComponent`).d('全部组件')}
            </Radio.Button>
          </Radio.Group>
        </div>
        <Table dataSet={bomListDs} columns={bomColumns} />
      </TabPane>
    </Tabs>
  );
};

export default forwardRef(ProductionOrderMgtDetailList);
