import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

const modelPrompt = 'tarzan.process.routes.model.routes';
const tenantId = getCurrentOrganizationId();

/**
 * 详情页，header中已分配对象
 * @param serveCode
 */
const allocateObjectDS: (serveCode, serveCodeMid) => DataSetProps = (serveCode, midUrl) => ({
  primaryKey: 'objectId',
  queryUrl: `${serveCode}/v1/${tenantId}/mt-${midUrl ||
    'method'}-router/object/distribution/list/ui`,
  autoQuery: false,
  autoCreate: true,
  selection: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'objectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectCode`).d('对象编码'),
    },
    {
      name: 'objectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectTypeDesc`).d('对象类型'),
    },
    {
      name: 'objectStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectStatus`).d('对象状态'),
    },
  ],
  transport: {
    read: () => {
      // 查询请求的 axios 配置或 url 字符串
      return {
        url: `${serveCode}/v1/${tenantId}/mt-${midUrl ||
          'method'}-router/object/distribution/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { allocateObjectDS };
