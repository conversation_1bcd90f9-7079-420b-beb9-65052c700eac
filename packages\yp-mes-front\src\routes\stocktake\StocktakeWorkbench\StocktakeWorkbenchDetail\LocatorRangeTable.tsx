/**
 * @Description: 转移规则维护-表格
 * @Author: <<EMAIL>>
 * @Date: 2021-03-01 17:45:28
 * @LastEditTime: 2022-02-12 14:52:47
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { SelectionMode } from 'choerodon-ui/pro/lib/table/enum';

interface SupplierLocationInfoTableProps {
  ds: DataSet;
  id: string;
}

const LocatorRangeTable = (props: SupplierLocationInfoTableProps) => {
  const { ds, id } = props;

  const columns: ColumnProps[] = [
    {
      name: 'locatorCode',
    },
    {
      name: 'locatorName',
    },
    {
      name: 'locatorTypeDesc',
    },
  ];

  return (
    <Table
      selectionMode={id !== 'create' ? SelectionMode.none : SelectionMode.rowbox}
      dataSet={ds}
      columns={columns}
    />
  );
};

export default LocatorRangeTable;
