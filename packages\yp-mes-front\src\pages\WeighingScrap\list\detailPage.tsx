/*
 * @Author: 47844 <EMAIL>
 * @Date: 2024-12-09 15:30:52
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2024-12-12 09:13:55
 * @FilePath: \yp-mes-front\packages\yp-mes-front\src\pages\WeighingScrap\list\detailPage.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useState } from 'react';
import {
  Form,
  Row,
  Col,
  TextField,
  Button,
  Select,
  Table,
  Lov,
  NumberField,
  DateTimePicker,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { useDataSet } from 'utils/hooks';
import { RouteComponentProps } from 'react-router';
import { Collapse, Popconfirm } from 'choerodon-ui';
import detailPageFactory from '../stores/detailPageDs';
import detailTable from '../stores/detailTableDs';
import detailBomTable from '../stores/detailBomTableDs';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import { differenceBy, intersectionBy } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import axios from 'axios';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { queryIdpValue } from '@/services/api';

const tenantId = getCurrentOrganizationId();

const { Panel } = Collapse;

const modelPrompt = 'tarzan.ass.weighingScrap';
interface RouterId {
  id: string;
}

const DetailPage: React.FC<RouteComponentProps<RouterId>> = ({ match: { params }, history }) => {
  const [edit, setEdit] = useState(false);

  const [docTypeTag, setDocTypeTag] = useState('');
  const [docStatus, setDocStatus] = useState('NEW');
  const [isDeleteButtonDisabled, setDeleteButtonDisabled] = useState(true);

  const detailDs = useDataSet(detailPageFactory, 'weighingScrapDetail');

  const detailTableDs = useDataSet(detailTable, 'weighingScrapTable');

  const bomTableDs = useDataSet(detailBomTable, 'weighingScrapBom');

  useEffect(() => {
    bomTableDs.addEventListener('select', handleDataSetSelect);
    bomTableDs.addEventListener('unselect', handleDataSetSelect);
    return () => {
      bomTableDs.removeEventListener('select', handleDataSetSelect);
      bomTableDs.removeEventListener('unselect', handleDataSetSelect);
    };
  }, [bomTableDs, edit]);

  useEffect(() => {
    const fetchAndLoadData = async () => {
      if (params.id === 'create') {
        setEdit(true);
        detailDs.loadData([
          {
            docStatus: 'NEW',
          },
        ]);
      } else {
        detailDs.setQueryParameter('docId', params.id);
        const res = await detailDs.query();
        if (res) {
          const { lineList, docType, tag, meaning, scrapReason, scrapReasonMeaning } = res.rows;
          setDocStatus(res.rows.docStatus);
          detailDs.current!.set('docTypeCode', { value: docType, tag, meaning });
          detailTableDs.setState('tag', tag);

          queryIdpValue('HME_WEIGHING_SCRAP_DOC_TYPE').then(res => {
            if (res) {
              const result = res.filter(item => item.meaning === meaning);

              if (result.length > 0) {
                const tag = result[0].tag;
                setDocTypeTag(tag);
              }
            }
          });
          const processedLineListPromises = (lineList || []).map(async lineItem => {
            const lineItemMeaning = await getScrapReasonLovMeaning(lineItem.scrapReason);
            return {
              ...lineItem,
              scrapReasonLov: {
                value: scrapReason || lineItem.scrapReason,
                meaning: scrapReasonMeaning || lineItemMeaning,
              },
              tag,
            };
          });

          const processedLineList = await Promise.all(processedLineListPromises);

          detailTableDs.loadData(processedLineList);
          onHandleBomQuery('detail');
        }
      }
    };

    fetchAndLoadData();

    return () => {
      detailDs.loadData([]);
      detailTableDs.loadData([]);
      bomTableDs.loadData([]);
    };
  }, [params]);

  const columns: ColumnProps[] = [
    {
      name: 'materialCode',
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'materialDesc',
    },
    {
      name: 'lot',
    },
    {
      width: 150,
      name: 'primaryUomQty',
    },
    {
      name: 'scrapReasonLov',
      editor: record => record?.get('editing') || edit,
    },
    {
      name: 'scarpQty',
      editor: record => record?.get('editing') || edit,
    },
    {
      name: 'uomCode',
    },
    {
      width: 150,
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      renderer: ({ record }) =>
        (record!.getState('editing') || edit) && (
          <>
            {docTypeTag === '1' && (
              <Button
                color={ButtonColor.primary}
                funcType={FuncType.flat}
                onClick={() => onHandelRecordCopy(record)}
              >
                {intl.get('tarzan.common.button.copy').d('复制')}
              </Button>
            )}
            <Popconfirm
              title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
              onConfirm={() => deleteRecord(record)}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <Button color={ButtonColor.primary} funcType={FuncType.flat}>
                {intl.get('tarzan.common.button.delete').d('删除')}
              </Button>
            </Popconfirm>
          </>
        ),
    },
  ];

  const bomCcolumns: ColumnProps[] = [
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'qty',
    },
    {
      name: 'scarpQty',
    },
    {
      name: 'uomCode',
    },
  ];

  // bom组件表格选择回调
  const handleDataSetSelect = () => {
    setDeleteButtonDisabled(bomTableDs.selected.length === 0);
  };

  // 删除表格某一行的回调
  const deleteRecord = async record => {
    detailTableDs.remove(record);
  };

  // bom组件信息删除操作
  const onHandleBomDelete = e => {
    e.preventDefault();
    e.stopPropagation();
    const selectedRecords = bomTableDs.selected;

    if (selectedRecords.length > 0) {
      selectedRecords.forEach(record => bomTableDs.remove(record));
    }
  };

  // 获取报废原因lov Meaning
  const getScrapReasonLovMeaning = async value => {
    const lovCode = 'HME_WEIGHING_SCRAP_REASON';
    const url = `/hpfm/v1/${tenantId}/lovs/value/page`;

    const params = new URLSearchParams({
      lovCode,
      page: '0',
      size: '10',
      tenantId,
      value,
    });
    const response = await axios.get(`${url}?${params.toString()}`);

    if (response && response.content && response.content.length > 0) {
      const meaning = response.content[0].meaning;

      return meaning;
    }
  };

  const onHandleSave = async (submit?) => {
    const validate = await detailDs.validate();
    const validateTable = await detailTableDs.validate();
    if (validate && validateTable) {
      const data: any = detailDs.toData()[0];

      const url = `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/save/ui`;
      const res: any = await axios.post(url, {
        ...data,
        lineList: detailTableDs.toData(),
        bomList: bomTableDs.toData(),
      });

      if (res && res.success) {
        if (submit === 'submit') {
          onHandleSubmit(res.rows);
        } else {
          if (params.id === 'create') {
            history.push(`/hmes/weighingScrap/detail/${res.rows}`);
          } else {
            detailDs.setQueryParameter('docId', res.rows);
            detailDs.query().then(async data => {
              if (data) {
                const { lineList } = data.rows;

                setDocStatus(data.rows.docStatus);

                const updatedLineListPromises = (lineList || []).map(async lineItem => {
                  const meaning = await getScrapReasonLovMeaning(lineItem.scrapReason);
                  return {
                    ...lineItem,
                    scrapReasonLov: {
                      value: lineItem.scrapReason,
                      meaning,
                    },
                  };
                });

                // 等待所有异步操作完成
                const updatedLineList = await Promise.all(updatedLineListPromises);

                // 加载更新后的 lineList 到 detailTableDs
                detailTableDs.loadData(updatedLineList);
                onHandleBomQuery('detail');
              }
            });
          }
          notification.success({});
          setEdit(false);
        }
      } else {
        notification.error({
          message: res.message,
        });
      }
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.title.required`).d('请输入必填项'),
      });
    }
  };

  const onHandleSubmit = async id => {
    const data: any = detailDs.toData()[0];
    const url = `${
      BASIC.HMES_BASIC
    }/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/save/submit/ui`;
    const res: any = await axios.post(url, {
      docId: id,
      docType: data.docType,
      workOrderId: data.workOrderId,
      bomList: bomTableDs.toData(),
      lineList: detailTableDs.toData(),
    });
    if (res && res.success) {
      if (params.id === 'create') {
        history.push(`/hmes/weighingScrap/detail/${id}`);
      } else {
        detailDs.setQueryParameter('docId', id);
        detailDs.query().then(data => {
          if (data) {
            const { lineList } = data.rows;
            setDocStatus(data.rows.docStatus);
            detailTableDs.loadData(lineList || []);
            onHandleBomQuery('detail');
          }
        });
      }
      notification.success({});
      setEdit(false);
    } else {
      notification.error({
        message: res.message,
      });
    }
  };

  const onHandelRecordCopy = async record => {
    const url = `${
      BASIC.HMES_BASIC
    }/v1/${getCurrentOrganizationId()}/mt-material-lot-trace/material-lot/ui`;
    const res: any = await axios.post(url, {
      materialLotCodes: record.get('materialLotCode'),
    });
    if (res && res.success && res.rows.content) {
      const recordData = {
        ...record.toData(),
        scrapReasonLov: null,
        scarpQty: null,
        primaryUomQty: res.rows.content[0].primaryUomQty,
      };
      detailTableDs.appendData([recordData]);
    } else if (res.message || !res.success) {
      notification.error({
        message: res.message,
      });
    }
  };

  const onHandleEdit = () => {
    setEdit(true);
  };

  const onHandleCancel = () => {
    if (params.id === 'create') {
      history.push(`/hmes/weighingScrap/list`);
    } else {
      setEdit(false);
      detailDs.reset();
      detailTableDs.reset();
    }
  };

  const onHandleEquipment = async () => {
    const { siteId, workOrderId, equipmentId, scarpWeight, docType }: any = detailDs.toData()[0];
    if (siteId && workOrderId && equipmentId) {
      const data = {
        siteId,
        workOrderId,
        equipmentId,
        scarpWeight,
        docType,
      };
      const url = `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/query/order`;
      const res: any = await axios.post(url, {
        ...data,
      });
      if (res && res.success) {
        const { weightFactor, meterFactor, scarpMeter } = res.rows;
        detailDs.current!.set('weightFactor', weightFactor);
        detailDs.current!.set('meterFactor', meterFactor);
        detailDs.current!.set('scarpMeter', scarpMeter);
      } else {
        notification.error({ message: res.message });
      }
    }

    onHandleBomQuery('newBuild');
  };

  const onHandleBomQuery = async type => {
    const { siteId, workOrderId, docType, scarpMeter, bomList }: any = detailDs.toData()[0];

    if (siteId && workOrderId && docType && scarpMeter) {
      const data = {
        siteId,
        workOrderId,
        docType,
        scarpMeter,
      };

      const url = `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/query/bom`;

      if (type === 'newBuild') {
        const res: any = await axios.post(url, { ...data });
        if (res && res.success && res.rows.content) {
          bomTableDs.loadData(res.rows.content);
        } else {
          notification.error({ message: res.message });
        }
      } else if (bomList && bomList.length > 0) {
        bomTableDs.loadData(bomList);
      } else {
        const res: any = await axios.post(url, { ...data });
        if (res && res.success && res.rows.content) {
          bomTableDs.loadData(res.rows.content);
        } else {
          notification.error({ message: res.message });
        }
      }
    }
  };

  const onHandleDocType = () => {
    const { tag }: any = detailDs.toData()[0];
    detailTableDs.setState('tag', tag);
    setDocTypeTag(tag);
    if (['1', '2'].includes(tag)) {
      const list = detailTableDs.toData().map(item => ({ ...item, scrapReasonLov: null }));
      detailTableDs.loadData(list);
    }
    onHandleEquipment();
  };

  const handleMaterialLots = async () => {
    const { materialLotCodes, docType, scrapReasonLov }: any = detailDs.toData()[0];
    if (materialLotCodes) {
      const url = `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/query/lot`;
      const res: any = await axios.post(url, {
        docType,
        materialLotCodes,
        bomList: bomTableDs.toData(),
      });
      if (res && res.success && res.rows.content) {
        const list = intersectionBy(res.rows.content, detailTableDs.toData(), 'materialLotCode');
        const noList = differenceBy(res.rows.content, detailTableDs.toData(), 'materialLotCode');
        const listMap = new Map();
        const listKey = list.map(item => item.materialLotCode);
        list.map(item => {
          listMap.set(item.materialLotCode, item.primaryUomQty);
        });
        const updateList = detailTableDs.toData().map((item: any) => {
          if (listKey.includes(item.materialLotCode)) {
            return {
              ...item,
              primaryUomQty: listMap.get(item.materialLotCode),
            };
          } else {
            return item;
          }
        });

        const updatedNoList = noList.map((item: any) => ({
          ...item,
          scrapReasonLov: {
            value: scrapReasonLov?.value,
            meaning: scrapReasonLov?.meaning,
          },
        }));

        detailTableDs.loadData([...updateList, ...updatedNoList]);
      } else if (res.message || !res.success) {
        notification.error({
          message: res.message,
        });
      }
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.weighingScrapDetail`).d('称重报废平台详情')}
        backPath="/hmes/weighingScrap/list"
      >
        <Button
          disabled={['CLOSED', 'COMPLETED'].includes(docStatus)}
          color={ButtonColor.primary}
          onClick={() => onHandleSave('submit')}
        >
          {intl.get(`${modelPrompt}.title.saveSubmit`).d('保存并提交')}
        </Button>
        {edit ? (
          <div>
            <Button onClick={() => onHandleCancel()}>
              {intl.get(`${modelPrompt}.title.cancel`).d('取消')}
            </Button>
            <Button color={ButtonColor.primary} onClick={onHandleSave}>
              {intl.get(`${modelPrompt}.title.save`).d('保存')}
            </Button>
          </div>
        ) : (
          <>
            <Button
              disabled={['CLOSED', 'COMPLETED'].includes(docStatus)}
              color={ButtonColor.primary}
              onClick={() => onHandleEdit()}
            >
              {intl.get(`${modelPrompt}.title.edit`).d('编辑')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['1']}>
          <Panel key="1" header={intl.get(`${modelPrompt}.title.basicInfo`).d('基本信息')}>
            <Row type="flex" align="top" justify="space-between">
              <Col span={20}>
                <Form dataSet={detailDs} disabled={!edit} columns={3} labelWidth={112}>
                  <Lov name="siteLov" onChange={() => onHandleBomQuery('newBuild')} />
                  <TextField name="docNum" />
                  <Select name="docStatus" />
                  <Select name="docTypeCode" onChange={onHandleDocType} />
                  <Lov name="scrapReasonLov" />
                  <Lov name="workOrderLov" onChange={onHandleEquipment} />
                  <Lov name="equipmentLov" onChange={onHandleEquipment} />
                  <NumberField name="weightFactor" />
                  <NumberField name="meterFactor" />
                  <TextField name="remark" />
                  <NumberField name="scarpWeight" onChange={onHandleEquipment} />
                  <NumberField name="scarpMeter" onChange={() => onHandleBomQuery('newBuild')} />
                  <DateTimePicker name="scrapTime" />
                </Form>
              </Col>
            </Row>
          </Panel>
        </Collapse>
        <Collapse bordered={false} defaultActiveKey={['2']}>
          <Panel
            key="2"
            header={intl.get(`${modelPrompt}.title.bom`).d('BOM组件信息')}
            extra={
              <Popconfirm
                title="确定要删除这个组件吗?"
                onConfirm={onHandleBomDelete}
                okText="是"
                cancelText="否"
              >
                <Button
                  color={ButtonColor.primary}
                  funcType={FuncType.flat}
                  onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  disabled={!edit || isDeleteButtonDisabled}
                >
                  {intl.get('tarzan.common.button.delete').d('删除组件')}
                </Button>
              </Popconfirm>
            }
          >
            <Table
              dataSet={bomTableDs}
              columns={bomCcolumns}
              key="weighingScrapBomTable"
              queryBar={TableQueryBarType.none}
              searchCode="weighingScrapBomTable" // 动态筛选条后端接口唯一编码
              customizedCode="weighingScrapBomTable" // 个性化编码
            />
          </Panel>
        </Collapse>
        <Collapse bordered={false} defaultActiveKey={['3']}>
          <Panel key="3" header={intl.get(`${modelPrompt}.title.numDetail`).d('单据明细')}>
            <Row>
              <Col span={16}>
                <Form dataSet={detailDs} disabled={!edit} columns={1}>
                  <TextField name="materialLotCodes" onChange={handleMaterialLots} />
                </Form>
              </Col>
              <Col span={6}>
                <Button
                  onClick={() => {
                    detailDs.current!.set('materialLotCodes', []);
                  }}
                  style={{ marginTop: '4px' }}
                >
                  {intl.get('hzero.common.button.reset').d('重置')}
                </Button>
              </Col>
            </Row>
            <Table
              dataSet={detailTableDs}
              columns={columns}
              key="weighingScrapTable"
              queryBar={TableQueryBarType.professionalBar}
              queryFieldsLimit={1} // 头部显示的查询字段的数量
              searchCode="weighingScrapTable" // 动态筛选条后端接口唯一编码
              customizedCode="weighingScrapTable" // 个性化编码
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default DetailPage;
