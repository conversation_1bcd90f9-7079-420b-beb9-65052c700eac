/**
 * @Description:  领退料工作台-详情页
 */
import React, { useEffect, useState } from 'react';
import {
  DataSet,
  Table,
  TextField,
  Form,
  DatePicker,
  NumberField,
  Lov,
  Select,
  Button,
  Spin,
  TextArea,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { BASIC } from '@utils/config';
import { getResponse } from '@utils/utils';
import request from 'utils/request';
import notification from 'utils/notification';
import moment from 'moment';
import { headerFormDS, lineTableDS } from '../stores/DetailDS';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.confirmationTeamWorkingHours';
const HMES_BASIC = BASIC.HMES_BASIC;

const OrderDetail = props => {
  const {
    headerFormDs,
    lineTableDs,
    match: {
      params: { id },
    },
    location: {
      state,
    },
  } = props;

  const [hasPermissionFlag, setHasPermissionFlag] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasAllocation, setHasAllocation] = useState(false);

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      headerFormDs.loadData([
        {
          shiftUser: getCurrentUser().id,
          realName: getCurrentUser().realName,
        },
      ]);
      lineTableDs.loadData([]);
    } else {
      setCanEdit(false);
      headerFormDs.loadData([{...state}]);
      queryDetail()
    }
  }, [id]);
  const queryDetail = async () => {
    lineTableDs.setQueryParameter('handoverId', id);
    await lineTableDs.query();
    if(getCurrentUser().id === state.shiftUser){
      setHasPermissionFlag(true);
    }else{
      setHasPermissionFlag(false);
    }
  }

  const handleSave = async () => {
    if(id === 'create'&&!hasAllocation){
      notification.warning({
        message: intl.get(`${modelPrompt}.pleaseAllocateWorkHours`).d('请先工时分配后保存！'),
      })

      return
    }
    if(lineTableDs.toData().some(item => !item.manHour))return;
    if(await headerFormDs.validate()){
      setLoading(true);
      const res = await request(
        `${HMES_BASIC}/v1/${tenantId}/hme-handovers/work/hour/save`,
        {
          method: 'POST',
          body: {
            ...headerFormDs.current.toJSONData(),
            shiftDate: headerFormDs.current?.get('shiftDate').format('YYYY-MM-DD'),
            hmeTeamHoursVO1List: lineTableDs.toData(),
          },
        },
      )
      setLoading(false);
      const result = getResponse(res)
      if(result){
        notification.warning({
          message: intl.get(`${modelPrompt}.success`).d('保存成功！'),
        })
        if(id === 'create'){
          props.history.push({
            pathname: `/hmes/confirmation-team-working-hours/detail/${result.rows.handoverId}`,
            state: {
              ...result.rows,
            },
          });
        }else{
          setCanEdit(false);
        }
      }
    }
  };

  // 提交
  // const handleSubmit = async () => {
  //   if(id === 'create'&&!hasAllocation){
  //     notification.warning({
  //       message: '请先工时分配后保存！',
  //     })
  //     return
  //   }
  //   if(lineTableDs.toData().some(item => !item.manHour))return;
  //   if(await headerFormDs.validate()){
  //     setLoading(true);
  //     request(
  //       `${HMES_BASIC}/v1/${tenantId}/hme-handovers/work/hour/save`,
  //       {
  //         method: 'POST',
  //         body: {
  //           ...headerFormDs.current.toJSONData(),
  //           hmeTeamHoursVO1List: lineTableDs.toData(),
  //         },
  //       },
  //     ).then(res => {
  //       setLoading(false);
  //       const result = getResponse(res)
  //       if(result){
  //         setLoading(true);
  //         request(
  //           `${HMES_BASIC}/v1/${tenantId}/hme-handover-ifaces/submit/${result.rows.handoverId}`,
  //           {
  //             method: 'GET',
  //           },
  //         ).then(res1 => {
  //           setLoading(false);
  //           const result1 = getResponse(res1)
  //           if(result1){
  //             notification.success({
  //               message: '提交成功！',
  //             })
  //             if(id === 'create'){
  //               props.history.push({
  //                 pathname: `/hmes/confirmation-team-working-hours/detail/${result.rows.handoverId}`,
  //                 state: {
  //                   ...result.rows,
  //                 },
  //               });
  //             }else{
  //               setCanEdit(false);
  //             }
  //           }
  //         })
  //       }
  //     })
  //   }
  // };

  const createLineTableColumns = [
    {
      name: 'workOrderNum',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'quantity',
    },
    {
      name: 'manHour',
      renderer: ({ record }) => record.get('manHour'),
      editor: () => {
        return (
          canEdit && (
            <NumberField
              name="manHour"
            />
          )
        );
      },
    },
    // {
    //   name: 'sapFlagMeaning',
    // },
  ];

  const handleWorkAllocation = async (e) => {
    e.stopPropagation();
    if(headerFormDs?.current?.get('shiftType')&&
      headerFormDs?.current?.get('productionLineId')&&
      headerFormDs?.current?.get('totalManHour')
    ){
      setLoading(true);
      const res = await request(
        `${HMES_BASIC}/v1/${tenantId}/hme-handovers/work/hour/allocate`,
        {
          method: 'POST',
          body: {
            shiftType: headerFormDs?.current?.get('shiftType'),
            productionLineId: headerFormDs?.current?.get('productionLineId'),
            prodLineCode: headerFormDs?.current?.get('prodLineCode'),
            totalManHour: headerFormDs?.current?.get('totalManHour'),
            shiftDate: moment(headerFormDs?.current?.get('shiftDate')).format('YYYY-MM-DD'),
          },
        },
      )
      setLoading(false);
      const result = getResponse(res)
      if(result){
        setHasAllocation(true);
        lineTableDs.loadData(result.rows);
      }
    }else{
      notification.warning({
        message: intl.get(`${modelPrompt}.pleaseEnter`).d('请输入必输项！'),

      })
    }
  };
  const extraButton = (
    <Button onClick={handleWorkAllocation} disabled={!canEdit} color="primary">
      {intl.get(`${modelPrompt}.workAllocation`).d('工时分配')}
    </Button>
  );
  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/confirmation-team-working-hours/list');
      return;
    }
    headerFormDs.loadData([{...state}]);
    queryDetail(id);
    setCanEdit(prev => !prev);
  };

  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        <Header
          title={intl.get(`${modelPrompt}.title.detail.head`).d('班组工时确认')}
          backPath="/hmes/confirmation-team-working-hours/list"
        >
          {canEdit && (
            <>
              {/* <Button
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSubmit}
                loading={loading}
                // disabled={instructionDocStatus !== 'NEW'}
              >
                {intl.get(`${modelPrompt}.submit`).d('提交')}
              </Button> */}
              <Button
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
                loading={loading}
                // disabled={instructionDocStatus !== 'NEW'}
              >
                {intl.get('tarzan.common.button.submit').d('提交')}
              </Button>
              <Button
                onClick={handleCancel}
                loading={loading}
                icon="close"
              >
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && (
            <Button
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              disabled={!hasPermissionFlag}
              onClick={() => {
                setCanEdit(prev => !prev);
              }}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'workOrderInfo']}>
            <Panel
              header={intl.get(`${modelPrompt}.header.basic`).d('基本信息')}
              key="basicInfo"
              dataSet={headerFormDs}
            >
              <Form
                disabled={!canEdit}
                dataSet={headerFormDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <Lov
                  name="site"
                  disabled={!canEdit}
                />
                <TextField name="handoverNum" disabled />
                <Select
                  name="shiftType"
                  disabled={!canEdit}
                />
                <Lov
                  name="prodLov"
                  disabled={!canEdit}
                />
                <TextField name="prodLineName" disabled />
                <NumberField name="totalManHour" disabled={!canEdit} />
                <Lov
                  name="userLov"
                  disabled={id==='create'||!canEdit}
                />
                <DatePicker name="shiftDate" />
                <TextArea name="matters" colSpan={3} newLine/>
                <TextArea name="solution" colSpan={3} />
                <TextArea name="remark" colSpan={3}/>
              </Form>,
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.line.information`).d('工单信息')}
              key="workOrderInfo"
              dataSet={lineTableDs}
              extra={extraButton}
            >
              <Table
                searchCode="manHour"
                customizedCode="manHour"
                dataSet={lineTableDs}
                columns={createLineTableColumns}
                highLightRow
                queryBar="filterBar"
                queryBarProps={{
                  fuzzyQuery: false,
                }}
              />
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.receive.receiveReturn', 'tarzan.common'] }),
  withProps(
    () => {
      const headerFormDs = new DataSet({ ...headerFormDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerFormDs,
        lineTableDs,
      };
    },
    {},
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.HEAD`, `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_DETAIL.LINE`] }),
)(OrderDetail);
