/**
 * @Description: 预装物料生产指令创建-ds
 * @Author: <<EMAIL>>
 * @Date: 2022-10-12 16:02:01
 * @LastEditTime: 2023-07-19 15:28:40
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { isNumber } from 'lodash';

const modelPrompt = 'tarzan.workshop.productionOrderMgt';
const tenantId = getCurrentOrganizationId();

const eoDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  paging: false,
  dataKey: 'rows',
  fields: [
    // 基本属性
    {
      name: 'materialObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialCode`).d('物料编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.PRE_MATERIAL`,
      required: true,
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId,
            workOrderId: record.get('workOrderId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      bind: 'materialObject.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialCode`).d('物料编码'),
      disabled: true,
      bind: 'materialObject.materialCode',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderNum`).d('WO编码'),
      disabled: true,
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.qty`).d('WO数量'),
      disabled: true,
    },
    {
      name: 'releasedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.releasedAreadyQty`).d('已下达数量'),
      disabled: true,
    },
    {
      name: 'canReleaseQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.canQty`).d('可下达数量'),
      disabled: true,
    },
    {
      name: 'totalQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.releasedQty`).d('下达数量'),
      required: true,
      precision: 2,
      min: 0,
      max: 'canReleaseQty',
      defaultValidationMessages: {
        rangeOverflow: intl
          .get(`${modelPrompt}.model.productionOrderMgt.notLessMsg`)
          .d('下达数量不能大于可下达数量'),
      },
      validator: (value, _, record) => {
        const assembleList = record.get('assembleList');
        let overRecordNum = 0;
        if (assembleList && assembleList.length > 0) {
          assembleList.forEach(item => {
            if (value * (item.primaryUnitRatio || 0) > item.canReleaseQty) {
              overRecordNum++;
            }
          });
        }
        if (value === 0) {
          return intl
            .get(`${modelPrompt}.model.productionOrderMgt.validation.moreThanZero`)
            .d('数量必须大于0!');
        }
        if (overRecordNum > 0) {
          return intl
            .get(`${modelPrompt}.model.productionOrderMgt.validation.beforehandMax`)
            .d('预计生成数量不能大于可下达数量!');
        }
      },
    },
    {
      name: 'eoQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.unitQty`).d('单位EO数量'),
      required: true,
      precision: 2,
      min: 0,
      max: 'totalQty',
      defaultValidationMessages: {
        rangeOverflow: intl
          .get(`${modelPrompt}.model.productionOrderMgt.notUomLessMsg`)
          .d('单位EO数量不能小于下达数量'),
      },
      validator: value => {
        if (value === 0) {
          return intl
            .get(`${modelPrompt}.model.productionOrderMgt.validation.moreThanZero`)
            .d('数量必须大于0!');
        }
      },
    },
    {
      name: 'mantissaDeal',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.mantissa`).d('尾数处理方式'),
      textField: 'description',
      valueField: 'typeCode',
      disabled: true,
      required: false,
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=EO_CREAT_MANTISSA_DEAL&type=eoMantissaOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled({ record }) {
          return !(
            isNumber(record.get('totalQty')) &&
            isNumber(record.get('eoQty')) &&
            record.get('totalQty') % record.get('eoQty') > 0
          );
        },
        required({ record }) {
          return (
            isNumber(record.get('totalQty')) &&
            isNumber(record.get('eoQty')) &&
            record.get('totalQty') % record.get('eoQty') > 0
          );
        },
      },
    },
    {
      name: 'eoCount',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.eoCount`).d('EO生成个数'),
      disabled: true,
      precision: 2,
    },
  ],
});

const eoBeforehandListDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.beforehandMaterialName`).d('预装物料编码'),
    },
    {
      name: 'primaryUnitRatio',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUnitRatio`).d('与主物料装配比例'),
    },
    {
      name: 'releasedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.releasedAreadyQty`).d('已下达数量'),
    },
    {
      name: 'canReleaseQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.canQty`).d('可下达数量'),
    },
    {
      name: 'forecastQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.forecastQty`).d('预计生成数量'),
    },
  ],
});

const eoListDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'eoId',
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-eo/detail/for/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialCode`).d('物料编码'),
    },
    {
      name: 'targetQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.targetQty`).d('目标数量'),
    },
    {
      name: 'actualQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.actualQty`).d('实际数量'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoStatus`).d('状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'materialTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialType`).d('类型'),
    },
    {
      name: 'unStartedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.notWorkingQty`).d('未开工数量'),
    },
    {
      name: 'wipSumQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.inWorkingQty`).d('在制汇总数量'),
    },
    {
      name: 'wipScrappedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.inWorkingScrappedQty`).d('在制报废数量'),
    },
    {
      name: 'completedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.completedQty`).d('完成数量'),
    },
    {
      name: 'confirmScrappedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.confirmScrappedQty`).d('报废确认数量'),
    },
  ],
});

export { eoDS, eoListDS, eoBeforehandListDS };
