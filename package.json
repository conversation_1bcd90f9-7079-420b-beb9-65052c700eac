{"name": "yp-mes-front", "private": true, "scripts": {"start": "cross-env UMI_ENV=dev umi dev", "build:dep-all:prod": "cross-env UMI_ENV=prod umi hzero-build-dep --all", "build:app:prod": "cross-env UMI_ENV=prod umi hzero-build --only-build-parent", "build": "umi hzero-build", "build:c7n": "cross-env NODE_ENV=development SKIP_NO_CHANGE_MODULE=true node ./node_modules/umi/bin/umi.js hzero-build-dep --package-name choerodon-ui", "build:app": "umi hzero-build --only-build-parent", "build:ms-dev": "cross-env JSXINJS=true UMI_ENV=dev NODE_OPTIONS='--max_old_space_size=10240' umi hzero-build --only-build-micro", "build:ms": "cross-env JSXINJS=true UMI_ENV=prod NODE_OPTIONS='--max_old_space_size=10240' umi hzero-build --only-build-micro", "build:ms-dev-all": "cross-env JSXINJS=true UMI_ENV=dev umi hzero-build --only-build-micro --all-packages", "build:ms-all": "cross-env JSXINJS=true UMI_ENV=prod umi hzero-build --only-build-micro --all-packages", "build:hb": "cross-env UMI_ENV=prod umi hb-cmf", "build:hb-dev": "cross-env UMI_ENV=dev umi hb-cmf", "transpile": "umi hzero-transpile", "build:analyze": "ANALYZE=1 umi build", "hzero-build:dep-all": "umi hzero-build-dep --all", "hzero-build:dep": "umi hzero-build-dep --package-name", "start:additional": "cross-env ADDITIONAL=true umi dev", "build:additional": "cross-env ADDITIONAL=true umi hzero-build", "build:additional:ms": "cross-env ADDITIONAL=true umi hzero-build --only-build-micro", "build:additional-dep": "cross-env ADDITIONAL_NAME=145_hotfix ADDITIONAL_OUTPUT=145_hotfix umi hzero-build-dep --package-name", "build:hzero": "micro-prepare hzero", "prepare": "husky install && node node_modules/@hzerojs/plugin-micro/postinstall-script", "lint": "eslint \"packages/**/*.{js,jsx,tsx,ts}\" && npm run lint:style && tsc --noEmit --emitDeclarationOnly false", "lint:fix": "eslint --quiet --fix 'packages/**/*.{js,jsx,tsx,ts}' && npm run lint:style", "lint:style": "stylelint \"packages/**/*.less\" --syntax less", "lint-staged": "lint-staged", "bootstrap": "yarn --registry http://nexus.saas.hand-china.com/content/groups/hzero-npm-group/", "test": "node scripts/test.js", "changelog": "node node_modules/.bin/conventional-changelog -p eslint -i CHANGELOG.md -s -r 0 && git add CHANGELOG.md", "tree": "tree -I node_modules -L 3", "release": "standard-version", "release-module": "node scripts/release.js", "icon": "npx cross-env ICON_FONT_URL=//at.alicdn.com/t/font_1440728_2mntu9m71ej.css node scripts/icon.js", "hzero-version": "node scripts/version.js", "prettier": "prettier -c --write \"packages/**/*.{js,jsx,tsx,ts}\""}, "dependencies": {"@hzerojs/preset-hzero": "1.2.12-beta.0", "@antv/data-set": "latest", "@antv/g6": "^3.5.3", "immutability-helper": "3.0.1", "array-move": "3.0.1", "@hzero-front-ui/cfg": "5.0.1-alpha.17", "styled-components": "5.3.0", "hzero-cli-preset-ui": "^3.0.0", "hzero-front": "1.11.6-alpha.23", "@babel/plugin-syntax-top-level-await": "^7.14.5", "babel-plugin-module-resolver": "^3.1.1", "babel-plugin-use-const-enum": "^0.0.9", "copy-to-clipboard": "^3.3.1", "@typescript-eslint/eslint-plugin": "^5.38.1", "@typescript-eslint/parser": "^5.38.1", "axios": "~0.19.2", "bizcharts": "^4.0.14", "core-js": "^3.6.4", "cropperjs": "1.5.6", "cross-env": "^7.0.2", "escape-goat": "^3.0.0", "eslint": "^7.32.0", "eslint-plugin-import-helpers": "^1.1.0", "eslint-plugin-react": "^7.27.0", "eslint-plugin-jsx-a11y": "6.1.2", "eslint-plugin-import": "2.14.0", "event-emitter": "^0.3.5", "husky": "^7.0.4", "hzero-ui": "^1.0.76", "is-absolute-url": "^2.0.0", "jsencrypt": "^3.0.0-rc.1", "lerna": "^3.20.2", "lint-staged": "^10.0.8", "load-script": "1.0.0", "lodash-decorators": "^6.0.1", "mobx": "^4.15.5", "mobx-react": "~6.1.4", "new-github-release-url": "^1.0.0", "prettier": "^1.19.1", "rc-drawer-menu": "1.1.0", "react": "^16.12.0", "react-cropper": "1.3.0", "react-custom-scrollbars": "~4.2.1", "react-document-title": "^2.0.3", "react-dom": "^16.12.0", "react-grid-layout": "^1.0.0", "react-intl-universal": "^2.3.2", "react-resizable": "^1.10.1", "react-viewer": "2.11.1", "react-virtualized": "latest", "umi": "^3.2.19", "universal-cookie": "^4.0.3", "webpack": "^5.65.0", "webpack-merge": "^5.8.0", "choerodon-ui": "1.5.9-alpha.5", "echarts-for-react": "^2.0.14", "echarts": "^4.8.0", "gg-editor": "2.0.4", "hfins-front-common": "1.5.1-alpha", "less-loader": "^6", "mini-css-extract-plugin": "2.4.5", "@antv/g2": "^4.1.14", "react-sortable-pane": "1.1.0", "ali-table-fix-hd": "0.0.4-alpha.0.8", "mf-ali-table-fix-hd": "0.0.1", "hzero-front-cusz": "1.2.2-alpha.2", "hcm-components-front": "7.0.6", "hcm-mes-front": "7.0.8", "halm-front": "1.5.7-fix.1", "jspdf": "^2.5.1"}, "devDependencies": {"@babel/cli": "*", "@babel/plugin-proposal-class-properties": "7.7.4", "@babel/plugin-proposal-decorators": "^7.7.4", "@smock/umi-plugin-smock": "^0.1.22", "@smock/umi-plugin-sproxy": "^0.1.14", "@typescript-eslint/eslint-plugin": "^5.38.1", "@typescript-eslint/parser": "^5.38.1", "conventional-changelog-cli": "^2.0.12", "hzero-cli-preset-ui": "^3.3.0", "lerna": "3.20.2", "standard-version": "^5.0.2", "typescript": "^4.2.0", "yarn": "^1.13.0", "eslint-config-airbnb": "^17.1.0", "@types/react": "16.14.0", "@types/react-dom": "16.9.8", "@types/prettier": "1.16.1", "prettier": "^2.6.2", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-markdown": "^1.0.0", "stylelint-config-prettier": "^5.2.0"}, "resolutions": {"@hzero-front-ui/cfg": "5.0.1-alpha.19", "@hzero-front-ui/hzero-ui": "5.0.1-alpha.6", "@hzero-front-ui/themes": "5.0.1-alpha.19", "@hzero-front-ui/c7n-ui": "5.0.1-alpha.18", "@hzero-front-ui/core": "5.0.1-alpha.5", "@hzero-front-ui/font": "5.0.1-alpha.3", "hzero-front": "1.11.6-alpha.23", "@hzerojs/preset-hzero": "1.2.12-beta.0", "monaco-editor": "^0.34.1", "react-monaco-editor": "0.50.1", "monaco-editor-webpack-plugin": "^7.0.1", "styled-components": "^5.3.0", "choerodon-ui": "1.5.9-alpha.5", "choerodon-ui-font": "0.2.13", "hzero-ued-icon": "0.1.17", "webpack": "^5.65.0", "mobx": "4.15.7", "mobx-react": "6.1.4", "@babel/core": "7.12.3", "eslint": "^7.32.0", "react-router": "5.2.1", "react-router-dom": "5.2.1", "dva": "2.6.0-beta.22", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^4.0.0", "eslint-config-react-app": "^3.0.7", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-compat": "^3.3.0", "eslint-plugin-flowtype": "2.50.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-jsx-a11y": "6.1.2", "eslint-plugin-react": "7.12.4", "stylelint": "13.7.1", "conventional-changelog": "~3.0.6", "browserslist": "^4.16.0", "lerna": "3.20.2", "optimize-css-assets-webpack-plugin": "5.0.3", "@babel/cli": "^7.8.4", "react": "16.12.0", "react-dom": "16.12.0", "html-webpack-plugin": "4.0.0-alpha.2", "typescript": "^4.2.0", "redux": "3.7.2", "echarts-for-react": "^2.0.14", "echarts": "^4.8.0", "@types/react": "16.14.0", "@types/react-dom": "16.9.8"}, "description": "hzero-demo", "author": "", "version": "0.0.2", "eslintConfig": {"extends": "react-app"}, "husky": {"hooks": {"pre-commit": "lint-staged", "post-merge": "git submodule update", "post-checkout": "git submodule update"}}, "browserslist": [">0.2%", "not dead", "not ie <= 10", "not op_mini all"], "lint-staged": {"packages/*/src/**/*.{js,jsx,tsx,ts}": ["eslint --quiet --fix", "git add"], "packages/*/src/**/*.{js,jsx,tsx,ts,less}": ["prettier --write", "git add"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "resolver": "jest-pnp-resolver", "setupFiles": ["react-app-polyfill/jsdom"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/?(*.)(spec|test).{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "testURL": "http://localhost", "transform": {"^.+\\.(js|jsx|ts|tsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["<rootDir>/node_modules/jest-watch-typeahead/filename.js", "<rootDir>/node_modules/jest-watch-typeahead/testname.js"]}, "workspaces": ["packages/*"]}