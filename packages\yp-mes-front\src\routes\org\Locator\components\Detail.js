/**
 * @Description: 新库位维护-详情页组件
 * @Author: <<EMAIL>>
 * @Date: 2021-03-05 14:14:48
 * @LastEditTime: 2023-05-29 12:30:01
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useImperativeHandle, forwardRef, useState, useMemo } from 'react';
import {
  TextField,
  NumberField,
  Form,
  Switch,
  Select,
  IntlField,
  Tabs,
  Lov,
  Tooltip,
  Icon,
  Modal,
  DataSet,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { isNull, intersection, isEmpty } from 'lodash';
import intl from 'utils/intl';
import notification from 'utils/notification';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import myInstance from '@utils/myAxios';
import { BASIC } from '@utils/config';
import BasicInfoTab from './BasicInfoTab';
import { C7nFormItemSortCoord } from 'hcm-components-front/lib/components/tarzan-ui';
import { BasicsAtttribtesDS } from '../stores/LocatorDetailDS';

const modelPrompt = 'tarzan.model.org.locator';
const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;

const Detail = (props, ref) => {
  const {
    canEdit,
    kid,
    columns = 1,
    handleValue,
    componentType,
    customizeForm,
  } = props;
  const detailDS = useMemo(() => new DataSet(BasicsAtttribtesDS()), []);

  const [activeKey, setActiveKey] = useState('basic');

  const [disabledFollow, setFollow] = useState(kid === 'create');

  const [disabledParent, setParent] = useState(false); // 启用状态控制禁用上层库位lov

  const [disabledParentLocation, setParentLocation] = useState(true); // 库位类别控制禁用上层库位lov

  const [disabledSite, setSite] = useState(false); // 分配站点是否禁用

  const [isConfirm, setConfirm] = useState(false); // 分配站点的值是否改变过

  const [parentLocatorNames, setParentLocatorName] = useState(null); // 上层库位描述存起来，重置上层库位时使用

  const [parentLocatorIds, setParentLocatorId] = useState(null); // 上层库位id存起来，重置上层库位时使用

  const [parentLocatorCodes, setParentLocatorCode] = useState(''); // 上层库位code存起来，重置上层库位时使用

  const [siteData, setSiteData] = useState([]); // 把站点Option存下来

  const [siteList, setSiteList] = useState([]); // 把站点接口查询出来的站点列表存下来

  const [childrenFlags, setChildrenFlag] = useState([]); // 把是否用来判断比较站点的值存起来

  const [locatorIds, setLocatorIds] = useState(kid !== 'create' ? kid : 0);

  const [coordinateType, setCoordinateType] = useState('3D');

  const [originalDataIds, setOriginalDataId] = useState('');

  useEffect(() => {
    if (kid !== 'create') {
      detailQuery(kid);
    } else {
      setFollow(false);
      handleSiteQuery();
    }
  }, [kid]);

  // 详情页查询
  const detailQuery = async id => {
    detailDS.setQueryParameter('locatorId', id);
    detailDS.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.LOACTOR`)
    const res = await detailDS.query();
    const {
      parentLocatorName = null,
      parentLocatorId = null,
      parentLocatorCode = null,
      enableFlag,
      locatorCategory = null,
      childrenFlag = 'Y',
      // eslint-disable-next-line no-shadow
      coordinateType,
      coordinateId,
    } = res.rows || {};
    if (coordinateType) {
      setCoordinateType(coordinateType);
    }
    setParentLocatorId(parentLocatorId);
    setParentLocatorCode(parentLocatorCode);
    setParentLocatorName(parentLocatorName);
    setParent(enableFlag === 'N');
    setParentLocation(isNull(locatorCategory));
    setSite(!isNull(parentLocatorId));
    setChildrenFlag(childrenFlag);
    handleQuery();
    handleSiteQuery();
    setOriginalDataId(coordinateId);
    if (handleValue) {
      handleValue(coordinateId, coordinateId);
    }
  };

  // 查询分配站点select框中的值
  const handleQuery = async () => {
    const url = `${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}/mt-mod-locator/user/distribution/site/list/ui?locatorId=${locatorIds}`;
    myInstance.get(url).then(res => {
      const { rows = [] } = res.data || {};
      const sites = [];
      rows.forEach(val => {
        const { distributedFlag, siteId } = val || {};
        if (distributedFlag) {
          sites.push(siteId);
        }
      });
      detailDS.current.set('siteIds', sites);
      setSiteList(sites);
    });
  };

  // 查询分配站点select框中的Option
  const handleSiteQuery = async (value = null) => {
    const url = `${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}/mt-mod-locator/user/distribution/site/list/ui?locatorId=${value ||
      locatorIds}`;
    myInstance.get(url).then(res => {
      const { rows = [], success } = res.data || {};
      if (success) {
        setSiteData(rows);
      }
    });
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    submit: async () => {
      detailDS.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验

      const validate = await detailDS.validate();
      let returnData = { success: false, newKid: '' };

      if (validate) {
        let success = false;
        let newKid = '';
        if (kid !== 'create' && childrenFlags === 'Y' && isConfirm) {
          await Modal.confirm({
            title: intl.get(`tarzan.common.title.tips`).d('提示'),
            children: (
              <div>
                <p>
                  {intl
                    .get(`${modelPrompt}.prompt.content1`)
                    .d(
                      '此库位存在下层库位，若修改改库位分配的站点信息，则下层所有库位对应的站点信息也将同步修改！',
                    )}
                </p>
              </div>
            ),
          }).then(async button => {
            if (button === 'ok') {
              await detailDS.submit().then(res => {
                const { rows = [] } = res || {};
                if (!isEmpty(rows) && rows[0].success) {
                  notification.success({});
                  success = true;
                  newKid = rows[0].rows;
                  setLocatorIds(newKid);
                  detailQuery(newKid);
                  setConfirm(false);
                  returnData = { success, newKid };
                }
              });
            }
          });
          return returnData;
        }
        await detailDS.submit().then(res => {
          const { rows = [] } = res || {};
          if (!isEmpty(rows) && rows[0].success) {
            success = true;
            newKid = rows[0].rows;
            setLocatorIds(newKid);
            if (kid !== 'create') {
              detailQuery(newKid);
            }
            returnData = { success, newKid };
          } else if (!isEmpty(rows) && !rows[0].success && rows[0].statusCode) {
            Modal.warning({
              title: intl.get('hzero.common.status.warning'),
              children: (
                <div>
                  {intl
                    .get(`${modelPrompt}.prompt.content2`)
                    .d(
                      '当前库位或上层库位已存在于组织关系中，为保持两边结构的一致性，请先将本次操作涉及到的库位/上层库位在组织关系中相关的关系删除，再更改本功能中的库位结构。',
                    )}

                  <div>
                    {intl
                      .get(`${modelPrompt}.prompt.content3`)
                      .d(
                        '若有需要，可在本功能更改完库位结构后，再次在组织关系中维护库位与组织的关系。',
                      )}
                  </div>
                </div>
              ),
            });
          } else if (!isEmpty(rows) && !rows[0].success) {
            notification.error({
              message: rows[0].message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
        });
        return returnData;
      }
      return returnData;
    },
    reset: () => {
      detailQuery(kid);
    },
  }));

  const customizeCode = useMemo(() => {
    switch (componentType) {
      case 'LOACTOR':
        return `${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_DETAIL.BASIC`;
      case 'ORG_RELATION':
        return `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.LOACTOR`;
      default:
        console.error('父组件传入类型错误！')
        break;
    }
  }, [componentType]);

  const handleChangeTab = value => {
    setActiveKey(value);
  };

  // 库位类别改变时
  const handleLocatorCategory = value => {
    detailDS.current.set('parentLocatorLov', undefined);
    detailDS.current.set('parentLocatorName', undefined);
    detailDS.current.init('siteIds', undefined);
    setSite(false);
    if (isNull(value)) {
      setParentLocation(true);
    } else {
      setParentLocation(false);
    }
  };

  // 上层库位lov改变时
  const handleUpperLocation = value => {
    detailDS.current.set('xValue', undefined);
    detailDS.current.set('yValue', undefined);
    detailDS.current.set('zValue', undefined);
    if (isNull(value)) {
      detailDS.current.set('parentLocatorName', undefined);
      detailDS.current.set('coordinate', undefined);
      setCoordinateType('1D');
      setSite(false);
      const _noPermissionSiteList = siteData.filter(site => !site.permissionFlag).map(site => site.siteId);
      const _currentSiteIds = (detailDS.current.get('siteIds') || []).filter(site => !_noPermissionSiteList.includes(site));
      detailDS.current.set('siteIds', _currentSiteIds)
    } else {
      const { locatorName, locatorId, coordinateId, coordinateCode, description } = value || {};
      if (value.coordinateType) {
        setCoordinateType(value.coordinateType);
      }
      detailDS.current.set('coordinateId', coordinateId);
      detailDS.current.set('coordinateCode', coordinateCode);
      detailDS.current.set('coordinateType', value.coordinateType || undefined);
      detailDS.current.set('description', description);

      setLocatorIds(locatorId);
      handleAfterUpperLocationQuerySites(locatorId);
      detailDS.current.set('parentLocatorName', locatorName);

      setSite(true);
    }
  };

  const handleAfterUpperLocationQuerySites = async (value = null) => {
    const url = `${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}/mt-mod-locator/user/distribution/site/list/ui?locatorId=${value ||
      locatorIds}`;
    myInstance.get(url).then(res => {
      const { rows = [], success } = res.data || {};
      if (success) {
        setSiteData(rows);
        const siteIds = [];
        rows.forEach(val => {
          const { siteId, distributedFlag } = val || {};
          if (distributedFlag) {
            siteIds.push(siteId);
          }
        });
        detailDS.current.set('siteIds', siteIds);
        setConfirm(
          siteList.length !== siteIds.length ||
          siteList.length !== intersection(siteList, siteIds).length,
        );
      }
    });
  };

  // 分配站点改变时
  const handleSite = (value = []) => {
    if (!isNull(value)) {
      setConfirm(
        siteList.length !== value.length ||
        siteList.length !== intersection(siteList, value).length,
      );
    } else if (kid === 'create') {
      setLocatorIds(0);
      handleSiteQuery(0);
    }
  };

  // 启用状态改变时
  const handleEnableFlag = value => {
    if (value === 'N') {
      if (kid === 'create') {
        detailDS.current.set('parentLocatorLov', {});
        detailDS.current.set('parentLocatorName', undefined);
        setParent(true);
        setSite(false);
      } else {
        detailDS.current.set('parentLocatorId', parentLocatorIds);
        detailDS.current.set('parentLocatorCode', parentLocatorCodes);
        detailDS.current.set('parentLocatorName', parentLocatorNames);
        setParent(true);
        if (isNull(parentLocatorIds)) {
          // 上层库位没有值时，不禁用站点，有值时 则禁用
          setSite(false);
        } else {
          setSite(true);
        }
      }
    } else if (kid !== 'create') {
      detailDS.current.set('parentLocatorId', parentLocatorIds);
      detailDS.current.set('parentLocatorCode', parentLocatorCodes);
      detailDS.current.set('parentLocatorName', parentLocatorNames);
      setParent(false);
    } else {
      setParent(false);
    }
  };

  const childProps = {
    canEdit,
    columns,
    disabledFollow,
  };

  const handleCoordinate = res => {
    if (res && res.coordinateId) {
      if (handleValue) {
        handleValue(res.coordinateId, originalDataIds);
      }
    } else if (handleValue) {
      handleValue(null, originalDataIds);
    }
  };

  const showHelp = (
    <span>
      <Tooltip
        placement="top"
        title={
          <div>
            <p>
              {intl
                .get(`${modelPrompt}.locator.tips1`)
                .d(
                  '1、区域类别库位下可以挂区域、库存和地点类别的库位；库存类别库位下仅可以挂地点类别的库位；地点类别库位下不可以挂其他库位',
                )}
            </p>
            <p>
              {intl
                .get(`${modelPrompt}.locator.tips2`)
                .d(
                  '2、上层库位所分配站点要与当前库位所分配的站点保持一致，即存在关系的库位所分配站点信息都是相同的',
                )}
            </p>
            <p>
              {intl
                .get(`${modelPrompt}.locator.tips3`)
                .d('3、根据用户已分配站点的权限，显示允许选择的上层库位')}
            </p>
          </div>
        }
      >
        <Icon type="help_outline" />
      </Tooltip>
    </span>
  );

  let itemWidth = [];
  if (coordinateType === '2D') {
    itemWidth = ['5%', '40%', '10%', '40%', '5%'];
  } else if (coordinateType === '1D') {
    itemWidth = ['5%', '90%', '5%'];
  } else {
    itemWidth = ['5%', '26.6%', '5%', '26.6%', '5%', '26.6%', '5%'];
  }

  return (
    <>
      <Collapse bordered={false} defaultActiveKey={['locationInformation']}>
        <Panel
          header={intl.get(`${modelPrompt}.locationInformation`).d('库位信息')}
          key="locationInformation"
          dataSet={detailDS}
        >
          {customizeForm(
            {
              code: customizeCode,
            },
            <Form
              disabled={!canEdit}
              dataSet={detailDS}
              columns={columns}
              labelLayout="horizontal"
              labelWidth={112}
            >
              <TextField name="locatorCode" />
              <IntlField
                name="locatorName"
                modalProps={{
                  title: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
                }}
              />
              <Select
                name="locatorCategory"
                disabled={kid !== 'create'}
                onChange={handleLocatorCategory}
              />
              <Lov
                name="parentLocatorLov"
                addonAfter={showHelp}
                disabled={disabledParent || disabledParentLocation}
                onChange={handleUpperLocation}
                className="show-help-lov"
                placeholder=" "
                noCache
              />
              <TextField name="parentLocatorName" disabled />
              <Select name="locatorType" />
              <Select
                name="siteIds"
                colSpan={2}
                disabled={disabledSite}
                onChange={handleSite}
                onFocus={() => handleSiteQuery(locatorIds)}
              // onOption={handleOption}
              // noCache
              >
                {siteData.length > 0 &&
                  siteData.map(val => {
                    const { permissionFlag, siteId, siteTypeDesc, siteCode, siteName } = val || {};
                    return (
                      <Option disabled={!permissionFlag} value={siteId}>
                        {`${siteTypeDesc}-${siteCode}-${siteName}`}
                      </Option>
                    );
                  })}
              </Select>
              <TextField name="identification" />
              <Lov name="coordinate" onChange={handleCoordinate} placeholder=" " noCache />
              <TextField name="description" />
              {coordinateType === '3D' && (
                <C7nFormItemSortCoord name="xValue" itemWidth={itemWidth}>
                  {' ( '}
                  <NumberField name="xValue" min={0} />
                  {' , '}
                  <NumberField name="yValue" min={0} />
                  {' , '}
                  <NumberField name="zValue" min={0} />
                  {' ) '}
                </C7nFormItemSortCoord>
              )}
              {coordinateType === '2D' && (
                <C7nFormItemSortCoord name="xValue" itemWidth={itemWidth}>
                  {' ( '}
                  <NumberField name="xValue" min={0} />
                  {' , '}
                  <NumberField name="yValue" min={0} />
                  {' ) '}
                </C7nFormItemSortCoord>
              )}
              {coordinateType === '1D' && (
                <C7nFormItemSortCoord name="xValue" itemWidth={itemWidth}>
                  {' ( '}
                  <NumberField name="xValue" min={0} />
                  {' ) '}
                </C7nFormItemSortCoord>
              )}
              <Switch name="enableFlag" onChange={handleEnableFlag} />
              <Switch name="negativeFlag" />
            </Form>,
          )}
        </Panel>
      </Collapse>
      <Tabs activeKey={activeKey} onChange={handleChangeTab}>
        <TabPane tab={intl.get(`${modelPrompt}.basic`).d('基础属性')} key="basic" forceRender>
          <BasicInfoTab dataSet={detailDS} focus={activeKey !== 'basic'} {...childProps} />
        </TabPane>
      </Tabs>
    </>
  );
};

export default withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.LOACTOR`],
})(forwardRef(Detail));
