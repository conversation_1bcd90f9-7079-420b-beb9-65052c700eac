/**
 * @feature 班次装配实绩
 * @date 2021-5-11
 * <AUTHOR> <<EMAIL>>
 */

import React, { useEffect, useMemo } from 'react';
import { Table, DataSet, Button, Modal } from 'choerodon-ui/pro';
import { Badge } from 'hzero-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { entranceDS, detailDS } from './stories/EntranceDs';
import './index.module.less';

const modelPrompt = 'tarzan.workshop.assembleReport.model';

const entrance = props => {
  const detailDs = useMemo(() => new DataSet(detailDS()), []);

  const columnsData = [
    {
      name: 'workcellCode',
      align: 'left',
      width: 200,
    },
    {
      name: 'workcellName',
      align: 'left',
      width: 200,
    },
    {
      name: 'shiftDate',
      align: 'center',
      width: 150,
    },
    {
      name: 'shiftCode',
      width: 150,
      align: 'left',
    },
    {
      name: 'materialCode',
      width: 200,
      align: 'left',
    },
    {
      name: 'revisionCode',
      align: 'left',
      width: 150,
    },
    {
      name: 'disPatchDemandQty',
      align: 'right',
      width: 130,
    },
    {
      name: 'assembleQty',
      align: 'right',
      width: 100,
    },
    { name: 'removeQty', align: 'right', width: 100 },
    { name: 'scrappedQty', align: 'right', width: 100 },
  ];

  const operateColumns = {
    width: 100,
    header: intl.get('tarzan.common.label.action').d('操作'),
    align: 'center',
    lock: 'right',
    renderer: ({ record }) => {
      return (
        <a
          onClick={() => {
            handleCopyDrawerShow(record);
          }}
        >
          {intl.get(`${modelPrompt}.detail`).d('详情')}
        </a>
      );
    },
  };

  const columnsDetailData = [
    {
      name: 'eoNum',
      align: 'left',
      width: 150,
    },
    {
      name: 'workOrderNum',
      align: 'left',
      width: 150,
    },
    {
      name: 'eoMaterialCode',
      align: 'left',
      width: 150,
    },
    {
      name: 'eoMaterialRevisionCode',
      align: 'left',
      width: 150,
    },
    {
      name: 'componentMaterialCode',
      align: 'left',
      width: 150,
    },
    {
      name: 'componentRevisionCode',
      align: 'left',
      width: 100,
    },
    {
      name: 'assembleExcessFlag',
      width: 100,
      align: 'center',
      renderer: ({ record }) => (
        <Badge
          status={record.get('assembleExcessFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('assembleExcessFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'substituteFlag',
      width: 100,
      align: 'center',
      renderer: ({ record }) => (
        <Badge
          status={record.get('substituteFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('substituteFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    {
      name: 'disPatchDemandQty',
      width: 130,
      align: 'right',
    },
    {
      name: 'assembleQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'removeQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'scrappedQty',
      width: 100,
      align: 'right',
    },
  ];

  useEffect(() => {
    props.dataSet.query(props.dataSet.currentPage);
  }, []);

  useEffect(() => {
    function processDataSetListener(flag) {
      if (props.dataSet.queryDataSet) {
        const handler = flag
          ? props.dataSet.queryDataSet.addEventListener
          : props.dataSet.queryDataSet.removeEventListener;
        handler.call(props.dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
      }
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (!data.calendarShiftDateFrom && !data.calendarShiftDateTo) {
      record.set('shiftCode', '');
    }
  };

  let copyDrawer;
  const handleCopyDrawerShow = record => {
    detailDs.setQueryParameter('workcellId', record.get('workcellId'));
    detailDs.setQueryParameter('shiftDate', record.get('shiftDate'));
    detailDs.setQueryParameter('shiftCode', record.get('shiftCode'));
    detailDs.setQueryParameter('materialId', record.get('materialId'));
    detailDs.setQueryParameter('revisionCode', record.get('revisionCode'));
    detailDs.query();
    copyDrawer = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.detail`).d('班次装配实绩详情'),
      drawer: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal copy-drawer-modal',
      children: (
        <Table dataSet={detailDs} columns={columnsDetailData} />
      ),
      footer: (
        <>
          <div style={{ float: 'right' }}>
            <Button
              onClick={() => {
                copyDrawer.close();
              }}
            >
              {intl.get(`${modelPrompt}.back`).d('返回')}
            </Button>
          </div>
        </>
      ),
    });
  };

  return (
    <div className="hmes-style">
      <PageHeaderWrapper title={intl.get(`${modelPrompt}.title.list`).d('班次装配实绩')}>
        <Table
          queryBar='filterBar'
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={props.dataSet}
          columns={[...columnsData, operateColumns]}
          searchCode="AssembleReport"
          customizedCode="AssembleReport"
        />
      </PageHeaderWrapper>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.workshop.assembleReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...entranceDS(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(entrance),
);
