import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.EquipmentPointMaintenance';
// const Host = `/key-ne-focus-mes-34861`;

const tableDs = () => {
  return {
    name: 'tableDs',
    paging: true,
    autoQuery: true,
    selection: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'assemblePointCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('装配点描述'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
      },
    ],
    queryFields: [
      {
        name: 'siteCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        labelWidth: 150,
      },
      {
        name: 'siteId',
        type: 'number',
        bind: 'siteCode.siteId',
        labelWidth: 150,
      },
      {
        name: 'assemblePointCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
        pattern: '^[a-zA-Z0-9_]{0,}$',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('装配点描述'),
      },
      {
        type: 'string',
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'MT.ENABLE_FLAG',
        // defaultValue: 'Y',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-assemble-points/get/assemble/point`,
          method: 'GET',
        };
      },
    },
  };
};

const formDs = () => {
  return {
    name: 'formDs',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'siteObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.siteObj`).d('站点'),
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        labelWidth: 150,
        textField: 'siteCode',
        ignore: 'always',
        required: true,
      },
      {
        name: 'siteCode',
        type: 'string',
        bind: 'siteObj.siteCode',
        labelWidth: 150,
      },
      {
        name: 'siteId',
        type: 'number',
        bind: 'siteObj.siteId',
        labelWidth: 150,
      },
      {
        name: 'assemblePointCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
        required: true,
        pattern: '^[a-zA-Z0-9_-]{0,}$',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('装配点描述'),
      },
      {
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        type: 'string',
        trueValue: 'Y',
        falseValue: 'N',
        required: true,
      },
      {
        name: 'pointTypeLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.pointTypeLov`).d('装配点类型'),
        lovCode: 'HME.ASSEMBLE_POINT_TYPE_LOV',
        labelWidth: 150,
        ignore: 'always',
      },
      {
        name: 'assemblePointType',
        type: 'string',
        bind: 'pointTypeLov.assemblePointTypeCode',
      },
      {
        name: 'assemblePointTypeName',
        type: 'string',
        bind: 'pointTypeLov.assemblePointTypeName',
      },
    ],
  };
};

const detailTableDs = () => {
  return {
    name: 'detailTableDs',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        type: 'string',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'uomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomName`).d('物料单位'),
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'maxQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.maxQty`).d('最大装载量'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-assemble-point-materials/get/assemble/point/material`,
          method: 'GET',
        };
      },
    },
  };
};

const detailFormDs = xFormDS => {
  return {
    name: 'detailFormDs',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
        lovCode: 'HME.ASSEMBL_EPOINT_SITE_MATERIAL',
        labelWidth: 150,
        ignore: 'always',
        textField: 'materialCode',
        dynamicProps: {
          lovPara: () => {
            const siteId = xFormDS.current.data.siteObj.siteId;
            return {
              tenantId: getCurrentOrganizationId(),
              siteId,
            };
          },
        },
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'materialSiteId',
        type: 'number',
        bind: 'materialObj.materialSiteId',
      },
      {
        name: 'materialCode',
        type: 'string',
        bind: 'materialObj.materialCode',
      },
      {
        name: 'revisionFlag',
        type: 'string',
        bind: 'materialObj.revisionFlag',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'materialObj.materialName',
        disabled: true,
      },
      {
        name: 'uomId',
        type: 'number',
        bind: 'materialObj.uomId',
      },
      {
        name: 'uomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomName`).d('物料单位'),
        bind: 'materialObj.uomName',
        disabled: true,
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        bind: 'materialObj.revisionCode',
        textField: 'revisionCode',
        valueField: 'revisionCode',
        lookupUrl: `${Host}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
        lookupAxiosConfig: ({ record }) => {
          const _params = record?.toData() || {};
          const siteId = xFormDS?.current?.data?.siteId;
          return {
            params: {
              materialSiteId: _params.materialSiteId,
              siteId,
            },
            transformResponse(data) {
              // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
              if (data instanceof Array) {
                return data;
              }
              if (data.failed) {
                return [];
              } 
              const rows = JSON.parse(data);
              return rows;
              
            },
          };
        },
        dynamicProps: {
          disabled: record => {
            const _params = record?.record.data || {};
            if (
              _params.materialObj &&
              _params.materialObj.revisionFlag &&
              _params.materialObj.revisionFlag === 'Y'
            ) {
              return false;
            }
            return true;
          },
        },
      },
      {
        name: 'maxQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.maxQty`).d('最大装载量'),
      },
      {
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        type: 'string',
        trueValue: 'Y',
        falseValue: 'N',
      },
    ],
  };
};
export { tableDs, formDs, detailTableDs, detailFormDs };
