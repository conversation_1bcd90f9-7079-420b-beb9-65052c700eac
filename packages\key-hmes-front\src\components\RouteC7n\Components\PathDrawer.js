/**
 * @description: 工艺路线(制造/物料)-路径选择
 * @date 2022-8-25
 * <AUTHOR> <<EMAIL>>
 */
import React, { Component } from 'react';
import intl from 'utils/intl';
import { Card, Checkbox, Input, Radio, Select } from 'hzero-ui';
import { DataSet, NumberField, Table, Button, Lov, Select as C7nSelect } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { DETAIL_CARD_TABLE_CLASSNAME } from 'utils/constants';
import uuid from 'uuid/v4';
import { cloneDeep, isArray } from 'lodash';
import notification from 'utils/notification';
import myInstance from '@/utils/myAxios';
import { pathTableDS } from '../stories/PathDrawerTableDS';
import '@/assets/iconfonts/iconfont.css';
import styles from './PathDrawer.module.less';

const modelPrompt = 'tarzan.process.routes.model.routes';

export default class PathDrawer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      drawerData: cloneDeep(props.stepsList) || [], // 将stepsList数据化为己用，在保存时再进行覆盖

      thisStepsList: cloneDeep(props.stepsList) || [], // 左侧步骤列表
      currentStepKeyValue: null, // 左侧步骤列表搜索过滤内容
      currentStepFilterType: 'all', // 左侧步骤列表过滤类型
      selectedCurrentStepId: '', // 选中的左侧当前步骤Id

      nextStepsList: cloneDeep(props.stepsList) || [], // 右侧下一步骤列表
      nextStepKeyValue: null, // 右侧下一步骤列表搜索过滤内容
      nextStepFilterType: 'all', // 右侧下一步骤列表过滤类型
      selectedNextStepIds: [], // 右侧下一步骤列表选中Id集合

      stepsTableList: [], // 工艺步骤中，所有步骤的连线信息
      lastTargetValue: '',
    };
  }

  pathTableDs = new DataSet({ ...pathTableDS(this.props.componentLov, this.props.currentBomId, this.props.tenantId) });

  componentDidMount() {
    const {
      editStatus,
      stepsList,
    } = this.props;
    this.setState({
      stepsTableList: this.createSortedStepsTableList(stepsList, editStatus),
    });
    // load 步骤分配信息表格中的数据
    this.pathTableDs.loadData([...this.createSortedStepsTableList(stepsList, editStatus)])
  }

  createSortedStepsTableList(list, editStatus) {
    const stepsTableList = [];
    // 转换表格数据
    list.forEach(item => {
      if (isArray(item.mtRouterNextStepDTO) && item.mtRouterNextStepDTO.length > 0) {
        const stepLineCurrentData = {
          // 当前步骤
          currentStepId: item.routerStepId,
          currentStepDescription: item.description,
          currentStepName: item.stepName,
          currentStepSequence: item.sequence,
        };
        item.mtRouterNextStepDTO.forEach(i => {
          const stepLine = {
            ...stepLineCurrentData,
            // 下一步骤
            nextStepId: i.nextStepId,
            nextStepDescription: i.nextStepDesc,
            nextStepName: i.nextStepName,
            nextStepSequence: list.find(em => em.routerStepId === i.nextStepId)?.sequence,
            // 步骤连线顺序
            sequence: i.sequence,
            // 下一步骤策略
            nextDecisionType: i.nextDecisionType,
            // 决策值
            nextDecisionObj: i.nextDecisionObj || i.nextDecisionValue, // 回显值
            nextDecisionValue: i.nextDecisionValue,
            nextDecisionValueCode: i.nextDecisionValueCode,
            // 该条数据行id
            routerNextStepId: i.routerNextStepId,
            uuid: uuid(),
            // 编辑状态
            _status: editStatus ? '' : 'update',
          };
          stepsTableList.push(stepLine);
        });
      }
    });
    return stepsTableList.sort((a, b) => a.sequence - b.sequence);
  }

  onOk = async () => {
    const validate = await this.pathTableDs.validate();
    if (!validate) {
      return;
    }

    const {
      UpdateStepList,
    } = this.props;
    const { drawerData } = this.state;

    const stepsTableList =  this.pathTableDs.toData();

    const newSaveData = drawerData.map(item => {
      const newMtRouterNextStepDTO = [];
      stepsTableList.forEach(i => {
        if (i.currentStepId === item.routerStepId) {
          const nextStepInfo = {
            nextDecisionType: i.nextDecisionType,
            nextDecisionObj: i.nextDecisionObj,
            nextDecisionValue: i.nextDecisionType === 'NC' ? (i.nextDecisionObj?.ncCodeId || i.nextDecisionObj) : (i.nextDecisionObj?.nextDecisionValue || ''),
            nextDecisionValueCode: i.nextDecisionType === 'NC' ? (i.nextDecisionObj?.ncCode || i.nextDecisionObj) : i.nextDecisionObj?.bomComponentMaterialCode,
            nextStepDesc: i.nextStepDescription,
            nextStepId: i.nextStepId,
            nextStepName: i.nextStepName,
            routerNextStepId: i.routerNextStepId,
            routerStepId: i.currentStepId,
            sequence: i.sequence,
          };
          newMtRouterNextStepDTO.push(nextStepInfo);
        }
      });
      return {
        ...item,
        mtRouterNextStepDTO: newMtRouterNextStepDTO,
      };
    });
    UpdateStepList(newSaveData);
    notification.success();
  };

  // 按顺序自动生成
  sortSequence = async () => {
    const {
      routesItem,
      stepsList,
      serveCode,
      tenantId,
    } = this.props;

    // 按顺序自动生成
    const url = `${serveCode}/v1/${tenantId}/mt-router/sort/ui`;

    const res = await myInstance.post(url, {
      ...routesItem,
      mtRouterStepDTO: stepsList,
    });

    if (res) {
      const dataList = this.createSortedStepsTableList(res.data.rows.mtRouterStepDTO || []);
      this.setState({
        stepsTableList: dataList,
      });
      // 渲染右侧列表
      const { drawerData, nextStepFilterType, stepsTableList, lastTargetValue } = this.state;
      const nextStepIds = [];
      stepsTableList.forEach(item => {
        if (item.currentStepId === lastTargetValue) {
          nextStepIds.push(item.nextStepId);
        }
      });
      const tempList1 = this.filterNextStepListWithStepType(
        this.sortNextStepsList(drawerData, lastTargetValue, nextStepIds),
        nextStepFilterType,
      );
      this.setState({
        selectedCurrentStepId: lastTargetValue,
        selectedNextStepIds: nextStepIds,
        nextStepsList: tempList1,
        nextStepKeyValue: '',
      });
      // 渲染步骤分配信息的表格
      this.pathTableDs.loadData([...dataList]);
      notification.success();
    }
  };

  // 渲染左侧当前步骤列表
  renderCurrentStepList = list => {
    const stepOptions = [];
    const { stepsTableList } = this.state;
    const sourceStepsIdList = stepsTableList.map(item => item.currentStepId);
    list.forEach(item => {
      stepOptions.push(
        <Radio value={item.routerStepId} className={styles.radioBox}>
          {item.sequence}-{item.description}
          {item.stepName && '/'}
          {item.stepName}
          <span className={styles.imgWrapper}>
            <i
              style={{
                visibility: item.routerDoneStepFlag === 'Y' ? 'visible' : 'hidden',
                color: '#D9D9D9',
              }}
              className="iconfont icondone-step-icon"
            />
            <i
              style={{
                visibility: item.entryStepFlag === 'Y' ? 'visible' : 'hidden',
                color: '#D9D9D9',
              }}
              className="iconfont iconentry-step-icon"
            />
            <i
              style={{
                visibility: sourceStepsIdList.includes(item.routerStepId) ? 'visible' : 'hidden',
                color: '#0840f8',
              }}
              className="iconfont iconbottpm-arrow-icon"
            />
          </span>
        </Radio>,
      );
    });
    return stepOptions;
  };

  // 渲染右侧下一步骤列表
  renderNextStepList = list => {
    const stepOptions = [];
    const { stepsTableList } = this.state;
    const targetStepsIdList = stepsTableList.map(item => item.nextStepId);

    list.forEach(item => {
      stepOptions.push(
        <Checkbox value={item.routerStepId} className={styles.radioBox}>
          {item.sequence}-{item.description}
          {item.stepName && '/'}
          {item.stepName}
          <span
            className={styles.imgCheckWrapper}
            style={{
              visibility: targetStepsIdList.includes(item.routerStepId) ? 'visible' : 'hidden',
            }}
          >
            <i className="iconfont icontop-arrow-icon" />
          </span>
        </Checkbox>,
      );
    });
    return stepOptions;
  };

  handleSelectCurrentStep = e => {
    this.setState({
      lastTargetValue: e.target.value,
    });
    const { drawerData, nextStepFilterType, stepsTableList } = this.state;
    const selectedId = e.target.value;
    const nextStepIds = [];
    stepsTableList.forEach(item => {
      if (item.currentStepId === selectedId) {
        nextStepIds.push(item.nextStepId);
      }
    });
    const tempList1 = this.filterNextStepListWithStepType(
      this.sortNextStepsList(drawerData, selectedId, nextStepIds),
      nextStepFilterType,
    );
    this.setState({
      selectedCurrentStepId: selectedId,
      selectedNextStepIds: nextStepIds,
      nextStepsList: tempList1,
      nextStepKeyValue: '',
    });
  };

  handleSelectNextSteps = checkedValues => {
    const { drawerData, selectedCurrentStepId, selectedNextStepIds, stepsTableList } = this.state;
    //  1.找到新增/删除的step，判断是新增还是删除
    // 取差集
    let arrA = [];
    let arrB = [];
    let selectType = '';
    if (checkedValues.length > selectedNextStepIds.length) {
      arrA = checkedValues;
      arrB = selectedNextStepIds;
      selectType = 'add';
    } else {
      arrA = selectedNextStepIds;
      arrB = checkedValues;
      selectType = 'desc';
    }
    let newList = [];
    if (this.pathTableDs.dirty) {
      newList = this.pathTableDs.toData()
      this.setState({
        stepsTableList: newList,
      });
    } else {
      newList = stepsTableList
    }
    const diffStepId = arrA.concat(arrB).filter(v => arrA.includes(v) && !arrB.includes(v))[0];
    const currentStepInfo = drawerData.find(item => item.routerStepId === selectedCurrentStepId);
    const diffStepInfo = drawerData.find(item => item.routerStepId === diffStepId);
    //  2.给表格新增/删除行数据
    if (selectType === 'add') {
      const row = {
        currentStepId: currentStepInfo.routerStepId,
        currentStepDescription: currentStepInfo.description,
        currentStepName: currentStepInfo.stepName,
        currentStepSequence: currentStepInfo.sequence,
        nextStepId: diffStepInfo.routerStepId,
        nextStepDescription: diffStepInfo.description,
        nextStepName: diffStepInfo.stepName,
        nextStepSequence: diffStepInfo.sequence,
        sequence: newList.length
          ? Number(newList[newList.length - 1].sequence) + 10
          : 10,
        nextDecisionType: 'MAIN',
        nextDecisionObj: undefined,
        nextDecisionValue: undefined,
        nextDecisionValueCode: undefined,
        routerNextStepId: undefined,
        uuid: uuid(),
        _status: 'update',
      };
      newList.push(row);
    } else {
      newList = newList.filter(
        item => item.currentStepId !== selectedCurrentStepId || item.nextStepId !== diffStepId,
      );
    }
    this.setState({
      selectedNextStepIds: checkedValues,
      stepsTableList: newList,
    });
    this.pathTableDs.loadData([...newList]);
  };

  /**
   * 为传入的下一步骤列表进行排序
   * 使下一步骤的排序成为
   * 当前步骤分配的下一步骤
   * 未被分配的步骤
   * 已分配给其他步骤的下一步骤
   * (再以sequence进行排序)
   *
   * @param {Array} list 用来排序的列表
   * @param {number} selectedCurrentId 选中的当前步骤Id
   * @param {Array} nextStepIds 下一步骤列表，选中步骤Id集合
   */
  sortNextStepsList = (list, selectedCurrentId, nextStepIds) => {
    const assignedNextStepsList = [];
    const unAssignedNextStepsList = [];
    list.forEach(item => {
      if (nextStepIds.includes(item.routerStepId)) {
        assignedNextStepsList.push(item);
      } else if (item.routerStepId !== selectedCurrentId) {
        unAssignedNextStepsList.push(item);
      }
    });
    function assignSort(a, b) {
      const x = isArray(a.mtRouterNextStepDTO) && a.mtRouterNextStepDTO.length > 0 ? 1 : 0;
      const y = isArray(b.mtRouterNextStepDTO) && b.mtRouterNextStepDTO.length > 0 ? 1 : 0;
      return y - x;
    }
    return assignedNextStepsList
      .sort((a, b) => a.sequence - b.sequence)
      .concat(unAssignedNextStepsList.sort((a, b) => a.sequence - b.sequence).sort(assignSort));
  };

  filterCurrentStepListWithStepType(list, stepType) {
    if (stepType === 'all') {
      // 全部
      return list;
    } if (stepType === 'assigned') {
      // 已分配
      return list.filter(i => isArray(i.mtRouterNextStepDTO) && i.mtRouterNextStepDTO.length > 0);
    }
    // 未分配
    return list.filter(
      i => !isArray(i.mtRouterNextStepDTO) || i.mtRouterNextStepDTO.length === 0,
    );

  }

  filterStepListWithKeyValue(list, KeyValue) {
    if (!KeyValue) {
      return list;
    }
    return list.filter(
      i => `${i.sequence}-${i.description}/${i.stepName}`.indexOf(KeyValue) !== -1,
    );
  }

  filterNextStepListWithStepType = (list, stepType) => {
    const { stepsTableList } = this.state;
    const tempAssignedStepsList = stepsTableList.map(item => item.nextStepId);
    if (stepType === 'all') {
      // 全部
      return list;
    } if (stepType === 'assigned') {
      // 已分配
      return list.filter(i => tempAssignedStepsList.includes(i.routerStepId));
    }
    // 未分配
    return list.filter(i => !tempAssignedStepsList.includes(i.routerStepId));
  };

  handleChangeCurrentStepType = val => {
    const { drawerData, currentStepKeyValue } = this.state;
    const newList = this.filterCurrentStepListWithStepType(drawerData, val);
    this.setState({
      currentStepFilterType: val,
      thisStepsList: this.filterStepListWithKeyValue(newList, currentStepKeyValue),
    });
  };

  handleChangeCurrentStepKeyValue = e => {
    const { drawerData, currentStepFilterType } = this.state;
    const newList = this.filterCurrentStepListWithStepType(drawerData, currentStepFilterType);
    this.setState({
      currentStepKeyValue: e.target.value.trim(),
      thisStepsList: this.filterStepListWithKeyValue(newList, e.target.value.trim()),
    });
  };

  handleChangeNextStepType = val => {
    const { drawerData, nextStepKeyValue, selectedCurrentStepId, selectedNextStepIds } = this.state;
    const tempList1 = this.sortNextStepsList(
      drawerData,
      selectedCurrentStepId,
      selectedNextStepIds,
    );
    const tempList2 = this.filterNextStepListWithStepType(tempList1, val);
    this.setState({
      nextStepFilterType: val,
      nextStepsList: this.filterStepListWithKeyValue(tempList2, nextStepKeyValue),
    });
  };

  handleChangeNextStepKeyValue = e => {
    const {
      drawerData,
      nextStepFilterType,
      selectedCurrentStepId,
      selectedNextStepIds,
    } = this.state;
    const tempList1 = this.sortNextStepsList(
      drawerData,
      selectedCurrentStepId,
      selectedNextStepIds,
    );
    const tempList2 = this.filterNextStepListWithStepType(tempList1, nextStepFilterType);
    this.setState({
      nextStepKeyValue: e.target.value.trim(),
      nextStepsList: this.filterStepListWithKeyValue(tempList2, e.target.value.trim()),
    });
  };

  handleSearchNextStepKeyValue = value => {
    const {
      drawerData,
      nextStepFilterType,
      selectedCurrentStepId,
      selectedNextStepIds,
    } = this.state;
    const tempList1 = this.sortNextStepsList(
      drawerData,
      selectedCurrentStepId,
      selectedNextStepIds,
    );
    const tempList2 = this.filterNextStepListWithStepType(tempList1, nextStepFilterType);
    this.setState({
      nextStepKeyValue: value,
      nextStepsList: this.filterStepListWithKeyValue(tempList2, value),
    });
  };

  pathTableColumn = [
    {
      name: 'currentStepDescription',
      renderer: ({ record }) => {
        return `${record.get("currentStepSequence")}-${record.get("currentStepDescription")} ${
          record.get("currentStepName") ? `/${record.get("currentStepName")}` : ''
        }`;
      },
    },
    {
      name: 'nextStepDescription',
      renderer: ({ record }) => {
        return `${record.get("nextStepSequence")}-${record.get("nextStepDescription")} ${
          record.get("nextStepName") ? `/${record.get("nextStepName")}` : ''
        }`;
      },
    },
    {
      name: 'sequence',
      editor: () =>
        !this.props.editStatus && <NumberField />,
    },
    {
      name: 'nextDecisionType',
      editor: record => !this.props.editStatus && <C7nSelect onChange={() => this.changeSelect(record)} />,

    },
    {
      name: 'nextDecisionObj',
      editor: record => {
        if (!this.props.editStatus && record.get('nextDecisionType') !== 'MAIN') {
          return <Lov />
        }
      },
    },
  ];

  changeSelect = (record) => {
    record.init('nextDecisionObj');
    record.set('nextDecisionValue', '');
    record.set('nextDecisionValueCode', '');
  };

  render() {
    const {
      editStatus,
      stepsList,
    } = this.props;
    const {
      nextStepKeyValue,
      nextStepFilterType,
      selectedCurrentStepId,
      selectedNextStepIds,
      currentStepKeyValue,
      currentStepFilterType,
      thisStepsList = [],
      nextStepsList = [],
    } = this.state;

    const iconStyle = {
      color: selectedCurrentStepId ? '#0840f8' : '#D9D9D9',
      fontSize: '18px',
    };
    return (
      <div className={styles.pathChoose}>
        <Card
          key="step-header"
          title={intl.get(`${modelPrompt}.stepDis`).d('步骤分配')}
          bordered={false}
          className={DETAIL_CARD_TABLE_CLASSNAME}
          size="small"
          extra={
            <Button
              color={ButtonColor.primary}
              disabled={editStatus || stepsList.length === 0}
              onClick={this.sortSequence}
            >
              {intl.get(`${modelPrompt}.seqAuto`).d('按顺序自动生成')}
            </Button>
          }
        >
          <div className={styles.topStepWrapper}>
            <div className={styles.stepWrapper}>
              <div className={styles.stepHeader}>
                <span>{intl.get(`${modelPrompt}.stepList`).d('步骤列表')}</span>
                <div className={styles.stepTitleDropdown}>
                  <Select
                    value={currentStepFilterType}
                    style={{ width: 102 }}
                    onChange={this.handleChangeCurrentStepType}
                  >
                    <Select.Option value="all">
                      {intl.get(`${modelPrompt}.stepAll`).d('全部步骤')}
                    </Select.Option>
                    <Select.Option value="assigned">
                      {intl.get(`${modelPrompt}.stepAssigned`).d('已分配步骤')}
                    </Select.Option>
                    <Select.Option value="unassigned">
                      {intl.get(`${modelPrompt}.stepUnassigned`).d('未分配步骤')}
                    </Select.Option>
                  </Select>
                </div>
              </div>
              <div className={styles.stepBottom}>
                <Input.Search
                  allowClear
                  dbc2sbc={false}
                  value={currentStepKeyValue}
                  style={{ width: 'calc(100% - 16px)' }}
                  placeholder={intl.get(`${modelPrompt}.inputSearch`).d('请输入搜索内容')}
                  onChange={this.handleChangeCurrentStepKeyValue}
                />
                <div className={styles.boxWrapper}>
                  <div className={styles.leftPathBox}>
                    <Radio.Group
                      onChange={this.handleSelectCurrentStep}
                      value={selectedCurrentStepId}
                    >
                      {this.renderCurrentStepList(thisStepsList)}
                    </Radio.Group>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.iconWrapper}>
              <i className="iconfont iconweb_youjiantoucomponent" style={iconStyle} />
            </div>
            <div className={styles.stepWrapper}>
              <div className={styles.stepHeader}>
                <span>{intl.get(`${modelPrompt}.nextStepList`).d('下一步骤列表')}</span>
                <div className={styles.stepTitleDropdown}>
                  <Select
                    style={{ width: 102 }}
                    value={nextStepFilterType}
                    onChange={this.handleChangeNextStepType}
                  >
                    <Select.Option value="all">
                      {intl.get(`${modelPrompt}.stepAll`).d('全部步骤')}
                    </Select.Option>
                    <Select.Option value="assigned">
                      {intl.get(`${modelPrompt}.stepAssigned`).d('已分配步骤')}
                    </Select.Option>
                    <Select.Option value="unassigned">
                      {intl.get(`${modelPrompt}.stepUnassigned`).d('未分配步骤')}
                    </Select.Option>
                  </Select>
                </div>
              </div>
              <div className={styles.stepBottom}>
                <Input.Search
                  allowClear
                  dbc2sbc={false}
                  value={nextStepKeyValue}
                  style={{ width: 'calc(100% - 16px)' }}
                  placeholder={intl.get(`${modelPrompt}.inputSearch`).d('请输入搜索内容')}
                  onChange={this.handleChangeNextStepKeyValue}
                  onSearch={this.handleSearchNextStepKeyValue}
                />
                <div className={styles.boxWrapper}>
                  <div className={styles.rightPathBox}>
                    <Checkbox.Group
                      onChange={this.handleSelectNextSteps}
                      value={selectedNextStepIds}
                      disabled={editStatus || !selectedCurrentStepId}
                    >
                      {this.renderNextStepList(nextStepsList)}
                    </Checkbox.Group>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
        <Card
          key="next-step-header"
          title={intl.get(`${modelPrompt}.stepDisInfo`).d('步骤分配信息')}
          bordered={false}
          className={DETAIL_CARD_TABLE_CLASSNAME}
          size="small"
        >
          <Table dataSet={this.pathTableDs} columns={this.pathTableColumn}/>
        </Card>
      </div>
    );
  }
}
