import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;
const modelPrompt = 'tarzan.receive.scrapBarcodeGeneration';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'eoId',
    paging: true,
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'siteId',
        type: 'string',
      },
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      },
      {
        name: 'siteName',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
      },
      {
        name: 'equipLov',
        type: 'object',
        lovCode: 'HME.PLATE_EQUIPMENT',
        ignore: 'always',
        required: true,
        label: intl.get(`${modelPrompt}.equipLov`).d('设备编码'),
        textField: 'equipmentCode',
      },
      {
        name: 'equipmentId',
        type: 'number',
        bind: 'equipLov.equipmentId',
      },
      {
        name: 'workcellId',
        type: 'number',
        bind: 'equipLov.workcellId',
      },

      {
        name: 'operationId',
        type: 'number',
      },
      {
        name: 'equipmentCode',
        type: 'string',
        bind: 'equipLov.equipmentCode',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        bind: 'equipLov.equipmentName',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      },
      {
        name: 'prodLineCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      },
      {
        name: 'prodLineId',
        type: 'number',
      },
      {
        name: 'workOrderLov',
        type: 'object',
        lovCode: 'HME.PROLDLINE_RELEASED_WO',
        ignore: 'always',
        required: true,
        textField: 'workOrderNum',
        label: intl.get(`${modelPrompt}.workOrderLov`).d('工单编码'),
        dynamicProps: {
          lovPara: ({record}) => {
            return {
              prodLineId: record.get('prodLineId'),
            };
          },
          disabled: ({record}) => {
            return !record.get('prodLineId')
          },
        },
      },
      {
        name: 'workOrderNum',
        type: 'string',
        bind: 'workOrderLov.workOrderNum',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      },
      {
        name: 'workOrderId',
        type: 'number',
        bind: 'workOrderLov.workOrderId',
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'workOrderLov.materialId',
      },
      {
        name: 'materialCode',
        type: 'string',
        bind: 'workOrderLov.materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        bind: 'workOrderLov.materialName',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'uomId',
        type: 'number',
        bind: 'workOrderLov.uomId',
      },
      {
        name: 'uomName',
        type: 'string',
        bind: 'workOrderLov.uomName',
        label: intl.get(`${modelPrompt}.uomName`).d('单位'),
      },
      {
        name: 'eoQty',
        type: 'number',
        min: 0,
        precision: 3,
        required: true,
        label: intl.get(`${modelPrompt}.eoQty`).d('数量'),
      },
      {
        name: 'ncCodeLov',
        type: 'object',
        lovCode: 'MT.METHOD.NC_CODE ',
        ignore: 'always',
        required: true,
        textField: 'ncCode',
        valueField: 'ncCodeId',
        label: intl.get(`${modelPrompt}.ncCodeLov`).d('不良代码'),
      },
      {
        name: 'ncCodeId',
        bind: 'ncCodeLov.ncCodeId',
      },
      {
        name: 'ncCode',
        bind: 'ncCodeLov.ncCode',
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoIdentification`).d('报废条码'),
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('创建人'),
      },
    ],
    queryFields: [
      {
        name: 'prodLov',
        type: 'object',
        lovCode: 'MT.MODEL.PRODLINE',
        ignore: 'always',
        textField: 'prodLineCode',
        label: intl.get(`${modelPrompt}.prodLov`).d('产线编码'),
      },
      {
        name: 'prodLineId',
        bind: 'prodLov.prodLineId',
      },
      {
        name: 'prodLineCode',
        bind: 'prodLov.prodLineCode',
      },
      {
        name: 'equipLov',
        type: 'object',
        lovCode: 'HME.PLATE_EQUIPMENT',
        ignore: 'always',
        textField: 'equipmentCode',
        label: intl.get(`${modelPrompt}.equipLov`).d('设备编码'),
      },
      {
        name: 'workcellId',
        type: 'number',
        bind: 'equipLov.workcellId',
      },
      {
        name: 'equipmentCode',
        type: 'string',
        bind: 'equipLov.equipmentCode',
      },
      {
        name: 'equipmentId',
        type: 'number',
        bind: 'equipLov.equipmentId',
      },
      {
        name: 'materialLov',
        type: 'object',
        lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.materialLov`).d('自制件物料'),
      },
      {
        name: 'materialId',
        bind: 'materialLov.materialId',
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('报废条码'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-partial-scrap/eo/query`,
          method: 'GET',
        };
      },
    },
  };
};



export {tableDS};
