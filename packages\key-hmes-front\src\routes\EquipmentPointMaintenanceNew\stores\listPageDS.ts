import intl from 'utils/intl';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.equipmentPointMaintenanceList.table';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'assemblePointId',
  queryFields: [
    {
      name: 'siteCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteCodeLov.siteId',
    },
    {
      name: 'assemblePointCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
      // pattern: '^[a-zA-Z0-9_]{0,}$',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('装配点描述'),
    },
    {
      type: FieldType.string,
      name: 'enableFlag',
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'assemblePointCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('装配点描述'),
    },
    {
      name: 'enableFlag',
      trueValue: 'Y',
      falseValue: 'N',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-points/get/assemble/point`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
