import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.deviceLock';

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'equipmentLockId',
    selection: false,
    paging: false,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'equipmentCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.equipmentName`).d('设备名称'),
      },
      {
        name: 'workcellCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.workcellCode`).d('工位编码'),
      },
    
      {
        name: 'lockStatus',
        label: intl.get(`${modelPrompt}.form.lockStatus`).d('锁定状态'),
          lookupCode:'MT.FLAG',
          type: FieldType.string,
      },
      {
        name: 'lockReasonMeaning',
        label: intl.get(`${modelPrompt}.form.lockReason`).d('锁定原因'),
        type: FieldType.string,
      },
      {
        name: 'lockTime',
        label: intl.get(`${modelPrompt}.form.lockTime`).d('锁定时间'),
        type: FieldType.dateTime,
      },
      {
        name: 'inspectRequestCode',
        label: intl.get(`${modelPrompt}.form.inspectRequestCode`).d('报检请求编码'),
        type: FieldType.string,
      },
      {
        name: 'inspectDocCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.inspectDocCode`).d('检验单号'),
      },
      {
        name: 'inspectBusinessType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.inspectBusinessType`).d('检验业务类型'),
      },
      {
        name: 'inspectReqUserRealName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.inspectReqUserRealName`).d('报检人'),
      },
      {
        name: 'inspectorRealName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.inspectorRealName`).d('检验员'),
      },
      {
        name: 'unlockReason',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.unlockReason`).d('解锁原因'),
      },
      {
        name: 'unlockTime',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.form.unlockTime`).d('解锁时间'),
      },
      {
        name: 'status',
        type: FieldType.string,
        lookupCode:'HME.EQU_LOCK_STATUS',
        label: intl.get(`${modelPrompt}.form.status`).d('审批状态'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-equipment-locks/query`,
          transformResponse: value => {
            let listData: any = {};
            try {
              listData = JSON.parse(value);
            } catch (err) {
              listData = {
                message: err,
              };
            }
            if (!listData.success) {
              return {
                ...listData,
                failed: true,
              };
            }
            return listData;
          },
        };
      },
    },
  });

export default listPageFactory;
