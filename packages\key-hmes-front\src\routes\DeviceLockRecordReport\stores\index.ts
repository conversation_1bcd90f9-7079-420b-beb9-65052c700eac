import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'recordId',
  queryFields: [
    {
      name: 'lockName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lockName`).d('接口名称'),
    },
    {
      name: 'equipmentCodes',
      type: FieldType.string,
      multiple: true,
      label: intl.get(`${modelPrompt}.equipmentCodes`).d('设备编码列表'),
    },
    {
      name: 'processBarcodes',
      type: FieldType.string,
      multiple: true,
      label: intl.get(`${modelPrompt}.processBarcodes`).d('产品条码'),
    },
    {
      name: 'materialLotCodes',
      type: FieldType.string,
      multiple: true,
      label: intl.get(`${modelPrompt}.materialLotCodes`).d('物料批条码'),
    },
    {
      name: 'lockTypeLov',
      type: FieldType.object,
      lookupCode: 'HME.REDISSON_LOCK_TYPE',
      textField: 'meaning',
      valueField: 'value',
      label: intl.get(`${modelPrompt}.lockTypeLov`).d('锁类型'),
    },
    {
      name: 'lockType',
      bind: 'lockTypeLov.value',
    },
    {
      name: 'attribute1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute1  `).d('锁定状态'),
    },
    {
      name: 'startLockTime',
      type: FieldType.dateTime,
      required: true,
      label: intl.get(`${modelPrompt}.startLockTime`).d('开始锁定时间'),
    },
    {
      name: 'endLockTime',
      type: FieldType.dateTime,
      required: true,
      min: 'startLockTime',
      label: intl.get(`${modelPrompt}.endLockTime`).d('结束锁定时间'),
    },
    {
      name: 'startUnlockTime',
      type: FieldType.dateTime,
      max: 'endUnlockTime',
      label: intl.get(`${modelPrompt}.startUnlockTime`).d('开始解锁时间'),
    },
    {
      name: 'endUnlockTime',
      type: FieldType.dateTime,
      min: 'startUnlockTime',
      label: intl.get(`${modelPrompt}.endUnlockTime`).d('结束解锁时间'),
    },
  ],
  fields: [
    {
      name: 'lockName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lockName`).d('接口名称'),
    },
    {
      name: 'lockTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lockTypeDesc`).d('锁定类型'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'processBarcode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processBarcode`).d('条码'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批条码'),
    },
    {
      name: 'lockKey',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lockKey`).d('锁key'),
    },
    {
      name: 'lockKeyEntity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lockKeyEntity`).d('锁对象'),
    },
    {
      name: 'attribute1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute1`).d('锁定状态'),
    },
    {
      name: 'startTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
    },
    {
      name: 'lockTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lockTime`).d('锁定时间'),
    },
    {
      name: 'unlockTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unlockTime`).d('解锁时间'),
    },
    {
      name: 'waitTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.waitTime`).d('等待耗时'),
    },
    {
      name: 'executeTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.executeTime`).d('执行耗时'),
    },
    {
      name: 'attribute2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute2`).d('解锁失败原因'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-redisson-lock-records/page/ui`,
        method: 'POST',
      };
    },
  },
});

export { tableDS };
