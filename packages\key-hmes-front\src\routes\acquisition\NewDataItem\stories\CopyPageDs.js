/**
 * @feature 新数据收集项维护-复制页面信息
 * @date 2021-3-14
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentLanguage } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.acquisition.dataItem.model.dataItem';

const copySourceDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'tagCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetTagCode`).d('目标数据项编码'),
      required: true,
    },
    {
      name: 'tagDescription',
      label: intl.get(`${modelPrompt}.targetTagDescription`).d('目标数据项描述'),
      type: FieldType.intl,
    },
  ],
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.mes.domain.entity.MtTag';
      return {
        data: { tagDescription: record.data.tagDescription },
        params: { fieldName, className },
        url: `${BASIC.HMES_BASIC}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
});

export { copySourceDS };
