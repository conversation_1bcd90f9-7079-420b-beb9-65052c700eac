import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.cassetteMaterial';

const historyFactory = () =>
    new DataSet({
        primaryKey: 'hisKeyId',
        selection: false,
        paging: true,
        autoQuery: false,
        dataKey: 'rows.content',
        totalKey: 'rows.totalElements',
        queryDataSet: new DataSet({
            fields: [
                {
                    name: 'creationFrom',
                    type: FieldType.dateTime,
                    label: intl.get(`${modelPrompt}.form.creationFrom`).d('创建时间从'),
                },
                {
                    name: 'creationTo',
                    type: FieldType.dateTime,
                    label: intl.get(`${modelPrompt}.form.creationTo`).d('创建时间至'),
                },]
        }),
        fields: [
            {
                name: 'siteCode',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.siteCode`).d('站点'),
            },
            {
                name: 'assembleGroupCode',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.assembleGroupCode`).d('装配组编码'),
            },
            {
                name: 'description',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.description`).d('装配组描述'),
            },
            {
                name: 'enableFlag',
                lookupCode: 'MT.YES_NO',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.enableFlag`).d('有效性'),
            },
            {
                name: 'creationDate',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.creationDate`).d('创建时间'),
            },
            {
                name: 'realName',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.realName`).d('创建人'),
            },
        ],
        transport: {
            read: (config: AxiosRequestConfig): AxiosRequestConfig => {
                return {
                    ...config,
                    method: 'GET',
                    url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-his-Assembly-select/list/ui`,
                };
            },
        },
    });

export default historyFactory;
