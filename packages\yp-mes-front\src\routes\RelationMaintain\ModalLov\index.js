import React, { useState, useEffect, useRef } from 'react';
import { Modal, message, Form } from 'hzero-ui';
import { isEmpty, isFunction } from 'lodash';
// import uuid from 'uuid/v4';

import intl from 'utils/intl';
import { getResponse } from '@utils/utils';

import LovModal from './LovModal';
import { queryLov, queryMapIdpValue } from './api';
import './index.module.less';

const defaultRowKey = 'key';
const ModalLov = props => {
  const [record, setRecord] = useState([]);
  const [ldpData, setLdpData] = useState({});
  const [title, setTitle] = useState('');
  const [width, setWidth] = useState(400);
  const [loading, setLoading] = useState(false);
  const [lov, setLov] = useState({});

  const { queryParams, requireValue = '', visible = false, form } = props;

  const modalRef = useRef(null);

  useEffect(() => {
    if (visible) {
      onSearch();
    }
  }, [visible]);

  const onSelect = value => {
    setRecord(value || []);
  };

  const selectRecord = () => {
    const { valueField: rowkey = defaultRowKey } = lov;

    const values = (record || []).map(item => {
      return item[rowkey];
    });
    if (props.onChange) {
      props.onChange(values, record);
    }
  };

  const onCancel = () => {
    const cancel = props.onCancel;
    if (isFunction(cancel)) {
      cancel();
    }
  };

  const modalWidth = tableFields => {
    let _width = 100;
    tableFields.forEach(n => {
      _width += n.width;
    });
    return _width;
  };

  const onSearch = () => {
    const {
      lovOptions: { valueField: customValueField, displayField: customDisplayField } = {},
      requireCode = [],
    } = props;
    if (loading) return; // 节流

    const {
      code: viewCode,
      queryParams: { tenantId },
    } = props;
    setLoading(true);
    queryLov({ viewCode, tenantId })
      .then(oriLov => {
        const _lov = { ...oriLov };
        let needFirstQuery = true;
        // const requireParamsCode = [];
        const { queryFields } = _lov;
        const newQuerFields = [];
        queryFields.forEach(item => {
          const _item = { ...item };
          if (requireCode.indexOf(item.field) > -1) {
            // if (item.dataType === 'SELECT') {
            //   requireParamsCode.push([item.sourceCode, item.field]);
            // }
            _item.require = true;
            if (item.dataType === null) {
              needFirstQuery = false;
            }
          } else {
            _item.require = false;
          }
          newQuerFields.push(_item);
        });
        _lov.queryFields = newQuerFields;
        if (customValueField) {
          _lov.valueField = customValueField;
        }
        if (customDisplayField) {
          _lov.displayField = customDisplayField;
        }
        if (!isEmpty(_lov)) {
          const { viewCode: hasCode, tableFields } = _lov;
          const lastSetp = () => {
            if (hasCode && needFirstQuery) {
              const _width = modalWidth(tableFields);
              setLov(_lov);
              setTitle(_lov.title);
              setWidth(_width);
              modalRef.current.loadBeforeSetDefaultValue();
              modalRef.current.loadOnFirstVisible();
            } else {
              setLoading(false);
              message.error(
                intl.get('hzero.common.components.lov.notification.undefined').d('值集视图未定义!'),
              );
            }
          };

          // 获取独立值集编码
          const valueList = _lov.queryFields.filter(
            item => item.dataType === 'SELECT' && item.sourceCode !== 'multiple',
          );
          if (valueList.length > 0) {
            const valueCode = {};
            valueList.forEach(({ sourceCode }) => {
              if (sourceCode) {
                valueCode[sourceCode] = sourceCode;
              }
            });
            queryMapIdpValue(valueCode)
              .then(res => {
                if (getResponse(res)) {
                  setLdpData(res);
                  lastSetp();
                } else {
                  lastSetp();
                }
              })
              .catch(() => {
                lastSetp();
              });
          } else {
            lastSetp();
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const modalProps = {
    title,
    width,
    destroyOnClose: true,
    wrapClassName: 'lov-modal',
    maskClosable: false,
    onOk: selectRecord,
    bodyStyle: title ? { padding: '16px' } : { padding: '56px 16px 0' },
    onCancel,
    style: {
      minWidth: 400,
    },
    visible,
  };
  return (
    <Modal {...modalProps}>
      <LovModal
        lov={lov}
        ldpData={ldpData}
        queryParams={queryParams}
        onSelect={onSelect}
        lovLoadLoading={loading}
        ref={modalRef}
        requireValue={requireValue}
        form={form}
      />
    </Modal>
  );
};
export default Form.create()(ModalLov);
