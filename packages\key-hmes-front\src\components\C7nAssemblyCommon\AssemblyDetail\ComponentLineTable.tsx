/**
 * @Description: 装配清单 - 组件行表格（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/7/26 14:32
 * @LastEditTime: 2023-05-18 15:03:23
 * @LastEditors: <<EMAIL>>
 */
import React, { FC, useEffect, useState } from 'react';
import { Button, DataSet, Modal, Table } from 'choerodon-ui/pro';
import { Icon, Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import { runInAction } from 'mobx';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { AttributeDrawer, drawerPropsC7n } from '@components/tarzan-ui';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import uuid from 'uuid/v4';
import { BASIC } from '@/utils/config';
import { ComponentLineDrawer } from './ComponentLineDrawer';
import { SubstituteItemDrawer } from './SubstituteItemDrawer';
import { ReferencePointDrawer } from './ReferencePointDrawer';
import styles from './index.module.less';

export interface ComponentLineTableProps {
  canEdit: boolean;
  lineDs: DataSet;
  substituteDrawerDs: DataSet;
  referencePointDrawerDs: DataSet;
  modelPrompt: string;
  path: string;
  id: string;
  siteIds: any;
  currentBomComponentId: number;
  setBomComponentId: any;
  attributeServerCode: string;
  customizeForm: any;
  customizeTable: any;
  custConfig: any;
  custCode: string;
}

export const ComponentLineTable: FC<ComponentLineTableProps> = props => {
  const {
    canEdit,
    lineDs,
    substituteDrawerDs,
    referencePointDrawerDs,
    modelPrompt,
    siteIds,
    path,
    id,
    currentBomComponentId,
    setBomComponentId,
    attributeServerCode,
    customizeForm,
    customizeTable,
    custConfig,
    custCode,
  } = props;
  const [ascendingFlag, setAscendingFlag] = useState(id !== 'create'); // 行号升序排列标识
  let _componentLineDrawer; // 组件行详情抽屉

  // 点击组件行详情抽屉“确认”按钮回调
  const handleSaveComponentLine = async record => {
    // form表单校验
    const formValidateResult = await record?.validate();
    if (!formValidateResult) {
      return;
    }
    // 校验通过后更新record中的state变量状态
    record.setState('isSubmit', true);
    record.setState('isCancel', false);
    // 点击确认后需要将当前的值变成初识值，否则后续取消会有问题
    record.init({ ...record.toData() });
    _componentLineDrawer.close();
  };

  // 关闭组件行编辑抽屉
  const handleCloseModal = record => {
    record.setState('isSubmit', false);
    record.setState('isCancel', true);
    _componentLineDrawer.close();
  };

  // 新增组件行的回调
  const handleAddLine = () => {
    // 获得当前组件行的最大行号
    let maxLineNumber = 0;
    lineDs.forEach(record => {
      if (record.get('lineNumber') > maxLineNumber) {
        maxLineNumber = record.get('lineNumber');
      }
    });
    // 打开组件行抽屉
    handleComponentLineDrawerShow(
      lineDs.create(
        {
          lineNumber: parseInt(String(maxLineNumber / 10), 10) * 10 + 10,
          uuid: uuid(),
        },
        0,
      ),
      true,
    );
  };

  // 打开组件行编辑抽屉
  const handleComponentLineDrawerShow = (record, isNew) => {
    record.setState('isCancel', false);
    record.setState('isSubmit', false);
    _componentLineDrawer = Modal.open({
      key: Modal.key(),
      title: record?.get('bomComponentId')
        ? intl.get('tarzan.product.bom.title.componentLineEdit').d('组件行编辑')
        : intl.get('tarzan.product.bom.title.componentLineCreate').d('组件行新增'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      onClose: () =>
        record.getState('isSubmit')
          ? record.setState('isCancel', false)
          : record.setState('isCancel', true),
      afterClose: () =>
        record.getState('isCancel') && (isNew ? lineDs.remove(record) : record.reset()),
      children: <ComponentLineDrawer
        record={record}
        canEdit={canEdit}
        customizeForm={customizeForm}
        custCode={custCode}
      />,
      footer: !canEdit ? (
        <div style={{ float: 'right' }}>
          <Button onClick={() => handleCloseModal(record)}>
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        </div>
      ) : (
        <div style={{ float: 'right' }}>
          <Button onClick={() => handleCloseModal(record)}>
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            type={ButtonType.submit}
            color={ButtonColor.primary}
            onClick={() => handleSaveComponentLine(record)}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  // 当升序标识ascendingFlag变化时，对表格中的数据做升序或降序处理
  useEffect(() => {
    // dataSet的records并不是原生js的数组，而是MobX的observable数组，参考mobx文档，有sort方法，但不会改变数组本身，而只是返回一个排序的拷贝
    const newRecords = ascendingFlag
      ? lineDs.records.sort((a, b) => a.get('lineNumber') - b.get('lineNumber'))
      : lineDs.records.sort((a, b) => b.get('lineNumber') - a.get('lineNumber'));

    runInAction(() => {
      lineDs.records = newRecords;
    });
  }, [ascendingFlag]);

  // 返回Table中”行号“列对应的header
  const sortHeader = (title: string) => {
    return (
      <div className={styles['header-sort']} onClick={() => setAscendingFlag(prev => !prev)}>
        <span>{title}</span>
        <Icon
          type={`${ascendingFlag ? 'arrow_upward' : 'arrow_downward'}`}
          className={styles['sort-icon']}
        />
      </div>
    );
  };

  // 删除表格某一行的回调
  const deleteRecord = record => {
    lineDs.remove(record);
  };

  // 组件行表格columns
  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit || !siteIds.length}
          onClick={handleAddLine}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      name: 'editColumn',
      align: ColumnAlign.center,
      lock: ColumnLock.left,
      width: 80,
      hideable: false,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={!siteIds.length || record?.get('bomComponentId')}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
    },
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      width: 100,
      hideable: false,
      header: sortHeader(intl.get(`${modelPrompt}.lineNumber`).d('排序号')),
    },
    {
      name: 'materialCode',
      width: 200,
      hideable: false,
      renderer: ({ value, record }) => (
        <a disabled={!siteIds.length} onClick={() => handleComponentLineDrawerShow(record, false)}>
          {value}
        </a>
      ),
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'materialName',
      width: 200,
    },
    {
      name: 'bomComponentTypeDesc',
      width: 150,
    },
    {
      name: 'unitQty',
      width: 100,
    },
    {
      name: 'qty',
      width: 100,
      renderer: ({ record }) => {
        // unitQty字符串
        const unitQtyStr = record?.get('unitQty')?.toString();
        // unitQty小数位数
        let unitQtyLength = 0;
        // primaryQty字符串
        const primaryQtyStr = (lineDs.parent!.current?.get('primaryQty') || 1).toString();
        // unitQty小数位数
        let primaryQtyLength = 0;
        if (unitQtyStr?.indexOf('.') !== -1) {
          // 如果有小数点
          unitQtyLength = unitQtyStr?.split('.')[1].length;
        }
        if (primaryQtyStr.indexOf('.') !== -1) {
          // 如果有小数点，求unitQty小数位数
          primaryQtyLength = primaryQtyStr.split('.')[1].length;
        }
        // 总共小数位数
        const maxLength = (unitQtyLength + primaryQtyLength) > 6 ? 6 : (unitQtyLength + primaryQtyLength);
        const data = (
          record?.get('unitQty') * (lineDs.parent!.current?.get('primaryQty') || 1)
        ).toFixed(maxLength);
        return Number(data);
      },
    },
    {
      name: 'uomName',
      width: 100,
    },
    {
      name: 'dateFrom',
      width: 200,
      align: ColumnAlign.center,
    },
    {
      name: 'dateTo',
      width: 200,
      align: ColumnAlign.center,
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      width: 300,
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a disabled={!siteIds.length} onClick={() => openSubstituteDrawer(record)}>
              {intl.get(`${modelPrompt}.substituteMaterial`).d('替代物料')}
            </a>
            <a disabled={!siteIds.length} onClick={() => openReferencePointDrawer()}>
              {intl.get(`${modelPrompt}.referencePoint`).d('参考点')}
            </a>
            {record?.get('bomComponentId') ? (
              <AttributeDrawer
                type="text"
                canEdit={canEdit}
                disabled={!siteIds.length || !record?.get('bomComponentId')}
                kid={record?.get('bomComponentId')}
                // tablename="mt_bom_component_attr"
                className="org.tarzan.method.domain.entity.MtBomComponent"
                serverCode={attributeServerCode}
                custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.${custCode}.BUTTON_COMP`}
                custConfig={custConfig}
              />
            ) : (
              <a disabled>{intl.get('tarzan.common.button.extendedField').d('扩展属性')}</a>
            )}
          </span>
        );
      },
    },
  ];

  // 对行表查询条回车事件的处理
  const handleLineQuery = () => {
    const queryMaterialCode = lineDs.queryDataSet?.current?.get('materialCode') || '';
    const queryMaterialName = lineDs.queryDataSet?.current?.get('materialName') || '';
    lineDs.forEach(record => {
      const currentMaterialCode = record.get('materialCode');
      const currentMaterialName = record.get('materialName');
      if (
        currentMaterialCode.includes(queryMaterialCode) &&
        currentMaterialName.includes(queryMaterialName)
      ) {
        record.setState('displayFlag', 'Y');
      } else {
        record.setState('displayFlag', 'N');
      }
    });
  };

  // 打开替代物料抽屉的回调
  const openSubstituteDrawer = record => {
    if (record.get('bomComponentId') && currentBomComponentId !== record.get('bomComponentId')) {
      const recordData = record.toData();
      const newList = (recordData.mtBomSubstituteGroupList || []).filter(
        item => item.substitutePolicy === 'PRIORITY',
      );
      substituteDrawerDs.loadData([...(newList[0]?.mtBomSubstituteList || [])]);
      setBomComponentId(record.get('bomComponentId'));
    }
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
        ds: substituteDrawerDs,
      }),
      key: Modal.key(),
      title: intl.get('tarzan.product.bom.title.componentAndSubstitute').d('装配清单行与替代组'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      afterClose: () => { },
      children: (
        <SubstituteItemDrawer
          modelPrompt={modelPrompt}
          substituteDrawerDs={substituteDrawerDs}
          canEdit={canEdit}
          path={path}
        />
      ),
    });
  };

  // 打开参考点抽屉的回调
  const openReferencePointDrawer = () => {
    Modal.open({
      ...drawerPropsC7n({
        canEdit: false,
        ds: referencePointDrawerDs,
      }),
      key: Modal.key(),
      title: intl.get('tarzan.product.bom.title.componentAndReference').d('装配清单行与参考点关系'),
      style: {
        width: 1080,
      },
      afterClose: () => { },
      children: (
        <ReferencePointDrawer
          referencePointDrawerDs={referencePointDrawerDs}
          canEdit={canEdit}
          path={path}
        />
      ),
    });
  };

  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.COMP`,
    },
    <Table
      className={styles['expand-table']}
      dataSet={lineDs}
      columns={columns}
      queryBar={TableQueryBarType.bar}
      queryBarProps={{
        onQuery: handleLineQuery,
      }}
      filter={record => {
        return (
          record.status !== 'delete' &&
          (!record.getState('displayFlag') || record.getState('displayFlag') === 'Y')
        );
      }}
    />,
  )
};
