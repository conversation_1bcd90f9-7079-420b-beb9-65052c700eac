import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.ass.specialCollection';

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'keyId',
    selection: DataSetSelection.multiple,
    paging: true,
    autoQuery: false,
    forceValidate: true,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'tagLov',
        required: true,
        type: FieldType.object,
        ignore: FieldIgnore.always,
        label: intl.get(`${modelPrompt}.ncCode`).d('收集项编码'),
        lovCode: 'MT.TAG',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'tagId',
        bind: 'tagLov.tagId'
      },
      {
        name: 'tagName',
        disabled: true,
        bind: 'tagLov.tagDescription',
        label: intl.get(`${modelPrompt}.tagName`).d('收集项描述'),
      },
      {
        name: 'tagCode',
        disabled: true,
        bind: 'tagLov.tagCode',
      },
      {
        name: 'equipmentLov',
        required: true,
        type: FieldType.object,
        ignore: FieldIgnore.always,
        label: intl.get(`${modelPrompt}.equipmentCode`).d('关联设备'),
        lovCode: 'MT.MODEL.EQUIPMENT',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId'
      },
      {
        name: 'equipmentCode',
        type: FieldType.string,
        bind: 'equipmentLov.equipmentCode'
      },
      {
        name: 'materialLov',
        required: true,
        type: FieldType.object,
        ignore: FieldIgnore.always,
        label: intl.get(`${modelPrompt}.materialCode`).d('关联物料'),
        lovCode: 'MT.MATERIAL.PERMISSION',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'materialId',
        bind: 'materialLov.materialId'
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        bind: 'materialLov.materialCode'
      },
      {
        name: 'missingNcCodeLov',
        required: true,
        label: intl.get(`${modelPrompt}.form.unNcCode`).d('缺失值不良代码'),
        type: FieldType.object,
        ignore: FieldIgnore.always,
        lovCode: 'MT.METHOD.NC_CODE',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'missingNcCodeId',
        type: FieldType.string,
        bind: 'missingNcCodeLov.ncCodeId'
      },
      {
        name: 'missingNcCode',
        type: FieldType.string,
        bind: 'missingNcCodeLov.ncCode'
      },
      {
        name: 'defaultNcCodeLov',
        required: true,
        label: intl.get(`${modelPrompt}.form.defaultNcCode`).d('默认不良代码'),
        type: FieldType.object,
        ignore: FieldIgnore.always,
        lovCode: 'MT.METHOD.NC_CODE',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'defaultNcCodeId',
        type: FieldType.string,
        bind: 'defaultNcCodeLov.ncCodeId'
      },
      {
        name: 'defaultNcCode',
        type: FieldType.string,
        bind: 'defaultNcCodeLov.ncCode'
      },
      {
        name: 'userName',
        disabled: true,
        label: intl.get(`${modelPrompt}.form.userName`).d('最后更新人'),
        type: FieldType.string,
      },
      {
        name: 'lastUpdateDate',
        disabled: true,
        label: intl.get(`${modelPrompt}.form.lastUpdateDate`).d('最后更新时间'),
        type: FieldType.dateTime,
      },
      {
        name: 'remark',
        label: intl.get(`${modelPrompt}.form.remark`).d('备注'),
        type: FieldType.string,
      },
      {
        name: 'conditionTagCodeLov',
        required: true,
        type: FieldType.object,
        ignore: FieldIgnore.always,
        label: intl.get(`${modelPrompt}.conditionTagCodeLov`).d('条件收集项编码'),
        lovCode: 'MT.TAG',
        lovPara: {
          tenantId,
        },
        textField: 'tagCode',
      },
      {
        name: 'conditionTagId',
        bind: 'conditionTagCodeLov.tagId',
      },
      {
        name: 'conditionTagCode',
        bind: 'conditionTagCodeLov.tagCode',
      },
      {
        name: 'conditionTagName',
        label: intl.get(`${modelPrompt}.conditionTagName`).d('条件收集项名称'),
        bind: 'conditionTagCodeLov.tagDescription',
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-look-process/head/query`,
          // transformResponse: value => {
          //   let listData: any = {};
          //   try {
          //     listData = JSON.parse(value);
          //   } catch (err) {
          //     listData = {
          //       message: err,
          //     };
          //   }
          //   if (!listData.success) {
          //     return {
          //       ...listData,
          //       failed: true,
          //     };
          //   }
          //   return listData;
          // },
        };
      },
    },
  });

export default listPageFactory;
