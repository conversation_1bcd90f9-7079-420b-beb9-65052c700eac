/**
 * 组件介绍: 功能级别组件，应用在以下3个功能。
 * 1.mes产品的制造装配清单
 * 2.mes产品的物料装配清单
 * 3.aps产品的计划装配清单
 * ----------------------------
 * @Description: 装配清单 - 详情页面（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/7/25 15:54
 * @LastEditTime: 2023-05-18 15:03:15
 * @LastEditors: <<EMAIL>>
 */
import React, { FC, useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  DateTimePicker,
  Form,
  Modal,
  NumberField,
  Select,
  Switch,
  TextField,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import { AttributeDrawer, C7nFormItemSort, drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@/utils/config';
import { saveAssemblyDetail, confirmAssembly, copyBom } from '../services/index';
import {
  copyDrawerDS,
  detailDS,
  lineDS,
  referencePointDrawerDS,
  siteListDS,
  substituteDrawerDS,
} from '../stores/DetailDS';
import { ComponentLineTable, ComponentLineTableProps } from './ComponentLineTable';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.product.bom.model.bom';

/**
 * @description 装配清单 - 详情页面入参
 * @param {string} featureTitle 列表页标题title
 * @param {any} history 页面路由的 history 对象
 * @param {any} match 页面路由相关的 match 对象
 * @param {string} attributeServerCode 扩展属性访问的服务
 * @param {string} uiServerCode 功能接口（如详情页的查询接口）要访问的服务
 * @param {string} typeGroup 详情界面类型的值集code
 * @param {any} customizeForm 详情页-表单个性化组件
 * @param {any} customizeTable 详情页-表格个性化组件
 * @param {any} custConfig 详情页-个性化配置数据
 * @param {any} custCode 详情页-个性化单元编码，中间的字符串(eg: ${BASIC.CUSZ_CODE_BEFORE}.{customizeTable}.QUERY)
 * @return {Component} 装配清单详情页面组件 <AssemblyDetail />
 */
export interface AssemblyDetailProps {
  featureTitle: string;
  history: any;
  match: any;
  attributeServerCode: string;
  uiServerCode: string;
  typeGroup: string;
  customizeForm: any;
  customizeTable: any;
  custConfig: any;
  custCode: string;
  location: any;
}

export const AssemblyDetail: FC<AssemblyDetailProps> = props => {
  const {
    featureTitle,
    history,
    match: {
      path,
      params: { id },
    },
    attributeServerCode,
    uiServerCode,
    typeGroup,
    customizeForm,
    customizeTable,
    custConfig,
    custCode,
    location,
  } = props;
  const basePath = useMemo(() => path.substring(0, path.indexOf('/dist')), []);

  const [canEdit, setCanEdit] = useState<boolean>(false);
  const [siteIds, setSiteIds] = useState([]); // 用于存放站点id
  const [currentBomComponentId, setBomComponentId] = useState<number>(0); // 用于存放当前打开的“替代组”对应的组件行id
  const [copiedFromBomId, setCopiedFromBomId] = useState<number>(0); // 用于存放来源装配清单id

  // 站点列表的DS
  const siteListDs = useMemo(() => new DataSet({ ...siteListDS(uiServerCode) }), []);
  // 替代物料抽屉DS
  const substituteDrawerDs = useMemo(() => new DataSet({ ...substituteDrawerDS() }), []);
  // 参考点抽屉DS
  const referencePointDrawerDs = useMemo(() => new DataSet({ ...referencePointDrawerDS() }), []);
  // 组件行DS
  const lineDs = useMemo(
    () =>
      new DataSet({
        ...lineDS(),
        children: {
          mtBomSubstituteList: substituteDrawerDs,
          mtBomReferencePointList: referencePointDrawerDs,
        },
      }),
    [],
  );
  // 基础信息DS
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(uiServerCode, typeGroup, siteListDs),
        children: {
          mtBomComponentList: lineDs,
        },
      }),
    [],
  );
  // 复制抽屉的DS
  const copyDrawerDs = useMemo(() => new DataSet({ ...copyDrawerDS(typeGroup) }), []);

  // 保存装配清单前进行验证
  const { run: handleConfirmAssembly, loading: confirmAssemblyLoading } = useRequest(
    confirmAssembly(uiServerCode),
    {
      manual: true,
    },
  );
  // 保存装配清单
  const { run: handleSaveAssembly, loading: saveAssemblyLoading } = useRequest(
    saveAssemblyDetail(uiServerCode, custCode),
    {
      manual: true,
      showNotification: false,
    },
  );
  // 装配清单-复制
  const { run: handleCopyBom } = useRequest(copyBom(uiServerCode), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      return;
    }
    initPageData();
  }, [props.match.params.id]);

  // 初始化详情界面数据
  const initPageData = async () => {
    setCanEdit(false);
    setBomComponentId(0);
    detailDs.setQueryParameter('bomId', id);
    detailDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.BASIC,${BASIC.CUSZ_CODE_BEFORE}.${custCode}.COMP`);
    await detailDs.query();
    setCopiedFromBomId(detailDs.current!.get('copiedFromBomId'));
    detailDs.setState('originBomStatus', detailDs.current!.get('bomStatus'));
    // 进入页面时初始化站点信息
    siteListDs.setQueryParameter('bomId', id);
    siteListDs.query().then(res => {
      const _siteIds: any = [];
      res.forEach((item: any) => {
        if (item.distributedFlag) {
          _siteIds.push(item.siteId);
        }
      });
      setSiteIds(_siteIds);
      detailDs.current!.init('siteIds', _siteIds);
    });
  };

  const handleCancel = () => {
    if (id === 'create') {
      history.push(`${basePath}/list`);
    } else {
      setCanEdit(prev => !prev);
      setBomComponentId(0);
      initPageData();
    }
  };

  const handleSave = () => {
    // 修改替代物料提交的数据层级
    const componentLineData = lineDs.toData();
    componentLineData.forEach((item: any) => {
      const newList = (item.mtBomSubstituteGroupList || []).filter(
        item => item.substitutePolicy === 'PRIORITY',
      );
      const sourceData = newList.length
        ? newList[0]
        : {
          substituteGroup: 'SUBSTITUTE_GROUP',
          substitutePolicy: 'PRIORITY',
          enableFlag: 'Y',
        };
      item.mtBomSubstituteGroupList = [
        {
          ...sourceData,
          mtBomSubstituteList: item.mtBomSubstituteList,
        },
      ];
      item.mtBomSubstituteList = undefined;
    });
    // 这里如果用await处理的话，c7n Button默认的loading和useRequest返回的loading会冲突
    detailDs.validate().then(res => {
      if (res) {
        handleSaveAssembly({
          params: {
            ...detailDs.toData()[0],
            mtBomComponentList: componentLineData,
          },
          onSuccess: saveRes => {
            notification.success({});
            if (id === 'create' || id !== saveRes) {
              props.history.push(`${basePath}/dist/${saveRes}`);
            } else {
              initPageData();
            }
          },
          onFailed: saveRes => {
            if (saveRes.rows === 'MT_BOM_0072') {
              Modal.confirm({
                title: intl.get(`tarzan.common.title.tips`).d('提示'),
                children: (
                  <div>
                    {intl
                      .get('tarzan.product.bom.info.existing')
                      .d('该BOM已有当前版本，是否确认更换当前版本？')}
                  </div>
                ),
              }).then(button => {
                if (button === 'ok') {
                  handleConfirmAssembly({
                    params: {
                      ...detailDs.toData()[0],
                      mtBomComponentList: componentLineData,
                    },
                    onSuccess: confirmRes => {
                      notification.success({});
                      props.history.push(`${basePath}/dist/${confirmRes}`);
                      initPageData();
                    },
                  });
                }
              });
            } else {
              notification.error({
                message:
                  saveRes.message || intl.get('hzero.common.notification.error').d('操作失败'),
              });
            }
          },
        });
      }
    });
  };

  // 站点Select变化的回调
  const handleChangeSite = val => {
    setSiteIds(val || []);
    // 站点变化时需要清空组件行上的投料库位
    lineDs.forEach(record => {
      record.init('issuedLocatorLov', undefined);
      record.init('issuedLocatorId', undefined);
      record.init('issuedLocatorCode', undefined);
      record.init('issuedLocatorName', undefined);
    });
  };

  // 自动升版本标识变化的回调
  const handleAutoRevisionFlag = () => {
    detailDs.current?.init('revision', undefined);
  };

  /**
   *  跳转到来源装配清单
   */
  const goToBom = () => {
    const _copiedFromService = detailDs!.current!.get('copiedFromService');
    const _copiedFromBomId = detailDs!.current!.get('copiedFromBomId');
    if (_copiedFromService === 'MES') {
      // 跳转到制造装配清单
      history.push(`/hmes/product/manufacture-list/dist/${_copiedFromBomId}`);
    } else if (_copiedFromService === 'APS') {
      // 跳转到APS装配清单
      history.push(`/aps/orderAssembly/dist/${_copiedFromBomId}`);
    } else if (_copiedFromService === 'METHOD') {
      // 跳转到物料装配清单
      history.push(`/hmes/product/assembly-list/dist/${_copiedFromBomId}`);
    } else {
      // 为空的话跳转到自身
      history.push(`${basePath}/dist/${_copiedFromBomId}`);
    }
  };

  // 点击“装配清单复制”抽屉确认按钮的回调
  const handleCopyDrawerOk = async () => {
    const valRes = await copyDrawerDs.validate();
    if (!valRes) {
      return;
    }
    return handleCopyBom({
      params: {
        ...copyDrawerDs.toData()[0],
        bomId: id,
      },
      onSuccess: res => {
        notification.success({});
        props.history.push(`${basePath}/dist/${res}`);
      },
    });
  };

  // 点击“复制”按钮的回调
  const handleCopy = () => {
    Modal.open({
      ...drawerPropsC7n({
        ds: copyDrawerDs?.current,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.bomCopy`).d('装配清单复制'),
      destroyOnClose: true,
      style: {
        width: 360,
      },
      onOk: handleCopyDrawerOk,
      children: (
        <Form dataSet={copyDrawerDs} columns={1}>
          <TextField name="bomNameNew" />
          <TextField name="revisionNew" />
          <Select name="bomTypeNew" />
        </Form>
      ),
    });
  };

  const lineProps: ComponentLineTableProps = {
    canEdit,
    lineDs,
    substituteDrawerDs,
    referencePointDrawerDs,
    path,
    id,
    modelPrompt,
    siteIds,
    currentBomComponentId,
    setBomComponentId,
    attributeServerCode,
    customizeForm,
    customizeTable,
    custConfig,
    custCode,
  };

  return (
    <div className="hmes-style">
      <Header title={featureTitle} backPath={`${basePath}/list`}>
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              loading={saveAssemblyLoading || confirmAssemblyLoading}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`tarzan.common.button.save`).d('保存')}
            </PermissionButton>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            disabled={detailDs?.current?.get('bomStatus') === 'ABANDON' || location?.state?.from === 'productionOrderMgt'}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`tarzan.common.button.edit`).d('编辑')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          icon="copy"
          disabled={canEdit || detailDs?.current?.get('bomStatus') === 'ABANDON' || detailDs?.current?.get('bomStatus') === 'FREEZE'}
          onClick={handleCopy}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.copy').d('复制')}
        </PermissionButton>
        <AttributeDrawer
          // tablename="mt_bom_attr"
          className="org.tarzan.method.domain.entity.MtBom"
          kid={id}
          canEdit={canEdit}
          disabled={id === 'create'}
          serverCode={attributeServerCode}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.${custCode}.BUTTON`}
          custConfig={custConfig}
          // type="text"
        />
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['baseInfo', 'componentLine']}>
          <Panel
            header={intl.get('tarzan.product.bom.title.detail').d('装配清单基础信息')}
            key="baseInfo"
            dataSet={detailDs}
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.BASIC`,
              },
              <Form labelWidth={112} dataSet={detailDs} columns={3}>
                <TextField name="bomName" disabled={!canEdit} />
                <TextField name="revision" disabled={!canEdit} />
                <Switch name="currentFlag" disabled={!canEdit} />
                <Select
                  name="siteIds"
                  searchable
                  colSpan={2}
                  disabled={!canEdit}
                  onChange={handleChangeSite}
                  onOption={({ record }) => {
                    const disabled = !record.get('permissionFlag');
                    return {
                      disabled,
                    };
                  }}
                />
                <Switch name="usageFlag" disabled={!canEdit} />
                <TextField name="description" disabled={!canEdit} />
                <Select name="bomStatus" disabled={!canEdit} clearButton={false} />
                <Switch name="assembleAsMaterialFlag" disabled={!canEdit} />
                <Select name="bomType" disabled={!canEdit} />
                <NumberField name="primaryQty" disabled={!canEdit} />
                <Switch
                  name="autoRevisionFlag"
                  disabled={!canEdit}
                  onChange={handleAutoRevisionFlag}
                />
                <DateTimePicker name="dateFrom" disabled={!canEdit} />
                <DateTimePicker name="dateTo" disabled={!canEdit} />
                <C7nFormItemSort name="copiedFromBomName" itemWidth={['70%', '15%', '15%']}>
                  <TextField name="copiedFromBomName" disabled={!canEdit} />
                  <TextField name="copiedFromBomRevision" disabled={!canEdit} />
                  <Button
                    funcType={FuncType.link}
                    icon="link"
                    disabled={!copiedFromBomId}
                    onClick={goToBom}
                  // onClick={() => props.history.push(`${basePath}/dist/${copiedFromBomId}`)}
                  />
                </C7nFormItemSort>
              </Form>,
            )}
          </Panel>
          <Panel
            header={intl.get('tarzan.product.bom.title.componentLine').d('组件行')}
            key="componentLine"
            dataSet={detailDs}
          >
            <ComponentLineTable {...lineProps} />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};
