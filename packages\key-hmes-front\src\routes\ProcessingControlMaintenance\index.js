/*
 * @Author: 47844 <EMAIL>
 * @Date: 2024-12-12 10:33:33
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2024-12-12 17:50:53
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\ProcessingControlMaintenance\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState, useMemo, useEffect } from 'react';
import { DataSet, Table, Button, Switch, Lov, Select, Modal } from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import request from 'utils/request';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import {
  tableDS,
  singleAssObjectDS,
  assObjectsDS,
  siteDS,
  historyHeadTableDS,
  historyLineTableDS,
} from './stores/ProcessingControlMaintenanceDs';
import LineList from './LineList';
import { Host } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.processingControlMaintenance';
// const Host = `/yp-mes-20000`;

const tenantId = getCurrentOrganizationId();

const ProcessingControlMaintenance = observer(() => {
  const [canEdit, setCanEdit] = useState(false);
  const [historyQueryBtnDisbled, setHistoryQueryBtnDisbled] = useState(true);
  const siteDs = useMemo(() => new DataSet(siteDS()), []);
  const singleAssObjectDs = useMemo(() => new DataSet(singleAssObjectDS()), []); // 复制ds
  const assObjectsDs = useMemo(() => new DataSet(assObjectsDS()), []);
  const tableDs = useMemo(() => new DataSet(tableDS(siteDs)), []);
  const historyHeadTableDs = useMemo(() => new DataSet(historyHeadTableDS()), []);
  const historyLineTableDs = useMemo(() => new DataSet(historyLineTableDS()), []);


  useEffect(() => {
    tableDs.query();
    siteDs.query();
    tableDs.addEventListener('select', handleDataSetSelect);
    tableDs.addEventListener('selectAll', handleDataSetSelect);
    tableDs.addEventListener('unSelect', handleDataSetSelect);
    tableDs.addEventListener('unSelectAll', handleDataSetSelect);
    tableDs.addEventListener('query', handleClear);
    return () => {
      tableDs.removeEventListener('select', handleDataSetSelect);
      tableDs.removeEventListener('unSelect', handleDataSetSelect);
      tableDs.removeEventListener('query', handleClear);
    };
  }, []);
  const handleClear = () => {
    assObjectsDs.loadData([]);
  };

  const handleDataSetSelect = () => {
    const selectedLength = tableDs.selected;
    if (selectedLength.length > 0) {
      setHistoryQueryBtnDisbled(false);
      assObjectsDs.setQueryParameter(
        'processCtrlConfigId',
        selectedLength[0].data.processCtrlConfigId,
      );
      assObjectsDs.query();
    } else {
      assObjectsDs.loadData([]);
    }
  };

  // 保存
  const handelSave = async () => {
    if (tableDs.validate() && assObjectsDs.validate()) {
      let params = [];
      const tableList = tableDs.toJSONData();
      const assObjectsData = assObjectsDs.toJSONData();
      // 原来的deleteFlag保持不动
      if (tableList.length > 0) {
        tableList.forEach(item => {
          item.deleteFlag = item._status === 'delete' ? 'Y' : null;
          if (item.processCtrlConfigId === assObjectsData[0]?.processCtrlConfigId) {
            item.lineList = assObjectsData;
          } else {
            item.lineList = [];
          }
        });
        params = tableList;
      } else if (assObjectsData.length > 0) {
        assObjectsData.forEach(item => {
          item.deleteFlag = item._status === 'delete' ? 'Y' : null;
        });
        const obj = {
          ...tableDs.selected[0].data,
          lineList: assObjectsData,
        };
        params.push(obj);
      }
      const res = await request(`${Host}/v1/${tenantId}/hme-process-ctrl-configs/save/ui`, {
        method: 'post',
        body: params,
      });
      const result = getResponse(res);
      if (result) {
        notification.success();
        setCanEdit(false);
        tableDs.query();
        assObjectsDs.loadData([]);
      }
    }
  };

  // 编辑按钮
  const handelEdit = () => {
    setCanEdit(true);
  };

  // 取消按钮
  const handelCancel = () => {
    setCanEdit(false);
    tableDs.query();
    assObjectsDs.loadData([]);
  };

  // lov变化事件
  const changeObject = (lovRecords, record) => {
    if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
      record.getField('revisionCode').set('required', true);
      record.set('revisionFlag', 'Y');
      record.set('revisionCode', lovRecords.revisionCode);
    } else {
      record.getField('revisionCode').set('required', false);
      record.set('revisionCode', '');
      record.set('revisionFlag', 'N');
    }
  };

  // 版本下拉框变化事件
  const changeVersion = record => {
    if (record.data.revisionFlag === 'Y') {
      record.getField('revisionCode').set('required', true);
    }
  };

  const handleCreate = () => {
    tableDs.create();
    tableDs.select(tableDs.current);
  };

  const handleQueryHistory = async () => {
    const selectedRecords = tableDs.selected;

    if (!selectedRecords.length) return;

    const processCtrlConfigIds = selectedRecords.map(record => record.data.processCtrlConfigId);

    historyHeadTableDs.setQueryParameter('processCtrlConfigIds', processCtrlConfigIds);
    historyHeadTableDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history.query`).d('历史查询'),
      drawer: true,
      style: { width: 1080 },
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      closable: true,
      maskClosable: false,
      destroyOnClose: true,
      children: (
        <>
          <Table queryBar="filterBar" dataSet={historyHeadTableDs} columns={historyHeadColumns} />
        </>
      ),
    });
  };

  const handleCreateLine = () => {
    assObjectsDs.create({ processCtrlConfigId: tableDs.selected[0].data.processCtrlConfigId });
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!canEdit || tableDs.records.some(record => record.status === 'add')}
          onClick={() => handleCreate()}
          funcType="flat"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`${modelPrompt}.error.delete`).d(`是否确认删除？`)}
          onConfirm={() => {
            tableDs.remove(record);
          }}
        >
          <Button funcType="flat" icon="remove" shape="circle" size="small" disabled={!canEdit} />
        </Popconfirm>
      ),
      lock: 'left',
    },
    // 产线
    {
      name: 'prodLineObj',
      align: 'left',
      renderer: ({ record }) => record.get('prodLineCode'),
      editor: () => {
        return canEdit && <Lov dataSet={tableDs} name="prodLineObj" />;
      },
    },
    // 产线描述
    {
      name: 'prodLineName',
      align: 'left',
      renderer: ({ record }) => record.get('prodLineName'),
    },
    // 物料编码
    {
      name: 'materialObj',
      align: 'left',
      renderer: ({ record }) => record.get('materialCode'),
      editor: record => {
        return (
          canEdit && (
            <Lov
              dataSet={tableDs}
              name="materialObj"
              onChange={lovRecords => changeObject(lovRecords, record)}
            />
          )
        );
      },
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
    },
    // 物料版本
    {
      name: 'revisionCode',
      align: 'left',
      editor: record => {
        return (
          canEdit &&
          record.get('revisionFlag') === 'Y' && (
            <Select name="revisionCode" dataSet={tableDs} onChange={() => changeVersion(record)} />
          )
        );
      },
    },
    // 工艺
    {
      name: 'operationObj',
      align: 'left',
      renderer: ({ record }) => record.get('operationName'),
      editor: () => {
        return canEdit && <Lov dataSet={tableDs} name="operationObj" />;
      },
    },
    // 工艺描述
    {
      name: 'description',
      align: 'left',
      renderer: ({ record }) => record.get('description'),
    },
    // 工艺版本
    {
      name: 'revision',
      align: 'left',
      renderer: ({ record }) => record.get('revision'),
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      editor: canEdit && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  const historyHeadColumns = [
    // 产线
    {
      name: 'prodLineObj',
      align: 'left',
    },
    // 产线描述
    {
      name: 'prodLineDescription',
      align: 'left',
    },
    // 物料编码
    {
      name: 'materialObj',
      align: 'left',
    },
    // 物料描述
    {
      name: 'materialDescription',
      align: 'left',
    },
    // 物料版本
    {
      name: 'revisionCode',
      align: 'left',
      editor: record => {
        return (
          canEdit &&
          record.get('revisionFlag') === 'Y' && (
            <Select name="revisionCode" dataSet={tableDs} onChange={() => changeVersion(record)} />
          )
        );
      },
    },
    // 工艺
    {
      name: 'operationObj',
      align: 'left',
    },
    // 工艺描述
    {
      name: 'operationDescription',
      align: 'left',
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    // 创建人
    {
      name: 'createdBy',
      align: 'left',
    },
    // 创建时间
    {
      name: 'creationDate',
      align: 'left',
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('加工控制配置维护')}>
        {!canEdit && (
          <Button onClick={handelEdit} style={{ marginRight: 15 }} icon="edit" color="primary">
            {intl.get('tarzan.common.label.edit').d('编辑')}
          </Button>
        )}
        {canEdit && (
          <>
            <Button onClick={handelCancel}>
              {intl.get('tarzan.common.label.cancel').d('取消')}
            </Button>
            <Button onClick={handelSave} style={{ marginRight: 15 }} icon="save" color="primary">
              {intl.get('tarzan.common.label.save').d('保存')}
            </Button>
          </>
        )}
        <Button
          onClick={handleQueryHistory}
          style={{ marginRight: 15 }}
          disabled={historyQueryBtnDisbled}
        >
          {intl.get('tarzan.common.label.history.query').d('历史查询')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          style={{ height: 400 }}
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={4}
          searchCode="ProcessingControlMaintenance"
          customizedCode="ProcessingControlMaintenance"
        />
        <LineList
          assObjectsDs={assObjectsDs}
          historyLineTableDs={historyLineTableDs}
          singleAssObjectDs={singleAssObjectDs}
          canEdit={canEdit}
          tableDs={tableDs}
          handleCreateLine={handleCreateLine}
          historyQueryBtnDisbled={historyQueryBtnDisbled}
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(ProcessingControlMaintenance);
