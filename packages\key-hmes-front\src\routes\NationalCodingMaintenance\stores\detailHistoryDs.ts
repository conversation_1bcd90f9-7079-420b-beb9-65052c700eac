import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.cassetteMaterial';

const historyFactory = () =>
  new DataSet({
    primaryKey: 'hisId',
    selection: false,
    paging: true,
    autoQuery: false,
    validateBeforeQuery: true,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'startDate',
          type: FieldType.dateTime,
          max: 'endDate',
          label: intl.get(`${modelPrompt}.form.startDate`).d('开始时间'),
        },
        {
          name: 'endDate',
          min: 'startDate',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.endDate`).d('结束时间'),
        },
      ]
    }),
    fields: [
      {
        name: 'description',
        label: intl.get(`${modelPrompt}.description`).d('编码含义'),
        type: FieldType.string,
      },
      {
        name: 'fromBit',
       type: FieldType.number,
        label: intl.get(`${modelPrompt}.fromBit`).d('起始位'),
        required: true,
        pattern: '^[1-9]*[1-9][0-9]*$',
      },
      {
        name: 'toBit',
       type: FieldType.number,
        label: intl.get(`${modelPrompt}.toBit`).d('截止位'),
        required: true,
        pattern: '^[1-9]*[1-9][0-9]*$',
      },
      {
        name: 'codingType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.codingType`).d('编码类型'),
        lookupCode: 'HME.GB_CODING_RULE_TYPE',
      },
      {
        name: 'codingValue',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.codingValue`).d('编码值'),
      },
      {
        name: 'shieldField',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.shieldField`).d('屏蔽字段'),
      },
      {
        name: 'enableFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
      {
        name: 'operationTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.operationTime`).d('创建日期'),
      },
      {
        name: 'username',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.username`).d('创建人'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'POST',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-gb-coding-rule-line-his/query`,
        };
      },
    },
  });

export default historyFactory;
