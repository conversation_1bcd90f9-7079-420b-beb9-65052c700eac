/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-07-31 14:37:16
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-07-31 15:27:57
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\TraceabilityDataTemplateMaintenance\services\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @Description: 追溯数据模板维护-services
 * @Author: AI Assistant
 * @Date: 2025-07-31
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import request from 'utils/request';

const tenantId = getCurrentOrganizationId();

/**
 * 保存追溯数据模板
 * @function saveTraceTemplate
 * @param {object} params - 保存参数
 * @returns {Promise} fetch Promise
 */
export function saveTraceTemplate(params: any): Promise<any> {
  return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-trace-templates/save`, {
    method: 'POST',
    body: params,
  });
}


