import React, { useEffect, useMemo, useState } from 'react';
import {
  DataSet,
  Table,
  Button,
  Form,
  Output,
  NumberField,
  DatePicker,
  Lov,
  Row, Col,
  Select,
  TextArea,
} from 'choerodon-ui/pro';
import uuid from 'uuid/v4';
import { getResponse } from '@utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import {
  TarzanSpin,
} from '@components/tarzan-ui';
import moment from 'moment';
import { useRequest } from '@components/tarzan-hooks';
import { observer } from 'mobx-react';
import notification from 'utils/notification';
import { Collapse } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
// import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { lineTableADS,lineTableBDS,lineTableCDS, headFormDS, searchDS, headTableDS } from './stores';
import {QueryData, SaveData, ExportExcel} from './services';
import './index.less';
import fileTypeIcon from './img/blob.png';

// const Host = `/yp-mes-38510`;
const modelPrompt = 'tarzan.hmes.DailyProductionReport';
const Panel = Collapse.Panel;
const DailyProductionReport = observer(() => {

  const { run: queryData, loading: queryDataLoading } = useRequest(
    QueryData(),
    {
      needPromise: true,
      manual: true,
    },
  );
  const { run: saveData, loading: saveDataLoading } = useRequest(
    SaveData(),
    {
      needPromise: true,
      manual: true,
    },
  );

  const { run: exportExcel, loading: exportExcelLoading } = useRequest(
    ExportExcel(),
    {
      needPromise: true,
      manual: true,
    },
  );



  const lineTableADs = useMemo(() => new DataSet(lineTableADS()), []);
  const lineTableBDs = useMemo(() => new DataSet(lineTableBDS()), []);
  const lineTableCDs = useMemo(() => new DataSet(lineTableCDS()), []);


  const headFormDs = useMemo(() => new DataSet(headFormDS()), []);
  const headTableDs = useMemo(() => new DataSet(headTableDS()), []);
  const [flag, setFlag] = useState(false); // 是否生成生产日报
  const [tableHeight, setTableHeight] = useState(600);

  // 头表列
  const [columnHead, setColumnHead] = useState([]);
  const [operationId, setOperationId] = useState([]);
  // 头表数据
  const [reportData, setReportData] = useState({});
  const [canEdit, setCanEdit] = useState(false);
  // 头表行/key值
  const [workCellCol] = useState(['计划产量/planQty', '实际产量/actualQty', '实际良品/actualOkQty', '当月目标/monthPlannedQty', '当月累计达成/monthActualQty','开动率/operatingRate', '差异产量/differQty', '良率/yield', '安全/safeQty']);
  const searchDs = useMemo(() => new DataSet(searchDS()), []);
  const handleUpdate = async () => {
    if(await searchDs.validate()){
      // 返回数据的格式
      // const data = {
      //   tableHead: ['烘烤', '一次注液'],
      //   planQty: [1, 2],
      //   actualQty: [1, 2],
      //   actualOkQty: [1, 2],
      //   monthPlannedQty: [1, 2],
      //   monthActualQty: [1, 2],
      //   operatingRate: [1, 2],
      //   differQty: [1, 2],
      //   Yield: [1, 2],
      //   safeQty: [1, 2],
      // }
      // const problemList = [
      //   {
      //     description: 'A',
      //     problemType: '类型',
      //     phenomenon: 'i',
      //     timeQuantum: '上午', // 聚合字段
      //     uuid: uuid(),
      //   },{
      //     description: 'A',
      //     problemType: '类型',
      //     phenomenon: 'h',
      //     uuid: uuid(),
      //     timeQuantum: '下午',
      //   },
      //   {
      //     description: 'A',
      //     problemType: '类型',
      //     uuid: uuid(),
      //     phenomenon: 'g',
      //     timeQuantum: '加班',
      //   },
      // ]
      queryDataFun()
    }else{
      setCanEdit(false)
      headFormDs.loadData([])
      headTableDs.loadData([])
      lineTableADs.loadData([])
      lineTableBDs.loadData([])
      lineTableCDs.loadData([])
      setColumnHead([])
      setOperationId([])
      setReportData({})
    }
  }

  const queryDataFun = async() => {
    setCanEdit(false)
    if(await searchDs.validate(false, true)){
      const res = await queryData({
        params: {
          prodLineGroup: searchDs.toJSONData()[0]?.prodLineGroup,
          prodLineCode: searchDs.toJSONData()[0]?.prodLineCode,
          prodLineId: searchDs.toJSONData()[0]?.prodLineId,
          shiftCode: searchDs.toJSONData()[0]?.shiftCode,
          dateStr: moment(searchDs.toJSONData()[0]?.dateStr).format('YYYY-MM-DD'),
        },
      })
      if(res&&res.rows &&res.success){
        const result = res.rows
        setFlag(result.flag === 'Y')
        headFormDs.loadData([result.reportHeader])
        setReportData(result.reportData)
        setOperationId(result.reportData.operationId)
        // 以下为处理问题点的数据 分为三个表数据
        lineTableADs.getField('operationId')?.set('options',
          new DataSet({
            data: result.operationList,
          }),
        );
        lineTableBDs.getField('operationId')?.set('options',
          new DataSet({
            data: result.operationList,
          }),
        );
        lineTableCDs.getField('operationId')?.set('options',
          new DataSet({
            data: result.operationList,
          }),
        );
        if(result.problemList&&result.problemList.length){
          result.problemList.forEach(item => item.uuid = uuid())
          lineTableADs.loadData(result.problemList.filter(item => item.timeQuantum === 'A'))
          lineTableBDs.loadData(result.problemList.filter(item => item.timeQuantum === 'B'))
          lineTableCDs.loadData(result.problemList.filter(item => item.timeQuantum === 'C'))
        }else{
          lineTableADs.loadData([])
          lineTableBDs.loadData([])
          lineTableCDs.loadData([])
        }
        lineTableADs.forEach(record => {
          record.selectable = result.flag === 'N'
        })
        lineTableBDs.forEach(record => {
          record.selectable = result.flag === 'N'
        })
        lineTableCDs.forEach(record => {
          record.selectable = result.flag === 'N'
        })
        // 处理头表的数据
        handleData(result.reportData)
      }
    }

  }
  useDataSetEvent(searchDs, 'update', handleUpdate);

  const handleData = (resData) => {
    const data = resData
    const arr = []
    // 工序列/key值
    workCellCol.forEach(item => {
      const obj = {
        workCellName: item.split('/')[0],
        key: item.split('/')[1],
      }
      arr.push(obj)
    })

    // 未生成生产日报的时候，开动率需要计算
    if(!flag){
      const actualQty = data.actualQty
      const newOperatingRate = []
      const totalDuration = headFormDs?.current?.get('totalDuration')
      const beat = headFormDs?.current?.get('beat')
      if(totalDuration||beat){
        actualQty.forEach((item, index) => {
          const rate = (((actualQty[index]/(totalDuration*60))/beat)*100).toFixed(0)
          newOperatingRate.push(`${rate}%`)
        })
        data.operatingRate = newOperatingRate
      }
    }
    arr.forEach(item => {
      data[item.key].forEach((i, ind) => {
        item[`col${ind}`] = i
      })
    })
    data.tableHead.forEach((item, index) => {
      headTableDs.addField(`col${index}`, {
        label: item,
        dynamicProps: {
          required({ record }) {
            return (
              ['planQty', 'safeQty'].includes(record?.get('key'))
            );
          },
          // type({ record }) {
          //   return (
          //     ['operatingRate', 'safeQty'].includes(record?.get('key'))?'string':'number'
          //   );
          // },
        },
      });
    })
    headTableDs.loadData(arr)
  }

  const handlePlanQty = (record) => {
    if(record.data?.key === "planQty"){
      // 差异产量 实际产量-计划产量，实际产量有值时自动计算；
      headTableDs.records.forEach(item => {
        if(item?.get('key') === 'actualQty'){
          reportData.tableHead.forEach((_, index) => {
            const actualQty = item?.get(`col${index}`)
            if(actualQty||actualQty === 0){
              if(record?.get(`col${index}`)||record?.get(`col${index}`)=== 0){
                const differQty = Subtr(actualQty, record?.get(`col${index}`))
                headTableDs.records.forEach(i => {
                  if(i?.get('key') === 'differQty'){
                    i?.set(`col${index}`, differQty)
                  }
                })
              }
            }
          })
        }
      })
    }
  }

  useMemo(() => {
    const columnHead = [{
      name: 'workCellName',
      width: 150,
    }]
    reportData?.tableHead?.forEach((item, index) => {
      const obj = {
        name: `col${index}`,
        // 计划产量 当月目标 安全 三项可编辑
        editor: record => canEdit&&['planQty','monthPlannedQty', 'safeQty'].includes(record?.get('key'))&&<NumberField name={`col${index}`} onChange={() => handlePlanQty(record)} />,
      }
      columnHead.push(obj)
    })
    setColumnHead(columnHead)
  }, [reportData.tableHead, canEdit])

  const columnLine = useMemo(() =>
    [
      {
        title: '',
        aggregation: true,
        children: [
          { name: 'operationId',
            editor: canEdit},
          { name: 'problemType', editor: canEdit},
          { name: 'phenomenon' , editor: canEdit},
          { name: 'startTime', editor: canEdit },
          { name: 'endTime', editor: canEdit },
          { name: 'totalTime', width: 150 },
          { name: 'reason', editor: canEdit },
          { name: 'countermeasure', editor: canEdit },
        ],
      },
    ], [canEdit, lineTableADs?.toData()?.length])

  const groupsA =  useMemo(() =>
    [
      {
        name: 'timeQuantumDesc',
        type: 'column',
        columnProps: {
          header: ({ title }) => (<div>
            <span>{title}</span>
            <Button
              icon="control_point"
              funcType="flat"
              shape="circle"
              size="small"
              disabled={!canEdit}
              onClick={() => handleCreate('A')}
            /></div>),
          width: 200,
        },
      },
    ], [canEdit, lineTableADs?.toData()?.length])
  const groupsB =  useMemo(() =>
    [
      {
        name: 'timeQuantumDesc',
        type: 'column',
        columnProps: {
          header: ({ title }) => (<div>
            <span>{title}</span>
            <Button
              icon="control_point"
              funcType="flat"
              shape="circle"
              size="small"
              disabled={!canEdit}
              onClick={() => handleCreate('B')}
            /></div>),
          width: 200,
        },
      },
    ], [canEdit, lineTableBDs?.toData()?.length])

  const groupsC =  useMemo(() =>
    [
      {
        name: 'timeQuantumDesc',
        type: 'column',
        columnProps: {
          header: ({ title }) => (<div>
            <span>{title}</span>
            <Button
              icon="control_point"
              funcType="flat"
              shape="circle"
              size="small"
              disabled={!canEdit}
              onClick={() => handleCreate('C')}
            /></div>),
          width: 200,
        },
      },
    ], [canEdit, lineTableCDs?.toData()?.length])

  useEffect(() => {
    setTableHeight(document.body.clientHeight
      - document.getElementById('headSearch').clientHeight
      - document.getElementById('headImg').clientHeight
      - document.getElementById('headForm').clientHeight
      -200)
  }, [headTableDs?.toData()?.length, headFormDs?.toData()?.length, searchDs?.current?.get('prodLineGroup'), searchDs?.current?.get('prodLineLov')])

  const handleCreate = (timeQuantum) => {
    const newLine = {
      timeQuantum,
      uuid: uuid(),
    }
    if(timeQuantum === 'A'){
      lineTableADs.create({
        ...newLine,
        timeQuantumDesc: '上午',
      })
    }else if(timeQuantum === 'B'){
      lineTableBDs.create({
        ...newLine,
        timeQuantumDesc: '下午',
      })
    }else{
      lineTableCDs.create({
        ...newLine,
        timeQuantumDesc: '加班',
      })
    }
  }

  const handleEdit = () => {
    setCanEdit(prev => !prev);
  }
  const handleCancel = () => {
    setCanEdit(prev => !prev);
  }
  const handleDelete = (event) => {
    event.stopPropagation();
    if(lineTableADs.selected.length){
      lineTableADs.remove(lineTableADs.selected);
    }
    if(lineTableBDs.selected.length){
      lineTableBDs.remove(lineTableBDs.selected);
    }
    if(lineTableCDs.selected.length){
      lineTableCDs.remove(lineTableCDs.selected);
    }
  }
  const handleSave = async() => {
    const obj = {}
    if(await headFormDs.validate()&&await headTableDs.validate()){
      headTableDs.records.forEach(record => {
        obj[`${record?.get('key')}`] = []
        reportData?.tableHead.forEach((item, index) => {
          if(record?.get('key') === 'yield'||record?.get('key') === 'operatingRate'){
            obj[`${record?.get('key')}`].push(accDiv(record?.get(`col${index}`)?.replace('%', ''), 100))
          }else{
            obj[`${record?.get('key')}`].push(record?.get(`col${index}`))
          }
        })
      })
      obj.operationId = operationId;
      const res = await saveData({
        params: {
          reportData: obj,
          reportHeader: headFormDs.toJSONData()[0],
          problemList: lineTableADs.toData().concat(lineTableBDs.toData()).concat(lineTableCDs.toData()),
        },
      })
      if(res&&res.success){
        notification.success({
          message: intl.get('hzero.common.notification.success').d('操作成功'),
        })
        setCanEdit(false);
        queryDataFun()
      }
    }
  }

  const extraButton = (
    <>
      <Button
        disabled={(lineTableADs.selected.length === 0&&lineTableBDs.selected.length === 0&&lineTableCDs.selected.length === 0)||flag}
        onClick={handleDelete}
        color="primary"
        type="c7n-pro"
        icon="delete"
      >
        {intl.get(`tarzan.common.button.delete`).d('删除')}
      </Button></>
  );

  // 开动率 实际产出/总开时*60/生产节拍*100%，四舍五入，取整；
  const handleOperationRate = () => {
    const totalDuration = headFormDs?.current?.get('totalDuration')
    const beat = headFormDs?.current?.get('beat')
    headTableDs.records.forEach(record => {
      if(record?.get('key') === 'actualQty'){
        reportData.tableHead.forEach((item, index) => {
          const actualQty = record?.get(`col${index}`)
          const operatingRate = (((Number(actualQty)/(totalDuration*60))/beat)*100).toFixed(0)
          headTableDs.records.forEach(i => {
            if(i?.get('key') === 'operatingRate'){
              i?.set(`col${index}`, `${operatingRate}%`)
            }
          })
          // }
        })
      }
    })
  }
  const handleChangeTotal = (value) => {
    const beat = headFormDs?.current?.get('beat')
    if(value&&beat){
      handleOperationRate()
    }
  }
  const handleChangeBeat = (value) => {
    const totalDuration = headFormDs?.current?.get('totalDuration')
    if(value&&totalDuration){
      handleOperationRate()
    }
  }

  const Subtr = (arg1,arg2) => {
    let r1; let r2;
    try{r1=arg1.toString().split(".")[1].length}catch(e){r1=0}
    try{r2=arg2.toString().split(".")[1].length}catch(e){r2=0}
    // eslint-disable-next-line no-restricted-properties
    const m=Math.pow(10,Math.max(r1,r2));
    const n=(r1>=r2)?r1:r2;
    return ((arg1*m-arg2*m)/m).toFixed(n);
  }
  function accDiv(arg1,arg2){
    let t1=0; let t2=0;
    // eslint-disable-next-line no-empty
    try{t1=arg1.toString().split(".")[1].length}catch(e){}
    // eslint-disable-next-line no-empty
    try{t2=arg2.toString().split(".")[1].length}catch(e){}
    const r1=Number(arg1.toString().replace(".",""))
    const r2=Number(arg2.toString().replace(".",""))
    // eslint-disable-next-line no-restricted-properties
    return accMul((r1/r2), Math.pow(10,t2-t1));
  }

  function accMul(arg1,arg2)
  {
    let m=0; const s1=arg1.toString(); const s2=arg2.toString();
    // eslint-disable-next-line no-empty
    try{m+=s1.split(".")[1].length}catch(e){}
    // eslint-disable-next-line no-empty
    try{m+=s2.split(".")[1].length}catch(e){}
    // eslint-disable-next-line no-restricted-properties
    return Number(s1.replace(".",""))*Number(s2.replace(".",""))/Math.pow(10,m)
  }
  const handleExport = async () => {
    const res = await exportExcel({
      params:{
        prodLineGroup: searchDs.toJSONData()[0]?.prodLineGroup,
        prodLineCode: searchDs.toJSONData()[0]?.prodLineCode,
        prodLineId: searchDs.toJSONData()[0]?.prodLineId,
        shiftCode: searchDs.toJSONData()[0]?.shiftCode,
        dateStr: moment(searchDs.toJSONData()[0]?.dateStr).format('YYYY-MM-DD'),
      },
    })
    if (res) {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
          notification.error({ message: jsonData.message });
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], {
          type: 'vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        const url = window.URL.createObjectURL(file);
        const a = document.createElement('a');
        a.href = url;
        a.download = decodeURIComponent('生产日报.xlsx');
        document.body.appendChild(a);
        a.click();
        setTimeout(() => {
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
        }, 1000);
      }
    }

  };
  return (
    <div className="hmes-style" style={{height: '100%'}}>
      <TarzanSpin
        dataSet={searchDs}
        spinning={queryDataLoading||saveDataLoading||exportExcelLoading}
      >
        <Header title={intl.get(`${modelPrompt}.title`).d('生产日报')}>
          <>
            {canEdit?(<>
              <Button
                onClick={handleSave}
                color="primary"
                type="c7n-pro"
              >
                {intl.get(`tarzan.common.button.save`).d('保存')}
              </Button><Button
                onClick={handleCancel}
                type="c7n-pro"
              >
                {intl.get(`tarzan.common.button.cancel`).d('取消')}
              </Button>

            </>):(<>
              <Button
                disabled={headTableDs.records.length === 0||flag}
                onClick={handleEdit}
                color="primary"
                type="c7n-pro"
                icon="edit-o"
              >
                {intl.get(`tarzan.common.button.edit`).d('编辑')}
              </Button>
              <Button
                disabled={headTableDs.records.length === 0||!flag}
                onClick={handleExport}
                color="primary"
                type="c7n-pro"
                icon="edit-o"
              >
                {intl.get(`tarzan.common.button.export`).d('导出')}
              </Button>
            </>)}
          </>
        </Header>
        <Content>
          <Row id="headSearch" style={{marginBottom: '15px', display: 'flex',alignItems: 'flex-start'}}>
            <Col span={20}>
              <Form dataSet={searchDs} columns={4}>
                <Select name="prodLineGroup" />
                <Lov name="prodLineLov" />
                <DatePicker name="dateStr"  />
                <Select name="shiftCode" />
              </Form>
            </Col>
            <Col span={4} style={{display: 'flex',justifyContent: 'flex-end'}}>
              <Button
                onClick={queryDataFun}
                color="primary"
                type="c7n-pro"
              >
                {intl.get(`tarzan.common.button.search`).d('查询')}
              </Button></Col>
          </Row>
          <div id="headImg" style={{margin: '25px 0' ,display: 'flex', position: 'relative', justifyContent: 'center', alignItems: 'center'}}>
            <img style={{position: 'absolute', width: 200,left: 0}} alt="" src={fileTypeIcon} />
            <div style={{textAlign: 'center', fontWeight: 'bold', fontSize: 20}}>生产日报</div>
          </div>
          <Row id="headForm">
            <Col span={24}>
              <Form dataSet={headFormDs} columns={10} disabled={!canEdit} labelWidth={40}>
                {searchDs?.current?.get('prodLineGroup')&&searchDs?.current?.get('prodLineLov')?
                  <Output labelWidth={70} colSpan={2} name="prodLineName" renderer={({record}) => {
                    return ( `${record?.get('prodLineGroup')||''}/${record?.get('prodLineCode')||''}/${record?.get("prodLineName")||''}`)
                  }} />:searchDs?.current?.get('prodLineGroup')?
                    <Output labelWidth={70} colSpan={2} name="prodLineGroup" />:searchDs?.current?.get('prodLineLov')?
                      <Output labelWidth={70} colSpan={2} name="prodLineName" renderer={({record}) => {
                        return ( `${record?.get('prodLineCode')||''}/${record?.get("prodLineName")||''}`)
                      }} />: <Output labelWidth={70} colSpan={2} label={intl.get(`${modelPrompt}.prodLineGroup`).d('产线组')}/>
                }
                <Output name="shiftCodeMeaning" labelWidth={60} colSpan={2} renderer={({record}) => {
                  return (record?.get('date')?`${record?.get('date')}/${record?.get("shiftCodeMeaning")}`:'')
                }} />
                {/* <Output name="date"/> */}
                {/* <Output name="shiftCodeMeaning" labelWidth={60} /> */}
                <NumberField name="totalDuration" onChange={handleChangeTotal} labelWidth={70}/>
                <NumberField name="beat" onChange={handleChangeBeat} labelWidth={80}/>
                <NumberField name="plannedAttendance" labelWidth={110}/>
                <NumberField name="actualAttendance"  labelWidth={110}/>
                <NumberField name="packPlannedAttendance" labelWidth={110}/>
                <NumberField name="packActualAttendance" labelWidth={120}/>
              </Form>
            </Col>
          </Row>
          <div id="content" style={{height: tableHeight, overflow: 'auto'}}>
            <Table
              dataSet={headTableDs}
              columns={columnHead}
              searchCode="DailyProductionReport"
              customizedCode="DailyProductionReport"
            />
            <Collapse defaultActiveKey={['2']}>
              <Panel header={intl.get(`${modelPrompt}.problem`).d('影响问题点')} key="2"
                extra={extraButton}>
                <Table
                  dataSet={lineTableADs}
                  columns={columnLine}
                  groups={groupsA}
                  searchCode="DailyProductionReport"
                  customizedCode="DailyProductionReport"
                  columnDraggable
                  columnTitleEditable
                  aggregation={false}
                  virtual
                  footer={
                    <p style={{marginBottom: 0,textAlign: 'center',background: '#eee', lineHeight: '32px'}}>按轮休制度休息：15min</p>
                  }
                />
                <Table
                  dataSet={lineTableBDs}
                  columns={columnLine}
                  groups={groupsB}
                  searchCode="DailyProductionReport"
                  customizedCode="DailyProductionReport"
                  columnDraggable
                  columnTitleEditable
                  aggregation={false}
                  virtual
                  footer={
                    <p style={{marginBottom: 0,textAlign: 'center',background: '#eee', lineHeight: '32px'}}>按轮休制度休息：15min</p>
                  }
                />
                <Table
                  dataSet={lineTableCDs}
                  columns={columnLine}
                  groups={groupsC}
                  searchCode="DailyProductionReport"
                  customizedCode="DailyProductionReport"
                  columnDraggable
                  columnTitleEditable
                  aggregation={false}
                  virtual
                />
              </Panel>
            </Collapse>
            <Form dataSet={headFormDs} columns={1} disabled={!canEdit}>
              <TextArea name="remark" />
            </Form>
          </div>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.DailyProductionReport', 'tarzan.common'],
})(DailyProductionReport);
