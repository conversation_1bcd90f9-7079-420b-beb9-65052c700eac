import React from 'react';
import { Table, Button } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import request from 'utils/request';
import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();

import intl from 'utils/intl';


const LineList = props => {
  const { canEdit, assObjectsDs, handleCreateLine, shipmentReportTagId } = props;

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!canEdit}
          onClick={() => handleCreateLine()}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      lock: 'left',
      renderer: ({ record }) => {
        return (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              if (record.get('shipmentReportTagLineId')) {
                request(`${Host}/v1/${tenantId}/hme-shipment-report/delete`, {
                  method: 'post',
                  body: {
                    // shipmentReportTagId: shipmentReportTagId,
                    shipmentReportTagLineId: record.get('shipmentReportTagLineId'),
                  },
                }).then(res => {
                  if (res.success) {
                    assObjectsDs.remove(record);
                  } else {
                    notification.error({ message: res.message });
                  }
                });
              } else {
                assObjectsDs.remove(record);
              }
            }}
          >
            <Button funcType="flat" icon="remove" shape="circle" size="small" disabled={!canEdit} />
          </Popconfirm>
        );
      },
      // renderer: ({ record }) => {
      //   return (
      //     record.get('shipmentReportTagLineId') === '' && (
      //       <Popconfirm
      //         title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
      //         onConfirm={() => {
      //           assObjectsDs.remove(record);
      //         }}
      //       >
      //         <Button funcType="flat" icon="remove" shape="circle" size="small" />
      //       </Popconfirm>
      //     )
      //   );
      // },
    },
    {
      name: 'serialNumber',
      align: 'left',
      width: 120,
      editor: canEdit,
    },
    // 采集项
    {
      name: 'tagLov',
      align: 'left',
      renderer: ({ record }) => record.get('tagCode'),
      editor: record => record.get('shipmentReportTagLineId') === '' || canEdit,
    },
    // 采集项描述
    {
      name: 'tagDescription',
      align: 'left',
    },
  ];

  return <Table queryBar="bar" dataSet={assObjectsDs} columns={columns} virtual style={{ height: 200 }} />;
};

export default LineList;
