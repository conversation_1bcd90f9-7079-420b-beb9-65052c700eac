import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { searchCopy } from '@utils/utils';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.ass.inOutStorage';

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'materialLotCodes',
    selection: DataSetSelection.multiple,
    paging: false,
    autoQuery: false,
    dataKey: 'rows',
    events: {
      update({ record, name, value }) {
        if (name === 'materialLotCodes') {
          const arr = [...new Set(value)]
          searchCopy(
            ['materialLotCodes',], name, record, arr,);
        }
      }
    },
    fields: [
      {
        name: 'materialLotCodes',
        multiple: ',',
        label: intl.get(`${modelPrompt}.form.materialLotCode`).d('条码号'),
        type: FieldType.string,
      },
      {
        name: 'invTransferDocNum',
        disabled: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.invTransferDocNum`).d('单据编码'),
      },
      {
        name: 'transferDocStatus',
        disabled: true,
        lookupCode: 'HME.INV_TRANSFER_DOC_STATUS',
        type: FieldType.string,
        defaultValue: 'NEW',
        label: intl.get(`${modelPrompt}.transferDocStatus`).d('单据状态'),
      },
      {
        name: 'siteLov',
        required: true,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
        lovCode: 'MT.MODEL.SITE',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'siteId',
        bind: 'siteLov.siteId',
      },
      {
        name: 'siteCode',
        bind: 'siteLov.siteCode',
      },
      {
        name: 'locatorLov',
        required: true,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.locator`).d('目标库位'),
        lovCode: 'HME.USER_LOCATOR',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'toLocatorId',
        bind: 'locatorLov.locatorId',
      },
      {
        name: 'toLocatorCode',
        bind: 'locatorLov.locatorCode',
      },
      {
        name: 'createdByRealName',
        type: FieldType.string,
        disabled: true,
        label: intl.get(`${modelPrompt}.createdByRealName`).d('创建人'),
      },
      {
        name: 'approveLov',
        required: true,
        type: FieldType.object,
        textField: 'realName',
        label: intl.get(`${modelPrompt}.approve`).d('审批人'),
        lovCode: 'LOV_USER',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'approveId',
        bind: 'approveLov.id',
      },
      {
        name: 'approveRealName',
        bind: 'approveLov.realName',
      },
      {
        name: 'remark',
        required: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'GET',
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-inv-transfer/detail/ui`,
        };
      },
    },
  });

export default listPageFactory;
