/**
 * @date 2019-7-30
 * <AUTHOR> <<EMAIL>>
 */
import { get as chainget } from 'lodash';
import { createPagination } from 'utils/utils';
import { getResponse, ergodicData } from '@utils/utils';
import uuid from 'uuid/v4';
import { getUserRole } from '@services/api';
import {
  fetchExtendTableList,
  saveExtendTable,
  fetchServicePackageList,
  fetchExtendFieldList,
  saveExtendFieldList,
} from '../../services/hmes/extendTableService';

export default {
  namespace: 'extendTable',
  state: {
    extendTableList: [], // 扩展表数据源
    extendTablePagination: {}, // 扩展表格分页
    servicePackageList: [], // 服务包下拉框数据
    fieldDrawerList: [], // 扩展字段抽屉数据源
    uesrRole: 'N', // 用户权限
  },
  effects: {
    // 获取用户权限
    *getUserRole({ payload }, { call, put }) {
      const res = yield call(getUserRole, payload);
      const list = getResponse(res);
      if (list) {
        yield put({
          type: 'updateState',
          payload: {
            uesrRole: list.rows,
          },
        });
      }
    },

    // 获取扩展表数据
    *fetchExtendTableList({ payload }, { call, put }) {
      const res = yield call(fetchExtendTableList, payload);
      const list = getResponse(res);
      if (list) {
        yield put({
          type: 'updateState',
          payload: {
            extendTableList: chainget(list, 'rows.content', []),
            extendTablePagination: createPagination(list.rows),
          },
        });
      }
    },

    // 获取扩展字段表数据
    *fetchExtendFieldList({ payload }, { call, put }) {
      const res = yield call(fetchExtendFieldList, payload);
      const list = getResponse(res);
      if (list) {
        yield put({
          type: 'updateState',
          payload: {
            fieldDrawerList: chainget(list, 'rows', []).map(item => {
              return {
                ...item,
                uuid: uuid(),
                _status: 'update',
              };
            }),
          },
        });
      }
    },
    // 保存扩展表
    *saveExtendFieldList({ payload }, { call }) {
      const result = yield call(saveExtendFieldList, payload.fieldDrawerList);
      return getResponse(result);
    },
    // 排序
    *changeOvge({ payload }, { call }) {
      const list = payload.fieldDrawerList.map((item, index) => {
        return {
          ...item,
          sequence: index,
        };
      });
      const res = yield call(saveExtendFieldList, list);
      return getResponse(res);
    },

    // 获取服务包下拉框数据
    *fetchServicePackageList({ payload }, { call, put }) {
      const res = yield call(fetchServicePackageList, payload);
      const list = getResponse(res);
      if (list) {
        yield put({
          type: 'updateState',
          payload: {
            servicePackageList: chainget(list, 'rows', []),
          },
        });
      }
    },

    // 保存扩展表
    *saveExtendTable({ payload }, { call }) {
      const result = yield call(saveExtendTable, ergodicData(payload));
      return getResponse(result);
    },
  },

  reducers: {
    updateState(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
