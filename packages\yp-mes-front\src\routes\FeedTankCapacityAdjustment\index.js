import React, { useState, useMemo, useEffect } from 'react';
import {
  DataSet,
  Table,
  Button,
  Spin,
  Switch,
  TextField,
  Icon,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import { Badge } from 'choerodon-ui';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { getResponse } from '@utils/utils';
import { overrideTableBar } from '@components/tarzan-ui';
import intl from 'utils/intl';
import { tableDS } from './stores/FeedTankCapacityAdjustmentDS';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import LovModal from '@/components/BatchInput/LovModal';
import { Host } from '@/utils/config';

// const Host = `/yp-mes-38510`;
const modelPrompt = 'tarzan.hmes.FeedTankCapacityAdjustment';

const tenantId = getCurrentOrganizationId();

const FeedTankCapacityAdjustment = observer(() => {
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
      // handleSearch()
    }
  };

  // useEffect(() => {
  //   tableDs.queryDataSet.addEventListener('update', handleUpdate);
  //   return () => {
  //     tableDs.queryDataSet.removeEventListener('update', handleUpdate);
  //   };
  // }, [])
  // const handleUpdate = () => {
  //   const exitAssembleLov  = tableDs.queryDataSet.current.get('assembleLov')
  //   const exitEquipLov  = tableDs.queryDataSet.current.get('equipLov')
  //   const materialLov  = tableDs.queryDataSet.current.get('materialLov')
  //   const identifications  = tableDs.queryDataSet.current.get('identifications')
  //   tableDs.queryDataSet.current.getField('equipLov')?.set('required', !exitAssembleLov&&!materialLov&&!identifications);
  //   tableDs.queryDataSet.current.getField('assembleLov')?.set('required', !exitEquipLov&&!materialLov&&!identifications);
  //   tableDs.queryDataSet.current.getField('materialLov')?.set('required', !exitEquipLov&&!exitAssembleLov&&!identifications);
  //   tableDs.queryDataSet.current.getField('identifications')?.set('required', !exitEquipLov&&!materialLov&&!exitAssembleLov);
  // }
  const [loading, setLoading] = useState(false);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  // 保存
  const handelSave = async record => {
    if (await record.validate()) {
      setLoading(true);
      const res = await request(
        `${Host}/v1/${tenantId}/hme-adjustment-of-feeding-tank-capacity/capacity/save`,
        {
          method: 'post',
          body: record.toJSONData(),
        },
      );
      setLoading(false);
      const result = getResponse(res);
      if (result) {
        notification.success();
        record.setState('editing', false);
        tableDs.loadData([]);
        const workcellId = record.get('workcellId')
        tableDs.queryDataSet.current.set('workcellId', workcellId)
        tableDs.query();
      }
    }
  };

  // 取消按钮
  const handelCancel = record => {
    tableDs.remove(record);
  };

  const handleCreate = () => {
    if (tableDs.selected.length > 0) {
      tableDs.unSelectAll();
    }
    tableDs.create({
      workcellMaterialLotId: '',
    }, 0);
    tableDs.current.setState('editing', true);
  };

  // 删除
  const handleDelete = async () => {
    setLoading(true);
    const res = await request(`${Host}/v1/${tenantId}/hme-adjustment-of-feeding-tank-capacity/unbind`, {
      method: 'post',
      body: tableDs.selected.map(item => item.toData()),
    })
    setLoading(false);
    const result = getResponse(res);
    if(result){
      notification.success();
      tableDs.query();
    }
  };

  const columns = [
    // 搅拌机编码
    {
      name: 'equipmentLov',
      align: 'left',
      renderer: ({ record }) => record.get('equipmentCode'),
      editor: record => {
        return record.getState('editing');
      },
    },
    // 搅拌机名称
    {
      name: 'equipmentName',
      align: 'left',
    },
    // 搅拌机编码
    {
      name: 'assembleObj',
      align: 'left',
      renderer: ({ record }) => record.get('assemblePointCode'),
      editor: record => {
        return record.getState('editing');
      },
    },
    // 搅拌机名称
    {
      name: 'assemblePointName',
      align: 'left',
    },
    // 物料编码
    {
      name: 'materialObj',
      align: 'left',
      renderer: ({ record }) => record.get('materialCode'),
      editor: record => record.getState('editing'),
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
    },
    // 物料版本
    {
      name: 'revisionCode',
      align: 'left',
      editor: record => {
        return record.getState('editing') && record.get('revisionFlag') === 'Y';
      },
    },
    // 原材料条码
    {
      name: 'materialLotCode',
      align: 'left',
      width: 200,
      editor: record => {
        return record.getState('editing');
      },
    },
    // 剩余数量
    {
      name: 'primaryUomQty',
      align: 'left',
    },
    {
      name: 'creationDate',
      width: 200,
      align: 'left',
      editor: record => {
        return record.getState('editing');
      },
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 100,
      editor: record => record.getState('editing') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'createdRealName',
      align: 'left',
    },
    {
      name: 'lastUpdatedRealName',
      align: 'left',
    },{
      name: 'lastUpdateDate',
      width: 200,
      align: 'left',
    },
    {
      width: 200,
      align: 'center',
      lock: 'right',
      header: intl.get('tarzan.common.button.action').d('操作'),
      renderer: ({ record }) => (
        <>
          <Button funcType='flat' disabled={!record.getState('editing')} onClick={() => handelSave(record)}>
            {intl.get('tarzan.common.button.save').d('保存')}
          </Button>
          <Button funcType='flat' disabled={!record.getState('editing')} onClick={() => handelCancel(record)}>

            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
        </>
      ),
    },
  ];

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  return (
    <React.Fragment>
      <Header title={intl.get(`${modelPrompt}.title`).d('投料罐容量调整')}>
        <>
          <Button
            onClick={handleCreate}
            disabled={tableDs.records.some(item => item.getState('editing'))}
            style={{ marginRight: 15 }}
            icon="edit"
            color="primary"
          >
            {intl.get(`tarzan.common.button.add`).d('新建')}
          </Button>
          <Button
            onClick={handleDelete}
            disabled={
              tableDs.selected.length < 1 || tableDs.records.some(item => item.getState('editing'))
            }
            style={{ marginRight: 15 }}
            icon="edit"
            color="primary"
          >
            {intl.get(`tarzan.common.button.delete`).d('删除')}
          </Button>
        </>
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Table
            dataSet={tableDs}
            columns={columns}
            style={{ height: 400 }}
            queryBar={overrideTableBar}
            queryFields={{
              identifications: (
                <TextField
                  name="identifications"
                  suffix={
                    <div className="c7n-pro-select-suffix">
                      <Icon
                        type="search"
                        onClick={() =>
                          onOpenInputModal(
                            true,
                            'identifications',
                            intl.get(`${modelPrompt}.identification`).d('原材料条码'),
                          )
                        }
                      />
                    </div>
                  }
                />
              ),
            }}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            queryFieldsLimit={4}
            searchCode="FeedTankCapacityAdjustment"
            customizedCode="FeedTankCapacityAdjustment"
          />
          <LovModal {...lovModalProps} />
        </Spin>
      </Content>
    </React.Fragment>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.FeedTankCapacityAdjustment', 'tarzan.common'],
})(FeedTankCapacityAdjustment);
