/**
 * @Description: 生产指令管理详情页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2022-09-06 18:14:40
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.productionOrderMgt';
const tenantId = getCurrentOrganizationId();

const workingListDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/working-actual-info/ui`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (!datas.rows) {
            datas.rows = {
              content: [],
            };
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'routerStepId',
  autoLocateFirst: false,
  fields: [
    {
      name: 'routerStepId',
      type: FieldType.number,
    },
    {
      name: 'sequence',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sequence`).d('顺序'),
    },
    {
      name: 'stepTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stepTypeDesc`).d('步骤类型'),
    },
    {
      name: 'operationNameRevision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationNameRevision`).d('工艺编码/版本'),
    },
    {
      name: 'stepNameCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stepNameCode`).d('工艺描述/识别码'),
    },
    {
      name: 'operationQueueQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.operationQueueQty`).d('开工数量'),
    },
    {
      name: 'wipQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.wipQty`).d('在制/在制报废'),
    },
    {
      name: 'completedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.completedQty`).d('完工'),
    },
    {
      name: 'scrapConfirmQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.wipScrappedQty`).d('报废'),
    },
  ],
});
const bomListDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/assemble-actual-info/ui`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (!datas.rows) {
            datas.rows = {
              content: [],
            };
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'index',
  autoLocateFirst: false,
  fields: [
    {
      name: 'index',
      type: FieldType.number,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'assembleMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMaterialCode`).d('需求物料编码'),
    },
    {
      name: 'assembleMaterialRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMaterialRevisionCode`).d('需求物料版本'),
    },
    {
      name: 'assembleMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMaterialName`).d('需求物料描述'),
    },
    {
      name: 'assembleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.assembleQty`).d('需求数量'),
    },
    {
      name: 'assembleProgress',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleProgress`).d('装配进度'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('装配物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('装配物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('装配物料描述'),
    },
    {
      name: 'demandQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.demandQty`).d('装配数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
    },
  ],
});

export { workingListDS, bomListDS };
