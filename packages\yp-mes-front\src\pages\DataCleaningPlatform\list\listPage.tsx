import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, Select } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { useDataSetEvent } from 'utils/hooks';
import { Card } from 'choerodon-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { Toolt<PERSON> } from 'choerodon-ui/pro/lib/core/enum';
import { listPageFactory, relationListPageFactory } from '../stores/listPageDs';

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
  relationListDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.unitWork';

const ListPageComponent: FC<ListPageProps> = ({ listDs, relationListDs }) => {
  const [edit, setEdit] = useState(false);
  const [deleteFlag, setDeleteFlag] = useState(true);

  useDataSetEvent(listDs, 'query', () => {
    setEdit(false);
    setDeleteFlag(true);
  });

  useDataSetEvent(listDs, 'select', () => {
    setStatus();
  });

  useDataSetEvent(listDs, 'unSelect', () => {
    setStatus();
  });

  useDataSetEvent(listDs, 'selectAll', () => {
    setStatus();
  });

  useDataSetEvent(listDs, 'unSelectAll', () => {
    setStatus();
  });

  const setStatus = () => {
    if (listDs.selected.length) {
      setDeleteFlag(false);
    } else {
      setDeleteFlag(true);
    }
  };

  useEffect(() => {
    queryFirstLineDetails();
  }, []);

  const queryFirstLineDetails = () => {
    listDs.query().then(res => {
      if (res?.content?.length > 0) {
        handleLine(res.content[0]?.cleanArchiveConfigDataId);
      } else {
        relationListDs.loadData([]);
      }
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'subFlag',
      align: ColumnAlign.center,
      width: 80,
      editor: record => record.get('editing'),
    },
    {
      name: 'subRule',
      align: ColumnAlign.center,
      width: 80,
      tooltip: Tooltip.always,
      editor: record => record.get('editing'),
      renderer: ({ record, text }) => {
        if (record && record.get('subFlag') === 'N') {
          return null;
        }
        return text;
      },
    },
    {
      name: 'tableName',
      align: ColumnAlign.center,
      width: 200,
      editor: record => record.get('editing'),
    },
    {
      name: 'tableDatasource',
      align: ColumnAlign.center,
      width: 80,
      editor: record => record.get('editing') && <Select />,
    },
    {
      name: 'tableType',
      align: ColumnAlign.center,
      width: 80,
      editor: record => record.get('editing') && <Select />,
    },
    {
      name: 'archiveFlag',
      align: ColumnAlign.center,
      width: 80,
      tooltip: Tooltip.always,
      editor: record => record.get('editing'),
    },
    {
      name: 'archiveWay',
      align: ColumnAlign.center,
      width: 80,
      editor: record => record.get('editing') && <Select />,
    },
    {
      name: 'clearUom',
      align: ColumnAlign.center,
      width: 80,
      editor: record => record.get('editing') && <Select />,
    },
    {
      name: 'reserveCycle',
      align: ColumnAlign.center,
      width: 80,
      editor: record => record.get('editing'),
    },
    {
      name: 'preExcute',
      align: ColumnAlign.center,
      width: 180,
    },
    {
      name: 'preType',
      align: ColumnAlign.center,
      width: 80,
    },
    {
      name: 'preStatus',
      align: ColumnAlign.center,
      width: 180,
    },
    {
      name: 'status',
      align: ColumnAlign.center,
      width: 80,
    },
    {
      name: 'enableFlag',
      align: ColumnAlign.center,
      width: 80,
      editor: record => record.get('editing'),
    },
    {
      name: 'createdNameBy',
      align: ColumnAlign.center,
      width: 80,
    },
    {
      name: 'creationDate',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'lastUpdatedNameBy',
      align: ColumnAlign.center,
      width: 80,
    },
    {
      name: 'lastUpdateDate',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      width: 120,
      header: intl.get('tarzan.aps.common.button.action').d('操作'),
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      renderer: ({ record }) =>
        record!.get('editing') ? (
          <>
            <Button funcType={FuncType.flat} onClick={() => handleEdit(record, false)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button funcType={FuncType.flat} onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        ) : (
          <Button funcType={FuncType.flat} onClick={() => handleEdit(record, true)}>
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        ),
    },
  ];

  const relationColumns: ColumnProps[] = [
    {
      name: 'dataRange',
      align: ColumnAlign.center,
      width: 120,
    },
    {
      name: 'type',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'status',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'msg',
      align: ColumnAlign.center,
      width: 300,
    },
    {
      name: 'createdNameBy',
      align: ColumnAlign.center,
      width: 80,
    },
    {
      name: 'creationDate',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'lastUpdatedNameBy',
      align: ColumnAlign.center,
      width: 80,
    },
    {
      name: 'lastUpdateDate',
      align: ColumnAlign.center,
      width: 150,
    },
  ];

  // 删除
  const handleDelete = async () => {
    const params = listDs.selected.map(item => {
      return item.toData();
    });
    const url = `/archive/v1/${getCurrentOrganizationId()}/hme-clean-archive`;
    const res: any = await request(url, {
      body: params,
      method: 'delete',
    });
    if (res && res.message) {
      notification.error({
        message: res.message,
      });
    } else {
      setEdit(false);
      notification.success({});
      queryFirstLineDetails();
    }
  };

  const handleSave = async record => {
    const validate = await record.validate();
    if (validate) {
      const params = [record.toData()];
      const url = `/archive/v1/${getCurrentOrganizationId()}/hme-clean-archive`;
      const res: any = await request(url, {
        body: params,
        method: 'POST',
      });
      if (res && res.message) {
        notification.error({
          message: res.message,
        });
      } else {
        notification.success({});
        queryFirstLineDetails();
        handleEdit(record, false);
      }
    }
  };

  const handleCreate = () => {
    listDs.create({ status: 'add', editing: true }, 0);
    setEdit(true);
  };

  const handleEdit = (record, flag) => {
    record.set('editing', flag);
    if (!flag) {
      if (record.status === 'add') {
        listDs.remove(record);
      } else {
        record.reset();
      }
    }
  };

  const handleLine = async cleanArchiveConfigDataId => {
    relationListDs.setQueryParameter('hmeCleanArchiveConfigDataId', cleanArchiveConfigDataId);
    // 查询展示行信息
    relationListDs.query();
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.cleaningPlatform`).d('数据归档清理配置平台')}>
        <Button icon="add" color={ButtonColor.primary} disabled={edit} onClick={handleCreate}>
          {intl.get('tarzan.common.button.create').d('新增')}
        </Button>
        <Button disabled={deleteFlag} onClick={handleDelete}>
          {intl.get('tarzan.common.button.delete').d('删除')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          onRow={({ record }) => ({
            onClick: () => handleLine(record.get('cleanArchiveConfigDataId')),
          })}
          highLightRow
          key="unitWork"
          rowHeight={35}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={5} // 头部显示的查询字段的数量
          searchCode="unitWork" // 动态筛选条后端接口唯一编码
          customizedCode="unitWork" // 个性化编码
        />
        <Card bordered={false} title="行信息">
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false, // 是否开启模糊查询
            }}
            queryFieldsLimit={5} // 头部显示的查询字段的数量
            dataSet={relationListDs}
            columns={relationColumns}
            key="unitWork"
            rowHeight={35}
          />
        </Card>
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    const relationListDs = relationListPageFactory();
    return {
      listDs,
      relationListDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.unitWork'],
})(ListPage);
