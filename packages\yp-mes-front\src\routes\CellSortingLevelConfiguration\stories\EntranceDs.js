

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.hmes.cellSortingLevelConfiguration';
const tenantId = getCurrentOrganizationId();

/**
 * 列表和详情页
 */
const entranceDS = () => ({
  primaryKey: 'tagCode',
  autoQuery: false,
  autoCreate: false,
  selection: false,
  queryFields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      lovCode: 'MT.MATERIAL',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'materialLov.materialId',
    },
    {
      name: 'levelType',
      type: FieldType.string,
      lookupCode: 'HME.LEVEL_TYPE',
      label: intl.get(`${modelPrompt}.levelType`).d('等级类型'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },{
      name: 'levelTypeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.levelTypeMeaning`).d('等级类型'),
    },{
      name: 'levelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.levelCode`).d('等级编码'),
    },
    {
      name: 'capacity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.capacity`).d('容量'),
    },
    {
      name: 'voltage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.voltage`).d('电压'),
    },
    {
      name: 'DCR',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.DCR`).d('DCR'),
    },
    {
      name: 'ACR',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ACR`).d('ACR'),
    },
    {
      name: 'K1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.K1`).d('K1'),
    },
    {
      name: 'K2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.K2`).d('K2'),
    },
    {
      name: 'voltageFormationEnd',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.voltageFormationEnd`).d('化成结束电压'),
    },
    {
      name: 'capacityFormationEnd',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.capacityFormationEnd`).d('化成结束容量'),
    },
    {
      name: 'chargeCapacitySupplement',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chargeCapacitySupplement`).d('补电容量'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.enable`).d('启用') },
          { value: 'N', key: intl.get(`tarzan.common.label.disable`).d('禁用') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-specified-level-configs/query`,
        method: 'GET',
      };
    },
  },
});
export { entranceDS };
