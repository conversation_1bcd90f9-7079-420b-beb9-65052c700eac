import { BASIC, Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/yp-mes-20000`;
const modelPrompt = 'tarzan.receive.productBatchProcessCancellation';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'eoId',
    paging: true,
    autoQuery: false,
    selection: 'multiple',
    pageSize: 50,
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'eoStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoStatusDesc`).d('执行作业状态'),
      },
      {
        name: 'qualityStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      },
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
      },
      {
        name: 'routerStepDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.routerStepDesc`).d('当前工艺'),
      },
      {
        name: 'wipStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.wipStatusDesc`).d('当前工艺状态'),
      },
    ],
    queryFields: [
      {
        name: 'identifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.identifications`).d('产品条码'),
        required: true,
      },
      {
        name: 'status',
        type: 'string',
        label: intl.get(`${modelPrompt}.status`).d('生产状态'),
        textField: 'description',
        valueField: 'statusCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=EO_STATUS`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.material`).d('物料编码'),
        lovCode: 'MT.MATERIAL.PERMISSION',
        textField: 'materialCode',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: getCurrentOrganizationId(),
              siteId: record?.get('siteId'),
            };
          },
        },
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'operationLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationLov`).d('当前工艺'),
        lovCode: 'MT.METHOD.OPERATION',
      },
      {
        name: 'operationName',
        bind: 'operationLov.operationName',
      },
      {
        name: 'eoStepStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoStepStatus`).d('当前工艺状态'),
        textField: 'description',
        valueField: 'statusCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=EO_STEP_STATUS`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${Host}/v1/${tenantId}/hme-production-batch-revoke/query`,
          method: 'POST',
          body: data[0],
        };
      },
    },
  };
};

export { tableDS };
