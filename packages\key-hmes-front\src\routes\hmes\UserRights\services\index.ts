/**
 * @Description: 班组维护-分配组织-接口
 * @Author: <<EMAIL>>
 * @Date: 2022-07-29 16:20:12
 * @LastEditTime: 2022-10-17 13:57:41
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 查询组织树
export function GetOrgTree() {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/tree/ui`,
    method: 'GET',
  };
}

// 查询库位树
export function GetLocatorTree() {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/all/locator/tree/ui`,
    method: 'GET',
  };
}

// 查询用户已分配的组织信息
export function GetUserOrgList() {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/limit-user/ui`,
    method: 'GET',
  };
}

// 用户权限分配
export function UserOrganizationSave() {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/batch/save/ui`,
    method: 'POST',
  };
}

// 设定用户默认组织
export function SetUserDefaultOrganization() {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/default-flag/update/ui`,
    method: 'POST',
  };
}
