/**
 * @Description: 装配清单 - 入口页面DS（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/7/25 14:29
 * @LastEditTime: 2023-05-11 11:05:34
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { queryAssemblyList } from '../services/index';

const modelPrompt = 'tarzan.product.bom.model.bom';
const tenantId = getCurrentOrganizationId();

const tableDS: (queryListUrl: string, typeGroup: string) => DataSetProps = (
  uiServerCode,
  typeGroup,
) => ({
  selection: false,
  autoQuery: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'bomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomName`).d('编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
    },
    {
      name: 'bomType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomType`).d('类型'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=BOM&typeGroup=${typeGroup}`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'bomStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomStatus`).d('状态'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=BOM&statusGroup=BOM_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
  ],
  fields: [
    {
      name: 'bomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomName`).d('编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
    },
    {
      name: 'bomTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomType`).d('类型'),
    },
    {
      name: 'bomStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomStatus`).d('状态'),
    },
    {
      name: 'currentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
    },
    {
      name: 'dateTo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
    },
  ],
  transport: {
    read: queryAssemblyList(uiServerCode),
  },
});

export { tableDS };
