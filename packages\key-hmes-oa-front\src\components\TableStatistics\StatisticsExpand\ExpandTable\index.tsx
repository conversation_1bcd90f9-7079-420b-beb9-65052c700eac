import React from 'react';
import { Select, NumberField, Switch } from 'choerodon-ui/pro';
import { Table } from 'choerodon-ui';
import intl from 'utils/intl';

interface ExpanTableProps {
  dataSource: any[];
  isLoading?: boolean;
  changeSwitchValue: (value: boolean, type: string, index: number) => void;
  changeSeqValue: (value: number | undefined | string, index: number) => void;
  changeSelectValue: (value: string, index: number) => void;
  numberColumns: any[];
}

const modelPrompt = 'tarzan.aps.statistics';

const ExpandTable: React.FC<ExpanTableProps> = ({
  dataSource = [],
  isLoading,
  changeSwitchValue,
  changeSeqValue,
  changeSelectValue,
  numberColumns = [],
}) => {
  const renderSwitch = (val, type, index, record = {} as any) => (
    <Switch
      defaultChecked={val === 'Y'}
      disabled={record.disabled}
      onChange={(value) => changeSwitchValue(value, type, index)}
    />
  );

  const renderSelect = (val, _, index) => {
    return (
      <Select value={val} onChange={(value) => changeSelectValue(value, index)}>
        {numberColumns.map((item) => {
          return <Select.Option key={item.fieldValue} value={item.fieldValue}>{item.fieldMeaning}</Select.Option>;
        })}
      </Select>
    );
  };

  const columns: Array<any> = [
    {
      title: intl.get(`${modelPrompt}.table.fieldName`).d('字段名'),
      dataIndex: 'fieldMeaning',
      width: 150,
    },
    {
      title: intl.get(`${modelPrompt}.table.hidden`).d('是否汇总'),
      dataIndex: 'isCollect',
      align: 'center',
      width: 150,
      render: (val, record, index) => renderSwitch(val, 'isCollect', index, record),
    },
    {
      title: intl.get(`${modelPrompt}.table.orderSeq`).d('显示顺序'),
      dataIndex: 'showOrder',
      width: 150,
      render: (val, record, index) => (
        <NumberField
          disabled={record.disabled}
          defaultValue={val}
          onChange={(value) => changeSeqValue(value, index)}
        />
      ),
    },
    {
      title: intl.get(`${modelPrompt}.table.fixedLeft`).d('汇总字段'),
      dataIndex: 'collectFieldValue',
      align: 'center',
      width: 150,
      render: (val, record, index) => renderSelect(val, record, index),
    },
  ];
  return (
    <Table
      pagination={false}
      bordered
      filterBar={false}
      loading={isLoading}
      dataSource={dataSource}
      rowKey="fieldValue"
      columns={columns}
    />
  );
};

export default ExpandTable;
