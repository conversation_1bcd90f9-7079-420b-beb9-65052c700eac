/**
 * @Description: 数据收集项预览组件
 * @Author: <<EMAIL>>
 * @Date: 2021-04-26 18:55:56
 * @LastEditTime: 2023-01-10 10:58:55
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useRef, useEffect } from 'react';
import { Tag, Icon, Tooltip } from 'choerodon-ui';
import intl from 'utils/intl';
import styles from './index.module.less';
import CloseWapper from './Components/CloseWapper';
import InputComponent from './InputComponent';
import fileTypeIcon from './fileTypeIcon';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

const tagClassName = {
  True: 'green',
  False: 'red',
};

const ListItemContent = props => {
  const { className, rule = {}, setItemValidateInfo } = props;
  const InputRef = useRef();
  const [tagList, setTagList] = useState([]);
  const [fieldValue, setFieldValue] = useState(null);
  const [numId, setNumId] = useState(0);
  const [inputVisible, setInputVisible] = useState(true);
  const classNames = [className, styles['hcm-dataItem-group']];
  const {
    mandatoryNum = null,
    optionalNum = null,
    valueType,
    minimumValue,
    maximalValue,
    trueValue,
  } = rule;
  // 只有判断值，数值类型，需要校验是否正确
  const needVelidateValueFlag = valueType === 'DECISION_VALUE' || valueType === 'VALUE';
  /* 对于采集行输入添加框是否出现的动态变化逻辑
    情况   必选		    可选        结果
    1.     0/null		  0/null		  不限
    2.     是		      0		        必输添加完后无法添加
    3.     是		      null		    必输添加完后不限
    4.     是		      是		      必输和选输添加完后无法添加
    5.     0/null		  是		      选输添加完后无法添加

    分为总量有限制和总量无限制
    有限制        无限制
    2.4.5.       1.3.
  */

  // 用于情况1.
  const numberOfRuleIsEmptyFlag = !(mandatoryNum + optionalNum);
  // 已输条数小于必输条数   用于情况2.3.
  const tagListLengthLessThanMandatoryNumFlag = tagList.length < mandatoryNum;
  // 已输条数小于设置的总条数   用于情况4.5.
  const tagListLengthLessThanNumberOfRuleFlag = tagList.length < mandatoryNum + optionalNum;

  // 添加按钮的显示
  const inputIconVisible = () => {
    if (numberOfRuleIsEmptyFlag) {
      // 1.
      return true;
    } if (tagListLengthLessThanMandatoryNumFlag) {
      // 2.
      return true;
    } if (tagListLengthLessThanNumberOfRuleFlag) {
      // 4.5.
      return true;
    } if (!tagListLengthLessThanMandatoryNumFlag && optionalNum === null) {
      // 3.
      return true;
    }
    return false;
  };

  useEffect(() => {
    // 输入框出现时，光标放在输入框里
    if (InputRef.current) {
      InputRef.current.focus();
    }
  }, [inputVisible]);

  // 在tagList有修改时，给上层报备验证情况
  useEffect(() => {
    const numberValidate = tagList.length - mandatoryNum > -1;
    if (!tagList.length) {
      // 列表没有值的时候，传null通知上层，用于判断采集结果
      setItemValidateInfo(rule, null, numberValidate);
      return;
    }
    let valueValidate = true;
    if (needVelidateValueFlag) {
      if (valueType === 'VALUE') {
        if (minimumValue * maximalValue) {
          valueValidate = !tagList.some(
            item => item.text < minimumValue || item.text > maximalValue,
          );
        } else if (minimumValue) {
          valueValidate = !tagList.some(item => item.text < minimumValue);
        } else if (maximalValue) {
          valueValidate = !tagList.some(item => item.text > maximalValue);
        }
      } else if (valueType === 'DECISION_VALUE') {
        valueValidate = !tagList.some(item => item.text !== trueValue);
      }
    }
    setItemValidateInfo(rule, valueValidate ? 'Y' : 'N', numberValidate ? 'Y' : 'N');
  }, [tagList]);

  const handleInputChange = value => {
    setFieldValue(value);
  };

  const showInput = () => {
    setInputVisible(true);
  };

  const handleCloseInput = () => {
    setInputVisible(false);
  };

  /**
   * 没有onPressEnter事件的输入组件，用handleInputEnter代替onChange方法，此时需要传改变的值来替代fieldValue使用
   *
   * @param {*} value 输入的值
   * @returns
   */
  const handleInputEnter = value => {
    let useValue = value || fieldValue;
    if (typeof useValue === 'string') {
      useValue = useValue.trim();
    }
    if (!useValue && useValue !== 0) {
      return;
    }
    if (
      !numberOfRuleIsEmptyFlag &&
      tagList.length + 1 >= mandatoryNum + optionalNum &&
      optionalNum !== null
    ) {
      // 有必选或可选，输入的条数大于等于总条数，可选条数不为null时，隐藏输入框
      setInputVisible(false);
    }
    setTagList([...tagList, { id: numId, text: useValue }]);
    setNumId(prev => prev + 1);
    setFieldValue(null);
  };
  const handleTagClose = removeTagId => {
    const newValueList = tagList.filter(tag => tag.id !== removeTagId);
    setTagList(newValueList);
  };

  const validateComponentsRequired = () => {
    // debugger;
    if (numberOfRuleIsEmptyFlag) {
      return 'normal';
    } if (tagListLengthLessThanMandatoryNumFlag) {
      return 'required';
    }
    return 'normal';
  };

  const itemValidate = textValue => {
    let validateFlag = true;
    switch (valueType) {
      case 'VALUE':
        if (minimumValue * maximalValue) {
          validateFlag = textValue >= minimumValue && textValue <= maximalValue;
        } else if (minimumValue) {
          validateFlag = textValue >= minimumValue;
        } else if (maximalValue) {
          validateFlag = textValue <= maximalValue;
        }
        break;
      case 'DECISION_VALUE':
        validateFlag = textValue === trueValue;
        break;
      default:
        break;
    }
    return validateFlag ? 'True' : 'False';
  };

  const getAddInfoText = () => {
    const num = tagList.length;
    if (numberOfRuleIsEmptyFlag) {
      // 没有限制的必选/可选数据条数
    } else if (tagListLengthLessThanMandatoryNumFlag) {
      // tag条数小于必输条数
      return `${intl.get(`${modelPrompt}.remainingRequiredItems`).d('剩余必选')} ${mandatoryNum -
        num}`;
    } else if (tagListLengthLessThanNumberOfRuleFlag) {
      // tag条数小于(必输+可选)条数
      return `${intl.get(`${modelPrompt}.remainingItems`).d('剩余可选')} ${mandatoryNum +
        optionalNum -
        num}`;
    }
  };

  const addTextClassName = () => {
    const addClassNames = [styles['hcm-dataItem-group-add-text']];
    if (mandatoryNum) {
      addClassNames.push(styles['hcm-dataItem-group-add-text-required']);
    }
    return addClassNames;
  };

  const renderTagList = () => {
    let renderContent = null;
    if (valueType === 'ENCLOSURE') {
      renderContent = tagList.map(item => {
        return (
          <div className={styles['hcm-dataItem-group-add-picList']}>
            <CloseWapper onClick={() => handleTagClose(item.id)}>
              <img alt="" src={fileTypeIcon(item.text)} />
            </CloseWapper>
            <Tooltip title={item.text.name}>
              <div className={styles['hcm-dataItem-group-add-picList-picName']}>
                {item.text.name}
              </div>
            </Tooltip>
          </div>
        );
      });
    } else {
      renderContent = tagList.map(item => {
        const isLongTag = item.text.length > 16;
        const tagElem = (
          <Tag
            key={item.id}
            color={tagClassName[itemValidate(item.text)]}
            closable
            afterClose={() => handleTagClose(item.id)}
          >
            {isLongTag ? `${item.text.slice(0, 20)}...` : item.text}
          </Tag>
        );
        return isLongTag ? (
          <Tooltip title={item.text} key={`tooltip${item.id}`}>
            {tagElem}
          </Tooltip>
        ) : (
          tagElem
        );
      });
    }
    return renderContent;
  };

  const addText = () => {
    switch (valueType) {
      case 'VALUE':
        return intl.get(`${modelPrompt}.inputValue`).d('请输入数值');
      case 'DECISION_VALUE':
        return intl.get(`${modelPrompt}.inputDecisionValue`).d('添加判断值');
      case 'ENCLOSURE':
        return intl.get(`${modelPrompt}.inputEnclosure`).d('上传采集附件');
      case 'TEXT':
        return intl.get(`${modelPrompt}.inputText`).d('请输入文本');
      case 'VALUE_LIST':
        return intl.get(`${modelPrompt}.inputValueList`).d('添加采集值');
      default:
        return '';
    }
  };

  return (
    <div className={classNames.join(' ')}>
      <div className={addTextClassName().join(' ')}>{addText()}</div>
      <div className={styles['hcm-dataItem-group-add']}>
        {renderTagList()}
        {inputVisible && (
          <CloseWapper onClick={handleCloseInput}>
            <InputComponent
              value={fieldValue}
              ref={InputRef}
              requiredFlag={validateComponentsRequired()}
              onChange={handleInputChange}
              onPressEnter={handleInputEnter}
              rule={rule}
            />
          </CloseWapper>
        )}
        {!inputVisible && inputIconVisible() && (
          <Tag onClick={showInput} className={styles['hcm-dataItem-group-add-tag']}>
            <Icon type="add" />
          </Tag>
        )}
        <div className={styles['hcm-dataItem-group-add-info']}>{getAddInfoText()}</div>
      </div>
    </div>
  );
};

export default ListItemContent;
