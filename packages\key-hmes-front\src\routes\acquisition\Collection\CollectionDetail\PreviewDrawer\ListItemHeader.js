/**
 * @Description: 数据项行头信息
 * @Author: <<EMAIL>>
 * @Date: 2021-04-28 13:50:23
 * @LastEditTime: 2021-05-17 14:45:15
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import intl from 'utils/intl';
import { isEmpty } from 'lodash';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

const RenderDataItemInfo = props => {
  const {
    displayValueFlag,
    valueType,
    minimumValue,
    maximalValue,
    trueValue,
    falseValue,
    valueList,
  } = props.rule;
  let renderComponent = null;
  if (displayValueFlag !== 'Y' && valueType === 'VALUE') {
    return null;
  }
  switch (valueType) {
    case 'VALUE':
      renderComponent = (
        <>
          <div className={styles['hcm-row-header-item-info']}>
            {minimumValue
              ? `${intl.get(`${modelPrompt}.minimumValue`).d('最小值')}：${minimumValue}`
              : ''}
          </div>
          <div className={styles['hcm-row-header-item-info']}>
            {maximalValue
              ? `${intl.get(`${modelPrompt}.maximalValue`).d('最大值')}：${maximalValue}`
              : ''}
          </div>
        </>
      );
      break;
    case 'DECISION_VALUE':
      renderComponent = (
        <>
          <div className={styles['hcm-row-header-item-info']}>
            {`${intl.get(`${modelPrompt}.trueValue`).d('符合值')}：${trueValue}`}
          </div>
          <div className={styles['hcm-row-header-item-info']}>
            {`${intl.get(`${modelPrompt}.falseValue`).d('不符合值')}：${falseValue}`}
          </div>
        </>
      );
      break;
    case 'VALUE_LIST':
      renderComponent = (
        <>
          <div className={styles['hcm-row-header-item-info']}>
            {`${intl.get(`${modelPrompt}.optionalValue`).d('可选采集值')}：${valueList.replace(
              /,/g,
              '/',
            )}`}
          </div>
        </>
      );
      break;
    default:
      break;
  }
  return renderComponent;
};

const ListItemHeader = props => {
  const { rule } = props;
  const className = ['iconfont'];
  switch (rule.valueType) {
    case 'VALUE':
      className.push('icontubiaozhizuomoban-19');
      break;
    case 'DECISION_VALUE':
      className.push('icontubiaozhizuomoban-15');
      break;
    case 'ENCLOSURE':
      className.push('icontubiaozhizuomoban-18');
      break;
    case 'TEXT':
      className.push('icontubiaozhizuomoban-16');
      break;
    case 'VALUE_LIST':
      className.push('icontubiaozhizuomoban-17');
      break;
    default:
      break;
  }
  return (
    <div className={styles['hcm-row-header-wrapper']}>
      <i className={className.join(' ')} />
      {isEmpty(rule.tagDescription) ? (
        <div className={styles['hcm-row-header-title']}>{rule.tagCode}</div>
      ) : (
        <div className={styles['hcm-row-header-title']}>
          {rule.tagCode}-{rule.tagDescription}
        </div>
      )}
      <RenderDataItemInfo rule={rule} />
    </div>
  );
};

export default ListItemHeader;
