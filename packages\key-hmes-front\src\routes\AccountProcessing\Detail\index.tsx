import React, { useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  Form,
  Lov,
  Modal,
  Select,
  Table,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import { Collapse, Popconfirm, } from 'choerodon-ui';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { useDataSetEvent } from 'utils/hooks';
import { TarzanSpin, } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentUser } from 'utils/utils';
import { refreshTab } from 'utils/menuTab';
import { detailDS, scanFormDS } from '../stores/detailDS';
import scanImg from '@/assets/icons/scan-o.svg';
import { ncRecordDS } from '../stores/detailRecordTypeDS';
import openBatchBarCodeModal from './BatchBarCodeModal';
import InputLovDS from '../stores/InputLovDS';
import { compact } from 'lodash';
import { formDS } from '../stores/detailDS';
import { queryIdpValue } from '@/services/api';
import {
  LineDelete,
  SaveNcRecord,
  ScanMaterialLotRelatedInfo,
  InitiateReview,
  RecordCancel,
} from '../services';
import scrapFactory from '../stores/scrapModalDs';
import { useDataSet, } from 'utils/hooks';
import { fetchDefaultSite } from '@/services/api';
const { Panel } = Collapse;
const modelPrompt = 'tarzan.mes.event.accountProcessing';

const userInfo = getCurrentUser();

let containerCreateModal;

const BadRecordDetail = observer(props => {
  const {
    history,
    location,
    match: { params },
  } = props;

  const scrapDS = useDataSet(scrapFactory, 'inputLovDS');

  const siteId = location?.state?.siteLov?.siteId || '';

  const { id } = params as any;
  // 不良对象类型
  const [ncRecordType, setNcRecordType] = useState<string>('');
  // 是否能编辑
  const [canEdit, setCanEdit] = useState<boolean>(id === 'create');
  // 折叠activeKey
  const [activeKey, setActiveKey] = useState<any>([
    'badRecordFormInfo',
    'badRecordLineInfo',
    'badRecordLineDetail',
  ]);
  const [status, setStatus] = useState(false);

  const inputLovDS = new DataSet({ ...InputLovDS() });
  const formDs = new DataSet({ ...formDS() });
  // 不良记录明细Ds
  const ncRecordDs = useMemo(() => new DataSet(ncRecordDS()), []); // 库存不良
  const scanFormDs = useMemo(() => new DataSet(scanFormDS()), []);

  // 详情界面Ds
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const { run: scanMaterialLotRelatedInfo, loading: scanMaterialLotLoading } = useRequest(
    ScanMaterialLotRelatedInfo(),
    {
      manual: true,
      needPromise: true,
    },
  );
  // 保存
  const { run: saveNcRecord, loading: saveLoading } = useRequest(SaveNcRecord(), {
    manual: true,
  });

  // 记录行删除
  const { run: lineDelete, loading: lineDeleteLoading } = useRequest(LineDelete(), {
    manual: true,
    needPromise: true,
  });
  // 状态取消
  const { run: cancel, loading: cancelLoading } = useRequest(RecordCancel(), {
    manual: true,
    needPromise: true,
  });
  // 发起评审
  const { run: initiateReview, loading: initiateReviewLoading } = useRequest(InitiateReview(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    queryIdpValue("HME.ROLE_CONTROL_NC").then((res) => {
      if (res) {
        let currentAccord = false;
        (res || []).forEach((item) => {
          if (item.value === userInfo.currentRoleCode) {
            currentAccord = true;
          }
        })
        setStatus(currentAccord)
      } else {
        setStatus(false);
      }
    });
    if (id === 'create') {
      if (location.state) {
        setCanEdit(true);
        detailDs.loadData([{}]);
        ncRecordDs.loadData([]);
        detailDs.current?.set('siteId', siteId);
        detailDs.current?.set('siteCode', location?.state?.siteLov?.siteCode);
        detailDs.current?.set('siteName', location?.state?.siteLov?.siteName);
        detailDs.current?.set('ncIncidentStatus', 'NEW');
        detailDs.current?.set('ncRecordType', location?.state?.ncRecordType);
        setNcRecordType(location?.state?.ncRecordType);
        const list = location?.state?.detailObjectDsList
        const { materialId, materialCode, materialName } = list[0]
        detailDs.current?.set('materialObj', {
          materialCode,
          materialId,
          materialName,
        });
        ncRecordDs.loadData(list)
        return
      }
      fetchDefaultSite().then(res => {
        if (res && res.success) {
          detailDs.current?.set('siteLov', res.rows);
          detailDs.current?.set('siteId', res.rows?.siteId);
          detailDs.current?.set('siteName', res.rows?.siteName);
          detailDs.current?.set('siteCode', res.rows?.siteCode);
        }
      });
      return;
    }
    handleQueryDetail(id);
  }, [id, siteId]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('ncIncidentId', id);
    detailDs.query().then(res => {
      const { ncRecordType, lineList } = res || {};
      setNcRecordType(ncRecordType);
      ncRecordDs.setState('ncRecordType', ncRecordType);
      ncRecordDs.loadData(lineList);
      scanFormDs.loadData([res]);
      ncRecordDs.current = lineList[0];
    });
  };


  // 监听不良记录明细表格勾选数据
  const handleUpdate = ({ name }) => {
    scanFormDs.loadData([{ ...detailDs.current?.toJSONData() }]);
    if (name === 'materialObj') {
      ncRecordDs.loadData([]);
    }
    if (name === 'ncRecordType') {
      ncRecordDs.loadData([]);
      detailDs.current?.set('operationId', undefined);
      detailDs.current?.set('operationName', undefined);
    }
    if (name === 'siteLov') {
      detailDs.current?.init('materialObj', undefined);
      detailDs.current?.init('revisionCode', undefined);
      ncRecordDs.loadData([]);
    }
  };
  useDataSetEvent(detailDs, 'update', handleUpdate);

  // 头保存
  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const params = {
      ...detailDs.toData()[0],
      lineList: ncRecordDs.toData(),
    };
    saveNcRecord({
      params,
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        history.push(`/hmes/accountProcessing/detail/${res}`);
        handleQueryDetail(res);
      },
    });
  };
  const handleChangeNcRecordType = (value,) => {
    detailDs.current?.set('materialObj', null);
    detailDs.current?.set('revisionCode', null);
    setNcRecordType(value);
    ncRecordDs.setState('ncRecordType', ncRecordType);
  };

  // 库存品材料不良/库存品自制件不良
  const inventoryTableColumns: Array<any> = [
    {
      name: 'materialCode',
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'qty',
      editor: canEdit,
    },
    {
      name: 'uomName',
    },
    {
      name: 'workNumOrderLov',
      editor: canEdit,
    },
    {
      name: 'scrapReason',
      editor: canEdit,
    },
  ];

  const handleLineDelete = () => {
    if (params.id === 'create') {
      ncRecordDs.remove(ncRecordDs.selected);
    } else {
      const ids = compact(ncRecordDs.selected.map(item => item.get('ncRecordId')))
      if (ids.length > 0) {
        lineDelete({
          params: ids,
        }).then(res => {
          if (res && res.success) {
            ncRecordDs.remove(ncRecordDs.selected);
            if (ncRecordDs.toData().length === 0) {
              detailDs.current?.set('materialObj', null);
              detailDs.current?.set('revisionCode', null);
            }
          }
        });
      } else {
        ncRecordDs.remove(ncRecordDs.selected);
      }
    }
  };

  // 去重
  const uniqueArray = (res, tableList, field) => {
    // tableList为原值 res为新增数据 field为去重字段
    const temp: any = [];
    res.forEach(i => {
      if (tableList.every((j: any) => j[field] !== i[field])) {
        temp.push(i);
      }
    });
    return temp;
  };
  // 取消-状态变更
  const clickMenu = async () => {
    cancel({
      params: [detailDs.current?.get('ncIncidentId')],
    }).then(res => {
      if (res && res.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        history.push('/hmes/accountProcessing/list');
      }
    });
  };

  const cancelEdit = () => {
    if (id === 'create') {
      history.push('/hmes/accountProcessing/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(id);
    }
  };
  // 批量输入物料批&eo弹框
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    openBatchBarCodeModal({
      inputLovDS,
      inputLovFlag,
      inputLovTitle,
      inputLovVisible,
      targetDS: detailDs,
      submit: handleScan,
    });
    inputLovDS.queryDataSet?.current?.set('code', '');
    inputLovDS.data = [];
    inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
  };
  // 物料批&eo扫描
  const handleScan = (inputLovFlag, str) => {
    if (inputLovFlag === 'materialLots') {
      scanMaterialLot(str);
    }
  };

  const scanMaterialLot = (data) => {
    scanMaterialLotRelatedInfo({
      params: {
        materialLotCodes: data,
      },
    }).then(res => {
      if (res && res.success && res.rows) {
        const { content } = res.rows
        if (content.length > 0) {
          const list = content.map(item => ({
            ...item,
            qty: item.primaryUomQty,
            uomName: item.primaryUomCode,
          }))
          scanFormDs.current?.init('materialLots', null);
          const temp = ncRecordDs.toData();
          detailDs.current?.set('materialObj', {
            materialCode: list[0]?.materialCode,
            materialId: list[0]?.materialId,
            materialName: list[0]?.materialName,
          });
          detailDs.current?.set('revisionCode', list[0]?.revisionCode);
          ncRecordDs.loadData(
            temp.concat(uniqueArray(list, ncRecordDs.toData(), 'materialLotCode')),
          );
        } else {
          notification.error({ message: '未扫描到数据，请检查' })
        }
        // 默认查询第一条
      } else {
        scanFormDs.current?.set('materialLots', null)
      }
    });
  };

  // 发起评审
  const handleInitiateReview = async () => {
    if (await detailDs.validate()) {
      // 校验欲处置结果
      if (ncRecordDs.toData().length < 1) {
        notification.warning({
          message: intl
            .get(`${modelPrompt}.error.noLineInfo`)
            .d(`当前无行信息，无法执行！`),
        });
        return;
      }
      initiateReview({
        params: {
          ...detailDs.toData()[0],
          lineList: ncRecordDs.toData(),
        },
      }).then(res => {
        if (res && res.success) {
          refreshTab(location?.state?.sourceKey)
          history.push('/hmes/accountProcessing/list');
        }
      });
    }
  };

  const handleWorking = () => {
    scrapDS.setState('ncRecordType', ncRecordType)
    const materialLotId = ncRecordDs.selected[0].toData().materialLotId;
    scrapDS.loadData([{ materialLotId }])
    scrapDS.getField('workNumOrderLov')?.set('required', true)
    scrapDS.getField('cancelReason')?.set('required', false)
    containerCreateModal = Modal.open({
      key: 'workNumOrder',
      style: {
        width: '360px',
      },
      closable: true,
      title: '批量选择工单',
      destroyOnClose: true, // 关闭时是否销毁
      children: (
        <Form dataSet={scrapDS}>
          <Lov name="workNumOrderLov" />
        </Form>
      ),
      cancelButton: false,
      onOk: async () => {
        return handleToOkCreateWork();
      },
      onCancel: () => {
        scrapDS.reset();
      },
    });
  }

  const handleScrapping = () => {
    containerCreateModal = Modal.open({
      key: 'createContainer',
      closable: true,
      title: '批量选择原因',
      destroyOnClose: true, // 关闭时是否销毁
      children: (
        <Form dataSet={formDs}>
          <Select name="scrapping" />
        </Form>
      ),
      cancelButton: false,
      onOk: () => {
        return handleToOkCreateInsp();
      },
      onCancel: () => {
        formDs.reset();
      },
    });
  }
  const handleToOkCreateWork = async () => {
    const vaidate = await scrapDS.validate();
    if (!vaidate) {
      notification.error({
        message: '请校验数据！',
      });
      return false;
    }
    const workNumOrderLov = scrapDS.current?.get('workNumOrderLov');
    const workOrderId = scrapDS.current?.get('workOrderId');
    const workOrderNum = scrapDS.current?.get('workOrderNum');
    const selectedRecordsData = ncRecordDs.selected?.map(item => ({
      ...item.toData(),
      workNumOrderLov,
      workOrderId,
      workOrderNum,
    }));
    ncRecordDs.loadData(
      selectedRecordsData.concat(uniqueArray(ncRecordDs.toData(), selectedRecordsData, 'identification')),
    );
    containerCreateModal.close();
    scrapDS.reset();
  };

  const handleToOkCreateInsp = async () => {
    const vaidate = await formDs.current?.validate(false, true);
    if (!vaidate) {
      notification.error({
        message: '请校验数据！',
      });
      return false;
    }
    const scrapping = formDs.current?.get('scrapping');
    const selectedRecordsData = ncRecordDs.selected?.map(item => ({
      ...item.toData(),
      scrapReason: scrapping,
    }));
    ncRecordDs.loadData(
      selectedRecordsData.concat(uniqueArray(ncRecordDs.toData(), selectedRecordsData, 'identification')),
    );
    containerCreateModal.close();
    formDs.reset();
  };


  return (
    <div className="hmes-style" style={{ height: '95%', overflow: 'auto' }}>
      <TarzanSpin
        dataSet={detailDs}
        spinning={
          saveLoading ||
          lineDeleteLoading ||
          scanMaterialLotLoading ||
          initiateReviewLoading ||
          cancelLoading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('账务处理平台详情')}
          backPath="/hmes/accountProcessing/list"
        >
          {detailDs.current?.get('ncIncidentStatus') === 'NEW' && (
            <>
              {canEdit ? (
                <>
                  <Button
                    color={ButtonColor.primary}
                    icon="save"
                    loading={saveLoading}
                    onClick={() => handleSave()}
                  >
                    {intl.get('tarzan.common.button.save').d('保存')}
                  </Button>
                  <Button color={ButtonColor.default} onClick={cancelEdit}>
                    {intl.get('tarzan.common.button.cancel').d('取消')}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    color={ButtonColor.primary}
                    onClick={() => setCanEdit(true)}
                    loading={saveLoading}
                    icon="edit-o"
                  >
                    {intl.get('tarzan.common.button.edit').d('编辑')}
                  </Button>
                  {/* 取消状态 */}
                  <Button
                    disabled={detailDs.current?.get('ncIncidentStatus') !== 'NEW'}
                    onClick={clickMenu}
                    icon="cached"
                    color={ButtonColor.primary}
                  >
                    {intl.get(`tarzan.common.button.cancel`).d('取消')}
                  </Button>
                </>
              )}
              <Button
                color={ButtonColor.primary}
                onClick={handleInitiateReview}
                disabled={detailDs.current?.get('ncIncidentStatus') !== 'NEW'}
              >
                {intl.get(`${modelPrompt}.button.initiateReview`).d('发起评审')}
              </Button>
            </>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} activeKey={activeKey} onChange={value => setActiveKey(value)}>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecordFormInfo`).d('单据信息')}
              key="badRecordFormInfo"
            >
              <Form columns={3} labelWidth={112} dataSet={detailDs} disabled={!canEdit}>
                <TextField name="ncIncidentNum" />
                <Select
                  name="ncRecordType"
                  disabled={id !== 'create'}
                  onChange={handleChangeNcRecordType}
                />
                <Select name="ncIncidentStatus" disabled />
                <Lov name="siteLov" disabled={id !== 'create'} />
                <TextArea name="remark" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecordLineInfo`).d('记录信息')}
              key="badRecordLineInfo"
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '60%' }}>
                  {detailDs.current?.get('ncIncidentStatus') === 'NEW' && (
                    <Form
                      columns={2}
                      labelWidth={112}
                      dataSet={scanFormDs}
                      disabled={
                        !canEdit ||
                        (ncRecordType === 'EO_ALL_NC' && !detailDs.current?.get('siteId')) ||
                        (ncRecordType === 'RM_NC' && !detailDs.current?.get('siteId'))
                      }
                    >
                      <>
                        <TextField
                          name="materialLots"
                          readOnly
                          clearButton={false}
                          placeholder="请扫描物料批"
                          suffix={
                            <img
                              alt=""
                              style={{ width: '20px', paddingRight: '5px' }}
                              src={scanImg}
                              onClick={() => onOpenInputModal(true, 'materialLots', '物料批编码')}
                            />
                          }
                        />
                      </>
                    </Form>
                  )}
                </div>
                <div>
                  {detailDs.current?.get('ncIncidentStatus') === 'NEW' && (
                    <>
                      <Popconfirm
                        title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                        onConfirm={() => handleLineDelete()}
                        okText={intl.get('tarzan.common.button.confirm').d('确认')}
                        cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                      >
                        <Button
                          icon="delete"
                          funcType={FuncType.flat}
                          disabled={!canEdit || !ncRecordDs.selected.length}
                        >
                          {intl.get(`tarzan.common.button.delete`).d('删除')}
                        </Button>
                      </Popconfirm>
                    </>
                  )}
                  {/* <>
                    <Button
                      funcType={FuncType.flat}
                      disabled={ncRecordDs.selected.length === 0}
                      onClick={handleWorking}
                    >
                      {intl.get(`${modelPrompt}.title.workNum`).d('批量选择工单')}
                    </Button>
                  </> */}
                  <>
                    <Button
                      funcType={FuncType.flat}
                      disabled={ncRecordDs.selected.length === 0}
                      onClick={handleScrapping}
                    >
                      {intl.get(`${modelPrompt}.title.batchPreDisposal`).d('批量选择原因')}
                    </Button>
                  </>
                </div>
              </div>
              {!!ncRecordType && (
                <Table
                  dataSet={ncRecordDs}
                  columns={inventoryTableColumns}
                  highLightRow
                  rowNumber
                  style={{
                    height: ncRecordDs.toData().length === 0 ? 230 : 350,
                  }}
                />
              )}
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withCustomize({
    unitCode: [],
  })(BadRecordDetail as any),
);
