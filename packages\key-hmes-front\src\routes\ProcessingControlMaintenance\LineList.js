import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Switch, Lov } from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import request from 'utils/request';
import { notification } from 'hzero-ui';
import { observer } from 'mobx-react';
import AssociatedObjectsDrawer from './AssociatedObjectsDrawer';
import { Host } from '@/utils/config';
// const Host = `/yp-mes-20000`;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.processingControlMaintenance';

const LineList = observer(props => {
  const {
    canEdit,
    assObjectsDs,
    singleAssObjectDs,
    handleCreateLine,
    tableDs,
    historyLineTableDs,
    historyQueryBtnDisbled,
  } = props;

  const associatedObjectsDrawerSubmit = async () => {
    const validate = await singleAssObjectDs.validate(false, true);
    if (!validate) {
      return false;
    }
    const tableList = singleAssObjectDs.toJSONData();
    // 原来的逻辑 用deleteFlag
    const paramsData = tableList.map(item => {
      return {
        ...item,
        deleteFlag: item._status === 'delete' ? 'Y' : 'N',
      };
    });
    const res = await request(`${Host}/v1/${tenantId}/hme-process-ctrl-config-dtls/save/ui`, {
      method: 'post',
      body: paramsData,
    });
    const result = getResponse(res);
    if (result) {
      notification.success({
        message: intl.get('tarzan.aps.common.notification.success').d('操作成功'),
      });
      return true;
    }
    return false;
  };

  const associatedObjectsModify = async (record, edit) => {
    singleAssObjectDs.setQueryParameter('processCtrlConfigLId', record.data.processCtrlConfigLId);
    await singleAssObjectDs.query();
    const controlItemCode = record.get('controlItemCode');
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.modal`).d('明细维护'),
      drawer: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <AssociatedObjectsDrawer
          singleAssObjectDs={singleAssObjectDs}
          canEdit={edit}
          record={record || {}}
          controlItemCode={controlItemCode}
          // 用get方法取，他是ObservableArrayAdministration，还需要spile
        />
      ),
      onOk: () => associatedObjectsDrawerSubmit(record),
    });
  };

  const handleQueryHistory = async () => {
    const selectedRecords = tableDs.selected;

    if (!selectedRecords.length) return;

    const processCtrlConfigIds = selectedRecords.map(record => {
      return record.data.processCtrlConfigId;
    });

    historyLineTableDs.setQueryParameter('processCtrlConfigIds', processCtrlConfigIds);
    historyLineTableDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history.query`).d('历史查询'),
      drawer: true,
      style: { width: 1080 },
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      closable: true,
      maskClosable: false,
      destroyOnClose: true,
      children: (
        <Table queryBar="filterBar" dataSet={historyLineTableDs} columns={historyLineColumns} />
      ),
    });
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={
            !canEdit ||
            tableDs.selected.length === 0 ||
            !tableDs.selected[0]?.data?.processCtrlConfigId
          }
          onClick={() => handleCreateLine()}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => {
            assObjectsDs.remove(record);
          }}
        >
          <Button disabled={!canEdit} funcType="flat" icon="remove" shape="circle" size="small" />
        </Popconfirm>
      ),
      lock: 'left',
    },
    // 控制项编码
    {
      name: 'operationObj',
      align: 'left',
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a onClick={() => associatedObjectsModify(record)}>{record.get('controlItemCode')}</a>
          </span>
        );
      },
      editor: () => {
        return canEdit && <Lov dataSet={assObjectsDs} name="operationObj" />;
      },
    },
    // 控制项描述
    {
      name: 'controlItemName',
      align: 'left',
      renderer: ({ record }) => record.get('controlItemName'),
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      editor: canEdit && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      title: intl.get('tarzan.common.label.action').d('操作'),
      name: 'action',
      width: 120,
      lock: 'right',
      renderer: ({ record }) => {
        return (
          <a
            disabled={record.status === 'add'}
            onClick={() => associatedObjectsModify(record, true)}
          >
            {intl.get(`tarzan.common.label.edit`).d('编辑')}
          </a>
        );
      },
    },
  ];

  const historyLineColumns = [
    // 控制项编码
    {
      name: 'operationObj',
      align: 'left',
    },
    // 控制项描述
    {
      name: 'controlItemDescription',
      align: 'left',
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    // 创建人
    {
      name: 'createdBy',
      align: 'left',
    },
    // 创建日期
    {
      name: 'creationDate',
      align: 'left',
    },
  ];

  return (
    <>
      <Button
        onClick={handleQueryHistory}
        style={{ marginRight: 15 }}
        disabled={historyQueryBtnDisbled}
      >
        {intl.get('tarzan.common.label.history.query').d('历史查询')}
      </Button>
      <Table dataSet={assObjectsDs} columns={columns} virtual style={{ height: 200 }} />
    </>
  );
});

export default LineList;
