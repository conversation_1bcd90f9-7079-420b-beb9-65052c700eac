/**
 * @Description: 盘点工作台-详情页
 * @Author: <<EMAIL>>
 * @Date: 2022-02-09 10:47:34
 * @LastEditTime: 2023-05-18 16:10:35
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useEffect } from 'react';
import {
  Button,
  Form,
  TextField,
  Select,
  DataSet,
  Lov,
  Dropdown,
  Menu,
  SelectBox,
  Modal,
} from 'choerodon-ui/pro';
import { Spin } from 'choerodon-ui';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
// import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { observer } from 'mobx-react';

import intl from 'utils/intl';
import notification from 'utils/notification';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { ViewMode as SelectViewModel } from 'choerodon-ui/pro/lib/radio/enum';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { ExpandCardC7n, drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import {
  detailDS,
  locatorRangeTableDS,
  materialRangeTableDS,
  stocktakeDetailTableDS,
  stocktakeBarcodeDetailDS,
} from '../stores/StocktakeWorkbenchDetailDS';
import LocatorRangeTable from './LocatorRangeTable';
import MaterialRangeTable from './MaterialRangeTable';
import StocktakeDetail from './StocktakeDetail';
import StockTakeBarcodeDrawer from './StocktakeDetail/StockTakeBarcodeDrawer';
import {
  FetchStocktageDetail,
  SaveStocktageDetail,
  BatchAddByAreaLocator,
  BatchAddByLocatorRange,
  ChangeStatus,
} from '../services';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';
const tenantId = getCurrentOrganizationId();

// /**
//  * 取差集 arr1 - arr2
//  *
//  * @param {any[]} arr1
//  * @param {any[]} arr2
//  * @return {any[]}
//  */
// const getDefferenceSet = (arr1: any[], arr2: any[]) => {
//   return arr1.filter(x => arr2.every(y => y !== x));
// };
// 定义弹窗
let _modal;
const MaterialSitePropertiesDetail = observer(props => {
  const { history } = props;
  const { id } = props.match.params;
  const createFlag = useMemo(() => id === 'create', [id]);
  const {
    match: { path },
    customizeForm,
  } = props;

  const [canEdit, setCanEdit] = useState(false);
  const [selectedSiteId, setSelectedSiteId] = useState<number | null>(null);
  // 表格选中的行库位Ids
  const [selectedLocatorTableIds, setSelectedLocatorTableIds] = useState<number[]>([]);
  // 表格选中的行物料Ids
  const [selectedMaterialTableIds, setSelectedMaterialTableIds] = useState<number[]>([]);
  // Lov选中的仓库Id
  const [selectedAreaLocatorId, setSelectedAreaLocatorId] = useState<number | null>(null);
  // 盘点单的当前状态
  const [currentStocktakeStatus, setCurrentStocktakeStatus] = useState<string | null>(null);
  // 能否输入差异原因
  const [isCompleted] = useState(currentStocktakeStatus === 'COMPLETED');
  // 抽盘按钮

  // 详情页展示的视图类型
  const [viewType, setViewType] = useState<'basicProperties' | 'stocktakeDetail'>(
    'basicProperties',
  );
  // 盘点明细表格选中的数据
  const [selectedStocktakeBarcodeList, setSelectedStocktakeBarcodeList] = useState<any[]>([]);
  // 获取盘点单数据
  const fetchStocktageDetail = useRequest(FetchStocktageDetail(), { manual: true });
  // 保存盘点单数据
  const saveStocktageDetail = useRequest(SaveStocktageDetail(), { manual: true });
  // 根据仓库添加仓库下的所有库位到库位范围
  const batchAddByAreaLocator = useRequest(BatchAddByAreaLocator(), { manual: true });
  // 根据库位批量新建物料数据
  const batchAddByLocatorRange = useRequest(BatchAddByLocatorRange(), { manual: true });
  // 盘点单状态变更
  const changeStatus = useRequest(ChangeStatus(), { manual: true });
  const detailDs = useMemo(() => new DataSet({ ...detailDS() }), []);
  const locatorRangeTableDs = useMemo(() => new DataSet(locatorRangeTableDS()), []);
  const materialRangeTableDs = useMemo(() => new DataSet(materialRangeTableDS()), []);
  const stocktakeDetailTableDs = useMemo(() => new DataSet(stocktakeDetailTableDS()), []);
  const stocktakeBarcodeDetailDs = useMemo(() => new DataSet(stocktakeBarcodeDetailDS()), []);

  const [modalDs] = useState(
    () =>
      new DataSet({
        fields: [
          {
            name: 'userLov',
            type: FieldType.object,
            required: true,
            lovCode: 'HME.INVENTORY_USER',
            textField: 'realName',
            valueField: 'id',
            ignore: FieldIgnore.always,
            label: intl.get(`${modelPrompt}.userLov`).d('盘点人'),
            dynamicProps: {
              lovPara: () => {
                const selectedRecord = materialRangeTableDs.selected[0];
                return {
                  tenantId,
                  areaLocatorId: selectedRecord?.get('locatorId'),
                };
              },
            },
          },
          {
            name: 'userId',
            bind: 'userLov.id',
          },
          {
            name: 'realName',
            bind: 'userLov.realName',
          },
        ],
      }),
  );

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      detailDs.current!.set('stocktakeStatus', 'NEW');
      return;
    }
    initPageData();
  }, [id]);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 条码明细抽屉表格事件监听
  useEffect(() => {
    barcodeListener(true);
    return function clean() {
      barcodeListener(false);
    };
  }, [_modal]);

  // 生成行列表DS查询项
  const listener = flag => {
    // 表单交互件套
    if (detailDs) {
      const handler = flag ? detailDs.addEventListener : detailDs.removeEventListener;
      // 更新事件
      handler.call(detailDs, 'update', handleDetailDsChange);
      handler.call(detailDs, 'update', handleDetailDsChange);
    }
    // 列表交互监听
    if (locatorRangeTableDs) {
      const handler = flag
        ? locatorRangeTableDs.addEventListener
        : locatorRangeTableDs.removeEventListener;
      // 选中和撤销选中事件
      handler.call(locatorRangeTableDs, 'batchSelect', handleLocatorRangeTableChange);
      handler.call(locatorRangeTableDs, 'batchUnSelect', handleLocatorRangeTableChange);
    }
    if (materialRangeTableDs) {
      const handler = flag
        ? materialRangeTableDs.addEventListener
        : materialRangeTableDs.removeEventListener;
      // 选中和撤销选中事件
      handler.call(materialRangeTableDs, 'batchSelect', handleMaterialRangeChange);
      handler.call(materialRangeTableDs, 'batchUnSelect', handleMaterialRangeChange);
    }
  };

  const barcodeListener = flag => {
    if (stocktakeBarcodeDetailDs) {
      const handler = flag
        ? stocktakeBarcodeDetailDs.addEventListener
        : stocktakeBarcodeDetailDs.removeEventListener;
      // 选中和撤销选中事件
      handler.call(stocktakeBarcodeDetailDs, 'batchSelect', handleStocktakeBarcodeRangeChange);
      handler.call(stocktakeBarcodeDetailDs, 'batchUnSelect', handleStocktakeBarcodeRangeChange);
      // 查询事件
      handler.call(stocktakeBarcodeDetailDs, 'query', handleStocktakeBarcodeQuery);
    }
  };

  // 如果仓库和库位都被清空了，则清空物料范围
  const handleDetailDsChange = ({ record, name }) => {
    if (['areaLocatorLov', 'tableLocatorRangeLov'].includes(name)) {
      if (!record.get('areaLocatorId') && !record.get('locatorIds').length) {
        materialRangeTableDs.loadData([]);
        setSelectedMaterialTableIds([]);
        record.init('tableMaterialRangeLov', undefined);
      }
    }
  };

  // 库位范围表格行选中事件
  const handleLocatorRangeTableChange = ({ dataSet }) => {
    const _selectedTableLineIds: number[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLineIds.push(item.toData().locatorId);
    });
    setSelectedLocatorTableIds(_selectedTableLineIds);
  };

  // 物料范围表格行选中事件
  const handleMaterialRangeChange = ({ dataSet }) => {
    const _selectedTableLineIds: number[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLineIds.push(item.toData().locatorId);
    });
    setSelectedMaterialTableIds(_selectedTableLineIds);
  };

  // 条码明细表格行选中事件
  const handleStocktakeBarcodeRangeChange = ({ dataSet }) => {
    const _selectedTableLine: any[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLine.push(item.toData());
    });
    // if (_modal) {
    //   _modal.update({
    //     title: renderDrawerTitle(_selectedTableLine),
    //   });
    // }
  };

  const handleStocktakeBarcodeQuery = () => {
    // if (_modal) {
    //   _modal.update({
    //     title: renderDrawerTitle([]),
    //   });
    // }
  };

  const initPageData = () => {
    fetchStocktageDetail.run({
      params: {
        stocktakeId: id,
      },
      onSuccess: res => {
        const { locatorRangeList = [], materialRangeList = [], ...others } = res;
        detailDs.loadData([others]);
        if (locatorRangeList) {
          detailDs.current!.set('tableLocatorRangeLov', locatorRangeList);
          locatorRangeTableDs.loadData(locatorRangeList);
          stocktakeDetailTableDs.setQueryParameter(
            'locatorRangeIds',
            locatorRangeList.map(item => item.locatorId),
          );
        }
        if (materialRangeList) {
          detailDs.current!.set('tableMaterialRangeLov', materialRangeList);
          materialRangeTableDs.loadData(materialRangeList);
          stocktakeDetailTableDs.setQueryParameter(
            'materialRangeIds',
            materialRangeList.map(item => item.materialId),
          );
        }
        console.log(res.stocktakeStatus);
        setCurrentStocktakeStatus(res.stocktakeStatus);
        stocktakeDetailTableDs.setQueryParameter('stocktakeId', id);
        stocktakeDetailTableDs.setQueryParameter('stocktakeStatus', res.stocktakeStatus);
        stocktakeDetailTableDs.query();
      },
    });
  };

  const handleCancel = () => {
    if (id === 'create') {
      history.push('/hmes/inventory/inventory-workbench/list');
    } else {
      setCanEdit(prev => !prev);
      initPageData();
    }
  };

  const handleSave = async () => {
    const validate = await detailDs.validate();
    if (!validate) {
      return;
    }
    const { areaLocatorId, locatorIds = [] } = detailDs.current!.toData();
    if (!areaLocatorId && !locatorIds.length) {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.atLeastOneIsRequired`)
          .d('仓库和库位范围二者至少必输其一'),
      });
      return;
    }

    saveStocktageDetail.run({
      params: {
        ...detailDs.current!.toData(),
        locatorRangeList: locatorRangeTableDs.toData(),
        materialRangeList: materialRangeTableDs.toData(),
      },
      onSuccess: res => {
        if (res.toString() === id) {
          initPageData();
        } else {
          history.push(`/hmes/inventory/inventory-workbench/detail/${res}`);
        }
        notification.success({});
        setSelectedSiteId(null);
        setSelectedAreaLocatorId(null);
        setSelectedLocatorTableIds([]);
        setSelectedMaterialTableIds([]);
        setCanEdit(false);
      },
    });
  };

  const handleChangeSite = values => {
    if (values) {
      setSelectedSiteId(values.siteId);
    } else {
      setSelectedSiteId(null);
    }
    setSelectedLocatorTableIds([]);
    setSelectedMaterialTableIds([]);
    setSelectedAreaLocatorId(null);
    detailDs.current!.init('areaLocatorLov', {});
    detailDs.current!.init('tableLocatorRangeLov', undefined);
    detailDs.current!.init('tableMaterialRangeLov', undefined);
    locatorRangeTableDs.loadData([]);
    materialRangeTableDs.loadData([]);
  };

  const handleChangeAreaLocator = values => {
    if (values) {
      setSelectedAreaLocatorId(values.locatorId);
    } else {
      setSelectedAreaLocatorId(null);
    }
  };

  const handleSelectedRange = (type: 'locator' | 'material', values) => {
    console.log(type);
    console.log('values', values);
    if (type === 'locator') {
      locatorRangeTableDs.batchUnSelect(locatorRangeTableDs.selected);
      setSelectedLocatorTableIds([]);
      locatorRangeTableDs.loadData([]);
      (values || []).forEach(item => {
        locatorRangeTableDs.create({ ...item, locatorTypeDesc: item.typeDesc });
      });
    } else {
      const locatorId = detailDs.current?.get('areaLocatorLov');
      const materLocat = locatorId.locatorId;
      console.log(materLocat);
      if (!detailDs.current!.get('areaLocatorId') && !locatorRangeTableDs.toData().length) {
        notification.warning({
          message: intl.get(`${modelPrompt}.locatorInNeed`).d('需要首先选择仓库或者库位'),
        });
        return;
      }
      materialRangeTableDs.batchUnSelect(materialRangeTableDs.selected);
      setSelectedMaterialTableIds([]);
      materialRangeTableDs.loadData([]);
      values.forEach(item => {
        const List = {
          ...item,
          locatorId: materLocat,
        };
        materialRangeTableDs.create(List);
      });
    }
  };

  const deleteEnent = async (ds, type: 'locator' | 'material') => {
    // 删除 仓库范围/库位范围 Lov中记录的已选id
    if (type === 'locator') {
      const _selectedIds = ds.selected.map(item => {
        return item.toData().locatorId;
      });
      // 删除库位范围Lov的
      const _tableLocatorRangeLov = detailDs.current!.toData().tableLocatorRangeLov.filter(item => {
        return !_selectedIds.includes(item.locatorId);
      });
      detailDs.current!.set('tableLocatorRangeLov', _tableLocatorRangeLov);
      // 删除下方表格的记录
      await ds.delete(ds.selected, false);
      setSelectedLocatorTableIds([]);
      ds.batchUnSelect(ds.selected);
    } else {
      const _selectedIds = ds.selected.map(item => {
        return item.toData().materialId;
      });
      // 删除库位范围Lov的
      const _tableMaterialRangeLov = detailDs
        .current!.toData()
        .tableMaterialRangeLov.filter(item => {
          return !_selectedIds.includes(item.materialId);
        });
      detailDs.current!.set('tableMaterialRangeLov', _tableMaterialRangeLov);
      // 删除下方表格的记录
      await ds.delete(ds.selected, false);
      setSelectedMaterialTableIds([]);
      ds.batchUnSelect(ds.selected);
    }
  };

  const handleDeleteTableLine = (type: 'locator' | 'material') => {
    if (type === 'locator') {
      deleteEnent(locatorRangeTableDs, type);
    } else {
      deleteEnent(materialRangeTableDs, type);
    }
  };

  const handleBatchAddByAreaLocator = () => {
    batchAddByAreaLocator.run({
      params: {
        areaLocatorId: selectedAreaLocatorId,
      },
      onSuccess: res => {
        if (!res.length) {
          notification.warning({
            message: intl.get(`${modelPrompt}.noLocotar`).d('该仓库下不存在库位！'),
          });
          return;
        }
        // 找出不存在库位范围内的新库位
        const _locatorTableIds = locatorRangeTableDs.toData().map((item: any) => item.locatorId);
        const _newLocatorList = res.filter(item => {
          return !_locatorTableIds.includes(item.locatorId);
        });
        // 添加到库位范围表格内
        _newLocatorList.forEach(item => {
          locatorRangeTableDs.create(item);
        });
        // 添加到Lov中
        detailDs.current!.set(
          'tableLocatorRangeLov',
          detailDs.current!.get('tableLocatorRangeLov').concat(_newLocatorList),
        );
      },
    });
  };

  const handleBatchAddByLocatorRange = () => {
    const locatorId = detailDs.current?.get('areaLocatorLov');
    const materLocat = locatorId.locatorId;
    console.log('materLocat', materLocat);
    batchAddByLocatorRange.run({
      params: {
        locatorIds: detailDs!.current!.get('locatorIds').join(','),
        siteId: selectedSiteId,
      },
      onSuccess: res => {
        if (!res.length) {
          notification.warning({
            message: intl.get(`${modelPrompt}.noMaterial`).d('库位下不存在关联物料！'),
          });
          return;
        }
        // 找出不存在库位范围内的新库位
        const _materialTableIds = materialRangeTableDs.toData().map((item: any) => item.materialId);
        const _newMaterialList = res.filter(item => {
          return !_materialTableIds.includes(item.materialId);
        });
        // 添加到物料表格内
        _newMaterialList.forEach(item => {
          const List = {
            ...item,
            locatorId: materLocat,
          };
          materialRangeTableDs.create(List);
        });
        // 添加到Lov中
        detailDs.current!.set(
          'tableMaterialRangeLov',
          detailDs.current!.get('tableMaterialRangeLov').concat(_newMaterialList),
        );
      },
    });
  };

  const handleChangeStatus = (sourceStatus: string | null, targetStatus: string) => {
    changeStatus.run({
      params: {
        sourceStatus,
        stocktakeIds: [id],
        targetStatus,
      },
      onSuccess: () => {
        notification.success({});
        initPageData();
      },
    });
  };

  const menu = (
    <Menu style={{ width: '100px' }}>
      <Menu.Item disabled={currentStocktakeStatus !== 'NEW'}>
        <a onClick={() => handleChangeStatus('NEW', 'RELEASED')}>
          {intl.get(`${modelPrompt}.released`).d('下达')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={currentStocktakeStatus !== 'STCOUNTING'}>
        <a onClick={() => handleChangeStatus('COUNTCOMPLETED', 'COMPLETED')}>
          {intl.get(`${modelPrompt}.completed`).d('完成')}
        </a>
      </Menu.Item>
      {/* <Menu.Item> */}
      <Menu.Item disabled={!['RELEASED', 'NEW'].includes(currentStocktakeStatus)}>
        <a onClick={() => handleChangeStatus(null, 'CLOSED')}>
          {intl.get(`${modelPrompt}.closed`).d('关闭')}
        </a>
      </Menu.Item>
    </Menu>
  );

  // const handleAdjustDiffer = (list: any[]) => {
  //   adjustDiffer({
  //     params: {
  //       diffAdjustInfoList: list,
  //       stocktakeId: id,
  //     },
  //     onSuccess: () => {
  //       notification.success({});
  //       stocktakeBarcodeDetailDs.batchUnSelect(stocktakeBarcodeDetailDs.selected);
  //       stocktakeBarcodeDetailDs.query();
  //     },
  //   });
  // };

  // const renderDrawerTitle = (list: any[]) => {
  //   // let total = 0;
  //   // let adjustNumber = 0; // 差异数绝对值相加
  //   // let differNumber = 0; // 差异数相加
  //   // let adjustByName = '';
  //   // if (list.length) {
  //   //   total = list.length;
  //   //   list.forEach(item => {
  //   //     const { firstCountQty, reCountQty, currentQuantity } = item;
  //   //     if (!firstCountQty && !reCountQty) {
  //   //       // 没有进行初复的数据也是有差异的
  //   //       adjustNumber += Math.abs(currentQuantity);
  //   //       differNumber += -Math.abs(currentQuantity);
  //   //     } else {
  //   //       adjustNumber +=
  //   //         typeof item.reCountDiffQty === 'number'
  //   //           ? Math.abs(item.reCountDiffQty)
  //   //           : Math.abs(item.firstCountDiffQty);
  //   //       differNumber +=
  //   //         typeof item.reCountDiffQty === 'number' ? item.reCountDiffQty : item.firstCountDiffQty;
  //   //     }
  //   //   });
  //   //   adjustByName = getCurrentUser().realName;
  //   // }
  //   // return (intl.get(`${modelPrompt}.barcodeDetail`).d('条码明细')
  //   //   // <div className={styles['barcode-drawer-title-wapper']}>
  //   //   //   <div className={styles['barcode-drawer-title']}>
  //   //   //     {intl.get(`${modelPrompt}.barcodeDetail`).d('条码明细')}
  //   //   //   </div>
  //   //   //   <div className={styles['barcode-drawer-title-content']}>
  //   //   //     {list.length ? (
  //   //   //       <>
  //   //   //         <div className={styles['content-item']}>
  //   //   //           {`${intl.get(`${modelPrompt}.total`).d('合计')}: ${total}条`}
  //   //   //         </div>
  //   //   //         <div className={styles['content-item']}>
  //   //   //           {`${intl.get(`${modelPrompt}.adjustNumber`).d('调整数')}: ${adjustNumber}`}
  //   //   //         </div>
  //   //   //         <div className={styles['content-item']}>
  //   //   //           {`${intl.get(`${modelPrompt}.differNumber`).d('差异数')}: ${differNumber}`}
  //   //   //         </div>
  //   //   //         <div className={styles['content-item']}>
  //   //   //           {`${intl.get(`${modelPrompt}.adjustCountByName`).d('调整人')}: ${adjustByName}`}
  //   //   //         </div>
  //   //   //       </>
  //   //   //     ) : null}
  //   //   //     <PermissionButton
  //   //   //       // loading={}
  //   //   //       disabled={!list.length}
  //   //   //       onClick={() => handleAdjustDiffer(list)}
  //   //   //       type="c7n-pro"
  //   //   //       permissionList={[
  //   //   //         {
  //   //   //           code: `${path}.button.edit`,
  //   //   //           type: 'button',
  //   //   //           meaning: '详情页-编辑新建删除复制按钮',
  //   //   //         },
  //   //   //       ]}
  //   //   //     >
  //   //   //       {intl.get(`${modelPrompt}.differenceAdjust`).d('差异调整')}
  //   //   //     </PermissionButton>
  //   //   //     <PermissionButton
  //   //   //       disabled={!isCompleted}
  //   //   //       onClick={() => handleSpotCheck()}
  //   //   //       type="c7n-pro"
  //   //   //     >
  //   //   //       {intl.get(`${modelPrompt}.spotCheck`).d('抽盘')}
  //   //   //     </PermissionButton>
  //   //   //   </div>
  //   //   // </div>
  //   // );
  // };
  // const handleSpotCheck = () => {
  //   const _allBarcodeList = selectedStocktakeBarcodeList.length
  //     ? selectedStocktakeBarcodeList
  //     : stocktakeDetailTableDs.toData();
  //   _modal.update({
  //     children: (<StockTakeBarcodeDrawer
  //       stocktakeStatus={detailDs.current!.get('stocktakeStatus')}
  //       ds={stocktakeBarcodeDetailDs}
  //       selectedStocktakeBarcodeList={_allBarcodeList}
  //       editQtyLocator
  //     />),
  //   });
  // }

  const handleOpenStocktakeBarcodeDrawer = () => {
    stocktakeBarcodeDetailDs.loadData([]);
    // 没有选中数据，查全部
    const _allBarcodeList = selectedStocktakeBarcodeList.length
      ? selectedStocktakeBarcodeList
      : stocktakeDetailTableDs.toData();
    const _stocktakeStatus = detailDs.current!.get('stocktakeStatus');
    stocktakeBarcodeDetailDs.setQueryParameter('stocktakeStatus', _stocktakeStatus);
    stocktakeBarcodeDetailDs.setQueryParameter('stocktakeId', detailDs.current!.get('stocktakeId'));
    let _stockActualIds: number[] = [];
    if (_stocktakeStatus === 'NEW') {
      // 新建传这个
      stocktakeBarcodeDetailDs.setQueryParameter(
        'stockRanges',
        _allBarcodeList.map(item => {
          return {
            locatorId: item.locatorId,
            materialId: item.materialId,
            revisionCode: item.revisionCode,
          };
        }),
      );
    } else {
      // 除了新建以外传这个

      _allBarcodeList.forEach(item => {
        _stockActualIds = _stockActualIds.concat(item.stockActualIds);
      });
      stocktakeBarcodeDetailDs.setQueryParameter('stockActualIds', _stockActualIds);
    }
    stocktakeBarcodeDetailDs.query();
    _modal = Modal.open({
      ...drawerPropsC7n({ canEdit, ds: stocktakeBarcodeDetailDs, currentStocktakeStatus }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.barcodeDetail`).d('条码明细'),
      style: {
        width: 1080,
      },
      children: (
        <StockTakeBarcodeDrawer
          stocktakeStatus={detailDs.current!.get('stocktakeStatus')}
          stocktakeId={detailDs.current!.get('stocktakeId')}
          stockActualIds={_stockActualIds}
          ds={stocktakeBarcodeDetailDs}
          selectedStocktakeBarcodeList={_allBarcodeList}
          path={path}
          id={id}
          handleSearch={handleSearch}
        />
      ),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
    });
  };

  const handleSearch = () => {
    notification.success({});
    stocktakeBarcodeDetailDs.batchUnSelect(stocktakeBarcodeDetailDs.selected);
    stocktakeBarcodeDetailDs.query();
  };

  // 批量设置盘点人
  const handleBatchSetChecker = () => {
    modalDs.reset(); // 重置表单数据
    Modal.open({
      key: 'batchSetChecker',
      title: intl.get(`${modelPrompt}.batchSetChecker`).d('批量设置盘点人'),
      children: (
        <Form dataSet={modalDs}>
          <Lov name="userLov" />
        </Form>
      ),
      onOk: async () => {
        if (await modalDs.validate()) {
          const selectedRecords = materialRangeTableDs.selected;
          const modalData = modalDs.toData();
          const userLov = (modalData[0] as any).userLov;

          selectedRecords.forEach(record => {
            record.set('userId', userLov.id);
            record.set('realName', userLov.realName);
            record.set('userLov', userLov);
          });

          return true;
        }
        return false;
      },
    });
  };

  return (
    <div
      className={`hmes-style ${styles['stocktake-workbench-page']}`}
      style={{ height: '98%', overflow: 'auto' }}
    >
      <Spin
        spinning={
          fetchStocktageDetail.loading || saveStocktageDetail.loading || changeStatus.loading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.inventoryWorkbenchMes`).d('盘点工作台-MES')}
          backPath="/hmes/inventory/inventory-workbench/list"
        >
          {canEdit && (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && (
            // <PermissionButton
            //   type="c7n-pro"
            //   color={ButtonColor.primary}
            //   icon="edit-o"
            //   onClick={() => {
            //     setCanEdit(prev => !prev);
            //     setViewType('basicProperties');
            //   }}
            //   permissionList={[
            //     {
            //       code: `${path}.button.edit`,
            //       type: 'button',
            //       meaning: '详情页-编辑新建删除复制按钮',
            //     },
            //   ]}
            // >
            //   {intl.get('tarzan.common.button.edit').d('编辑')}
            // </PermissionButton>
            <Button
              color={ButtonColor.primary}
              icon="edit-o"
              onClick={() => {
                setCanEdit(prev => !prev);
                setViewType('basicProperties');
              }}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
          <Dropdown
            overlay={menu}
            placement={Placements.bottomRight}
            disabled={createFlag || canEdit}
          >
            {/* <PermissionButton
              type="c7n-pro"
              icon="cached"
              disabled={createFlag || canEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.statusChange`).d('状态变更')}
            </PermissionButton> */}
            <Button icon="cached" disabled={createFlag || canEdit}>
              {intl.get(`${modelPrompt}.statusChange`).d('状态变更')}
            </Button>
          </Dropdown>
          {viewType === 'stocktakeDetail' && (
            // <PermissionButton
            //   type="c7n-pro"
            //   icon="file-text"
            //   onClick={handleOpenStocktakeBarcodeDrawer}
            //   permissionList={[
            //     {
            //       code: `${path}.button.edit`,
            //       type: 'button',
            //       meaning: '列表页-编辑新建删除复制按钮',
            //     },
            //   ]}
            // >
            //   {intl.get(`${modelPrompt}.barcodeDetail`).d('条码明细')}
            // </PermissionButton>
            <Button icon="file-text" onClick={handleOpenStocktakeBarcodeDrawer}>
              {intl.get(`${modelPrompt}.barcodeDetail`).d('条码明细')}
            </Button>
          )}
        </Header>
        <Content>
          {!canEdit && (
            <SelectBox
              className={styles['select-box-view-type']}
              mode={SelectViewModel.button}
              value={viewType}
              onChange={value => {
                setViewType(value);
              }}
            >
              <SelectBox.Option value="basicProperties">
                {intl.get(`${modelPrompt}.basicInfo`).d('基础信息')}
              </SelectBox.Option>
              <SelectBox.Option value="stocktakeDetail">
                {intl.get(`${modelPrompt}.stocktakeDetail`).d('盘点明细')}
              </SelectBox.Option>
            </SelectBox>
          )}
          {viewType === 'basicProperties' ? (
            <>
              <ExpandCardC7n
                showExpandIcon
                title={intl.get(`${modelPrompt}.basicInfo`).d('基础信息')}
              >
                {customizeForm(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_DETAIL.BASIC`,
                  },
                  <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={3}>
                    <TextField name="stocktakeNum" />
                    <Select name="openFlag" />
                    <Select name="stocktakeStatus" disabled={createFlag} />
                    <TextField name="identification" />
                    <Lov name="siteLov" onChange={handleChangeSite} noCache />
                    <Lov name="areaLocatorLov" onChange={handleChangeAreaLocator} noCache />
                    <TextField name="remark" />
                  </Form>,
                )}
              </ExpandCardC7n>
              <ExpandCardC7n
                showExpandIcon
                title={intl.get(`${modelPrompt}.locatorRange`).d('库位范围')}
                extra={
                  createFlag && (
                    <>
                      <Button
                        disabled={!selectedAreaLocatorId}
                        onClick={handleBatchAddByAreaLocator}
                      >
                        {intl.get(`${modelPrompt}.createByAreaLocator`).d('根据仓库批量新建')}
                      </Button>
                      <Button
                        disabled={!selectedLocatorTableIds.length}
                        onClick={() => handleDeleteTableLine('locator')}
                      >
                        {intl.get('tarzan.common.button.delete').d('删除')}
                      </Button>
                      <Lov
                        color={selectedSiteId ? ButtonColor.primary : ButtonColor.gray}
                        dataSet={detailDs}
                        name="tableLocatorRangeLov"
                        mode={ViewMode.button}
                        clearButton={false}
                        autoSelectSingle={false}
                        onChange={values => handleSelectedRange('locator', values)}
                        noCache
                      >
                        {intl.get('tarzan.common.button.create').d('新建')}
                      </Lov>
                    </>
                  )
                }
              >
                <LocatorRangeTable ds={locatorRangeTableDs} id={id} />
              </ExpandCardC7n>
              <ExpandCardC7n
                showExpandIcon
                title={intl.get(`${modelPrompt}.materialRange`).d('物料范围')}
                extra={
                  createFlag && (
                    <>
                      <Button
                        funcType={FuncType.flat}
                        onClick={handleBatchSetChecker}
                        disabled={!selectedMaterialTableIds.length}
                      >
                        {intl.get(`${modelPrompt}.batchSetChecker`).d('批量设置盘点人')}
                      </Button>
                      <Button
                        funcType={FuncType.flat}
                        onClick={handleBatchAddByLocatorRange}
                        disabled={!canEdit || !locatorRangeTableDs.toData().length}
                      >
                        {intl
                          .get(`${modelPrompt}.createByLocatorRange`)
                          .d('根据物料库位关系批量新增')}
                      </Button>
                      <Button
                        disabled={!selectedMaterialTableIds.length}
                        onClick={() => handleDeleteTableLine('material')}
                      >
                        {intl.get('tarzan.common.button.delete').d('删除')}
                      </Button>
                      <Lov
                        color={selectedSiteId ? ButtonColor.primary : ButtonColor.gray}
                        dataSet={detailDs}
                        name="tableMaterialRangeLov"
                        mode={ViewMode.button}
                        clearButton={false}
                        onChange={values => handleSelectedRange('material', values)}
                        noCache
                      >
                        {intl.get('tarzan.common.button.create').d('新建')}
                      </Lov>
                    </>
                  )
                }
              >
                <MaterialRangeTable ds={materialRangeTableDs} id={id} />
              </ExpandCardC7n>
            </>
          ) : (
            <StocktakeDetail
              ds={stocktakeDetailTableDs}
              stocktakeStatus={detailDs.current!.get('stocktakeStatus')}
              setSelectedStocktakeBarcodeList={setSelectedStocktakeBarcodeList}
            />
          )}
        </Content>
      </Spin>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.stocktake.stocktakeWorkbench', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_DETAIL.BASIC`],
    // @ts-ignore
  })(MaterialSitePropertiesDetail),
);
