// import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.BlueGlueCodeNationalStandardCode';
// const prefix = '/yp-mes-38546'

const initialDs = () => ({
  autoQuery: false,
  primaryKey: 'lineNumber',
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'identificationList',
      type: 'string',
      label: intl.get(`${modelPrompt}.identificationList`).d('国标码'),
    },
    {
      name: 'blueGlueCodeList',
      type: 'string',
      label: intl.get(`${modelPrompt}.blueGlueCodeList`).d('蓝胶码'),
    },
    {
      name: 'positiveTopCodeList',
      type: 'string',
      label: intl.get(`${modelPrompt}.positiveTopCodeList`).d('正极顶盖码'),
    },
  ],
  fields: [
    {
      name: 'identification',
      type: 'string',
      label: intl.get(`${modelPrompt}.identification`).d('国标码'),
    },
    {
      name: 'blueGlueCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.blueGlueCode`).d('蓝胶码'),
    },
    {
      name: 'positiveTopCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.positiveTopCode`).d('正极顶盖码'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const { identificationList, blueGlueCodeList, positiveTopCodeList } = data;
      let identificationListWithQuotes = [];
      if(identificationList){
        identificationListWithQuotes = identificationList.split(',');
      }

      let blueGlueCodeListArray = [];
      if(blueGlueCodeList){
        blueGlueCodeListArray = blueGlueCodeList.split(',');
      }

      let positiveTopCodeListArray = [];
      if(positiveTopCodeList){
        positiveTopCodeListArray = positiveTopCodeList.split(',');
      }
      return {
        data: {
          identificationList: identificationListWithQuotes,
          blueGlueCodeList: blueGlueCodeListArray,
          positiveTopCodeList: positiveTopCodeListArray,
        },
        method: 'POST',
        url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-identification-blue-code-query/list/query`,
      };
    },
  },
});


export { initialDs };
