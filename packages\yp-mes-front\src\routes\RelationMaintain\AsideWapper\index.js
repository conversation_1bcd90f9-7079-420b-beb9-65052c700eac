/**
 * AsideWapper - 分栏抽屉容器
 * @date: 2021-1-27
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import React, { useRef, useState, useEffect } from 'react';
import { Tooltip, Icon } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { connect } from 'dva';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import intl from 'utils/intl';
import styles from './index.module.less';
import { BASIC } from '@utils/config';
import { AttributeDrawer } from '@components/tarzan-ui';
import { withRouter } from 'dva/router';
import Enterprise from '@/routes/org/EnterpriseComponents';
import Site from '@/routes/org/Site/components/Detail.js';
import Area from '@/routes/org/Area/components/Detail.js';
import ProLine from '@/routes/org/ProLine/components/Detail.js';
import WorkCell from '@/routes/org/WorkCell/components/Detail.js';
import Locator from '@/routes/org/Locator/components/Detail.js';

function AsideWapper(props) {
  const {
    addOriginDetailNode,
    focusType,
    dragGridAsideType,
    handleSuccess = () => { },
    dispatch,
    pageColumns,
    canEdit,
    setCanEdit,
    match: { path },
    custConfig,
  } = props;

  const { organizationId, detailType } = addOriginDetailNode;

  const [saveLoading, setSaveLoading] = useState(false);

  useEffect(() => {
    if (focusType === 'create') {
      setCanEdit(true);
    }
  }, [organizationId]);

  const otherProps = {
    canEdit,
    ref: useRef({}),
    kid: focusType === 'create' ? 'create' : organizationId,
    columns: pageColumns,
    componentType: 'ORG_RELATION',
  };

  const asideTypeMap = {
    ENTERPRISE: {
      childTitle: intl.get(`tarzan.model.org.enterprise.enterpriseMaintenance`).d('企业维护'),
      childRender: <Enterprise {...otherProps} />,
      CLASS_NAME: 'org.tarzan.model.domain.entity.MtModSite',
    },
    SITE: {
      childTitle: intl.get(`tarzan.model.org.site.siteMaintenance`).d('站点维护'),
      childRender: <Site {...otherProps} />,
      CLASS_NAME: 'org.tarzan.model.domain.entity.MtModSite',
    },
    AREA: {
      childTitle: intl.get(`tarzan.model.org.area.componentName`).d('区域维护'),
      childRender: <Area {...otherProps} detailType={detailType} />,
      CLASS_NAME: 'org.tarzan.model.domain.entity.MtModArea',
    },
    PROD_LINE: {
      childTitle: intl.get(`tarzan.model.org.prodLine.prodLineMaintenance`).d('生产线维护'),
      childRender: <ProLine {...otherProps} />,
      CLASS_NAME: 'org.tarzan.model.domain.entity.MtModProductionLine',
    },
    WORKCELL: {
      childTitle: intl.get(`tarzan.model.org.workcell.workcellMaintenance`).d('工作单元维护'),
      childRender: <WorkCell {...otherProps} />,
      CLASS_NAME: 'org.tarzan.model.domain.entity.MtModWorkcell',
    },
    LOCATOR: {
      childTitle: intl.get(`tarzan.model.org.locator.locationMaintenance`).d('库位维护'),
      childRender: <Locator {...otherProps} />,
      CLASS_NAME: 'org.tarzan.model.domain.entity.MtModLocator',
    },
  };

  const handleSave = async () => {
    setSaveLoading(true);
    const { success, newKid } = await otherProps.ref.current.submit();
    if (success) {
      handleSuccess(newKid);
      setCanEdit(false);
      setSaveLoading(false);
      if (focusType === 'create') {
        handelBack();
      }
    } else {
      setSaveLoading(false);
    }
  };

  const handleCancel = () => {
    if (organizationId === 'create') {
      handelBack();
    } else {
      try {
        otherProps.ref.current.reset();
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err);
      }
      setCanEdit(false);
    }
  };

  const handelEdit = () => {
    setCanEdit(true);
  };

  // 关闭分栏
  const handelBack = () => {
    setCanEdit(false);
    dispatch({
      type: 'relationMaintain/updateState',
      payload: {
        addOriginDetailNode: {},
        addOriginDetailIcon: {},
        dragGridAsideShow: false, // 关闭分栏
        focusType: undefined,
        dragGridAsideType: undefined,
      },
    });
  };

  return (
    <div className={styles['wapper-container']}>
      <div className={styles['wapper-header']}>
        <div className={styles['wapper-header-left']}>
          <Tooltip title={intl.get(`tarzan.common.button.back`).d('返回')} placement="bottom">
            <Icon type="arrow_back" className={styles['wapper-header-icon']} onClick={handelBack} />
          </Tooltip>
          <span>{asideTypeMap[dragGridAsideType]?.childTitle}</span>
        </div>
        <div className={styles['wapper-header-right']}>
          {dragGridAsideType !== 'ENTERPRISE' && (
            <AttributeDrawer
              // tablename={dragGridAsideType ? asideTypeMap[dragGridAsideType].TABLENAME : ''}
              className={dragGridAsideType ? asideTypeMap[dragGridAsideType].CLASS_NAME : ''}
              kid={organizationId}
              canEdit={canEdit}
              disabled={focusType === 'create'}
              serverCode={BASIC.TARZAN_MODEL}
              custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.BUTTON`}
              custConfig={custConfig}
            />
          )}
          {canEdit ? (
            <>
              <PermissionButton
                className={styles['wapper-header-btn']}
                icon="close"
                onClick={focusType === 'create' ? handelBack : handleCancel}
                key="cancel"
              >
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </PermissionButton>
              <PermissionButton
                className={styles['wapper-header-btn']}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
                type="primary"
                icon="save"
                onClick={handleSave}
                loading={saveLoading}
                key="submit"
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </PermissionButton>
            </>
          ) : (
            <PermissionButton
              className={styles['wapper-header-btn']}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              type="primary"
              icon="edit"
              onClick={handelEdit}
              key="edit"
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </div>
      </div>
      <div className={styles['wapper-aside']}>
        {dragGridAsideType && asideTypeMap[dragGridAsideType].childRender}
      </div>
    </div>
  );
}

export default formatterCollections({
  code: [
    'tarzan.common',
    'tarzan.model.org.enterprise',
    'tarzan.model.org.site',
    'tarzan.model.org.workcell',
    'tarzan.model.org.prodLine',
    'tarzan.model.org.area',
    'tarzan.model.org.locator',
  ],
})(
  connect(({ relationMaintain }) => {
    return relationMaintain;
  })(
    withRouter(
      withCustomize({
        unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.BUTTON`],
      })(AsideWapper)),
  ),
);
