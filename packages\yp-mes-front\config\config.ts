import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  routes: [
    {
      path: '/public/hmes/cost-center-approve/:code',
      component: '@/routes/OutFactoryApplyOa',
      authorized: true,
    },
    // 国标码与蓝胶码关系
    {
      path: '/hmes/blue-glue-code-national-standard-code',
      component: '@/routes/BlueGlueCodeNationalStandardCode',
    },
    // 设备查询
    {
      path: '/hmes/device-query',
      component: '@/routes/DeviceQuery',
    },
    // 进出站信息补录
    {
      path: '/hmes/additional-information-recording',
      routes: [
        {
          path: '/hmes/additional-information-recording/list',
          component: '../routes/AdditionalInformationRecording',
        },
      ],
    },
    // 原材料条码追溯报表
    {
      path: '/hmes/raw-material-barcode-traceability-report',
      component: '@/routes/RawMaterialBarcodeTraceabilityReport',
    },
    // 盘点工作台
    {
      path: '/hmes/inventory/inventory-workbench',
      priority: 10,
      routes: [
        {
          path: '/hmes/inventory/inventory-workbench/list',
          component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchList',
          priority: 10,
        },
        {
          path: '/hmes/inventory/inventory-workbench/detail/:id',
          component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchDetail',
          priority: 10,
        },
      ],
    },
    // 产品报废记录查询报表
    {
      path: '/hmes/product-scrap-record-query',
      component: '@/routes/ProductScrapRecordQuery',
    },
    // 在制品报表
    {
      path: '/hmes/workshop/process-report',
      priority: 10,
      component: '@/routes/workshop/ProcessReport',
    },

    // 盘点工作台-OA
    {
      path: '/public/hmes/inventory/inventory-workbench/:code',
      component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchListOA',
      authorized: true,
    },
    // 产品加工参数查询
    {
      path: '/hmes/product-processing-parameter-query',
      component: '@/routes/ProductProcessingParameterQuery',
    },
    // 设备加工参数查询报表
    {
      path: '/hmes/product-processing-parameter-report-query',
      component: '@/routes/ProductProcessingParameterReportQuery',
    },
    // 设备与工作单元维护
    {
      path: '/hmes/equipment/equipment-workcell',
      priority: 10,
      routes: [
        {
          path: '/hmes/equipment/equipment-workcell/list',
          component: '@/routes/equipment/WorkCell',
          priority: 10,
        },
      ],
    },
    // 电芯分选等级配置
    {
      path: '/hmes/cell-sorting-level-configuration',
      routes: [
        {
          path: '/hmes/cell-sorting-level-configuration/list',
          component: '../routes/CellSortingLevelConfiguration',
        },
        {
          path: '/hmes/cell-sorting-level-configuration/detail/:id',
          component: '../routes/CellSortingLevelConfiguration/Detail',
        },
      ],
    },
    // 产品批量工序撤销
    {
      path: '/hmes/product-batch-process-cancellation',
      component: '../routes/ProductBatchProcessCancellation',
    },
    // 生产日报
    {
      path: '/hmes/daily-production-report',
      component: '@/routes/DailyProductionReport',
    },
    // 条码与工位绑定报表
    {
      path: '/hmes/barcode-workstation-binding-report',
      routes: [
        {
          path: '/hmes/barcode-workstation-binding-report/list',
          component: '../routes/BarcodeWorkstationBinding/list/index',
        },
        {
          path: '/hmes/barcode-workstation-binding-report/history',
          component: '../routes/BarcodeWorkstationBinding/list/history',
        },
      ],
    },
    // 安灯故障字典
    {
      path: '/aori/andon-rc-assesment',
      priority: 10,
      routes: [
        // 安灯故障字典列表
        {
          path: '/aori/andon-rc-assesment/list',
          component: '@/routes/halm/AndonRcAssesment',
          priority: 10,
        },
      ],
    },
    // 进出站信息补录平台
    {
      path: '/hmes/information-supplementary-recording',
      component: '../routes/InformationSupplementaryRecording',
    },
    // 在制品导入
    {
      path: '/hmes/wip-import',
      title: '在制品导入',
      component: '../routes/WipImport',
    },
    // 投料罐容量调整
    {
      path: '/hmes/feed-tank-capacity-adjustment',
      routes: [
        {
          path: '/hmes/feed-tank-capacity-adjustment/list',
          component: '../routes/FeedTankCapacityAdjustment',
        },
      ],
    },
    // 领退料工作台-新
    {
      path: '/public/hmes/receive/receive-return-new-oa',
      // authorized: true,
      routes: [
        {
          title: '审批流程URL创建',
          path: `/public/hmes/receive/receive-return-new-oa/list/`,
          component: '@/routes/receive/ReceiveReturn',
          // authorized: true,
        },
        {
          title: '审批流程URL创建',
          path: `/public/hmes/receive/receive-return-new-oa/list/:code`,
          component: '@/routes/receive/ReceiveReturn',
          // authorized: true,
        },
      ],
    },
    {
      path: '/hmes/confirmation-team-working-hours',
      routes: [
        {
          path: '/hmes/confirmation-team-working-hours/list',
          component: '@/routes/ConfirmationTeamWorkingHours',
        },
        {
          path: '/hmes/confirmation-team-working-hours/detail/:id',
          component: '@/routes/ConfirmationTeamWorkingHours/Detail',
        },
      ],
    },
    {
      path: '/hmes/scrap-barcode-generation',
      component: '@/routes/ScrapBarcodeGeneration',
      // authorized: true,
    },
    // 产品结果参数补录
    {
      path: '/hmes/product-processing-parameters-supplement',
      // authorized: true,
      routes: [
        {
          title: '产品结果参数补录',
          path: '/hmes/product-processing-parameters-supplement/list',
          component: '../routes/ProductProcessingParametersSupplement',
        },
      ],
    },
    // 变化点管理
    {
      path: '/hmes/change-point-management',
      component: '@/routes/ChangePointManagement',
    },
    // 产品返修记录查询
    {
      path: '/hmes/query-product-rework-records',
      component: '@/routes/QueryProductReworkRecords',
    },
    // 产品质检记录
    {
      path: '/hmes/product-quality-inspection-record',
      component: '@/routes/ProductQualityInspectionRecord',
    },
    {
      path: '/hmes/product-quality-inspection-record',
      routes: [
        {
          path: '/hmes/product-quality-inspection-record/list',
          component: '@/routes/ProductQualityInspectionRecord',
        },
        {
          path: '/hmes/product-quality-inspection-record/detail/:id',
          component: '@/routes/ProductQualityInspectionRecord/disposeDetail',
        },
      ],
    },
    // 报检请求管理平台
    {
      path: '/hmes/inspection/inspection-management',
      priority: 10,
      routes: [
        {
          path: '/hmes/inspection/inspection-management/list',
          component: '@/routes/inspection/InspectionManagement',
          priority: 10,
        },
      ],
    },
    // 产品降级记录查询
    {
      path: '/hmes/query-product-degradation-records',
      component: '@/routes/QueryProductDegradationRecords',
    },
    // 不良记录查询
    {
      path: '/hmes/nc-query-record',
      component: '@/routes/NcRecordQuery',
    },
    // 报工事物报表平台
    {
      priority: 10000,
      path: '/hmes/work-transaction-report/platform',
      component: '@/routes/transactionReport/WorkTransactionReportPlatform',
    },
    // 设备安灯看板
    {
      path: '/hmes/andeng-monitoring-signboard',
      component: '@/routes/AndengMonitoringSignboard',
    },
    // 报工事物明细报表
    {
      path: '/hmes/work-transaction-report/transaction-detail-report',
      component: '@/routes/transactionReport/WorkTransactionDetailReport',
    },
    // 报工移动事件明细报表
    {
      path: '/hmes/work-transaction-report/mobile-event-detail-report',
      component: '@/routes/transactionReport/WorkMobileEventDetailReport',
    },
    // 标记维护
    {
      path: '/hmes/mark-maintenance',
      routes: [
        {
          path: '/hmes/mark-maintenance/list',
          component: '../routes/MarkMaintenance/MarkMaintenanceList',
        },
        {
          path: '/hmes/mark-maintenance/detail/:id',
          component: '../routes/MarkMaintenance/MarkMaintenanceDetail',
        },
      ],
    },
    // 标记维护-OA
    {
      path: '/public/hmes/mark-maintenance/:code',
      component: '../routes/MarkMaintenance/MarkMaintenanceOA',
      authorized: true,
    },
    // 一件追溯报表
    {
      path: '/hmes/one-trace-report',
      component: '@/routes/OneTraceReport',
    },
    {
      path: '/pub/hmes/one-trace-report/:code',
      component: '@/routes/OneTraceReport',
    },
    // 条码标记绑定
    {
      path: '/hmes/barcode-marking-binding',
      component: '@/routes/BarcodeMarkingBinding',
    },
    {
      authorized: true,
      path: '/pub/hmes/barcodeMarkingPub/:id',
      component: '@/routes/BarcodeMarkingPub',
    },
    // 设备状态监控报表
    {
      path: '/hmes/equipment-condition-monitoring-report',
      component: '@/routes/EquipmentConditionMonitoringReport',
    },
    // 班组设备关联关系
    {
      path: '/hmes/team-equipment-association-relationship',
      component: '@/routes/TeamEquipmentAssociationRelationship',
    },
    // 事物报表平台
    {
      path: '/hmes/transaction-report/platform',
      component: '@/routes/transactionReport/TransactionReportPlatform',
      priority: 10,
    },
    // 事务尾数报表平台
    {
      path: '/hmes/material-movement-tail-report/platform',
      component: '@/routes/MaterialMovementTailReport/TransactionReportPlatform',
    },
    // 产品加工履历查询报表
    {
      path: '/hmes/product-processing-history-query-report',
      component: '@/routes/ProductProcessingHistoryQueryReport',
    },
    // MARKING报表
    {
      path: '/hmes/marking',
      component: '@/pages/Marking/list/listPage',
    },
    // 批量追溯条码报表
    {
      path: '/hmes/batch-barcode-traceability-report',
      component: '@/routes/BatchBarcodeTraceabilityReport',
    },
    // 异常品处置
    {
      path: '/hmes/disposal-abnormal-products',
      component: '@/routes/DisposalAbnormalProducts',
    },
    // 装配记录查询
    {
      path: '/hmes/assembly-record-query',
      component: '@/routes/AssemblyRecordQuery',
    },
    // 物料库位关系维护
    {
      path: '/hmes/strategy/locator-relation',
      routes: [
        {
          path: '/hmes/strategy/locator-relation/list',
          component: '@/routes/strategy/LocatorRelation/LocatorRelationList',
        },
        {
          path: '/hmes/strategy/locator-relation/batch-import',
          component: '@/routes/strategy/LocatorRelation/BatchImport',
        },
      ],
    },
    // 库存日记账查询
    {
      path: '/hmes/inventory/journal/query',
      priority: 10,
      component: '@/routes/inventory/JournalQuery/JournalQueryList',
    },
    // 条码信息查询报表
    {
      path: '/hmes/barcode-info-query-report',
      priority: 1000,
      routes: [
        {
          priority: 1000,
          path: '/hmes/barcode-info-query-report/list',
          component: '@/routes/BarcodeInfoQueryReport/List',
        },
        {
          priority: 1000,
          path: '/hmes/barcode-info-query-report/advanced-query/list',
          component: '@/routes/BarcodeInfoQueryReport/List/AdvancedQueryList',
        },
      ],
    },
    // 特殊参数查询报表
    {
      path: '/hmes/special-parameter-query',
      component: '@/routes/SpecialParameterQuery',
    },
    // 特殊参数同步
    {
      path: '/hmes/special-parameter-sync',
      component: '@/routes/SpecialParameterSync/List',
    },
    // 库存查询
    {
      path: '/hmes/inventory/query/:timer?',
      component: '@/routes/Query/QueryList',
      priority: 10,
    },
    // 复采次数配置
    {
      path: '/hmes/wkc-repro-times',
      routes: [
        {
          path: '/hmes/wkc-repro-times/list',
          component: '@/routes/WkcReproTimes/WkcReproTimesList',
        },
      ],
    },
    // 发货报告打印平台
    {
      path: '/hmes/delivery-report-print',
      routes: [
        {
          path: '/hmes/delivery-report-print/list',
          component: '@/routes/DeliveryReportPrint/DeliveryReportPrintList',
        },
      ],
    },
    // 生产版本维护
    {
      path: '/hmes/product/production-version',
      priority: 10,
      component: '@/routes/product/ProductionVersion/index',
    },
    {
      title: '安灯状态监控报表',
      path: '/hmes/andengMonitor',
      component: '@/pages/AndengMonitor/list/listPage',
    },
    {
      title: '料盒物料查询',
      path: '/hmes/cassetteMaterial',
      component: '@/pages/CassetteMaterial/list/listPage',
    },
    // 迁移产品
    {
      title: '入库单查询',
      priority: 1000,
      path: '/hmes/inbound/inbound-order-query',
      routes: [
        {
          path: '/hmes/inbound/inbound-order-query/list',
          priority: 1000,
          component: '@/routes/InboundOrderQuery/InboundOrderQueryList',
        },
      ],
    },
    {
      title: '员工登录设备维护',
      path: '/hmes/equipmentMaintenance',
      component: '@/pages/EquipmentMaintenance/list/listPage',
    },
    {
      title: 'SPS接口调用记录',
      path: '/hmes/spsInterfaceRecord',
      component: '@/pages/SpsInterfaceRecord/list/listPage',
    },
    {
      title: '堆叠顺序维护',
      path: '/hmes/stackOrder',
      component: '@/pages/StackOrder/list/listPage',
    },
    {
      title: '班组区域维护-广播系统',
      path: '/hmes/teamMaintenanceRadio',
      component: '@/pages/TeamMaintenanceRadio/list/listPage',
    },
    {
      title: '特殊收集项校验(K值)',
      path: '/hmes/specialCollection',
      routes: [
        {
          path: '/hmes/specialCollection/list',
          component: '@/pages/SpecialCollection/list/listPage',
        },
        {
          path: '/hmes/specialCollection/detail/:id',
          component: '@/pages/SpecialCollection/list/detailPage',
        },
      ],
    },
    {
      title: '首件标记',
      path: '/hmes/firstArticleMarking',
      component: '@/pages/FirstArticleMarking/list/listPage',
    },
    {
      priority: 1000,
      path: '/hmes/organization-modeling/relation-maintenance',
      component: '@/routes/RelationMaintain',
      model: '@/models/relationMaintain',
    },
    {
      priority: 1000,
      path: '/hmes/organization-modeling/pro-line',
      routes: [
        {
          path: '/hmes/organization-modeling/pro-line/list',
          component: '@/routes/org/ProLine/ProLineList',
        },
        {
          path: '/hmes/organization-modeling/pro-line/detail/:proLineId',
          component: '@/routes/org/ProLine/ProLineDetail',
        },
      ],
    },
    {
      title: '发货报告角色客户维护',
      path: '/hmes/DeliveryReportRole',
      component: '@/pages/DeliveryReportRole/list/listPage',
    },
    {
      title: '工艺与工作单元维护-新',
      path: '/hmes/processWorkUnit',
      component: '@/pages/ProcessWorkUnit/list/listPage',
      authorized: true,
    },
    {
      title: '追溯平台对接管理',
      path: '/hmes/traceabilityPlatform',
      component: '@/pages/TraceabilityPlatform/list/listPage',
    },
    {
      title: '模组型号位置对应关系维护',
      path: '/hmes/moduleRelationship',
      component: '@/pages/ModuleRelationship/list/listPage',
    },
    {
      title: '称重报废平台',
      path: '/hmes/weighingScrap',
      routes: [
        {
          path: '/hmes/weighingScrap/list',
          component: '@/pages/WeighingScrap/list/listPage',
        },
        {
          path: '/hmes/weighingScrap/detail/:id',
          component: '@/pages/WeighingScrap/list/detailPage',
        },
      ]
    },
    {
      title: '库存调拨平台',
      path: '/hmes/inventoryTransfer',
      routes: [
        {
          path: '/hmes/inventoryTransfer/list',
          component: '@/pages/InventoryTransfer/list/listPage',
        },
        {
          path: '/hmes/inventoryTransfer/detail/:id',
          component: '@/pages/InventoryTransfer/list/detailPage',
        },
      ]
    },
    {
      path: '/hmes/dataCleaningPlatform',
      component: '@/pages/DataCleaningPlatform/list/listPage',
    },
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
          '@components': 'hcm-components-front/lib/components',
          '@services': 'hcm-components-front/lib/services',
          '@utils': 'hcm-components-front/lib/utils',
          '@assets': 'hcm-components-front/lib/assets',
        },
      },
    ],
  ],
});
