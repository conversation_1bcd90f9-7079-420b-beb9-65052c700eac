/**
 * @Description: 物料库位关系维护-抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-12-14 11:15:48
 * @LastEditTime: 2022-11-16 14:06:34
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { Form, DataSet, Lov, Select, Switch, TextField } from 'choerodon-ui/pro';

export interface DrawerProps {
  drawerDs: DataSet;
}

export const Drawer = props => {
  const { drawerDs } = props;
  const siteLovChange = () => {
    // eslint-disable-next-line no-unused-expressions
    drawerDs.current?.init('materialObject', undefined);
    // eslint-disable-next-line no-unused-expressions
    drawerDs.current?.init('locatorObject', undefined);
  };

  const ownerTypeChange = () => {
    // eslint-disable-next-line no-unused-expressions
    drawerDs.current?.init('ownerObject', undefined);
  };

  return (
    <Form dataSet={drawerDs} columns={1}>
      <Lov name="siteObject" onChange={siteLovChange} />
      <Lov name="materialObject" />
      <Select name="ownerType" onChange={ownerTypeChange} />
      <Lov name="ownerObject" />
      <TextField name="ownerName" />
      <Lov name="locatorObject" />
      <Switch name="enableFlag" />
    </Form>
  );
};
