/**
 * @Description: 用户权限编辑抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 17:31:30
 * @LastEditTime: 2022-10-17 14:06:12
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useEffect, useCallback, useRef } from 'react';
import intl from 'utils/intl';
import { DataSet, Row, Col, Form, Select, Lov, Button, SelectBox, TextField, Spin } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ViewMode } from 'choerodon-ui/pro/lib/radio/enum';
import { useDataSetEvent } from 'utils/hooks';
import { TarzanDrawer } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import OrganizationTree from './OrganizationTree';
import OrganizationTable from './OrganizationTable';
import { searchDS, orgTableDS } from './stores';
import { GetOrgTree, GetLocatorTree, GetUserOrgList } from './services';
import { DistributeButton, RevokeButton } from './components/Buttons'
import styles from './index.module.less';

const modelPrompt = 'tarzan.model.hmes.userRights';

interface UserRightsDrawerProps {
  visible: boolean,
  onClose: () => void,
  userInfo: { userId: number, userName: string, userDesc: string } | null,
}

const UserRightsDrawer = (props: UserRightsDrawerProps) => {
  const { visible, onClose, userInfo } = props;

  // 包裹层为获取tree外层高度
  const treeTableWrapRef = useRef<HTMLDivElement>(null);
  const treeMaxHeight = useRef<number>(600);

  const searchDs = useMemo(() => new DataSet(searchDS()), []);
  const getOrgTree = useRequest(GetOrgTree(), { manual: true });
  const getLocatorTree = useRequest(GetLocatorTree(), { manual: true });
  const getUserOrgList = useRequest(GetUserOrgList(), { manual: true });
  const siteListDs = useMemo(() => new DataSet(orgTableDS()), []);
  const areaListDs = useMemo(() => new DataSet(orgTableDS()), []);
  const prolineListDs = useMemo(() => new DataSet(orgTableDS()), []);
  const workcellListDs = useMemo(() => new DataSet(orgTableDS()), []);
  const areaLocatorListDs = useMemo(() => new DataSet(orgTableDS()), []);
  const inventoryLocatorListDs = useMemo(() => new DataSet(orgTableDS()), []);
  const locationLocatorListDs = useMemo(() => new DataSet(orgTableDS()), []);

  useEffect(() => {
    if (treeTableWrapRef.current && treeMaxHeight.current === 600) {
      treeMaxHeight.current = treeTableWrapRef.current.clientHeight - 128;
    }
  }, [treeTableWrapRef?.current?.clientHeight])

  useEffect(() => {
    if (visible && userInfo) {
      searchDs!.current!.init('userId', userInfo.userId);
      searchDs!.current!.init('userName', userInfo.userName);
      searchDs!.current!.init('userDesc', userInfo.userDesc);
      searchDs.getField('userLov')!.set('disabled', true);
      getTreeData();
      getTablesData();
    }
    return () => {
      searchDs.getField('userLov')!.set('disabled', false);
      getOrgTree.mutate([]);
      getLocatorTree.mutate([]);
      getUserOrgList.mutate([]);
      siteListDs.loadData([]);
      areaListDs.loadData([]);
      prolineListDs.loadData([]);
      workcellListDs.loadData([]);
      areaLocatorListDs.loadData([]);
      inventoryLocatorListDs.loadData([]);
      locationLocatorListDs.loadData([]);
    }
  }, [visible]);

  // 切换查询树类型时，进行查询
  useDataSetEvent(searchDs, 'update', ({ name, value, record }) => {
    switch (name) {
      case 'userLov':
        if (value) {
          getTreeData();
          getTablesData();
        }
        break;
      case 'allotType':
        record.set('organizationType', null);
        record.set('organizationLov', {});
        getTreeData();
        break;
      case 'organizationType':
        record.set('organizationLov', {});
        getTreeData();
        break;
      case 'expandedKeys':
      case 'checkedKeys':
        break;
      default:
        getTreeData();
    }
  });

  /**
   * 根据当前类型，来查询树的数据
   * @param {boolean} [clearExpandFlag] 是否清空expandedKeys
   * @param {string} [allotType] 类型
   */
  const getTreeData = (clearExpandFlag: boolean = true, allotType?: string) => {
    const _allotType = allotType || searchDs!.current!.get('allotType')
    const params = getQueryParams();
    // 重新查询时，需要清空树相关数据
    if (clearExpandFlag) {
      searchDs!.current!.set('expandedKeys', []);
    }
    searchDs!.current!.set('checkedKeys', []);
    if (_allotType === 'Organization') {
      return getOrgTree.run({ params });
    }
    return getLocatorTree.run({ params });
  }

  // 获取用户已分配的权限数据
  const getTablesData = () => getUserOrgList.run({
    params: getQueryParams(),
    onSuccess: (res) => {
      formatTablesData(res);
    },
  });

  const formatTablesData = (list) => {
    const organizationTypeMap = ['SITE', 'AREA', 'PROD_LINE', 'WORKCELL', 'LOCATOR'];
    const locatorTypeMap = ['AREA', 'INVENTORY', 'LOCATION'];
    const listStore: any[] = [[], [], [], [], [], [], []];
    list.forEach(item => {
      const itemIndex = organizationTypeMap.indexOf(item.organizationType);
      if (itemIndex !== -1 && itemIndex < 4) {
        listStore[itemIndex].push(item);
      } else if (itemIndex === 4) {
        const itemLocatorIndex = locatorTypeMap.indexOf(item.locatorCategory);
        listStore[4 + (itemLocatorIndex === -1 ? 2 : itemLocatorIndex)].push(item);
      }
    });

    siteListDs.loadData(listStore[0]);
    areaListDs.loadData(listStore[1]);
    prolineListDs.loadData(listStore[2]);
    workcellListDs.loadData(listStore[3]);
    areaLocatorListDs.loadData(listStore[4]);
    inventoryLocatorListDs.loadData(listStore[5]);
    locationLocatorListDs.loadData(listStore[6]);
  };

  const getQueryParams = useCallback(
    () => {
      const current = searchDs.current;
      return {
        orgId: current!.get('organizationId'),
        organizationId: current!.get('organizationId'),
        organizationType: current!.get('organizationType'),
        userId: current!.get('userId'),
      }
    },
    [searchDs],
  );

  const handleSearch = (clearExpandFlag: boolean = true) => {
    searchDs.validate().then(res => {
      if (res) {
        getTreeData(clearExpandFlag);
        getTablesData();
      }
    })
  };

  return (
    <TarzanDrawer
      title={
        userInfo ? intl.get(`${modelPrompt}.edit`).d('编辑用户权限')
          : intl.get(`${modelPrompt}.create`).d('新建用户权限')
      }
      keyboardClosable
      width={1080}
      visible={visible}
      canEdit={false}
      onOk={onClose}
      onClose={onClose}
      dataSet={searchDs}
    >
      <div className={styles.modalWrap} ref={treeTableWrapRef}>
        <Row>
          <Col span={16} offset={3}>
            <Form dataSet={searchDs} columns={2} labelWidth={112}>
              <Lov name="userLov" />
              <TextField name="userDesc" disabled />
              <Select name="organizationType" />
              <Lov name="organizationLov" />
            </Form>
          </Col>
          <Col span={3}>
            <Form dataSet={searchDs} columns={1} labelWidth={0}>
              <Form.Item>
                <Button
                  onClick={() => handleSearch}
                  loading={
                    getOrgTree.loading ||
                    getLocatorTree.loading ||
                    getUserOrgList.loading
                  }
                  icon="search"
                  color={ButtonColor.primary}
                  style={{ width: 'auto' }}
                >
                  {intl.get(`tarzan.common.button.search`).d('查询')}
                </Button>
              </Form.Item>
            </Form>
          </Col>
          <Col span={16}>
            <Form dataSet={searchDs} columns={1} labelWidth={0}>
              <SelectBox name="allotType" mode={ViewMode.button} />
            </Form>
          </Col>
        </Row>
        <div className={styles.treeTableWrap}>
          <div className={styles.leftTree}>
            <Spin spinning={getOrgTree.loading || getLocatorTree.loading} style={{ height: treeMaxHeight.current }}>
              <OrganizationTree
                ds={searchDs}
                orgTreeConfig={getOrgTree.data || []}
                locatorTreeConfig={getLocatorTree.data || []}
                maxHeight={treeMaxHeight.current}
              />
            </Spin>
          </div>
          <div className={styles.centerButton}>
            <Row className={styles.centerButtonRow}>
              <DistributeButton
                ds={searchDs}
                className={styles.centerButtonItem}
                orgTreeConfig={getOrgTree.data || []}
                locatorTreeConfig={getLocatorTree.data || []}
                onSearch={handleSearch}
              />
            </Row>
            <Row className={styles.centerButtonRow}>
              <RevokeButton
                ds={searchDs}
                className={styles.centerButtonItem}
                siteListDs={siteListDs}
                areaListDs={areaListDs}
                prolineListDs={prolineListDs}
                workcellListDs={workcellListDs}
                areaLocatorListDs={areaLocatorListDs}
                inventoryLocatorListDs={inventoryLocatorListDs}
                locationLocatorListDs={locationLocatorListDs}
                onSearch={handleSearch}
              />
            </Row>
          </div>
          <div className={styles.rightTable}>
            <OrganizationTable
              ds={searchDs}
              siteListDs={siteListDs}
              areaListDs={areaListDs}
              prolineListDs={prolineListDs}
              workcellListDs={workcellListDs}
              areaLocatorListDs={areaLocatorListDs}
              inventoryLocatorListDs={inventoryLocatorListDs}
              locationLocatorListDs={locationLocatorListDs}
              maxHeight={treeMaxHeight.current}
            />
          </div>
        </div>
      </div>
    </TarzanDrawer>
  );
}

export default UserRightsDrawer;