/*
 * @Description: 复采次数配置-service
 * @Author: <<EMAIL>>
 * @Date: 2024-02-22 09:44:36
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-02-22 11:20:07
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 新建/更新/删除复采次数配置
 * @function SaveReproTimes
 * @returns {object} fetch Promise
 */
export function SaveReproTimes(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wkc-repro-times/save`,
    method: 'POST',
  };
}

/**
 * 查询工位关联的产线编码
 * @function QueryProdLineInfo
 * @returns {object} fetch Promise
 */
export function QueryProdLineInfo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wkc-repro-times/query/prodLine`,
    method: 'GET',
  };
}