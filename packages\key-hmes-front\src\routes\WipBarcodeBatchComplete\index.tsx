/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-06-23 10:07:07
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-06-26 14:35:27
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\wip-barcode-batch-complete\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import React, { FC } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { ExecuteOutbound } from './api';
import listPageFactory from './stores/listPageDs';

interface ListPageProps extends RouteComponentProps {
  listPageDs: DataSet;
}

const modelPrompt = 'hmes.wipBarcodeBatchComplete';

const ListPageComponent: FC<ListPageProps> = ({ listPageDs }) => {
  const executeOutbound = useRequest(ExecuteOutbound(), { manual: true });
  const columns: ColumnProps[] = [
    { name: 'identification' },
    { name: 'materialCode' },
    { name: 'materialName' },
    { name: 'qualityStatusDesc' },
    { name: 'workOrderNum' },
    { name: 'woTypeDesc' },
    { name: 'currentOperationDesc' },
    { name: 'endOperationDesc' },
    { name: 'ncCode' },
  ];

  const handleOutbound = async () => {
    if (!listPageDs.selected || listPageDs.selected.length === 0) {
      notification.warning({
        message: intl.get(`${modelPrompt}.error.noSelectedData`).d('未选中数据，请检查'),
      });
      return;
    }

    const qualifiedItems = listPageDs.selected.filter(item => item.get('qualityStatus') === 'OK');

    if (qualifiedItems.length > 0) {
      const qualifiedBarcodes = qualifiedItems.map(item => item.get('identification')).join(', ');
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.qualifiedItem`)
          .d(`选中数据包含质量状态为合格的条码: ${qualifiedBarcodes}，不允许出站`),
      });
      return;
    }

    await executeOutbound.run({
      params: listPageDs.selected.map(item => item.toData()),
      onSuccess: () => {
        notification.success({
          message: intl.get('hzero.common.notification.success').d('操作成功'),
        });
      },
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('在制条码批量完工')}>
        <Button color={ButtonColor.primary} onClick={handleOutbound}>
          {intl.get(`${modelPrompt}.outbound`).d('出站')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={listPageDs}
          columns={columns}
          searchCode="wipBarcodeBatchComplete"
          customizedCode="wipBarcodeBatchComplete"
          pagination={{
            pageSizeOptions: ['10', '50', '100', '500'],
          }}
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listPageDs = listPageFactory();
    return {
      listPageDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);

export default formatterCollections({
  code: [modelPrompt],
})(ListPage);
