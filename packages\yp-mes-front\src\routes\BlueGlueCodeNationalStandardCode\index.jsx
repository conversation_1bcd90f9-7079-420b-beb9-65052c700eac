/**
 * @feature PageHeaderWrapper
 */
import React, { useState } from 'react';
import ExcelExport from 'components/ExcelExport';
import { DataSet, Table, Button, Row, Col, TextField, Form, Icon } from 'choerodon-ui/pro';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { isNil } from 'lodash';
import { observer } from 'mobx-react';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import { initialDs } from './stories/InitialDs';
import LovModal from '../ProductBatchProcessCancellation/LovModal';
import InputLovDS from '../../stores/InputLovDS';

const modelPrompt = 'tarzan.BlueGlueCodeNationalStandardCode';

/**
 * 头行结构的表单示例
 */
const BlueGlueCodeNationalStandardCode = observer(props => {
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);


  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row
          gutter={24}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identificationList"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'identificationList', '国标码', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <TextField
                name="blueGlueCodeList"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'blueGlueCodeList', '蓝胶码', queryDataSet)
                      }
                    />
                  </div>
                }
              />
              <TextField
                name="positiveTopCodeList"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(true, 'positiveTopCodeList', '正极顶盖码', queryDataSet)
                      }
                    />
                  </div>
                }
              />
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };

  const handleSearch = async () => {
    const {
      identificationList,
      blueGlueCodeList,
      positiveTopCodeList,
    } = props.dataSet?.queryDataSet?.toJSONData()[0];
    if (!identificationList && !blueGlueCodeList && !positiveTopCodeList) {
      notification.error({
        message: intl.get(`${modelPrompt}.pleaseEnterQueryCriteria`).d('请至少输入一个查询条件！'),
      });
      return;
    }
    props.dataSet.query();
  };
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: props.dataSet,
    onOpenInputModal,
  };

  const getExportQueryParams = () => {
    const queryParams = props.dataSet.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])|| i.includes('Lov') || i === '__dirty') {
        delete queryParams[i];
      }
    });
    const {blueGlueCodeList,identificationList, positiveTopCodeList}=queryParams
    queryParams.specialIdentificationIdList = props.dataSet.selected.map(i => i.get('specialIdentificationId'));
    queryParams.blueGlueCodeList = blueGlueCodeList ? blueGlueCodeList.split(',') : null
    queryParams.identificationList =  identificationList ? identificationList.split(',') : null
    queryParams.positiveTopCodeList =  positiveTopCodeList ? positiveTopCodeList.split(',') : null
    return queryParams;
  };

  const panelTableColumns = [{
    name: 'identification',
  },{
    name: 'blueGlueCode',
  },{
    name: 'positiveTopCode',
  }]

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get(`${modelPrompt}.title`).d('国标码与蓝胶码关系')}
        header={
          <ExcelExport
            method="POST"
            allBody
            otherButtonProps={{ disabled: props.dataSet.records.length===0 }}
            requestUrl={`${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-identification-blue-code-query/list/export`}
            queryParams={getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        }
      >
        <Table
          searchCode="BatchBarcodeTraceabilityReport"
          customizedCode="BatchBarcodeTraceabilityReport"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={props.dataSet}
          columns={panelTableColumns}
        />
        <LovModal {...lovModalProps} />
      </PageHeaderWrapper>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.BlueGlueCodeNationalStandardCode', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...initialDs(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(BlueGlueCodeNationalStandardCode),
);
