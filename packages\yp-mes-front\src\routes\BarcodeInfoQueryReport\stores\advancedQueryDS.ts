/*
 * @Description: 条码信息查询报表-高级查询DS
 * @Author: <<EMAIL>>
 * @Date: 2024-01-09 17:57:41
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-01-23 14:12:10
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.barcodeInfoQueryReport';
const tenantId = getCurrentOrganizationId();
const endUrl = "";

const advancedQueryDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'identificationList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      required: true,
      transformRequest: value => (value ? value?.split(',') : undefined),
      dynamicProps: {
        required: ({ record }) => !record?.get('workOrderNum')?.length && !record?.get('materialIdList')?.length,
      },
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      transformRequest: value => (value ? value?.split(',') : undefined),
      dynamicProps: {
        required: ({ record }) => !record?.get('identificationList')?.length && !record?.get('materialIdList')?.length,
      },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL.PERMISSION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        required: ({ record }) => !record?.get('identificationList')?.length && !record?.get('workOrderNum')?.length,
      },
    },
    {
      name: 'materialIdList',
      bind: 'materialLov.materialId',
    },
    {
      name: 'containerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
      lovCode: 'YP_MES.CONTAINER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'containerId',
      bind: 'containerLov.containerId',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
      lovCode: 'MT.MODEL.PRODLINE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'lotNumList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotNumList`).d('批次号'),
      transformRequest: value => (value ? value?.split(',') : undefined),
    },
    {
      name: 'workOrderLevel',
      type: FieldType.string,
      lookupCode: 'HME.WO_LEVEL',
      lovPara: { tenantId },
      label: intl.get(`${modelPrompt}.workOrderLevel`).d('等级'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('生产状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=EO_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'equipmentIdList',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('当前工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      textField: 'operationName',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'operationNameList',
      bind: 'operationLov.operationName',
    },
    {
      name: 'eoStepStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentOperationStatus`).d('当前工艺状态'),
      lookupCode: 'HME.EO_STEP_STATUS',
    },
    {
      name: 'startDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('生产开始时间'),
      max: 'startDateEnd',
    },
    {
      name: 'startDateEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('生产结束时间'),
      min: 'startDateFrom',
    },
  ],
  fields: [
    // {
    //   name: 'eoId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.eoId`).d('EO_ID'),
    // },
    // {
    //   name: 'eoNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.eoNum`).d('EO编码'),
    // },
    // {
    //   name: 'eoIdentification',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.eoIdentification`).d('EO标识'),
    // },
    // {
    //   name: 'materialLotId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.materialLotId`).d('物料批ID'),
    // },
    // {
    //   name: 'materialLotCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    // },
    // {
    //   name: 'materialLotIdentification',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.materialLotIdentification`).d('物料批标识'),
    // },
    // {
    //   name: 'materialId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.materialId`).d('物料ID'),
    // },
    // {
    //   name: 'materialCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    // },
    // {
    //   name: 'materialName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    // },
    // {
    //   name: 'eoQty',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.eoQty`).d('数量'),
    // },
    // {
    //   name: 'qualityStatus',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    //   textField: 'description',
    //   valueField: 'statusCode',
    //   lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
    //   lookupAxiosConfig: {
    //     transformResponse(data) {
    //       if (data instanceof Array) {
    //         return data;
    //       }
    //       const { rows } = JSON.parse(data);
    //       return rows;
    //     },
    //   },
    // },
    // {
    //   name: 'status',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.status`).d('生产状态'),
    //   textField: 'description',
    //   valueField: 'statusCode',
    //   lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=EO_STATUS`,
    //   lookupAxiosConfig: {
    //     transformResponse(data) {
    //       if (data instanceof Array) {
    //         return data;
    //       }
    //       const { rows } = JSON.parse(data);
    //       return rows;
    //     },
    //   },
    // },
    // {
    //   name: 'workcellId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.workcellId`).d('工位ID'),
    // },
    // {
    //   name: 'workcellCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.workcellCode`).d('当前工位'),
    // },
    // {
    //   name: 'workcellName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.workcellName`).d('当前工位名称'),
    // },
    // {
    //   name: 'equipmentId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.equipmentId`).d('设备ID'),
    // },
    // {
    //   name: 'equipmentCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    // },
    // {
    //   name: 'routerStepId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.routerStepId`).d('当前工艺ID'),
    // },
    // {
    //   name: 'routerStepDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.routerStepDesc`).d('当前工艺'),
    // },
    // {
    //   name: 'routerStepName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.routerStepName`).d('当前工艺名称'),
    // },
    // {
    //   name: 'routerStepStatus',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.routerStepStatus`).d('当前工艺状态'),
    //   textField: 'description',
    //   valueField: 'statusCode',
    //   lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=EO_STEP_STATUS`,
    //   lookupAxiosConfig: {
    //     transformResponse(data) {
    //       if (data instanceof Array) {
    //         return data;
    //       }
    //       const { rows } = JSON.parse(data);
    //       return rows;
    //     },
    //   },
    // },
    // {
    //   name: 'workOrderId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.workOrderId`).d('生产指令ID'),
    // },
    // {
    //   name: 'workOrderNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
    // },
    // {
    //   name: 'prodLineId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.prodLineId`).d('产线ID'),
    // },
    // {
    //   name: 'prodLineCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
    // },
    // {
    //   name: 'marking',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.marking`).d('MARKING'),
    // },
    // {
    //   name: 'disposalFunctionDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.disposalFunctionDesc`).d('不良处置方式'),
    // },
    // {
    //   name: 'ncIncidentId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.ncIncidentId`).d('NC_INCIDENT_ID'),
    // },
    // {
    //   name: 'ncIncidentNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncIncidentNum`).d('NC_INCIDENT_NUM'),
    // },
    // {
    //   name: 'ncRecordId',
    //   type: FieldType.number,
    //   label: intl.get(`${modelPrompt}.ncRecordId`).d('NC_RECORD_ID'),
    // },
    // {
    //   name: 'ncRecordNum',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncRecordNum`).d('NC_RECORD_NUM'),
    // },
    // {
    //   name: 'reworkFlag',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.reworkFlag`).d('返修标识'),
    //   lookupCode: 'MT.FLAG',
    //   lovPara: { tenantId },
    // },
    // {
    //   name: 'operationName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.operationName`).d('返修开始工艺'),
    // },
    // {
    //   name: 'endOperationName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.endOperationName`).d('返修结束工艺'),
    // },
    // {
    //   name: 'interceptionFlag',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.concessiveInterceptionFlag`).d('让步拦截标识'),
    //   lookupCode: 'MT.FLAG',
    //   lovPara: { tenantId },
    // },
    // {
    //   name: 'interceptionOperationName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.concesInterOperationName`).d('让步拦截工艺'),
    // },
    // {
    //   name: 'degradeFlag',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.degradeFlag`).d('降级标识'),
    //   lookupCode: 'MT.FLAG',
    //   lovPara: { tenantId },
    // },
    // {
    //   name: 'actualStartTime',
    //   type: FieldType.dateTime,
    //   label: intl.get(`${modelPrompt}.actualStartTime`).d('生产开始时间'),
    // },
    // {
    //   name: 'actualEndTime',
    //   type: FieldType.dateTime,
    //   label: intl.get(`${modelPrompt}.actualEndTime`).d('生产结束时间'),
    // },
    // {
    //   name: 'mtModLocatorCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.mtModLocatorCode`).d('库位编码'),
    // },
    // {
    //   name: 'mtModLocatorName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.mtModLocatorName`).d('库位名称'),
    // },
    // {
    //   name: 'lot',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.lot`).d('批次'),
    // },
    // {
    //   name: 'operation',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.operation`).d('操作'),
    // },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}${endUrl}/v1/${tenantId}/hme-identification/detailed/query`,
        method: 'POST',
        transformResponse: (val) => {
          const { success, rows, message } = JSON.parse(val);
          if (message) {
            notification.error({ message });
          }
          return {
            success,
            message,
            rows: rows || { content: [] },
          };
        },
      };
    },
  },
});

export { advancedQueryDS };
