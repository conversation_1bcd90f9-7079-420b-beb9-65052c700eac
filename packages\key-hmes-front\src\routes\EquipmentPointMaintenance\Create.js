import React, { Component } from 'react';
import {
  DataSet,
  Table,
  Spin,
  Button,
  Form,
  TextField,
  Modal,
  Lov,
  Switch,
  NumberField,
  Select,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Collapse, Popconfirm, Badge } from 'choerodon-ui';
import notification from 'utils/notification';
import myInstance from '@/utils/myAxios';
import { observer } from 'mobx-react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import { isUndefined } from 'lodash';
import { Header, Content } from 'components/Page';
import { Bind } from 'lodash-decorators';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { detailTableDs, formDs, detailFormDs } from './stores/EquipmentPointMaintenanceDs';
import { Host } from '@/utils/config';

const tenantId = getCurrentOrganizationId();
// const Host = `/key-ne-focus-mes-34861`;
const { Panel } = Collapse;

const modelPrompt = 'tarzan.hmes.EquipmentPointMaintenance';
@connect()
@observer
export default class Create extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      title: '',
      disabledFlag: true,
      flag: '',
      originTableList: [],
      deleteFlag: false,
      formRecord: {},
    };
    this.detailTableDs = new DataSet({ ...detailTableDs() });
    this.formDs = new DataSet({ ...formDs() });
    // this.detailFormDs = new DataSet({ ...detailFormDs() });
    this.detailFormDs = new DataSet({
      ...detailFormDs(this.formDs),
    });
  }

  componentDidMount() {
    const { payload } = this.props.location;
    if (!isUndefined(payload)) {
      if (payload.flag === 'NEW') {
        this.queryBasicData();
        this.setState({ title: intl.get(`${modelPrompt}.title`).d('新建物料'), disabledFlag: true, flag: 'NEW' });
      } else {
        this.setState({ title: intl.get(`${modelPrompt}.title`).d('编辑物料'), disabledFlag: false, flag: 'EDIT' });
        this.handleSearchForm(payload);
        this.handleSearchTable(payload.assemblePointId);
      }
    }
  }

  @Bind()
  queryBasicData = async () => {
    const url = `${Host}/v1/${tenantId}/hme-assemble-points/get/user/def/site`;
    const res = await myInstance.get(url);
    if (res) {
      this.formDs.loadData([
        {
          siteCode: res.data.siteCode,
          siteId: res.data.siteId,
          enableFlag: 'Y',
        },
      ]);
    }
  };

  @Bind()
  handleSearchForm(record) {
    this.formDs.loadData([record]);
    this.setState({ formRecord: record });
  }

  @Bind()
  handleSearchTable(assemblePointId) {
    this.detailTableDs.setQueryParameter('assemblePointId', assemblePointId);
    this.detailTableDs.query().then(res => {
      const resList = res.content.map((item, index) => {
        return {
          ...item,
          index,
        };
      });
      this.setState({ originTableList: resList });
    });
  }

  @Bind()
  openModal(record, isNew) {
    const modalKey = Modal.key();
    const { title } = this.state;
    if (!isNew) {
      this.detailFormDs.loadData([
        {
          ...record.data,
        },
      ]);
    }
    Modal.open({
      key: modalKey,
      drawer: true,
      width: 600,
      title,
      closable: true,
      children: (
        <Form dataSet={this.detailFormDs} columns={1}>
          <Lov name="materialObj" />
          <TextField name="materialName" disabled />
          <TextField name="uomName" disabled />
          <Select name="revisionCode" />
          <NumberField name="maxQty" />
          <Switch name="enableFlag" />
        </Form>
      ),
      onOk: () => this.createLine(isNew),
      onCancel: () => this.cancelLine(isNew),
      afterClose: () => isNew && this.detailFormDs.loadData([]),
    });
  }

  @Bind()
  cancelLine() {
    this.detailFormDs.loadData([]);
  }

  @Bind()
  createModal() {
    this.openModal(this.detailFormDs.create({ enableFlag: 'Y' }), true);
  }

  @Bind()
  createLine(isNew) {
    const { formRecord } = this.state;
    const formList = this.detailFormDs.toData();
    const list = formList.map((item, index) => {
      return {
        ...item,
        assemblePointId: formRecord.assemblePointId,
        index,
      };
    });
    if (list.length === 0 || !list[0].materialId) {
      notification.error({ message: intl
        .get(`${modelPrompt}.error.material.choose`)
        .d(`请选择物料编码！`) });
      return false;
    }
    if (isNew) {
      this.detailTableDs.create(...list);
    } else {
      this.detailTableDs.current.set(formList[0]);
    }
    this.detailFormDs.loadData([]);
  }

  @Bind()
  editModal(record) {
    this.openModal(record);
  }

  // @Bind()
  // handelSave = async () => {
  //   const validate = await this.formDs.validate(false, true);
  //   if (!validate) {
  //     return;
  //   }
  //   let params = {};
  //   const formObj = this.formDs.toData();
  //   if (formObj[0].siteId && formObj[0].assemblePointCode && formObj[0].enableFlag) {
  //     const tableListTemp = this.detailTableDs.toJSONData();
  //     const tableList = tableListTemp.map(item => {
  //       return {
  //         ...item,
  //         deleteFlag: item._status === 'delete' ? 'Y' : null,
  //       };
  //     });
  //     params = {
  //       ...formObj[0],
  //       pointMaterialVOList: tableList,
  //     };
  //     request(`${Host}/v1/${tenantId}/hme-assemble-points/save/data`, {
  //       method: 'post',
  //       body: { ...params },
  //     }).then(res => {
  //       if (res && !res.failed) {
  //         notification.success();
  //         this.formDs.loadData([res]);
  //         this.handleSearchTable(res.assemblePointId);
  //         this.setState({ disabledFlag: false, flag: 'EDIT' });
  //       } else {
  //         notification.error({ message: res.message });
  //       }
  //     });
  //   } else {
  //     notification.error({ message: '请输入必输条件!' });
  //   }
  // };

  @Bind()
  handelSave = async () => {
    const validate = await this.formDs.validate(false, true);
    if (!validate) {
      return;
    }
    const { originTableList, deleteFlag } = this.state;
    let params = {};
    const formObj = this.formDs.toData();
    if (formObj[0].siteId && formObj[0].assemblePointCode && formObj[0].enableFlag) {
      const tableList = this.detailTableDs.toData();
      if (tableList.length) {
        if (originTableList.length && deleteFlag) {
          const tempList = originTableList.map(item => {
            return {
              ...item,
              deleteFlag: 'Y',
            };
          });
          for (let i = 0; i < tableList.length; i++) {
            for (let j = 0; j < tempList.length; j++) {
              if (tableList[i].assemblePointMaterialId === tempList[j].assemblePointMaterialId) {
                tempList.splice(j, 1);
              }
            }
          }
          const finalList = tableList.concat(tempList);
          params = {
            ...formObj[0],
            pointMaterialVOList: finalList,
          };
        } else {
          params = {
            ...formObj[0],
            pointMaterialVOList: tableList,
          };
        }
      } else if (originTableList.length && deleteFlag) {
        const tempList = originTableList.map(item => {
          return {
            ...item,
            deleteFlag: 'Y',
          };
        });
        params = {
          ...formObj[0],
          pointMaterialVOList: tempList,
        };
      } else {
        params = {
          ...formObj[0],
          pointMaterialVOList: [],
        };
      }
      request(`${Host}/v1/${tenantId}/hme-assemble-points/save/data`, {
        method: 'post',
        body: { ...params },
      }).then(res => {
        if (res && !res.failed) {
          notification.success();
          this.formDs.loadData([res]);
          this.handleSearchTable(res.assemblePointId);
          this.setState({ disabledFlag: false, flag: 'EDIT' });
        } else {
          notification.error({ message: intl
            .get(`${modelPrompt}.error.message`)
            .d(res.message) });
        }
      });
    } else {
      notification.error({ message: intl
        .get(`${modelPrompt}.error.required`)
        .d(`请输入必输条件`) });
    }
  };

  @Bind()
  handelEdit() {
    this.setState({ disabledFlag: true });
  }

  @Bind()
  handelCancel() {
    const { dispatch } = this.props;
    const { flag } = this.state;
    if (flag === 'NEW') {
      dispatch(
        routerRedux.push({
          pathname: `/hmes/equipment-point-maintenance/list`,
        }),
      );
    } else {
      this.setState({ disabledFlag: false });
    }
  }

  render() {
    const { loading, title, disabledFlag } = this.state;
    const lineLength = this.detailTableDs.toData().length > 0;
    const columns = [
      {
        header: (
          <Button
            icon="add"
            disabled={!disabledFlag}
            onClick={() => this.createModal()}
            funcType="flat"
            // shape="circle"
            size="small"
          />
        ),
        align: 'center',
        width: 60,
        renderer: ({ record }) => (
          <Popconfirm
            title= {intl
              .get(`${modelPrompt}.error.delete`)
              .d(`是否确认删除？`)}
            onConfirm={() => {
              this.detailTableDs.remove(record);
              this.setState({ deleteFlag: true });
            }}
          >
            <Button
              funcType="flat"
              icon="remove"
              shape="circle"
              size="small"
              disabled={!disabledFlag}
            />
          </Popconfirm>
        ),
        lock: 'left',
      },
      // 物料编码
      {
        name: 'materialCode',
        align: 'left',
        renderer: ({ value, record }) => {
          if (disabledFlag) {
            return (
              <span className="action-link">
                <a onClick={() => this.editModal(record)}>{value}</a>
              </span>
            );
          }
          return (
            <span className="action-link">
              <a>{value}</a>
            </span>
          );
        },
      },
      // 物料描述
      {
        name: 'materialName',
        align: 'left',
      },
      // 单位
      {
        name: 'uomName',
        align: 'left',
      },
      // 物料版本
      {
        name: 'revisionCode',
        align: 'left',
      },
      // 最大装载量
      {
        name: 'maxQty',
        align: 'left',
      },
      // 有效性
      {
        name: 'enableFlag',
        width: 120,
        align: 'left',
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={value === 'Y' ? '有效' : '无效'}
          >
            {}
          </Badge>
        ),
      },
    ];
    return (
      <React.Fragment>
        <Header title={title} backPath="/hmes/equipment-point-maintenance/list">
          {!disabledFlag && (
            <Button
              onClick={this.handelEdit}
              style={{ marginRight: 15 }}
              icon="edit"
              color="primary"
            >

              {intl.get('tarzan.common.button.edit').d('编辑')}

            </Button>
          )}
          {disabledFlag && (
            <>
              <Button onClick={this.handelCancel}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
              <Button
                onClick={this.handelSave}
                style={{ marginRight: 15 }}
                icon="save"
                color="primary"
              >
                {intl.get('tarzan.common.button.save').d('保存')}

              </Button>
            </>
          )}
        </Header>
        <Content>
          <Spin spinning={loading}>
            <Collapse bordered={false} defaultActiveKey={['1', '2']}>
              <Panel header="基本属性" key="1">
                <Form dataSet={this.formDs} columns={3} labelWidth="200">
                  <Lov name="siteObj" disabled={!disabledFlag || lineLength} />
                  <TextField name="assemblePointCode" disabled={!disabledFlag} />
                  <TextField name="description" disabled={!disabledFlag} />
                  <Lov name="pointTypeLov" disabled={!disabledFlag || lineLength} />
                  <Switch name="enableFlag" disabled={!disabledFlag} />
                </Form>
              </Panel>
              <Panel header={intl.get(`${modelPrompt}.title`).d('物料信息')} key="2">
                <Table
                  dataSet={this.detailTableDs}
                  columns={columns}
                  style={{ height: 400 }}
                  dragRow
                />
              </Panel>
            </Collapse>
            ,
          </Spin>
        </Content>
      </React.Fragment>
    );
  }
}
