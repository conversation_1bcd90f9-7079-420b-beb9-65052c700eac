/**
 * @Description: 盘点工作台-service
 * @Author: <<EMAIL>>
 * @Date: 2022-02-10 16:49:24
 * @LastEditTime: 2023-05-18 16:10:02
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
// ${BASIC.HMES_BASIC}
// 盘点工作台-列表页-状态变更
export function ChangeStatus() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/status/ui`,
    method: 'POST',
  };
}
// 差异原因保存
export function SaveDifferencesReasons() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/contrast-reason/ui`,
    method: 'POST',
  };
}
// 条码明细-抽盘
export function SaveSpotCheck() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/recount/ui`,
    method: 'POST',
  };
}

export function Approve() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/approve/ui`,
    method: 'POST',
  };
}
// 盘点工作台-列表页-批量新建
export function BatchAdd() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/batch-add/ui`,
    method: 'POST',
  };
}

// 盘点工作台-详情页-获取单据信息
export function FetchStocktageDetail() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/detail/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_DETAIL.BASIC`,
    method: 'GET',
  };
}

// 盘点工作台-详情页-保存单据信息
export function SaveStocktageDetail() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_DETAIL.BASIC`,
    method: 'POST',
  };
}

// 盘点工作台-详情页-根据仓库批量新建库位数据
export function BatchAddByAreaLocator() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/locator/range/ui`,
    method: 'GET',
  };
}

// 盘点工作台-详情页-根据库位批量新建物料数据
export function BatchAddByLocatorRange() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-locator-material-rel/locator/limit/material/ui`,
    method: 'GET',
  };
}

// 盘点工作台-详情页-差异调整
export function AdjustDiffer() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/diff-adjust/ui`,
    method: 'POST',
  };
}
