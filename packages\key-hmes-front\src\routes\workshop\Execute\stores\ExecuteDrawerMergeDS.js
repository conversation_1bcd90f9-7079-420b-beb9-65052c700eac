/**
 * @Description: 生产指令管理抽屉 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2022-03-31 19:47:48
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.execute';
const tenantId = getCurrentOrganizationId();

const mergeDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-eo/detail/ui`,
        method: 'GET',
      };
    },
    submit: ({ dataSet }) => {
      const { eoId, eoNum, targetEoId, secondaryEoIds } = dataSet.current.toData();
      const orderIds = {};
      if (`${targetEoId}` === `${eoNum}`) {
        orderIds.targetEoNum = targetEoId;
      } else {
        if (targetEoId) {
          orderIds.targetEoNum = targetEoId;
        }
        if (eoId) {
          orderIds.primaryEoId = eoId;
        }
      }
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/merge/for/ui`,
        method: 'POST',
        data: {
          ...orderIds,
          secondaryEoIds,
        },
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
  fields: [
    // 基本属性
    {
      name: 'targetEoId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetEoId`).d('目标编码'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryEoId`).d('主编码'),
      disabled: true,
    },
    {
      name: 'secondaryEos',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.secondaryEos`).d('副编码'),
      required: true,
      defaultValue: {},
      lovCode: 'MT.EO.MERGE.SECONDARY',
      textField: 'eoNum',
      valueField: 'eoId',
      noCache: true,
      ignore: 'always',
      multiple: true,
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
            status: record.get('status'),
            eoId: record.get('eoId'),
          };
        },
      },
    },
    {
      name: 'secondaryEoIds',
      type: FieldType.string,
      bind: 'secondaryEos.eoId',
    },
    {
      name: 'secondaryEoNum',
      type: FieldType.string,
      bind: 'secondaryEos.eoNum',
    },
  ],
});

const mergeListDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'eoId',
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/merge/source/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialRevision`).d('物料版本'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('执行作业状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eoQty`).d('数量'),
      required: true,
      min: 0,
      precision: 2,
      defaultValue: 0,
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mergeTime`).d('合并时间'),
    },
  ],
});

export { mergeDS, mergeListDS };
