/**
 * @feature 生产版本维护-新增详情的抽屉的DS
 * @date 2021-8-31
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentLanguage, getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.product.productionVersion';

const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'productionVersionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionVersionCode`).d('生产版本编码'),
      required: true,
    },
    {
      name: 'productionVersionDesc',
      label: intl.get(`${modelPrompt}.productionVersionDesc`).d('生产版本描述'),
      type: FieldType.string,
    },
    {
      name: 'bom',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.bomName`).d('装配清单/版本'),
      lovCode: 'MT.METHOD.USER_SITE_BOM',
      textField: 'bomName',
      valueField: 'bomId',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'bomName',
      type: FieldType.string,
      bind: 'bom.bomName',
    },
    {
      name: 'bomId',
      type: FieldType.number,
      bind: 'bom.bomId',
    },
    {
      name: 'bomRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'bom.revision',
      ignore: 'always',
    },
    {
      name: 'router',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线/版本'),
      lovCode: 'MT.METHOD.USER_SITE_ROUTER',
      textField: 'routerName',
      valueField: 'routerId',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'routerName',
      type: FieldType.string,
      bind: 'router.routerName',
    },
    {
      name: 'routerId',
      type: FieldType.number,
      bind: 'router.routerId',
    },
    {
      name: 'routerRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'router.revision',
      ignore: 'always',
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
      max: 'dateTo',
      required: true,
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
      min: 'dateFrom',
    },
  ],
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'tarzan.tag.domain.entity.MtTag';
      return {
        data: { tagDescription: record.data.tagDescription },
        params: { fieldName, className },
        url: `${BASIC.HMES_BASIC}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
});

export { detailDS };
