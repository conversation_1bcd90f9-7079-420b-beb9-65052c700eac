import React, { FC } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, Modal, Form, DateTimePicker, Lov } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import axios from 'axios';
import { tableDS, createDS, updateDS } from './stores';

interface ListPageProps extends RouteComponentProps {
  tableDs: DataSet;
  createDS: DataSet;
  updateDS: DataSet;
}

const modelPrompt = 'tarzan.ass.productProcessShelfBatchUpdate';

const ListPageComponent: FC<ListPageProps> = ({ tableDs, createDs, updateDs }) => {
  const columns: ColumnProps[] = [
    {
      name: 'processBarcode',
      width: 150,
    },
    {
      name: 'materialCode',
      width: 180,
    },
    {
      name: 'materialName',
      width: 180,
    },
    {
      name: 'quantityStatus',
    },
    {
      name: 'quantityStatusDesc',
    },
    {
      name: 'fromOperationDesc',
      width: 150,
    },
    {
      name: 'toOperationDesc',
      width: 180,
    },
    {
      name: 'dateFrom',
      width: 180,
    },
    {
      name: 'dateTo',
      width: 150,
    },
    {
      name: 'expirationDate',
    },
    {
      name: 'lastUpdatedByRealName',
    },
  ];

  const handleCreate = () => {
    if (!tableDs.selected.length) {
      notification.error({
        message: intl.get(`${modelPrompt}.error.select`).d('未勾选数据，请检查!'),
      });
      return;
    }
    Modal.open({
      title: intl.get(`${modelPrompt}.title.create`).d('新增'),
      destroyOnClose: true,
      drawer: false,
      closable: true,
      keyboardClosable: true,
      onCancel: () => {
        createDs.reset();
      },
      className: 'hmes-style-modal',
      children: (
        <Form dataSet={createDs} columns={1} labelWidth={112}>
          <Lov name="firstProcessLov" />
          <Lov name="endProcessLov" />
          <DateTimePicker name="toDate" />
        </Form>
      ),
      onOk: async () => {
        const validate = await createDs.validate();
        if (validate) {
          const eoIds = tableDs.selected.map(record => record.get('eoId'));
          const params = { ...createDs?.toData()[0], eoIds };
          const url = `${
            BASIC.HMES_BASIC
          }/v1/${getCurrentOrganizationId()}/hme-material-op-expirations-batch/save/ui`;
          const res: any = await axios.post(url, params);
          if (res && res.success) {
            notification.success({});
            tableDs.query();
            createDs.reset();
          } else {
            notification.error({
              message: res.message,
            });
            return false;
          }
        } else {
          return false;
        }
      },
    });
  };

  const handleUpdate = async () => {
    if (!tableDs.selected.length) {
      notification.error({
        message: intl.get(`${modelPrompt}.error.select`).d('未勾选数据，请检查!'),
      });
      return;
    }

    // 检查选中数据是否有到期时间
    const hasExpirationDate = tableDs.selected.some(record => record.get('dateTo'));
    if (!hasExpirationDate) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.error.noExpirationDate`)
          .d('选中的数据无到期时间，请检查'),
      });
      return;
    }

    Modal.open({
      title: intl.get(`${modelPrompt}.title.update`).d('更新'),
      destroyOnClose: true,
      drawer: false,
      closable: true,
      keyboardClosable: true,
      onCancel: () => {
        updateDs.reset();
      },
      className: 'hmes-style-modal',
      children: (
        <Form dataSet={updateDs} columns={1} labelWidth={112}>
          <DateTimePicker name="toDate" />
        </Form>
      ),
      onOk: async () => {
        const validate = await updateDs.validate();
        if (validate) {
          const ids = tableDs.selected.map(record => record.get('id'));
          const params = { ...updateDs?.toData()[0], ids };
          const url = `${
            BASIC.HMES_BASIC
          }/v1/${getCurrentOrganizationId()}/hme-material-op-expirations-batch/update/ui`;
          const res: any = await axios.post(url, params);
          if (res && res.success) {
            notification.success({});
            tableDs.query();
            updateDs.reset();
          } else {
            notification.error({
              message: res.message,
            });
            return false;
          }
        } else {
          return false;
        }
      },
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.deviceLock`).d('产品工艺保质期批量更新')}>
        <Button color={ButtonColor.primary} onClick={handleCreate}>
          {intl.get(`${modelPrompt}.title.create`).d('新增')}
        </Button>
        <Button color={ButtonColor.primary} onClick={handleUpdate}>
          {intl.get(`${modelPrompt}.title.update`).d('更新')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          key="deviceLock"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={4}
          searchCode="productProcessShelfBatchUpdate" // 动态筛选条后端接口唯一编码
          customizedCode="productProcessShelfBatchUpdate" // 个性化编码
          pagination={{
            pageSizeOptions: ['10', '20', '50', '100', '200', '500', '1000'],
          }}
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const tableDs = tableDS();
    const createDs = createDS();
    const updateDs = updateDS();
    return {
      tableDs,
      createDs,
      updateDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.ass.productProcessShelfBatchUpdate'],
})(ListPage);
