/**
 * @Description: 返修工单条码绑定-列表页
 * @Author: <EMAIL>
 * @Date: 2023-06-15 10:19:56
 */
import React, { useEffect } from 'react';
import { DataSet, Table, Spin } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { headDS, lineDS } from './stores/TableDs';

const modelPrompt = 'tarzan.hmes.rebateWorkOrderBarcodeBinding';

// const Host = `/yp-mes-38283`;
const Panel = Collapse.Panel;
const RebateWorkOrderBarcodeBinding = props => {
  const {
    headDs,
    lineDs,
    match: { params },
  } = props;

  useEffect(() => {
    // if (props?.history?.action === 'PUSH') {
    if (params && params.id) {
      headDs.setQueryParameter('workOrderId', params.id);
      headDs.query().then(res => {
        if (res) {
          lineDs.setQueryParameter('workOrderId', params.id);
          lineDs.query().then(res => {
            if (res) {
              if (res.content && res.content.length > 0) {
                const lineData = res.content.filter(item => item.reworkBindingFlag === 'Y');
                lineDs.loadData(lineData);
              }
            }
          });
        }
      });
    }
    // }
  }, []);

  const headColumns = [
    {
      name: 'approvingFlagDesc',
    },
    {
      name: 'lastApprovalStatusDesc',
      width: 150,
    },
    {
      name: 'workOrderNum',
      width: 150,
    },
    {
      name: 'siteCode',
    },
    {
      name: 'statusDesc',
    },
    {
      name: 'workOrderTypeDesc',
    },
    {
      name: 'prodLineName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'qty',
    },
    {
      name: 'completedQty',
    },
    {
      name: 'pendingQty',
    },
  ];

  const lineColumns = [
    // {
    //   name: 'del',
    //   width: 80,
    //   align: 'center',
    //   renderer: ({record}) =>(<Icon onClick={() => handleDel(record)} type="delete_black-o" style={{ fontSize: 20, color: 'rgb(8, 64, 248)', cursor: 'pointer' }} />),
    // },
    {
      name: 'materialLotCode',
    },
    {
      name: 'reworkBindingFlagDesc',
    },
    {
      name: 'primaryUomQty',
    },
    {
      name: 'primaryUomCode',
    },
    // {
    //   name: 'siteCode',
    // },
    // {
    //   name: 'materialCode',
    // },
    // {
    //   name: 'materialName',
    // },
    // {
    //   name: 'revisionCode',
    // },
    {
      name: 'enableFlag',
      width: 140,
      align: 'center',
      renderer: ({ value }) => {
        return value === 'Y' ? '是' : '否';
      },
    },
    {
      name: 'qualityStatusDesc',
    },
    {
      name: 'workOrderNum',
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('返修工单条码绑定')}>
        {/* <Button color="primary" onClick={handleBind} disabled={!bindDisabled}>
          {intl.get('hzero.common.button.bindApply').d('绑定申请')}
        </Button> */}
      </Header>
      <Content>
        <Collapse defaultActiveKey={['1', '2']}>
          <Panel header={intl.get(`${modelPrompt}.tab.title1`).d('工单')} key="1">
            <Table
              queryFieldsLimit={6}
              searchCode="rebateWorkOrderBarcodeBindingHead"
              customizedCode="rebateWorkOrderBarcodeBindingHead"
              // queryBar={TableQueryBarType.filterBar}
              // queryBarProps={{
              //   fuzzyQuery: false,
              // }}
              dataSet={headDs}
              columns={headColumns}
            />
          </Panel>
          <Panel header={intl.get(`${modelPrompt}.tab.title2`).d('返修条码')} key="2">
            <Spin spinning={false}>
              {/* <Button
                onClick={handleDel}
                disabled={!lineDisabled}
                color="primary"
                style={{ marginBottom: '-45px', float: 'right' }}
              >
                {intl.get(`${modelPrompt}.button.delete`).d('删除')}
              </Button> */}
              <Table
                searchCode="rebateWorkOrderBarcodeBindingLine"
                customizedCode="rebateWorkOrderBarcodeBindingLine"
                // queryBar={renderBar}
                // queryBarProps={{
                //   fuzzyQuery: false,
                // }}
                dataSet={lineDs}
                columns={lineColumns}
              />
            </Spin>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.hmes.rebateWorkOrderBarcodeBinding', 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet({
        ...headDS(),
      });
      const lineDs = new DataSet({
        ...lineDS(),
      });

      return {
        headDs,
        lineDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(RebateWorkOrderBarcodeBinding),
);
