/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2022-11-08 09:31:24
 * @LastEditTime: 2022-11-16 14:08:26
 * @LastEditors: <<EMAIL>>
 */
/**
 * @feature 物料库位关系维护-入口页
 * @date 2021-12-14
 * <AUTHOR>
 */
import React, { useEffect } from 'react';
import notification from 'utils/notification';
import { Button, DataSet, Table, Modal } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Badge } from 'hzero-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from 'utils/intl';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import { entranceDS } from '../stores/EntranceDS';
import { drawerDS } from '../stores/DrawerDS';
import { Drawer } from './Drawer';
import { SaveLocatorRelationList } from '../services/index';

const modelPrompt = 'tarzan.strategy.locatorRelation';

const LocatorRelation = props => {
  const {
    dataSet,
    drawerDs,
    history,
    match: { path },
  } = props;

  const saveLocatorRelationList = useRequest(SaveLocatorRelationList(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    dataSet.query(dataSet.currentPage);
  }, []);

  // 列表所有者类型与预留对象类型更新时清空对应的编码
  useEffect(() => {
    function processDataSetListener(flag) {
      const handler = flag
        ? dataSet.queryDataSet.addEventListener
        : dataSet.queryDataSet.removeEventListener;
      handler.call(dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  // 当所有者类型和预留对象类型发生改变时更新对应的Lov
  const handleQueryDataSetUpdate = ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', {});
    }
  };

  const goImport = () => {
    history.push(`/hmes/strategy/locator-relation/batch-import`);
  };

  // 点击确定按钮的回调
  const saveLocatorList = async () => {
    drawerDs.current?.set({ nowDate: new Date().getTime() });
    const validate = await drawerDs.validate(false, true);
    const newData: any = drawerDs.toData()[0];
    const { ownerType } = newData;
    if (validate) {
      return saveLocatorRelationList.run({
        params: {
          ...newData,
          ownerType: ownerType === 'OWNER' || ownerType === null ? '' : ownerType,
        },
        onSuccess: () => {
          notification.success({});
          dataSet.query(dataSet.currentPage);
        },
      });
    }
    return false;
  };

  const goDetail = record => {
    const recordData = record === 'create' ? { enableFlag: 'Y' } : record.toData() || {};
    drawerDs.loadData([recordData]);

    Modal.open({
      key: Modal.key(),
      title:
        record === 'create'
          ? intl.get(`${modelPrompt}.title.locator-relation-add`).d('物料库位关系新增')
          : intl.get(`${modelPrompt}.title.locator-relation-edit`).d('物料库位关系编辑'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal',
      children: <Drawer drawerDs={drawerDs} />,
      onOk: saveLocatorList,
    });
  };

  const columns: ColumnProps[] = [
    { name: 'siteCode', width: 150 },
    { name: 'materialCode', width: 150 },
    { name: 'materialName', width: 150 },
    { name: 'locatorCode', width: 150 },
    { name: 'locatorName', width: 150 },
    {
      name: 'enableFlag',
      align: ColumnAlign.center,
      width: 100,
      renderer: ({ record }) => (
        <Badge
          status={record!.get('enableFlag') === 'Y' ? 'success' : 'error'}
          text={
            record!.get('enableFlag') === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    { name: 'ownerTypeDesc', width: 150 },
    { name: 'ownerCode', width: 150 },
    { name: 'ownerName', width: 150 },
    {
      header: intl.get(`${modelPrompt}.operation`).d('操作'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goDetail(record)}>{intl.get(`${modelPrompt}.edit`).d('编辑')}</a>
          </span>
        );
      },
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.locator-relation`).d('物料库位关系维护')}>
        <div>
          <Button icon="file_upload" onClick={goImport}>
            {intl.get(`${modelPrompt}.button.data.batch`).d('批量导入')}
          </Button>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={() => {
              goDetail('create');
            }}
            permissionList={[
              {
                code: `${path}.button.create`,
                type: 'button',
                meaning: '列表页-新建按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
        </div>
      </Header>
      <Content>
        <div className="disabled-table">
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={dataSet}
            columns={columns}
            searchCode="wlkwgxwh"
            customizedCode="wlkwgxwh"
          />
        </div>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.strategy.locatorRelation', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...entranceDS(),
      });
      const drawerDs = new DataSet({
        ...drawerDS(),
      });
      return {
        dataSet,
        drawerDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(LocatorRelation),
);
