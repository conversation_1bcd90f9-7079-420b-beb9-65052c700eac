import React, { FC, useMemo } from 'react';
import { observer } from 'mobx-react';
import { RouteComponentProps } from 'react-router';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import { Collapse, Tag } from 'choerodon-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
// import { BASIC } from '@utils/config';
import notification from 'utils/notification';
// import { getCurrentOrganizationId } from 'utils/utils';
import { TarzanSpin } from '@components/tarzan-ui';
import { lineTableDS, tableDS } from '../stores/platformListDS';
import { RecordCancel } from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.mes.event.badRecordPlatform';

interface BadRecordPlatformProps extends RouteComponentProps {
  headDs: DataSet;
  lineDs: DataSet;
  customizeTable: any;
}

const BadRecordPlatform: FC<BadRecordPlatformProps> = observer(props => {
  // const { customizeTable, history, headDs, lineDs } = props;
  const { history, headDs, lineDs } = props;

  const headerRowClick = record => {
    lineDs.setQueryParameter('ncIncidentId', record.get('ncIncidentId'));
    lineDs.query();
  };

  const { run: cancel, loading: cancelLoading } = useRequest(RecordCancel(), {
    needPromise: true,
    manual: true,
  });

  const queryLineTable = ncIncidentId => {
    if (ncIncidentId) {
      lineDs.setQueryParameter('ncIncidentId', ncIncidentId);
    }
    lineDs.query();
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    if (dataSet?.current?.toData().message) {
      notification.error({
        message: dataSet?.current?.toData().message,
      });
      headDs.loadData([]);
      lineDs.loadData([]);
      return;
    }
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('ncIncidentId'));
    } else {
      lineDs.loadData([]);
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const openDetail = (id, isFromListPage = true) => {
    history.push({
      pathname: `/hmes/bad-record/platform/detail/${id}`,
      state: { isFromListPage },
    });
  };

  const renderNcRecordTag = (value, record) => {
    switch (record.get('ncIncidentStatus')) {
      case 'NEW':
        return <Tag color="green">{value}</Tag>;
      case 'RELEASED':
        return <Tag color="blue">{value}</Tag>;
      case 'COMPLETED':
        return <Tag color="red">{value}</Tag>;
      case 'CANCEL':
        return <Tag color="gray">{value}</Tag>;
      case 'WORKING':
        return <Tag color="volcano">{value}</Tag>;
      case 'REVIEWING':
        return <Tag color="yellow">{value}</Tag>;
      default:
        return null;
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'ncIncidentNum',
        width: 180,
        lock: ColumnLock.left,
        renderer: ({ record }) => {
          return (
            <a
              onClick={() => {
                openDetail(record?.get('ncIncidentId'));
              }}
            >
              {record?.get('ncIncidentNum')}
            </a>
          );
        },
      },
      {
        name: 'ncIncidentStatusDesc',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record }) => renderNcRecordTag(value, record),
      },
      { name: 'ncRecordTypeDesc', width: 120 },
      // {
      //   name: 'ncReviewStatusDesc',
      //   align: ColumnAlign.center,
      //   renderer: ({ value, record }) => renderNcReviewTag(value, record),
      // },
      { name: 'siteCode' },
      { name: 'materialName' },
      { name: 'revisionCode' },
      { name: 'operationName' },
      { name: 'description' },
      { name: 'locatorName' },
      { name: 'ncStartTime', width: 150, align: ColumnAlign.center },
      { name: 'ncStartUserName' },
      { name: 'ncCloseTime', width: 150, align: ColumnAlign.center },
      { name: 'ncCloseUserName' },
      { name: 'remark', width: 200 },
    ];
  }, []);

  const listTableColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'identification', width: 180, lock: ColumnLock.left },
      { name: 'eoNum', width: 180, lock: ColumnLock.left },
      { name: 'materialLotCode', width: 180, lock: ColumnLock.left },
      { name: 'ncRecordStatusDesc', width: 150 },
      { name: 'qty', width: 150 },
      // {
      //   name: 'ncCodeAndDefectQty',
      //   width: 150,
      //   renderer: ({ record }) => {
      //     return (
      //       record?.toData().ncCodeAndDefectQty &&
      //       record?.toData().ncCodeAndDefectQty.length &&
      //       record?.toData().ncCodeAndDefectQty.map((item: any) => <Tag>{item}</Tag>)
      //     );
      //   },
      // },
      // {
      //   name: 'disposalDescAndQty',
      //   width: 150,
      //   renderer: ({ record }) => {
      //     return (
      //       record?.toData().disposalDescAndQty &&
      //       record?.toData().disposalDescAndQty.length &&
      //       record?.toData().disposalDescAndQty.map((item: any) => <Tag>{item}</Tag>)
      //     );
      //   },
      // },
      { name: 'prodLineName' },
      { name: 'workcellCode' },
      { name: 'operationCode' },
      { name: 'description' },
      { name: 'equipmentCode' },
      { name: 'locatorName', width: 150 },
      { name: 'containerCode', width: 150 },
      { name: 'uomName', width: 150 },
      { name: 'siteName', width: 150 },
      { name: 'ncStartTime', width: 150 },
      { name: 'ncStartUserName', width: 150 },
      { name: 'ncCloseTime', width: 150 },
      { name: 'ncCloseUserName', width: 150 },
      { name: 'intendedDisposalDesc', width: 150 },
      { name: 'disposalFunctionDesc', width: 150 },
    ];
  }, []);

  const clickMenu = async () => {
    cancel({
      params: headDs?.selected?.map(item => item?.get('ncIncidentId')),
    }).then(res => {
      if (res?.success) {
        headDs.batchUnSelect(headDs.selected);
        headDs.clearCachedSelected();
        headDs.query(props.headDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  return (
    <div className="hmes-style" style={{ height: '98%', overflow: 'auto' }}>
      <TarzanSpin dataSet={lineDs} spinning={cancelLoading}>
        <Header title={intl.get(`${modelPrompt}.title`).d('不良记录平台')}>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={() => openDetail('create')}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
          <Button
            color={ButtonColor.primary}
            onClick={clickMenu}
            disabled={
              !headDs.selected.length ||
              headDs.selected.some(item => item.get('ncIncidentStatus') !== 'NEW')
            }
          >
            {intl.get(`tarzan.common.button.cancel`).d('取消')}
          </Button>
        </Header>
        <Content>
          {/* {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.LIST`,
          },


        )} */}
          <Table
            highLightRow
            queryFieldsLimit={4}
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={headDs}
            columns={columns}
            searchCode="badRecordPlatform-listHeader"
            customizedCode="badRecordPlatform-listHeader"
            onRow={({ record }) => ({
              onClick: () => headerRowClick(record),
            })}
          />
          <Collapse bordered={false} defaultActiveKey={['transDetail']}>
            <Panel
              header={intl.get(`${modelPrompt}.title.listLine`).d('不良记录明细')}
              key="transDetail"
            >
              {/* {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.DETAIL.LIST`,
              },

            )} */}
              <Table
                dataSet={lineDs}
                columns={listTableColumns}
                customizedCode="badRecordPlatformList-listLine"
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(tableDS());
      const lineDs = new DataSet(lineTableDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.QUERY`,
        // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.LIST`,
        // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.DETAIL.LIST`,
      ],
    })(BadRecordPlatform as any),
  ),
);
