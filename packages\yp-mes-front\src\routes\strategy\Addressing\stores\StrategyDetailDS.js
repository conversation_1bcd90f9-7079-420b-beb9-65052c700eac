/**
 * @Description: 生产指令管理详情页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2023-07-19 14:50:11
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSet } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.addressing.strategy';
const tenantId = getCurrentOrganizationId();

const formDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-addressing-strategy/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && datas.rows) {
            if (datas.rows.bomId === 0) {
              datas.rows.bomId = null;
            }
            if (datas.rows.routerId === 0) {
              datas.rows.routerId = null;
            }
          }
          return {
            ...datas,
          };
        },
      };
    },
    submit: ({ dataSet }) => {
      const {
        locator,
        screeningConditionSwitch,
        addStrategyPackLimit,
        addStrategyPickMode,
        locatorId,
        locatorCode,
        locatorTypeValueRange,
        rangeType,
        addressingRange,
        ...data
      } = dataSet.current.toData();

      data.addressingRange = { ...addressingRange };
      data.addressingRange.locatorId = locatorId;
      data.addressingRange.locatorCode = locatorCode;
      data.addressingRange.rangeType = rangeType;
      data.addressingRange.locatorTypeValueRange = locatorTypeValueRange;
      if (rangeType === 'COORDINATE') {
        data.addressingRange.locatorTypeValueRange = [];
      } else if (rangeType === 'LOCATOR_TYPE') {
        data.addressingRange.coordinateValueRange = {};
      } else {
        data.addressingRange.locatorTypeValueRange = [];
        data.addressingRange.coordinateValueRange = {};
      }

      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-addressing-strategy/save/ui`,
        method: 'POST',
        data,
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
  fields: [
    // 基本属性
    {
      name: 'addressingStrategyId',
      type: FieldType.number,
    },
    {
      name: 'addressingStrategyType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      defaultValue: 'STORE',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ADDRESSING_STRATEGY_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled({ record }) {
          return record.get('addressingStrategyId');
        },
      },
    },
    {
      name: 'addressingStrategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addressingStrategyCode`).d('寻址策略编码'),
      required: true,
      dynamicProps: {
        disabled({ record }) {
          return record.get('addressingStrategyId');
        },
      },
    },
    {
      name: 'addressingStrategyLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addressingStrategyType`).d('策略层级'),
      required: true,
      textField: 'description',
      valueField: 'typeCode',
      defaultValue: 'MAIN',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ADDRESSING_STRATEGY_LEVEL`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled({ record }) {
          return record.get('addressingStrategyId');
        },
      },
    },
    {
      name: 'addStrategyLocationLimit',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addStrategyLocationLimit`).d('区域限制'),
      required: true,
      textField: 'description',
      valueField: 'typeCode',
      defaultValue: 'SAME_AREA',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=ADD_STRATEGY_LOCATION_LIMIT`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'descendantStrategy',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.descendantStrategy`).d('子策略'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.ADDRESSING_STRATEGY`,
      textField: 'addressingStrategyCode',
      valueField: 'addressingStrategyId',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara({ record }) {
          const _lovPara = {
            tenantId,
            addressingStrategyLevel: 'PARTIAL',
            addressingStrategyType: record.get('addressingStrategyType'),
          };
          if (
            record.get('addressingStrategyLevel') === 'PARTIAL' &&
            record.get('addressingStrategyId')
          ) {
            _lovPara.addressingStrategyId = record.get('addressingStrategyId');
          }
          return _lovPara;
        },
      },
    },
    {
      name: 'descendantStrategyId',
      type: FieldType.number,
      bind: 'descendantStrategy.addressingStrategyId',
    },
    {
      name: 'descendantStrategyCode',
      type: FieldType.string,
      bind: 'descendantStrategy.addressingStrategyCode',
    },
    {
      name: 'areaAddressingTrigger',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=AREA_ADDRESSING_TRIGGER`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled({ record }) {
          return !record.get('descendantStrategyId');
        },
        required({ record }) {
          return record.get('descendantStrategyId');
        },
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },

    // 寻址范围
    {
      name: 'locator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.upperLocator`).d('上层库位'),
      required: true,
      lovCode: 'MT.MODEL.USER.SITE.LOCATOR',
      textField: 'locatorCode',
      valueField: 'locatorId',
      noCache: true,
      ignore: 'always',
      lovPara: { tenantId },
      dynamicProps: {
        required({ record }) {
          return record.get('addressingStrategyLevel') === 'MAIN';
        },
      },
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'locator.locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'locator.locatorCode',
    },
    {
      name: 'rangeType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lowerLocatorLimit`).d('下层库位限定'),
      textField: 'text',
      valueField: 'value',
      allowClear: true,
    },
    {
      name: 'locatorTypeValueRange',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorType`).d('库位类型'),
      multiple: true,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=LOCATOR_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        required({ record }) {
          return record.get('rangeType') === 'LOCATOR_TYPE';
        },
      },
    },

    // 筛选条件
    {
      name: 'screeningConditionSwitch',
      type: FieldType.object,
      textField: 'text',
      valueField: 'value',
      defaultValue: 'a',
      options: new DataSet({
        data: [
          { text: intl.get(`${modelPrompt}.unlimited`).d('无限制'), value: 'a' },
          { text: intl.get(`${modelPrompt}.filterLimit`).d('筛选限制'), value: 'b' },
        ],
      }),
      selection: 'single',
    },
    {
      name: 'screeningCondition',
      type: FieldType.object,
    },
    {
      name: 'approximateLimit',
      type: FieldType.string,
      multiple: true,
      textField: 'meaning',
      valueField: 'value',
      lovPara: { tenantId },
      lookupCode: `MT.ADD_STRATEGY_SCREENING_CONDITION`,
      bind: 'screeningCondition.approximateLimit',
    },
    // 索引次序
    // 判定条件
    {
      name: 'judgmentCondition',
      type: FieldType.object,
      multiple: true,
      textField: 'meaning',
      valueField: 'value',
      lovPara: { tenantId },
      lookupCode: `MT.ADD_STRATEGY_JUDGMENT_CONDITION`,
    },
    {
      name: 'processingOrderSwitch',
      type: FieldType.object,
      multiple: true,
      textField: 'meaning',
      valueField: 'value',
    },
  ],
});

const locatorCoordinateDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-coordinates/limit-locator/ui`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && datas.rows) {
            if (datas.rows.bomId === 0) {
              datas.rows.bomId = null;
            }
            if (datas.rows.routerId === 0) {
              datas.rows.routerId = null;
            }
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
});

export { formDS, locatorCoordinateDS };
