.pathChoose {
  :global {
    .ant-card-extra {
      padding: 0 !important;
    }
  }

  .topStepWrapper {
    display: flex;
    justify-content: space-between;

    .stepWrapper {
      width: 480px;
      height: 340px;
      border: 1px solid rgba(0, 0, 0, 0.14901960784313725);
      border-radius: 2px;

      .stepHeader {
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        padding: 0 2px 0 16px;
        line-height: 50px;
        border-bottom: 2px solid rgba(0, 0, 0, 0.06);

        .stepTitleDropdown {
          :global {
            .ant-select-selection {
              border: none;
              //color: @primary-color;
            }

            .ant-select-selection-selected-value {
              color: #0840f8;
            }

            .ant-select-dropdown {
              font-size: 16px;
            }

            .ant-select-focused .ant-select-selection,
            .ant-select-selection:active,
            .ant-select-selection:focus {
              border: none;
              box-shadow: none;
            }
          }
        }
      }

      .stepBottom {
        padding: 12px 0 12px 16px;

        ::-webkit-scrollbar {
          width: 2px;
        }

        .boxWrapper {
          display: flex;
          justify-content: space-between;
          margin-top: 8px;
          height: 240px;
          overflow: auto;
          position: relative;

          .leftPathBox {
            .radioBox {
              display: block;
              height: 36px;
              line-height: 36px;

              .imgWrapper {
                //float: right;
                top: -1px;
                position: absolute;
                left: 386px;

                i {
                  font-size: 12px;
                  margin: 2px;
                }
              }

            }

            :global {
              .ant-checkbox-wrapper + .ant-checkbox-wrapper {
                margin-left: 0;
              }

              .ant-radio-group {
                width: 442px;
              }
            }
          }

          .rightPathBox {
            :global(.ant-checkbox-group) {
              display: grid;
            }

            .radioBox {
              display: block;
              height: 36px;
              line-height: 36px;

              .imgCheckWrapper {
                position: absolute;
                right: 27px;
                line-height: 35px;

                i {
                  font-size: 12px;
                  margin: 2px;
                  color: #0840f8;
                }
              }

            }

            :global {
              .ant-checkbox-wrapper + .ant-checkbox-wrapper {
                margin-left: 0;
              }
            }
          }
        }
      }

      .stepBottom::-webkit-scrollbar {
        width: 2px;
      }
    }

    .iconWrapper {
      width: 60px;
      height: 340px;
      line-height: 340px;
      text-align: center;
    }
  }
}

.customCol {
  :global {
    .ant-form-item {
      margin-bottom: 0 !important;
    }
  }
}
