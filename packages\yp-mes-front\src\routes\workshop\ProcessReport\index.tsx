/**
 * @Description: 在制品报表列表
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 16:51:01
 * @LastEditTime: 2022-09-26 16:03:10
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useState } from 'react';
import {
  DataSet,
  Table,
  Button,
  TextField,
  Lov,
  Icon,
  Row,
  Col,
  Form,
  Select,
} from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { observer } from 'mobx-react';
import ExcelExport from 'components/ExcelExport';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import { isNil } from 'lodash';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import LovModal from '@/components/BatchInput/LovModal';
import { tableDS } from './stores/ProcessReportListDs';

const tenantId = getCurrentOrganizationId();
const ProcessReport = observer(() => {
  const [expandForm, setExpandForm] = useState(false);
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'qty',
        width: 80,
        align: ColumnAlign.center,
      },
      {
        name: 'wipStatusMeaning',
        width: 120,
        align: ColumnAlign.center,
      },
      {
        name: 'materialCode',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'workOrderNum',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'operationName',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'prodLineCode',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'prodLineName',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'equipmentCode',
        width: 150,
        align: ColumnAlign.center,
      },
      {
        name: 'equipmentName',
        width: 150,
        align: ColumnAlign.center,
      },
    ];
  }, []);

  const inputLovDS = new DataSet(InputLovDS());

  const tableDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
      // handleSearch()
    }
  };

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    console.log('queryParmas', queryParmas);

    return queryParmas;
  };

  const toggleForm = () => {
    setExpandForm(!expandForm);
  }

  const handleSearch = async () => {
    const {
      productionLineIdStr,
      materialIdStr,
      equipmentIdStr,
      wipStatusStr,
      identificationStr,
      workOrderNumStr,
    } = tableDs.queryDataSet?.current?.toData();
    console.log("data", tableDs.queryDataSet?.current?.toData());
    
    if (
      !productionLineIdStr.length &&
      !materialIdStr.length &&
      !equipmentIdStr.length &&
      !wipStatusStr.length &&
      !identificationStr &&
      !workOrderNumStr
    ) {
      return notification.warning({
        message: '查询条件至少输入一条',
      });
    }
    tableDs.query();
  };

  const renderQueryBar = ({ buttons, queryDataSet, queryFields, dataSet }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="identificationStr"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identificationStr', '条码号')}
                    />
                  </div>
                }
              />
              <TextField
                name="workOrderNumStr"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'workOrderNumStr', '工单编码')}
                    />
                  </div>
                }
              />
              <Lov name="productionLineObj" />
              {expandForm && (
                <>
                  <Lov name="equipmentObj" />
                  <Lov name="materialObj" />
                  <Select name="wipStatusStr" />\
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button onClick={handleSearch} color={ButtonColor.primary}>
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  // const data = tableDs.queryDataSet.current.toData();

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.hmes.processReport.title.list').d('在制品报表')}>
        <ExcelExport
          otherButtonProps={{
            disabled: !tableDs.toData().length,
          }}
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-eo-step-wip-workings/export/ui`}
          queryParams={getExportQueryParams}
        />
      </Header>
      <Content>
        <Table
          searchCode="zzpbb1"
          customizedCode="zzpbb1"
          queryBar={renderQueryBar}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
      <LovModal {...lovModalProps} />
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.processReport', 'tarzan.common'],
})(ProcessReport);
