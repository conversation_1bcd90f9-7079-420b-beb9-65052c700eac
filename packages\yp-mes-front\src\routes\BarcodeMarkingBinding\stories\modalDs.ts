import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.ass.andengMonitor';

const modalFactory = () =>
    new DataSet({
        primaryKey: 'lampStatusId',
        selection: false,
        paging: true,
        autoQuery: false,
        dataKey: 'rows.content',
        totalKey: 'rows.totalElements',
        forceValidate:true,
        fields: [
            {
                name: 'unbindReason',
                required:true,
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.unbindReason`).d('解绑原因'),
            },
        ],
    });

export default modalFactory;
