import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.cassetteMaterial';

const materialHistoryFactory = () =>
  new DataSet({
    primaryKey: 'hisKeyId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'boxCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.boxCode`).d('料盒编码'),
      },
      {
        name: 'materialLotCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
      },
      {
        name: 'enableFlag',
        lookupCode: 'MT.YES_NO',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.enableFlag`).d('有效性'),
      },
      {
        name: 'creationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.creationDate`).d('创建时间'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'GET',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-box-bindings-his/material/query`,
        };
      },
    },
  });

export default materialHistoryFactory;
