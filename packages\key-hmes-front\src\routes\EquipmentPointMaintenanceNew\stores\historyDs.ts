import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.cassetteMaterial';

const historyFactory = () =>
  new DataSet({
    primaryKey: 'hisKeyId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'creationDateFrom',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.creationDateFrom`).d('创建时间从'),
        },
        {
          name: 'creationDateTo',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.creationDateTo`).d('创建时间至'),
        },
      ]
    }),
    fields: [
      {
        name: 'assemblePointCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.assemblePointCode`).d('装配点编码'),
      },
      {
        name: 'description',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.description`).d('装配点描述'),
      },
      {
        name: 'eventId',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.eventId`).d('事件ID'),
      },
      {
        name: 'assemblePointType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.assemblePointType`).d('装配点类型'),
      },
      {
        name: 'enableFlag',
        lookupCode: 'MT.YES_NO',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.enableFlag`).d('有效性'),
      },
      {
        name: 'createdByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.createdByName`).d('创建人'),
      },
      {
        name: 'creationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.creationDate`).d('创建时间'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'GET',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-his-assembly-point/assembly-point/his/ui`,
        };
      },
    },
  });

export default historyFactory;
