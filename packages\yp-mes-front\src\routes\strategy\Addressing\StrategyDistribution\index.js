/**
 * @Description: 寻址策略列表
 * @Author: <<EMAIL>>
 * @Date: 2021-09-06 10:52:19
 * @LastEditTime: 2021-10-09 09:54:12
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { DataSet, Table, Switch, Lov } from 'choerodon-ui/pro';
import { Badge, Popconfirm } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { Button as PermissionButton } from 'components/Permission';
import { getResponse } from '@utils/utils';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';

import { tableDS } from '../stores/StrategyDistributionListDS';
import { formDS } from '../stores/StrategyDetailDS';

const modelPrompt = 'tarzan.addressing.strategy';
const tenantId = getCurrentOrganizationId();

const StrategyList = props => {
  const {
    match: {
      path,
      params: { id },
    },
  } = props;

  const dataSet = useMemo(() => new DataSet({ ...tableDS() }), []);

  const formDs = useMemo(() => {
    return new DataSet(formDS());
  }, []);

  useEffect(() => {
    queryList(id);
    queryForm(id);
  }, [id]);

  // 查询上方表单,下方表格
  const queryForm = addressingStrategyId => {
    formDs.queryParameter = {
      addressingStrategyId,
    };
    formDs.query().then(res => {
      if (res && res.rows) {
        setAddressingStrategyCode(res.rows.addressingStrategyCode);
      }
    });
  };

  const queryList = addressingStrategyId => {
    dataSet.queryParameter = {
      addressingStrategyId,
      addressingStrategyFlag: 'Y',
    };
    dataSet.query(props?.dataSet?.currentPage).then(res => {
      if (res && res.rows) {
        setDuartyTable(!dataSet.dirty);
        setCanEdit(false);
      }
    });
  };

  const [duartyTable, setDuartyTable] = useState(true);
  const [canEdit, setCanEdit] = useState(false);

  const [addressingStrategyCode, setAddressingStrategyCode] = useState(undefined);

  const handleAddAssemblePoint = () => {
    const newRecord = dataSet.create();
    newRecord.set('addressingStrategyCode', addressingStrategyCode);
    newRecord.set('addressingStrategyId', id);
    setDuartyTable(!dataSet.dirty);
  };

  const deletePointRecord = record => {
    dataSet.remove(record);
    setDuartyTable(!dataSet.dirty);
  };

  const switchRecord = () => {
    setDuartyTable(!dataSet.dirty);
  };

  const handleSave = async () => {
    const validate = await dataSet.validate();
    if (validate) {
      const _data = [];
      dataSet.forEach(record => {
        if (record.dirty) {
          _data.push(record.toData());
        }
      });

      const response = await request(
        `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-add-strategy-assign/save/ui`,
        {
          method: 'POST',
          body: _data,
        },
      );
      const res = getResponse(response);
      if (res && res.success) {
        queryList(id);
      }
    }
  };

  const handleCancel = () => {
    queryList(id);
  };

  const rowSiteChange = record => {
    if (record.get('materialType')) {
      record.set('materialType', undefined);
    }
    if (record.get('locator')) {
      record.set('locator', undefined);
    }
    if (record.get('materialCategory')) {
      record.set('materialCategory', undefined);
    }
  };

  const columns = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          funcType="flat"
          onClick={handleAddAssemblePoint}
          shape="circle"
          size="small"
          disabled={!canEdit}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: 'center',
      width: 70,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`${modelPrompt}.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deletePointRecord(record)}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={record.status !== 'add' || !canEdit}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'site',
      width: 120,
      editor: record =>
        record.status === 'add' && (
          <Lov
            noCache
            dataSet={dataSet}
            placeholder=" "
            name="site"
            onChange={() => {
              rowSiteChange(record);
            }}
          />
        ),
    },
    {
      name: 'locator',
      width: 140,
      editor: record =>
        record.status === 'add' && <Lov noCache dataSet={dataSet} placeholder=" " name="locator" />,
    },
    {
      width: 140,
      name: 'locatorName',
    },
    {
      name: 'material',
      width: 140,
      editor: record =>
        record.status === 'add' && (
          <Lov noCache dataSet={dataSet} placeholder=" " name="material" />
        ),
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'materialCategory',
      width: 140,
      editor: record =>
        record.status === 'add' && (
          <Lov noCache dataSet={dataSet} placeholder=" " name="materialCategory" />
        ),
    },
    {
      name: 'materialCategoryName',
      width: 140,
    },
    {
      name: 'businessTypeObj',
      width: 140,
      editor: record =>
        record.status === 'add' && (
          <Lov noCache dataSet={dataSet} placeholder=" " name="businessTypeObj" />
        ),
    },
    {
      name: 'addressingStrategyCode',
      width: 140,
    },
    {
      name: 'enableFlag',
      align: 'center',
      editor: () => canEdit && <Switch onChange={switchRecord} />,
      renderer: ({ value }) => {
        return (
          // @ts-ignore
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.enable').d('启用')
                : intl.get('tarzan.common.label.disable').d('禁用')
            }
          />
        );
      },
    },
  ];

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.organizationStrategy`).d('组织策略分配')}
        backPath={`/hmes/strategy/addressing/detail/${id}`}
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-分配-编辑新建删除复制按钮',
                },
              ]}
              color={ButtonColor.primary}
              disabled={duartyTable}
              icon="save"
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton type="c7n-pro" icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-分配-编辑新建删除复制按钮',
              },
            ]}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Table dataSet={dataSet} columns={columns} highLightRow={false} />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.addressing.strategy', 'tarzan.common'],
})(StrategyList);
