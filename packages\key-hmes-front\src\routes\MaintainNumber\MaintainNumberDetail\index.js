/**
 * maintainNumberList - 编码规则维护
 * @date: 2020-5-21
 * @author: jrq <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2019, Hand
 */
import React from 'react';
import { Button, Form, Modal, Spin } from 'hzero-ui';
import { Collapse } from 'choerodon-ui';
import { connect } from 'dva';
import { Header, Content } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { verifyObjectHasValue } from '@utils/utils';
import DisplayForm from './DisplayForm';
import CompositionRule from './CompositionRule';

const { confirm } = Modal;
const { Panel } = Collapse;
const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';

const INPUT_MAP = ['FIXED_VALUE', 'SERIAL', 'DATE', 'TIME', 'SPECIFIC_OBJECT', 'INPUT_VALUE'];

/**
 * 物料存储属性维护编辑
 * @extends {Component} - React.Component
 * @reactProps {Object} [match={}] - react-router match路由信息
 * @reactProps {Object} [history={}]
 * @reactProps {Object} maintainNumber - 数据源
 * @reactProps {Object} form - 表单对象
 * @reactProps {Function} [dispatch=function(e) {return e;}] - redux dispatch方法
 * @return React.element
 */
@connect(({ maintainNumber, loading }) => ({
  maintainNumber,
  saveLoading: loading.effects['maintainNumber/saveMaintainNumber'],
  queryLoading: loading.effects['maintainNumber/fetchMaintainNumberDetail'],
}))
@formatterCollections({ code: ['tarzan.mes.maintainNumber', 'tarzan.common'] })
@Form.create({ fieldNameProp: null })
export default class MainTainNumberDetail extends React.Component {
  form;

  compositionRule;

  state = {
    canEdit: false,
  };

  componentDidMount() {
    const {
      dispatch,
      match,
      maintainNumber: { maintainNumberDetail = {} },
    } = this.props;
    const kid = match.params.id;
    dispatch({
      type: 'maintainNumber/getUserRole',
    });
    dispatch({
      type: 'maintainNumber/fetchDateFormatList',
      payload: {
        module: 'GENERAL',
        typeGroup: 'NUMRANGE_DATE_FORMAT',
      },
    });
    dispatch({
      type: 'maintainNumber/fetchTimeFormatList',
      payload: {
        module: 'GENERAL',
        typeGroup: ' NUMRANGE_TIME_FORMAT',
      },
    });
    dispatch({
      type: 'maintainNumber/fetchNumLevelList',
      payload: {
        module: 'GENERAL',
        typeGroup: 'NUMRANGE_NUM_LEVEL',
      },
    });
    dispatch({
      type: 'maintainNumber/fetchNumAlertTypeList',
      payload: {
        module: 'GENERAL',
        typeGroup: 'NUMRANGE_ALERT_TYPE',
      },
    });
    dispatch({
      type: 'maintainNumber/fetchNumRadixList',
      payload: {
        module: 'GENERAL',
        typeGroup: 'NUMRANGE_RADIX_TYPE',
      },
    });
    dispatch({
      type: 'maintainNumber/fetchNumResetTypeList',
      payload: {
        module: 'GENERAL',
        typeGroup: 'NUMRANGE_RESET_TYPE',
      },
    });

    if (kid === 'create') {
      this.setState({
        canEdit: true,
      });
      return;
    }
    if (JSON.stringify(maintainNumberDetail) !== '{}') {
      return;
    }
    this.queryMaintainNumberDetail(kid);
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'maintainNumber/clear',
    });
  }

  //  获取号码段详细信息
  queryMaintainNumberDetail = kid => {
    if (kid !== 'create') {
      this.props
        .dispatch({
          type: 'maintainNumber/fetchMaintainNumberDetail',
          payload: {
            ruleId: kid,
          },
        })
        .then(() => {
          this.compositionRule.initCompositionRule();
        });
    } else {
      this.compositionRule.initCompositionRule(true);
    }
  };

  changeEditStatus = () => {
    this.setState({
      canEdit: !this.state.canEdit,
    });
  };

  saveConfim = () => {
    const { match } = this.props;
    const kid = match.params.id;
    const that = this;
    if (kid === 'create') {
      this.saveAll();
      return;
    }
    confirm({
      title: intl.get('tarzan.mes.maintainNumber.info.saveTitle').d('编码规则维护'),
      content: intl
        .get('tarzan.mes.maintainNumber.info.saveInfo')
        .d('保存后可能会导致正在使用该编码规则的用户短暂卡顿！'),
      cancelText: intl.get('tarzan.common.button.cancel').d('取消'),
      okText: intl.get('tarzan.common.button.confirm').d('确认'),
      onOk() {
        that.saveAll();
      },
      onCancel() {},
    });
  };

  saveAll = () => {
    const {
      match,
      maintainNumber: { maintainNumberDetail = {} },
    } = this.props;
    const { objectColumnCodeList = [] } = maintainNumberDetail;
    const kid = match.params.id;
    this.form.validateFields((err, values) => {
      if (!err) {
        const rules = [];
        this.compositionRule.state.rulesList.forEach((item, index) => {
          if (INPUT_MAP[item.numRule - 1]) {
            rules.push({
              ...item,
              ruleType: INPUT_MAP[item.numRule - 1],
              ruleSequence: index + 1,
            });
          }
        });
        const saveData = {
          ...values,
          ruleId: kid === 'create' ? '' : kid,
          numExample: this.compositionRule.getExampleInputValue(),
          objectColumnCodeList,
          rules,
        };

        //  校验是否至少启用一条号段规则
        const flag = this.compositionRule.validateInputList();
        if (!flag) {
          notification.warning({
            message: intl
              .get(`${modelPrompt}.validation.selectRequired`)
              .d('请至少启用一条号段规则!'),
            duration: 5,
          });
          return;
        }
        const exampleInputLength = this.compositionRule.getExampleInputLength();
        if (exampleInputLength > 40) {
          notification.warning({
            message: intl
              .get(`${modelPrompt}.validation.lengthLimit`)
              .d('号段总长度不允许超过40位!'),
            duration: 5,
          });
          return;
        }
        //  取当前展示的form表单，检查是否符合规则，因为ruleInput的逻辑是切换时校验，用以预防用户直接报错
        const { ruleComponent } = this.compositionRule.rulesBlock;
        if (ruleComponent) {
          ruleComponent.validateFields(error => {
            if (!error) {
              this.onSave(saveData);
            }
          });
        } else {
          this.onSave(saveData);
        }
      }
    });
  };

  onSave = saveData => {
    const { dispatch, history, location } = this.props;
    const { state: locationState = {} } = location;
    const that = this;
    dispatch({
      type: 'maintainNumber/saveMaintainNumber',
      payload: {
        ...saveData,
      },
    }).then(async res => {
      if (res) {
        notification.success();
        history.push({
          pathname: `/hmes/mes/maintain-number-new/detail/${res.rows}`,
          state: verifyObjectHasValue(locationState) ? { ...locationState } : {},
        });
        await this.props.dispatch({
          type: 'maintainNumber/fetchMaintainNumberDetail',
          payload: {
            ruleId: res.rows,
          },
        });
        this.setState({
          canEdit: false,
        });
        setTimeout(() => {
          that.compositionRule.resetNumRangeRuleId();
        }, 0);
      }
    });
  };

  handleCancel = async () => {
    const { match, history, location } = this.props;
    const { state: locationState = {} } = location;
    const kid = match.params.id;
    const basePath = match.path.substring(0, match.path.indexOf('/detail'));
    const that = this;
    if (kid === 'create') {
      history.push({
        pathname: `${basePath}/list`,
        state: verifyObjectHasValue(locationState) ? { ...locationState } : {},
      });
      return;
    }
    await this.props.dispatch({
      type: 'maintainNumber/fetchMaintainNumberDetail',
      payload: {
        ruleId: kid,
      },
    });
    this.form.resetFields();
    //  取当前展示的form表单，检查是否符合规则，因为ruleInput的逻辑是切换时校验，用以预防用户直接报错
    const { ruleComponent } = this.compositionRule.rulesBlock;
    if (ruleComponent) {
      ruleComponent.resetFields();
    }
    this.setState({
      canEdit: false,
    });
    setTimeout(() => {
      that.compositionRule.resetNumRangeRuleId();
    }, 0);
  };

  //  当修改编码对象后，需要重新查询改编码对象下的编码对象属性，并清空调用标准对象编码的rule
  handleChangeObjectCode = objectCode => {
    const {
      dispatch,
      maintainNumber: { maintainNumberDetail },
    } = this.props;
    if (objectCode) {
      dispatch({
        type: 'maintainNumber/fetchObjectColumnCodeList',
        payload: {
          objectCode,
        },
      }).then(res => {
        dispatch({
          type: 'maintainNumber/updateState',
          payload: {
            maintainNumberDetail: {
              ...maintainNumberDetail,
              objectColumnCodeList: !!res && res.success ? res.rows : [],
            },
          },
        });
      });
    }
    //  调用子类方法，重置正在使用【调用标准对象编码】的rule
    this.compositionRule.resetObjectCode();
  };

  onRefCompositionRule = (ref = {}) => {
    this.compositionRule = ref;
  };

  onRef = (ref = {}) => {
    this.form = (ref.props || {}).form;
  };

  headerOnBack = basePath => {
    const { history, location } = this.props;
    const { state: locationState = {} } = location;
    history.push({
      pathname: `${basePath}/list`,
      state: verifyObjectHasValue(locationState) ? { ...locationState } : {},
    });
  };

  /**
   * 渲染方法
   * @returns
   */
  render() {
    const {
      match,
      saveLoading,
      queryLoading,
      maintainNumber: { maintainNumberDetail = {} },
      match: { path },
    } = this.props;
    const basePath = match.path.substring(0, match.path.indexOf('/detail'));
    const kid = match.params.id;
    const { ruleId = '' } = maintainNumberDetail;
    const { canEdit } = this.state;
    return (
      <div className="hmes-style">
        <Header
          title={intl.get('tarzan.mes.maintainNumber.title.maintainNumber').d('编码规则维护')}
          backPath={`${basePath}/list`}
          onBack={this.headerOnBack.bind(this, basePath)}
        >
          {canEdit ? (
            <>
              <Button type="primary" icon="save" loading={saveLoading} onClick={this.saveConfim}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={this.handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              onClick={this.changeEditStatus}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Spin spinning={queryLoading || false}>
            <Collapse bordered={false} defaultActiveKey={['basicProps', 'compositionRules']}>
              <Panel
                header={intl.get('tarzan.mes.maintainNumber.title.basicProps').d('基本属性')}
                key="basicProps"
              >
                <DisplayForm
                  key={ruleId}
                  changeObjectCode={this.handleChangeObjectCode}
                  onRef={this.onRef}
                  canEdit={canEdit}
                  kid={kid}
                />
              </Panel>
              <Panel
                header={intl.get('tarzan.mes.maintainNumber.title.compositionRules').d('组成规则')}
                key="compositionRules"
              >
                <CompositionRule
                  onRef={this.onRefCompositionRule}
                  key={ruleId}
                  canEdit={canEdit}
                  ruleId={kid}
                />
              </Panel>
            </Collapse>
          </Spin>
        </Content>
      </div>
    );
  }
}
