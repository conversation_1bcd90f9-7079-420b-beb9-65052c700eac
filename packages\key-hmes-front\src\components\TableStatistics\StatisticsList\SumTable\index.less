//  -----------Start 统计信息Table样式配置-----------
.statistical {
  width: 100%;
  max-height: 300px;
  overflow: auto;
}
.statistical table {
  // margin-top: 13px;
  border-spacing: 0;
  text-align: center;
}

.statistical table tr th {
  padding-left: 8px;
  padding-right: 8px;
  white-space: nowrap;
  border: 1px solid #e8e8e8;
}
.statistical table tr:not(:first-child) td {
  white-space: nowrap;
}

.statistical table tr:nth-child(4) td {
  white-space: nowrap;
  border-bottom: none;
  border-right: none;
}

.statistical table tr:nth-child(5) td {
  white-space: nowrap;
  border-right: none;
}

.statistical table tr td {
  white-space: nowrap;
  border: none;
}

// 子表格样式
// .statistical .information{
//   width: 300px;
//   overflow: scroll;
// }
.statistical .information table {
  text-align: left;
  height: 49px;
  margin: 0;
  border-spacing: 0;
  border-top: none;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.statistical .information table tr th {
  background-color: #f5f5f5;
  border: 1px solid #e8e8e8;
  padding-left: 8px;
  padding-right: 8px;
  width: 112px;
}

.statistical .information table tr td {
  background-color: #f0fffe;
  border: 1px solid #e8e8e8;
  padding-left: 16px;
  padding-right: 16px;
  width: 112px;
}

.statistical .information table tr.value th {
  background: rgb(240, 255, 254);
  color: #333;
}

.totalname {
  background-color: #6b686747;
  color: #1b1a1aa6;
}
//  -----------End 统计信息外层Table样式配置-----------
