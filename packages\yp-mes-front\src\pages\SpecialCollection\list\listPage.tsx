import React, { FC, useEffect } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useDataSetEvent } from 'utils/hooks';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import queryString from 'query-string';
import { openTab } from 'utils/menuTab';
import { Collapse } from 'choerodon-ui';
import axios from 'axios';
import listLineFactory from '../stores/listLinePageDs';
import listPageFactory from '../stores/listPageDs';

const Panel = Collapse.Panel;
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
  lineDs: DataSet;
}

const modelPrompt = 'tarzan.ass.specialCollection';

const ListPageComponent: FC<ListPageProps> = ({ listDs, lineDs, history }) => {
  useEffect(() => {
    listDs.query();
  }, []);

  useDataSetEvent(listDs, 'load', () => {
    if (listDs.toData().length > 0) {
      lineDs.setQueryParameter('keyId', listDs.toData()[0].keyId);
      lineDs.query();
    } else {
      lineDs.loadData([]);
    }
  });

  const columns: ColumnProps[] = [
    {
      name: 'tagCode',
      width: 250,
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => {
              onHandleDetail(record?.get('keyId'));
            }}
          >
            {record?.get('tagCode')}
          </a>
        );
      },
    },
    {
      name: 'tagName',
      width: 180,
    },
    {
      name: 'equipmentCode',
      width: 150,
    },
    {
      width: 180,
      name: 'materialCode',
    },
    {
      width: 180,
      name: 'missingNcCode',
    },
    {
      width: 180,
      name: 'defaultNcCode',
    },
    {
      name: 'userName',
    },
    {
      width: 150,
      name: 'lastUpdateDate',
    },
    {
      name: 'remark',
    },
    {
      name: 'conditionTagCode',
      width: 150,
    },
    {
      name: 'conditionTagName',
      width: 150,
    },
  ];

  const lineColumns: ColumnProps[] = [
    {
      name: 'ordinalLine',
    },
    {
      name: 'decide',
    },
    {
      name: 'trueValue',
    },
  ];

  const onHandleDetail = id => {
    history.push(`/hmes/specialCollection/detail/${id}`);
  };

  const handleCreate = () => {
    history.push(`/hmes/specialCollection/detail/create`);
  };

  const headerRowClick = record => {
    lineDs.setQueryParameter('keyId', record.get('keyId'));
    lineDs.query();
  };

  // 导入
  const handleImport = () => {
    openTab({
      key: '/himp/commentImport/HME.LOOK_PROCESS_IMPORT',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const handleDelete = async () => {
    const selected = listDs.selected?.map(i => i.get('keyId'));
    if (selected.length === 0) {
      notification.warning({
        message: intl.get('hzero.common.message.validation.atLeast').d('请至少选择一条数据'),
      });
      return;
    }
    const url = `${
      BASIC.HMES_BASIC
    }/v1/${getCurrentOrganizationId()}/hme-look-process/head/delete?lookProcessIds=${selected}`;
    const res: any = await axios.get(url);
    if (res && res?.success) {
      notification.success({});
      listDs.query();
    } else {
      notification.error({
        message: res?.message,
        description: '',
      });
    }
  };
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.specialCollection`).d('特殊收集项校验（K值）')}>
        <Button icon="add" color={ButtonColor.primary} onClick={handleCreate}>
          {intl.get('tarzan.common.button.create').d('新增')}
        </Button>
        <Button color={ButtonColor.primary} onClick={handleImport}>
          {intl.get(`${modelPrompt}.import`).d('导入')}
        </Button>
        <Button color={ButtonColor.primary} onClick={handleDelete}>
          {intl.get(`${modelPrompt}.delete`).d('删除')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="specialCollection"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
          queryFieldsLimit={4} // 头部显示的查询字段的数量
          searchCode="specialCollection" // 动态筛选条后端接口唯一编码
          customizedCode="specialCollection" // 个性化编码
        />
        <Collapse bordered={false} defaultActiveKey={['1']}>
          <Panel header={intl.get(`${modelPrompt}.title.lineMessage`).d('行信息')} key="1">
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              key="specialCollectionLine"
              queryBar={TableQueryBarType.none}
              queryFieldsLimit={4} // 头部显示的查询字段的数量
              searchCode="specialCollectionLine" // 动态筛选条后端接口唯一编码
              customizedCode="specialCollectionLine" // 个性化编码
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    const lineDs = listLineFactory();

    return {
      listDs,
      lineDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.ass.specialCollection'],
})(ListPage);
