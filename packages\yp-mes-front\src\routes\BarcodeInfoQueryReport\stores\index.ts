/*
 * @Description: 条码信息查询报表-列表页DS
 * @Author: <<EMAIL>>
 * @Date: 2024-01-09 15:46:34
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-01-22 18:43:52
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.barcodeInfoQueryReport';
const tenantId = getCurrentOrganizationId();
const endUrl = "";

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'identificationList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      transformRequest: value => (value ? value?.split(',') : undefined),
      dynamicProps: {
        required: ({ record }) => !record?.get('workOrderNum')?.length && !record?.get('materialIdList')?.length,
      },
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      transformRequest: value => (value ? value?.split(',') : undefined),
      dynamicProps: {
        required: ({ record }) => !record?.get('identificationList')?.length && !record?.get('materialIdList')?.length,
      },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL.PERMISSION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        required: ({ record }) => !record?.get('identificationList')?.length && !record?.get('workOrderNum')?.length,
      },
    },
    {
      name: 'materialIdList',
      bind: 'materialLov.materialId',
    },
    {
      name: 'containerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
      lovCode: 'YP_MES.CONTAINER',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'containerId',
      bind: 'containerLov.containerId',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
      lovCode: 'MT.MODEL.PRODLINE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.prodLineId',
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'lotNumList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotNumList`).d('批次号'),
      transformRequest: value => (value ? value?.split(',') : undefined),
    },
    {
      name: 'workOrderLevel',
      type: FieldType.string,
      lookupCode: 'HME.WO_LEVEL',
      lovPara: { tenantId },
      label: intl.get(`${modelPrompt}.workOrderLevel`).d('等级'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('生产状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=EO_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'equipmentIdList',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('当前工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      textField: 'operationName',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'operationNameList',
      bind: 'operationLov.operationName',
    },
    {
      name: 'eoStepStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentOperationStatus`).d('当前工艺状态'),
      lookupCode: 'HME.EO_STEP_STATUS',
    },
    {
      name: 'startDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('生产开始时间'),
      max: 'startDateEnd',
    },
    {
      name: 'startDateEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('生产结束时间'),
      min: 'startDateFrom',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorLov`).d('库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      lovPara: { tenantId },
      multiple: true,
    },
    {
      name: 'locatorIds',
      bind: 'locatorLov.locatorId',
    },
  ],
  fields: [
    {
      name: 'eoIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoIdentification`).d('条码号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'eoQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eoQty`).d('数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位名称'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'specifiedLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specifiedLevel`).d('电池等级'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('生产状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=EO_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('当前工位'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('当前工位名称'),
    },
    {
      name: 'routerStepStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerStepStatus`).d('当前工艺'),
    },
    {
      name: 'wipStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wipStatus`).d('当前工艺状态'),
      lookupCode: 'HME.EO_STEP_STATUS',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'marking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marking`).d('MARKING'),
    },
    {
      name: 'ncIncidentNumNotClosed',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncIncidentNumNotClosed`).d('未关闭不良单据'),
    },
    {
      name: 'disposalFunctionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalFunctionDesc`).d('不良处置方式'),
    },
    {
      name: 'reworkFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkFlag`).d('返修标识'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('返修开始工艺'),
    },
    {
      name: 'endOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.endOperationName`).d('返修结束工艺'),
    },
    {
      name: 'interceptionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.interceptionFlag`).d('让步拦截标识'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'interceptionOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.interceptionOperationName`).d('让步拦截工艺'),
    },
    {
      name: 'degradeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeFlag`).d('降级标识'),
      lookupCode: 'MT.FLAG',
      lovPara: { tenantId },
    },
    {
      name: 'actualStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualStartTime`).d('生产开始时间'),
    },
    {
      name: 'actualEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.actualEndTime`).d('生产结束时间'),
    },
    {
      name: 'mtModLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mtModLocatorCode`).d('库位编码'),
    },
    {
      name: 'mtModLocatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mtModLocatorName`).d('库位名称'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'blueGlueCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.blueGlueCode`).d('蓝胶码'),
    },
    {
      name: 'gbCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gbCode`).d('国标码'),
    },
    {
      name: 'operation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operation`).d('操作'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}${endUrl}/v1/${tenantId}/hme-identification/head/query`,
        method: 'POST',
        transformResponse: (val) => {
          const { success, rows, message } = JSON.parse(val);
          if (message) {
            notification.error({ message });
          }
          return {
            success,
            message,
            rows: rows || { content: [] },
          };
        },
      };
    },
  },
});

const historyDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'requestId',
  fields: [
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('操作时间'),
    },
    {
      name: 'eventId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
    },
    {
      name: 'eventTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型'),
    },
    {
      name: 'requestTypeId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.requestTypeId`).d('事件请求ID'),
    },
    {
      name: 'requestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('事件请求类型编码'),
    },
    {
      name: 'requestTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeDesc`).d('事件请求类型描述'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.realName`).d('操作人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}${endUrl}/v1/${tenantId}/hme-identification/history/query`,
        method: 'GET',
      };
    },
  },
});

const eoAttrFields = {
  eoSpecifiedLevel: intl.get(`${modelPrompt}.eoSpecifiedLevel`).d('等级'),
  supplierName: intl.get(`${modelPrompt}.supplierName`).d('电解液供应商'),
  eoTraceLevel: intl.get(`${modelPrompt}.eoTraceLevel`).d('追溯层级'),
  reworkStartFlag: intl.get(`${modelPrompt}.reworkStartFlag`).d('返修开始标识'),
  reworkWorkcell: intl.get(`${modelPrompt}.reworkWorkcell`).d('返修工序编码'),
  eoConcessiveInterceptionOpName: intl.get(`${modelPrompt}.eoConcessiveInterceptionOpName`).d('拦截工艺名称'),
  eoManPowerSupplierName: intl.get(`${modelPrompt}.eoManPowerSupplierName`).d('主粉供应商'),
  reworkOperationName: intl.get(`${modelPrompt}.reworkOperationName`).d('返修工艺'),
  overTimeReleaseFlag: intl.get(`${modelPrompt}.overTimeReleaseFlag`).d('超限放行标识'),
  overTimeReleaseTime: intl.get(`${modelPrompt}.overTimeReleaseTime`).d('超期放行时间'),
  concesInterOperationName: intl.get(`${modelPrompt}.concesInterOperationName`).d('让步拦截工艺'),
  eoDisposalFunctionCode: intl.get(`${modelPrompt}.eoDisposalFunctionCode`).d('处置方法编码'),
  eoDegradeLevel: intl.get(`${modelPrompt}.eoDegradeLevel`).d('降级等级'),
  eoMarking: intl.get(`${modelPrompt}.eoMarking`).d('标记'),
  partialScrapFlag: intl.get(`${modelPrompt}.partialScrapFlag`).d('部分报废标识'),
  partialScrapShiftDate: intl.get(`${modelPrompt}.partialScrapShiftDate`).d('部分报废班次日期'),
  partialScrapShiftCode: intl.get(`${modelPrompt}.partialScrapShiftCode`).d('部分报废班次编码'),
};

const materialAttrFields = {
  autoCode: intl.get(`${modelPrompt}.autoCode`).d('自动化系统'),
  collodionBarcode: intl.get(`${modelPrompt}.collodionBarcode`).d('胶棉条码'),
  lotConcessiveInterceptionFlag: intl.get(`${modelPrompt}.lotConcessiveInterceptionFlag`).d('拦截标识'),
  lotConcessiveInterceptionOpName: intl.get(`${modelPrompt}.lotConcessiveInterceptionOpName`).d('拦截工艺'),
  containerCode: intl.get(`${modelPrompt}.containerCode`).d('容器/包装编码'),
  containerNumber: intl.get(`${modelPrompt}.containerNumber`).d('托序号'),
  containerQty: intl.get(`${modelPrompt}.containerQty`).d('容器/包装数量'),
  containerType: intl.get(`${modelPrompt}.containerType`).d('容器/包装类型'),
  containerLabel: intl.get(`${modelPrompt}.containerLabel`).d('客户标签'),
  degradeFlag: intl.get(`${modelPrompt}.degradeFlag`).d('降级标识'),
  lotDegradeLevel: intl.get(`${modelPrompt}.lotDegradeLevel`).d('降级等级'),
  deliverDocLine: intl.get(`${modelPrompt}.deliverDocLine`).d('送货单行号'),
  deliverDocNum: intl.get(`${modelPrompt}.deliverDocNum`).d('送货单号'),
  directoryName: intl.get(`${modelPrompt}.directoryName`).d('国家危险废物名录名称'),
  disposalFlag: intl.get(`${modelPrompt}.disposalFlag`).d('处置标识'),
  lotDisposalFunctionCode: intl.get(`${modelPrompt}.lotDisposalFunctionCode`).d('处置方法编码'),
  firstArticleFlag: intl.get(`${modelPrompt}.firstArticleFlag`).d('首件标识'),
  lotManPowerSupplierName: intl.get(`${modelPrompt}.lotManPowerSupplierName`).d('主粉供应商'),
  lotMarking: intl.get(`${modelPrompt}.lotMarking`).d('标记'),
  packingNumber: intl.get(`${modelPrompt}.packingNumber`).d('包装序号'),
  produceArea: intl.get(`${modelPrompt}.produceArea`).d('产生车间/区域'),
  produceLot: intl.get(`${modelPrompt}.produceLot`).d('产生批次'),
  produceProcess: intl.get(`${modelPrompt}.produceProcess`).d('产生工序'),
  produceTime: intl.get(`${modelPrompt}.produceTime`).d('产生时间'),
  produceLine: intl.get(`${modelPrompt}.produceLine`).d('电芯产线'),
  produceAgent: intl.get(`${modelPrompt}.produceAgent`).d('产生部门经办人'),
  receipeTime: intl.get(`${modelPrompt}.receipeTime`).d('收货时间'),
  remark: intl.get(`${modelPrompt}.remark`).d('备注'),
  scrapReason: intl.get(`${modelPrompt}.scrapReason`).d('报废原因'),
  specifiedInstructionId: intl.get(`${modelPrompt}.specifiedInstructionId`).d('指定指令'),
  lotSpecifiedLevel: intl.get(`${modelPrompt}.lotSpecifiedLevel`).d('等级'),
  trace: intl.get(`${modelPrompt}.trace`).d('去向'),
  lotTraceLevel: intl.get(`${modelPrompt}.lotTraceLevel`).d('追溯层级'),
  transferAgent: intl.get(`${modelPrompt}.transferAgent`).d('运送部门经办人'),
  unitName: intl.get(`${modelPrompt}.unitName`).d('行业俗称/单位内部名称'),
  wasteContainer: intl.get(`${modelPrompt}.wasteContainer`).d('产生危险废物设施编码'),
};

const attributeDS: (attributeType) => DataSetProps = attributeType => ({
  primaryKey: 'keyId',
  selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'attrMeaning',
      label:
        attributeType === 'EO'
          ? intl.get(`${modelPrompt}.eoAttrMeaning`).d('EO扩展属性描述')
          : intl.get(`${modelPrompt}.materialLotAttrMeaning`).d('物料批扩展属性描述'),
      type: FieldType.string,
    },
    {
      name: 'attrValue',
      label: intl.get('tarzan.common.components.attrValue').d('扩展属性值'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}${endUrl}/v1/${tenantId}/hme-identification/ca/query`,
        method: 'GET',
        transformResponse: (value) => {
          const dataSource = JSON.parse(value)?.rows;
          const fieldsList = attributeType === 'EO' ? eoAttrFields : materialAttrFields;
          const fieldsKeys = Object.keys(fieldsList);
          const newData = fieldsKeys.map((item) => ({
            attrName: item,
            attrMeaning: fieldsList[item],
            attrValue: dataSource[item],
          }))
          return newData;
        },
      };
    },
  },
});

export { tableDS, historyDS, attributeDS };
