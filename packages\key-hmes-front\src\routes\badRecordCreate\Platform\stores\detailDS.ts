/**
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-16
 * @description 不良记录平台新建form表
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
// import uuid from 'uuid4';
import moment from 'moment';
import notification from 'utils/notification';

// const Host = '/yp-mes-20000'
const Host = `${BASIC.HMES_BASIC}`
const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  paging: false,
  autoCreate: true,
  selection: false,
  primaryKey: 'ncRecordId',
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-nc-record/platform/detail/query/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows = {}, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          return {
            ...rows,
            // disposalList,
          };
        },
      };
    },
  },
  fields: [
    {
      name: 'ncRecordId',
    },
    {
      name: 'ncRecordType',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
      lookupCode: 'HME.NC_RECORD_TYPE',
      lovPara: { tenantId },
      valueField: 'value',
      textField: 'meaning',
      dynamicProps: {
        disabled: ({ record }) => record.get('ncRecordId'),
      },
    },
    {
      name: 'ncRecordStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordStatus`).d('不良记录状态'),
      disabled: true,
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      defaultValue: 'NEW',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_RECORD_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipment`).d('单件码'),
      lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'equipmentCode',
      bind: 'equipmentLov.equipmentCode',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      required: true,
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      dynamicProps: {
        disabled: ({ record }) => record.get('ncRecordId'),
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'operation',
      label: intl.get(`${modelPrompt}.operation`).d('不良工艺'),
      type: FieldType.object,
      lovCode: 'MT.OPERATION',
      // required: true,
      disabled: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'operationId',
      bind: 'operation.operationId',
    },
    {
      name: 'operationName',
      bind: 'operation.operationName',
    },
    {
      name: 'materialLots',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      dynamicProps: {
        // required: ({record}) => {
        //   return record?.get('ncRecordType') === 'RM_NC'&&record?.get('materialLotId')&&record?.get('materialLotId').length===0
        // },
        // disabled: ({ record }) =>
        //   !(record?.get('siteId') && record?.get('operationId') && !record?.get('equipmentCode')),
      },
    },
    {
      name: 'materialLotList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      dynamicProps: {
        disabled: ({ record }) =>
          !(record?.get('siteId') && record?.get('operationId') && record?.get('equipmentCode')),
      },
    },
    {
      name: 'materialLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      lovCode: 'YP_MES.MES.MATERIAL_LOT_OK',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        disabled: ({ record }) => !(record?.get('siteId') && record?.get('operationId')),
        // required: ({record}) => record?.get('ncRecordType') === 'RM_NC'&&!record?.get('materialLots'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          enableFlag: 'Y',
        }),
      },
    },
    {
      name: 'materialLotId',
      bind: 'materialLotLov.materialLotId',
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      bind: 'materialLotLov.materialLotCode',
      multiple: true,
    },
    {
      name: 'eos',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('条码号'),
      dynamicProps: {
        // required: ({record}) => record?.get('ncRecordType') === 'EO_ALL_NC'&&record?.get('eoId')&&record?.get('eoId').length===0,
        // disabled: ({ record }) => !(record?.get('siteId') && record?.get('operationId')),
      },
    },
    {
      name: 'eoLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eoNum`).d('条码号'),
      lovCode: 'YP_MES.MES.EO_OK',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        disabled: ({ record }) => !(record?.get('siteId') && record?.get('operationId')),
        // required: ({record}) => record?.get('ncRecordType') === 'EO_ALL_NC'&&!record?.get('eos'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
      },
    },
    {
      name: 'eoId',
      bind: 'eoLov.eoId',
    },
    {
      name: 'eoNum',
      bind: 'eoLov.eoNum',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialNameAndRevision`).d('物料名称/版本'),
      disabled: true,
    },
    {
      name: 'ncStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncStartTime`).d('不良发生时间'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'ncRecordNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordNum`).d('不良记录编码'),
      disabled: true,
    },
  ],
});

const detailObjectDS: () => DataSetProps = () => ({
  autoQuery: false,
  paging: false,
  autoCreate: false,
  multiLine: true,
  primaryKey: 'ncRecordId',
  dataKey: 'rows',
  transport: {
    read: ({data}) => {
      return {
        url: `${Host}/v1/${tenantId}/hme-nc-record/material/lot/related`,
        method: 'POST',
        data: data.ids,
      };
    },
  },
  fields: [
    {
      name: 'materialLotId',
    },
    {
      name: 'workOrderId',
    },
    {
      name: 'workOrderNum',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'eoId',
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
    },
    {
      name: 'ncRecordId',
    },
    {
      name: 'uomId',
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'workOrderId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'workOrderQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.workOrderQty`).d('生产指令数量'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'supplierId',
      type: FieldType.number,
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'locatorId',
      type: FieldType.number,
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    },
    {
      name: 'containerId',
      type: FieldType.number,
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadContainerCode`).d('装载容器'),
    },
    {
      name: 'prodLineId',
      type: FieldType.number,
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线'),
    },
    {
      name: 'rootCauseEquipmentId',
    },
    {
      name: 'routerId',
    },
    {
      name: 'routerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线'),
    },
    {
      name: 'routerStepId',
    },
    {
      name: 'workcellId',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元'),
    },
    {
      name: 'operationId',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'equipmentId',
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备'),
    },
  ],
});

const detailLineDS: () => DataSetProps = () => ({
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows',
  primaryKey: 'ncRecordDetailId',
  paging: false,
  fields: [
    {
      name: 'ncRecordDetailId',
    },
    {
      name: 'lineNumber',
      label: intl.get('tarzan.common.label.serialNumber').d('序号'),
      defaultValue: 10,
    },
    {
      name: 'lineNum', // 组件选择时的唯一性索引
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      lovPara: { tenantId },
      required: true,
      ignore: FieldIgnore.always,
      textField: 'description',
      dynamicProps: {
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
      },
    },
    {
      name: 'ncCodeId',
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCodeDesc',
      bind: 'ncCodeLov.description',
    },
    {
      name: 'ncCodeStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeStatus`).d('不良代码状态'),
    },
    {
      name: 'ncCodeStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCodeStatusDesc`).d('不良代码状态'),
    },
    {
      name: 'rootCauseWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.rootCauseWorkcellName`).d('不良产生工作单元'),
      lovCode: 'MT.MODEL.WORKCELL_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
        lovPara: ({ dataSet }) => ({
          tenantId,
          siteId: dataSet.parent?.current?.get('siteId'),
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'rootCauseWorkcellName',
      bind: 'rootCauseWorkcellLov.workcellName',
      label: intl.get(`${modelPrompt}.rootCauseWorkcellName`).d('不良产生工作单元'),
    },
    {
      name: 'rootCauseWorkcellId',
      bind: 'rootCauseWorkcellLov.workcellId',
    },
    {
      name: 'rootCauseEquipmentCode',
      disabled: true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootCauseEquipmentCode`).d('不良产生设备'),
    },
    {
      name: 'rootCauseEquipmentId',
    },
    {
      name: 'rootCauseOperationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootCauseOperationCode`).d('不良产生工艺'),
      disabled: true,
    },
    {
      name: 'rootCauseOperationId',
    },
    {
      name: 'responsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('不良责任人'),
      lovCode: 'HIAM.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: { tenantId },
    },
    {
      name: 'responsibleUserId',
      bind: 'responsibleUserLov.id',
    },
    {
      name: 'responsibleUserName',
      bind: 'responsibleUserLov.realName',
    },
    {
      name: 'responsibleApartment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.responsibleApartment`).d('不良责任部门'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'mes',
      bucketDirectory: 'nc-record-platform-file',
      accept: ['.deb', '.txt', '.pdf', 'image/*'],
      viewMode: 'popup',
    },
  ],
});


export { detailDS, detailLineDS, detailObjectDS };
