import React, { useState, useMemo, useEffect } from 'react';
import { DataSet, Table, Button, Spin } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import request from 'utils/request';
import moment from 'moment';
import intl from 'utils/intl';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';

import { tableDS, assObjectsDS } from './stores/ShipmentReportPrintMaintenanceDS';
import LineList from './LineList';
import { Host } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.ShipmentReportPrintMaintenance';
// const Host = `/key-ne-focus-mes-38510`;

const tenantId = getCurrentOrganizationId();
const realName = getCurrentUser().realName;
const lastUpdateDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');

const ShipmentReportPrintMaintenance = () => {

  const [loading, setLoading] = useState(false);
  const [disabledFlag, setDisabledFlag] = useState(false);
  const [selectedRow, setSelectedRow] = useState([]);

  const assObjectsDs = useMemo(() => new DataSet(assObjectsDS()), []);
  const tableDs = useMemo(
    () =>
      new DataSet({
        ...tableDS(),
        events: {
          load: () => {
            assObjectsDs.loadData([]);
          },
        },
      }),
    [],
  );

  useEffect(() => {
    tableDs.query();
    // queryBasicData();
    tableDs.addEventListener('select', handleDataSetSelect);
    tableDs.addEventListener('unSelect', handleDataSetSelect);
  }, []);

  const handleDataSetSelect = () => {
    setSelectedRow(tableDs.selected);
    const selectedLength = tableDs.selected;
    if (selectedLength.length > 0) {
      const createlist = tableDs.toJSONData().map(item => item._status === 'create');
      if (createlist.includes(true)) {
        assObjectsDs.loadData([]);
        tableDs.unSelectAll();
        // setCreatFlag(true);
        notification.error({ message: intl
          .get(`${modelPrompt}.error.save.head`)
          .d(`请先保存头`) });
      } else {
        assObjectsDs.setQueryParameter(
          'shipmentReportTagIdList',
          [selectedLength[0].data.shipmentReportTagId],
        );
        assObjectsDs.query();
      }
    } else {
      setSelectedRow([]);
      assObjectsDs.loadData([]);
      setDisabledFlag(false);
    }
  };

  // 保存
  const handelSave = async () => {
    await tableDs.validate().then(valiResult => {
      assObjectsDs.validate().then(valiResult2 => {
        if (valiResult && valiResult2) {
          const tableList = tableDs.toJSONData();
          const assObjectsData = assObjectsDs.toJSONData();
          let listData = [];
          let ids = [];
          if (tableList.length || assObjectsData.length) {
            if (tableList.length > 0) {
              ids = tableList.map(item => item.shipmentReportTagId);
              listData = tableList.map(item => {
                return {
                  ...item,
                  lineDataList: [],
                };
              });
            }
            if (assObjectsData.length > 0) {
              if (tableList.length > 0) {
                if (ids.length && ids.includes(selectedRow[0].data.shipmentReportTagId)) {
                  listData = tableList.map(item => {
                    return {
                      ...item,
                      lineDataList:
                        item.shipmentReportTagId === selectedRow[0].data.shipmentReportTagId
                          ? assObjectsData
                          : [],
                    };
                  });
                } else {
                  tableList.push({ ...selectedRow[0].data, lineDataList: assObjectsData });
                  listData = tableList.map(item => {
                    return {
                      ...item,
                    };
                  });
                }
              } else {
                listData = [
                  {
                    ...selectedRow[0].data,
                    lineDataList: assObjectsData,
                  },
                ];
              }
            }
            setLoading(true);
            request(`${Host}/v1/${tenantId}/hme-shipment-report/save`, {
              method: 'post',
              body: listData,
            }).then(res => {
              setLoading(false);
              if (res && !res.failed) {
                notification.success();
                setDisabledFlag(false);
                setSelectedRow([]);
                tableDs.query();
                assObjectsDs.loadData([]);
              } else {
                notification.error({ message: res.message });
              }
            });
          } else {
            return notification.warning({
              message: intl
                .get(`${modelPrompt}.error.createEdit.data`)
                .d(`请先新建或修改数据`),
              placement: 'bottomRight',
            });
          }
        } else {
          return notification.warning({
            message: intl
              .get(`${modelPrompt}.error.required`)
              .d(`请填写必填项`),
            placement: 'bottomRight',
          });
        }
      });
    });
  };

  // 编辑按钮
  const handelEdit = () => {
    tableDs.records.forEach(item => {
      item.setState('editing', true);
    });
    setDisabledFlag(true);
  };

  // 取消按钮
  const handelCancel = () => {
    tableDs.records.forEach(item => {
      item.setState('editing', false);
    });
    tableDs.query();
    setDisabledFlag(false);
  };


  const handleCreate = () => {
    if (tableDs.selected.length > 0) {
      assObjectsDs.loadData([]);
      tableDs.unSelectAll();
    }
    tableDs.create({ shipmentReportTagId: '', realName, lastUpdateDate });
    tableDs.current.setState('editing', true);
    setDisabledFlag(true);
  };

  const handleCreateLine = () => {
    if (tableDs.selected.length) {
      const newLineNumber =
        (
          assObjectsDs.toData().sort((a, b) => b.serialNumber - a.serialNumber)[0] || {
            serialNumber: 0,
          }
        ).serialNumber || 0;
      assObjectsDs.create({
        shipmentReportTagId: tableDs.selected[0].data.shipmentReportTagId,
        shipmentReportTagLineId: '',
        realName,
        lastUpdateDate,
        serialNumber: parseInt(newLineNumber / 10, 10) * 10 + 10,
      });
    } else {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.error.check.head`)
          .d(`请先勾选头数据`),
        placement: 'bottomRight',
      });
    }
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          onClick={() => handleCreate()}
          funcType="flat"
          // shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      lock: 'left',
      renderer: ({ record }) => {
        return (
          (
            <Popconfirm
              title= {intl
                .get(`${modelPrompt}.error.delete`)
                .d(`是否确认删除？`)}
              onConfirm={() => {
                request(`${Host}/v1/${tenantId}/hme-shipment-report/delete`, {
                  method: 'post',
                  body: {
                    shipmentReportTagId: record.get('shipmentReportTagId'),
                  },
                }).then(res => {
                  if (res.success) {
                    tableDs.query();
                  } else {
                    notification.error({ message: res.message });
                  }
                });
                // tableDs.remove(record);
                // if (tableDs.toJSONData().length === 0) {
                //   setDisabledFlag(false);
                // }
              }}
            >
              <Button funcType="flat" icon="remove" shape="circle" size="small" disabled={!disabledFlag}/>
            </Popconfirm>
          )
        );
      },
    },
    // 物料编码
    {
      name: 'materialLov',
      align: 'left',
      renderer: ({ record }) => record?.get('materialCode'),
      editor: record => record?.getState('editing'),
    },
    // 物料版本
    {
      name: 'revisionCode',
      align: 'left',
      editor: record => record?.getState('editing'),
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
    },
    // 项目
    {
      name: 'item',
      align: 'left',
      editor: record => record?.getState('editing'),
    },
    // 产品简称
    {
      name: 'productShortName',
      align: 'left',
      editor: record => record?.getState('editing'),
    },
    // 产品型号
    {
      name: 'productModel',
      align: 'left',
      editor: record => record?.getState('editing'),
    },
    // 客户编码
    {
      name: 'customerLov',
      align: 'left',
      renderer: ({ record }) => record?.get('customerCode'),
      editor: record => record?.getState('editing'),
    },
    // 客户名称
    {
      name: 'customerName',
      align: 'left',
    },
  ];

  // 导入
  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/HME.SHIPMENT_REPORT_TAG`,
      title: intl.get('tarzan.hmes.ShipmentReportPrintMaintenance.import').d('发货报告打印项目维护导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('发货报告打印项目维护')}>
        {!disabledFlag && (
          <>
            <Button onClick={handelEdit} style={{ marginRight: 15 }} icon="edit" color="primary">
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
            <Button onClick={handleImport} style={{ marginRight: 15 }} color="primary" icon="daorucanshu">
              {intl.get('tarzan.common.button.repeat').d('导入')}
            </Button></>

        )}
        {disabledFlag && (
          <>
            <Button onClick={handelCancel}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
            <Button onClick={handelSave} style={{ marginRight: 15 }} icon="save" color="primary">
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Table
            dataSet={tableDs}
            columns={columns}
            style={{ height: 400 }}
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            searchCode="ShipmentReportPrintMaintenance"
            customizedCode="ShipmentReportPrintMaintenance"
          />
          <LineList
            assObjectsDs={assObjectsDs}
            canEdit={disabledFlag}
            handleCreateLine={handleCreateLine}
            shipmentReportTagId={selectedRow[0]?.data?.shipmentReportTagId}
          />
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.ShipmentReportPrintMaintenance', 'tarzan.common'],
})(ShipmentReportPrintMaintenance);
