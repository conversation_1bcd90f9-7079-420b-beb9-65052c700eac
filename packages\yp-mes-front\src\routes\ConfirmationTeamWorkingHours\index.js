/**
 * @Description:  班组工时确认-入口页
 */
import React, { useEffect } from 'react';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { headerTableDS } from './stores/ListDS';

const modelPrompt = 'tarzan.receive.confirmationTeamWorkingHours';

const ConfirmationTeamWorkingHours = props => {

  const {
    headerTableDs,
  } = props;

  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    if (props?.history?.action === 'PUSH') {
      headerTableDs.query(props.headerTableDs.currentPage);
    }
  }, []);

  const headerTableColumns = [
    {
      name: 'siteCode',
      // width: 140,
    },
    {
      name: 'handoverNum',
      width: 300,
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => {
              createDelivery(_, record)
            }}>
              {value}
            </a>
          </span>
        );
      },
    },
    {
      name: 'shiftTypeMeaning',
      // width: 140,
    },
    {
      name: 'prodLineCode',
      // width: 140,
    },
    {
      name: 'prodLineName',
      // width: 140,
    },
    {
      name: 'shiftDate',
    },
    {
      name: 'realName',
      // width: 140,
    },
  ];
  const createDelivery = (type, record) => {
    if(type === 'create') {
      props.history.push(`/hmes/confirmation-team-working-hours/detail/${type}`);
    } else {
      props.history.push({
        pathname: `/hmes/confirmation-team-working-hours/detail/${record?.get('handoverId')}`,
        state: {
          ...record.toData(),
        },
        query:{
          page: headerTableDs?.currentPage,
          pageSize: headerTableDs?.pageSize,
          searchParams: headerTableDs?.queryDataSet.toData(),
        },
      });
    }
  };

  return (
    <div className="hmes-style" style={{height: '98%', overflow: 'auto'}}>
      <Header title={intl.get(`${modelPrompt}.title`).d('班组工时确认')}>
        <Button type="c7n-pro"
          color={ButtonColor.primary}
          icon="add" onClick={() => createDelivery('create')}>
          {intl.get(`${modelPrompt}.button.createDelivery`).d('新建')}
        </Button>
      </Header>
      <Content>
        <Table
          searchCode="manHourIndex"
          customizedCode="manHourIndex"
          dataSet={headerTableDs}
          columns={headerTableColumns}
          highLightRow
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
        />
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.receive.confirmationTeamWorkingHours', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      return {
        headerTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_MATERIAL_LOT.QUERY`,
    ],
  }),
)(ConfirmationTeamWorkingHours);
