import React, { useState, useMemo, useEffect } from 'react';
import {
  DataSet,
  Table,
  Button,
  Form,
  TextField,
  Lov,
  Switch,
  NumberField,
  Select,
  Spin,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Collapse, Popconfirm, Badge } from 'choerodon-ui';
import { C7nFormItemSort } from '@components/tarzan-ui';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import myInstance from '@/utils/myAxios';
import { routerRedux } from 'dva/router';
import { isUndefined } from 'lodash';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { detailTableDS, formDS } from './stores/NationalCodingMaintenanceDS';
import { Host } from '@/utils/config';

// const Host1 = `/key-ne-focus-mes-20000`;
const modelPrompt = 'tarzan.message.message.NationalCodingMaintenance';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();

const Create = props => {
  const [loading, setLoading] = useState(false);
  const [title, setTitle] = useState('');
  const [disabledFlag, setDisabledFlag] = useState(true);
  const [lovDisabled, setLovDisabled] = useState(false);
  const [flag, setFlag] = useState('');
  const [originTableList, setOriginTableList] = useState([]);
  const [formRecord, setFormRecord] = useState([]);

  const formDs = useMemo(() => new DataSet(formDS()), []);
  const detailTableDs = useMemo(() => new DataSet(detailTableDS(formDs)), []); // 复制ds

  useEffect(() => {
    const { payload } = props.location;
    if (!isUndefined(payload)) {
      if (payload.flag === 'NEW') {
        queryBasicData();
        setTitle(intl.get(`${modelPrompt}.nationalCreate`).d('国标码编码规则新建'));
        setDisabledFlag(true);
        setFlag('NEW');
      } else {
        setTitle(intl.get(`${modelPrompt}.nationalDetail`).d('国标码编码规则详情'));
        setDisabledFlag(false);
        setFlag('EDIT');
        handleSearchTable(payload.gbCodeRuleId);
      }
    }
  }, []);

  // 查询站点
  const queryBasicData = async () => {
    const url = `${Host}/v1/${tenantId}/hme-assemble-points/get/user/def/site`;
    const res = await myInstance.get(url);
    if (res) {
      formDs.create({
        siteCode: res.data.siteCode,
        siteId: res.data.siteId,
        enableFlag: 'Y',
      });
    }
  };

  // 查询头，行
  const handleSearchTable = gbCodeRuleId => {
    setLoading(true);
    request(`${Host}/v1/${tenantId}/hme-gb-coding-rules/detail/ui?gbCodeRuleId=${gbCodeRuleId}`, {
      method: 'GET',
    }).then(res => {
      if (res && !res.failed) {
        formDs.create(res);
        setFormRecord(res);
        const lovValue = formDs.current.toData();
        if (lovValue.prodLineId && lovValue.materialId) {
          setLovDisabled(true);
        } else {
          setLovDisabled(false);
        }
      } else {
        notification.error({ message: res.message });
      }
    });
    detailTableDs.setQueryParameter('gbCodeRuleId', gbCodeRuleId);
    detailTableDs.query().then(resList => {
      setOriginTableList(resList.content || []);
    });
    setLoading(false);
  };

  // 确定新增
  const createLine = () => {
    if (detailTableDs.toData().length === 0) {
      detailTableDs.create({
        serialNumber: 1,
        enableFlag: 'Y',
        gbCodeRuleId: formRecord.gbCodeRuleId,
      });
    } else {
      const newLineNumber =
        (detailTableDs.toData().sort((a, b) => b.serialNumber - a.serialNumber)[0].serialNumber ||
          0) + 1;
      detailTableDs.create({
        serialNumber: Number(newLineNumber),
        enableFlag: 'Y',
        gbCodeRuleId: formRecord.gbCodeRuleId,
      });
    }
  };

  // 保存
  const handelSave = async () => {
    const validate = await formDs.validate();
    if (!validate) {
      return;
    }
    await detailTableDs.validate().then(valiResult => {
      if (valiResult) {
        let params = {};
        const formObj = formDs.current.toData();
        const tableList = detailTableDs.toJSONData();
        if (tableList.length > 0) {
          const filterList = tableList.filter(item => !item.serialNumber);
          if (filterList.length > 0) {
            return notification.warning({
              message: intl
                .get(`${modelPrompt}.error.required`)
                .d(`请填写必填项`),
              placement: 'bottomRight',
            });
          }
          const filterNumList = [];
          const numberList = [];
          // eslint-disable-next-line array-callback-return
          tableList.map(ele => {
            if (ele.codingValue) {
              const codeLength = ele.codingValue.length;
              const finalNum = Number(ele.toBit) - Number(ele.fromBit) + 1;
              if (Number(codeLength) !== Number(finalNum)) {
                filterNumList.push(ele);
                numberList.push(ele.serialNumber);
              }
            }
          });
          if (filterNumList.length > 0) {
            const numberString = numberList.join(',');
            return notification.warning({
              message: intl
                .get(`${modelPrompt}.info.lengthNoStartPosition`)
                .d(`当前数据第${numberString}行存在编码值长度不等于截止位-起始位+1，请检查`),
              placement: 'bottomRight',
            });
          }
        }
        const lineList = tableList.map(item => {
          return {
            ...item,
            deleteFlag: item._status === 'delete' ? 'Y' : null,
          };
        });
        params = {
          ...formObj,
          lineList,
        };
        setLoading(true);
        request(`${Host}/v1/${tenantId}/hme-gb-coding-rules/save/ui`, {
          method: 'post',
          body: { ...params },
        }).then(res => {
          setLoading(false);
          if (res && !res.failed) {
            notification.success();
            handleSearchTable(res);
            setDisabledFlag(false);
            setFlag('EDIT');
          } else {
            notification.error({ message: res.message });
          }
        });
      } else {
        return notification.warning({
          message: intl
            .get(`${modelPrompt}.info.valibleValue`)
            .d('请填写必填项或有效值'),
          placement: 'bottomRight',
        });
      }
    });
  };

  // 编辑按钮
  const handelEdit = () => {
    setDisabledFlag(true);
  };

  // 取消按钮
  const handelCancel = () => {
    const { dispatch } = props;
    if (flag === 'NEW') {
      dispatch(
        routerRedux.push({
          pathname: `/hmes/national-coding-maintenance/list`,
        }),
      );
    } else {
      detailTableDs.loadData(originTableList);
      setDisabledFlag(false);
    }
  };

  // 版本下拉框变化事件
  const changeVersion = (value, record) => {
    if (value) {
      if (value === 'SERIAL_NUMBER' || value === 'DATE') {
        record.set('codingValue', '');
        record.getField('codingValue').set('required', false);
        record.getField('codingValue').set('disabled', true);
      } else {
        record.getField('codingValue').set('required', true);
        record.getField('codingValue').set('disabled', false);
      }
    } else {
      record.set('codingValue', '');
      record.getField('codingValue').set('required', false);
      record.getField('codingValue').set('disabled', true);
    }
    if (value === 'FIXED_VALUE') {
      record.set('shieldField', '');
      record.getField('shieldField').set('disabled', true);
    } else {
      record.getField('shieldField').set('disabled', false);
    }
  };

  // 示例生成
  const handleIncrementalSynchronization = () => {
    setLoading(true);
    request(
      `${Host}/v1/${tenantId}/hme-gb-coding-rules/example/generation?gbCodeRuleId=${formRecord.gbCodeRuleId}`,
      {
        method: 'GET',
      },
    ).then(res => {
      setLoading(false);
      if (res && !res.failed) {
        notification.success();
        formDs.current.set('barCode', res);
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  // lov变化事件
  const changeObject = lovRecords => {
    if (lovRecords && lovRecords.revisionFlag && lovRecords.revisionFlag === 'Y') {
      formDs.current.getField('revisionCode').set('required', true);
      formDs.current.getField('revisionCode').set('disabled', false);
      formDs.current.set('revisionFlag', 'Y');
      formDs.current.set('revisionCode', lovRecords.revisionCode);
    } else {
      formDs.current.getField('revisionCode').set('required', false);
      formDs.current.getField('revisionCode').set('disabled', true);
      formDs.current.set('revisionCode', '');
      formDs.current.set('revisionFlag', 'N');
    }
    const lovValue = formDs.current.toData();
    if (lovValue.prodLineId || lovValue.materialId) {
      setLovDisabled(true);
    } else {
      setLovDisabled(false);
    }
  };

  const lovChange = () => {
    const lovValue = formDs.current.toData();
    if (lovValue.prodLineId || lovValue.materialId) {
      setLovDisabled(true);
    } else {
      setLovDisabled(false);
    }
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!disabledFlag}
          onClick={() => createLine()}
          funcType="flat"
          // shape="circle"
          size="small"
        />
      ),
      align: 'center',
      width: 60,
      renderer: ({ record }) => (
        <Popconfirm
          title= {intl
            .get(`${modelPrompt}.error.delete`)
            .d(`是否确认删除？`)}
          onConfirm={() => {
            detailTableDs.remove(record);
          }}
        >
          <Button
            funcType="flat"
            icon="remove"
            shape="circle"
            size="small"
            disabled={!disabledFlag}
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    // 序号
    {
      name: 'serialNumber',
      align: 'left',
      editor: disabledFlag && <NumberField dataSet={detailTableDs} name="serialNumber" required />,
    },
    // 编码含义
    {
      name: 'description',
      align: 'left',
      editor: disabledFlag && <TextField dataSet={detailTableDs} name="description" />,
    },
    // 起始位
    {
      name: 'fromBit',
      align: 'left',
      editor: disabledFlag && <NumberField dataSet={detailTableDs} name="fromBit" required />,
    },
    // 截止位
    {
      name: 'toBit',
      align: 'left',
      editor: disabledFlag && <NumberField dataSet={detailTableDs} name="toBit" required />,
    },
    // 编码类型
    {
      name: 'codingType',
      align: 'left',
      editor: record => {
        return (
          disabledFlag && (
            <Select
              name="codingType"
              dataSet={detailTableDs}
              onChange={value => changeVersion(value, record)}
            />
          )
        );
      },
    },
    // 编码值
    {
      name: 'codingValue',
      align: 'left',
      editor: disabledFlag && <TextField dataSet={detailTableDs} name="codingValue" />,
    },
    // 屏蔽字段
    {
      name: 'shieldField',
      align: 'left',
      editor: disabledFlag && <TextField dataSet={detailTableDs} name="shieldField" />,
    },
    // 有效性
    {
      name: 'enableFlag',
      width: 120,
      align: 'left',
      editor: disabledFlag && <Select dataSet={detailTableDs} name="enableFlag" />,
      renderer: ({ value }) => (
        <Badge status={value === 'Y' ? 'success' : 'error'} text={value === 'Y' ? '有效' : '无效'}>
          {}
        </Badge>
      ),
    },
  ];

  return (
    <React.Fragment>
      <Header title={title} backPath="/hmes/national-coding-maintenance/list">
        {!disabledFlag && (
          <Button onClick={handelEdit} style={{ marginRight: 15 }} icon="edit" color="primary">
            编辑
          </Button>
        )}
        {disabledFlag && (
          <>
            <Button onClick={handelCancel}>取消</Button>
            <Button onClick={handelSave} style={{ marginRight: 15 }} icon="save" color="primary">
              保存
            </Button>
          </>
        )}
      </Header>
      <Content>
        <Spin spinning={loading}>
          <Collapse bordered={false} defaultActiveKey={['1', '2']}>
            <Panel header="基本属性" key="1">
              <Form dataSet={formDs} columns={3}>
                <Lov name="siteObj" disabled={!disabledFlag || lovDisabled}/>
                <TextField name="ruleCode" disabled={!disabledFlag}/>
                <TextField name="description" disabled={!disabledFlag}/>
                <NumberField name="numberBit" disabled={!disabledFlag}/>
                <Lov name="prodLineObj" disabled={!disabledFlag} onChange={lovChange} />
                <TextField name="prodLineName" disabled />
                <C7nFormItemSort name="materialObj" itemWidth={['70%', '30%']}>
                  <Lov
                    name="materialObj"
                    disabled={!disabledFlag}
                    onChange={lovRecords => changeObject(lovRecords)}
                    colSpan={2}
                  />
                  <Select name="revisionCode" disabled={!disabledFlag} colSpan={1} />
                </C7nFormItemSort>
                <TextField name="materialName" disabled  />
                <Select name="workOrderType" disabled={!disabledFlag}/>
                <Switch name="enableFlag" disabled={!disabledFlag} />
                {!disabledFlag && (
                  <>
                    <Button
                      onClick={handleIncrementalSynchronization}
                      style={{ float: 'right', marginTop: '4px', width: '50%' }}
                      color="primary"
                      colSpan={1}
                    >
                      示例生成
                    </Button>
                    <TextField name="barCode" disabled />
                  </>
                )}
              </Form>
            </Panel>
            <Panel header="编码信息" key="2">
              <Table
                dataSet={detailTableDs}
                columns={columns}
                style={{ height: 400 }}
                // dragRow
              />
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.NationalCodingMaintenance', 'tarzan.common'],
})(Create);
