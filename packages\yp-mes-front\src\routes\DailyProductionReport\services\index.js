import { getCurrentOrganizationId } from 'utils/utils';
import { Host ,TARZAN_REPORT} from '@/utils/config';

const API = `${TARZAN_REPORT}`;
// const API =  '/yp-mes-25223';

export function QueryData() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-product-report/query/for/ui`,
    method: 'GET',
  };
}

export function SaveData() {
  return {
    url: `${Host}/v1/${getCurrentOrganizationId()}/hme-product-report/save/for/ui`,
    method: 'post',
  };
}
export function ExportExcel() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-product-report/export/for/ui`,
    method: 'GET',
    responseType: 'blob',
  };
}
