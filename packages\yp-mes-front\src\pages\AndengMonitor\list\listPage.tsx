import React, { FC, useEffect, } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Modal, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { HMES_BASIC, } from '@src/utils/constants';
import notification from 'utils/notification';
import { useDataSet, } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import listPageFactory from '../stores/listPageDs';
import detailPageFactory from '../stores/detailPageDs';
import axios from 'axios';

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.andengMonitor';

const ListPageComponent: FC<ListPageProps> = ({ listDs, location: { state }, }) => {

  const detailDs = useDataSet(detailPageFactory, 'andengMonitorApplicationDetail');

  useEffect(() => {
    listDs.query();
  }, [state]);

  const columns: ColumnProps[] = [

    {
      name: 'equipmentCode',
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'statusCode',
    },
    {
      name: 'positionCode',
    },
    {
      name: 'equipmentLocationName',
    },
    {
      name: 'startTime',
    },
    {
      name: 'endTime',
    },
    {
      name: 'continueTime',
    },
    {
      name: 'processBarcode',
      renderer: ({ record }) => {
        return <a onClick={() => { openModal(record?.get('lampStatusId')) }}>明细</a>
      }
    },
    {
      name: 'loginName',
    },

  ];

  const columnModal: ColumnProps[] = [
    {
      name: 'processBarcode',
    }
  ]

  const openModal = (id?) => {
    detailDs.queryDataSet?.loadData([{ lampStatusId: id }])
    detailDs.query()
    Modal.open({
      key: 'andengMonitor',
      title: intl.get(`${modelPrompt}.title.barcode`).d('产品条码'),
      destroyOnClose: true,
      drawer: true,
      closable: false,
      keyboardClosable: true,
      okText: intl.get(`${modelPrompt}.title.save`).d('保存'),
      className: 'hmes-style-modal',
      children: <Table
        dataSet={detailDs}
        columns={columnModal}
        key="andengMonitor"
        queryBar={TableQueryBarType.none}
        queryBarProps={{
          fuzzyQuery: false, // 是否开启模糊查询
        }}
        queryFieldsLimit={5} // 头部显示的查询字段的数量
        searchCode="andengMonitor" // 动态筛选条后端接口唯一编码
        customizedCode="andengMonitor" // 个性化编码
      />,
      style: {
        width: '360px',
      },
      onCancel: () => {
        detailDs.loadData([])
      },
      onOk: async () => {
        const validate = await detailDs.validate()
        if (validate) {
          const params = detailDs.toData()
          const url = `${HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-sales-service-shop-applys/save`;
          const res: any = await axios.post(url,
            params,
          )
          if (res && res.success) {
            notification.success({});
            listDs.query()
          } else {
            notification.error({
              message: res.message,
            });
            return false
          }
        }
      },
    })
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.andengMonitorAppplication`).d('安灯状态监控')}>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="andengMonitor"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={5} // 头部显示的查询字段的数量
          searchCode="andengMonitor" // 动态筛选条后端接口唯一编码
          customizedCode="andengMonitor" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.andengMonitor'],
})(ListPage);
