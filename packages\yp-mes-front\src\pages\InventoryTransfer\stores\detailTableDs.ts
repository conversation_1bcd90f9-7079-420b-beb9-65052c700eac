import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.ass.inOutStorage';

const detailTableFactory = () =>
  new DataSet({
    selection: false,
    paging: false,
    autoQuery: false,
    dataKey: 'rows',
    fields: [
      {
        name: 'materialLotCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotCode`).d('条码号'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'transferStatus',
        lookupCode: 'HME.INV_TRANSFER_STATUS',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.transferStatus`).d('调拨状态'),
      },
      {
        name: 'qty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.qty`).d('数量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'lot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lot`).d('批次'),
      },
      {
        name: 'fromLocatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.fromLocatorCode`).d('来源库位'),
      },
      {
        name: 'toLocatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toLocatorCode`).d('目标库位'),
      },
    ],
  });

export default detailTableFactory;
