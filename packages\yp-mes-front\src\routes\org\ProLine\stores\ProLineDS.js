/**
 * @Description: 站点维护DS
 * @Author: <<EMAIL>>
 * @Date: 2021-02-02 15:48:05
 * @LastEditTime: 2023-05-26 17:48:49
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.model.org.prodLine';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  selection: false,
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线短描述'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('生产线长描述'),
    },
    {
      name: 'prodLineType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=PROD_LINE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.prodLineType`).d('生产线类型'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线短描述'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('生产线长描述'),
    },
    {
      name: 'prodLineTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineType`).d('生产线类型'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-production-line/query/ui`,
        method: 'get',
      };
    },
  },
});

const operationTableDS = () => ({
  selection: false,
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  paging: false,
  fields: [
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'operation',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationCode`).d('工艺编码'),
      textField: 'operationName',
      valueField: 'operationId',
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'operationId',
      type: FieldType.string,
      bind: 'operation.operationId',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺描述'),
      bind: 'operation.description',
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationRevision`).d('工艺版本'),
      bind: 'operation.revision',
    },
  ],
  transport: {
    read: ({ dataSet }) => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation/limit-prod-line/dispatch/operation/ui`,
        method: 'get',
        data: { prodLineId: dataSet.queryParameter.payload.prodLineId },
        transformResponse: response => {
          const newList = getResponse(JSON.parse(response));
          const returnList = [];
          if (newList && newList.rows) {
            newList.rows.forEach(item => {
              returnList.push({
                prodLineCode: dataSet.queryParameter.payload.prodLineCode,
                operation: {
                  operationName: item.operationName,
                  operationId: item.operationId,
                  description: item.description,
                  revision: item.revision,
                },
              });
            });
          }
          // TODO 这里这样写，dataset会报错【TypeError: Cannot read property '_status' of undefined】，但是不影响使用，后续需查看源码给出处理方法
          return returnList;
        },
      };
    },
  },
});

const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'prodLineManufacturing',
      type: FieldType.object,
    },
    {
      name: 'productionLine',
      type: FieldType.object,
    },
    {
      name: 'prodLineSchedule',
      type: FieldType.object,
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
      bind: 'productionLine.prodLineCode',
    },
    {
      name: 'prodLineName',
      type: FieldType.intl,
      required: true,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线短描述'),
      bind: 'productionLine.prodLineName',
    },
    {
      name: 'description',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.description`).d('生产线长描述'),
      bind: 'productionLine.description',
    },
    {
      name: 'prodLineType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=PROD_LINE_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
      label: intl.get(`${modelPrompt}.prodLineType`).d('生产线类型'),
      bind: 'productionLine.prodLineType',
      dynamicProps: {
        disabled({ record }) {
          return record.data.proLineId;
        },
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
      bind: 'productionLine.enableFlag',
    },
    // 基础属性
    {
      name: 'supplier',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      bind: 'productionLine.supplier',
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      bind: 'productionLine.supplier.supplierName',
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
      disabled: true,
    },
    {
      name: 'supplierSite',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierSiteCode`).d('供应商地点编码'),
      lovCode: 'MT.MODEL.SUPPLIER_SITE',
      dynamicProps: {
        disabled({ record }) {
          return !(record.get('supplier') || {}).supplierId;
        },
        lovPara({ record }) {
          return {
            tenantId,
            supplierId: (record.get('supplier') || {}).supplierId,
          };
        },
      },
      bind: 'productionLine.supplierSite',
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      bind: 'productionLine.supplierSite.supplierSiteName',
      label: intl.get(`${modelPrompt}.supplierSiteName`).d('供应商地点描述'),
      disabled: true,
    },
    // 计划属性
    {
      name: 'orderTimeFence',
      type: FieldType.number,
      bind: 'prodLineSchedule.releaseTimeFence',
      label: intl.get(`${modelPrompt}.releaseTimeFence`).d('下达时间栏'),
      min: 0,
    },
    {
      name: 'releaseTimeFence',
      type: FieldType.number,
      bind: 'prodLineSchedule.demandTimeFence',
      label: intl.get(`${modelPrompt}.demandTimeFence`).d('需求时间栏'),
      min: 0,
    },
    {
      name: 'demandTimeFence',
      type: FieldType.number,
      bind: 'prodLineSchedule.orderTimeFence',
      label: intl.get(`${modelPrompt}.orderTimeFence`).d('提前顶层下达时间'),
      min: 0,
    },
    {
      name: 'fixTimeFence',
      type: FieldType.number,
      bind: 'prodLineSchedule.fixTimeFence',
      label: intl.get(`${modelPrompt}.fixTimeFence`).d('固定时间栏'),
      min: 0,
    },
    {
      name: 'frozenTimeFence',
      type: FieldType.number,
      bind: 'prodLineSchedule.frozenTimeFence',
      label: intl.get(`${modelPrompt}.frozenTimeFence`).d('冻结时间栏'),
      min: 0,
    },
    {
      name: 'forwardPlanningTimeFence',
      type: FieldType.number,
      bind: 'prodLineSchedule.forwardPlanningTimeFence',
      label: intl.get(`${modelPrompt}.forwardPlanningTimeFence`).d('顺排时间栏'),
      min: 0,
    },
    // 生产属性
    {
      name: 'issuedLocator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.issuedLocatorCode`).d('默认发料库位编码'),
      textField: 'locatorCode',
      valueField: 'locatorId',
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      bind: 'prodLineManufacturing.issuedLocator',
    },
    {
      name: 'issuedLocatorName',
      type: FieldType.string,
      bind: 'prodLineManufacturing.issuedLocator.locatorName',
      label: intl.get(`${modelPrompt}.issuedLocatorName`).d('默认发料库位描述'),
      disabled: true,
    },
    {
      name: 'completionLocator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.completionLocatorCode`).d('默认完工库位编码'),
      textField: 'locatorCode',
      valueField: 'locatorId',
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      bind: 'prodLineManufacturing.completionLocator',
    },
    {
      name: 'completionLocatorName',
      type: FieldType.string,
      bind: 'prodLineManufacturing.completionLocator.locatorName',
      label: intl.get(`${modelPrompt}.completionLocatorName`).d('默认完工库位描述'),
      disabled: true,
    },
    {
      name: 'inventoryLocator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inventoryLocatorCode`).d('默认入库库位编码'),
      textField: 'locatorCode',
      valueField: 'locatorId',
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      bind: 'prodLineManufacturing.inventoryLocator',
    },
    {
      name: 'inventoryLocatorName',
      type: FieldType.string,
      bind: 'prodLineManufacturing.inventoryLocator.locatorName',
      label: intl.get(`${modelPrompt}.inventoryLocatorName`).d('默认入库库位描述'),
      disabled: true,
    },
    {
      name: 'dispatchMethod',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=DISPATCH_METHOD`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.dispatchMethod`).d('调度方式'),
      bind: 'prodLineManufacturing.dispatchMethod',
    },
  ],
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.model.domain.entity.MtModProductionLine';
      return {
        data: { prodLineId: (record.data.productionLine || {}).prodLineId },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_MODEL}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-production-line/record/query/ui`,
        method: 'get',
        transformResponse: response => {
          const _response = getResponse(JSON.parse(response));
          if (_response && _response.rows) {
            const _item = {
              ..._response.rows,
              ..._response.rows.productionLine,
            };
            _item.productionLine.supplier = {
              supplierId: (_item.productionLine || {}).supplierId,
              supplierName: (_item.productionLine || {}).supplierName,
              supplierCode: (_item.productionLine || {}).supplierCode,
            };
            _item.productionLine.supplierSite = {
              supplierSiteCode: (_item.productionLine || {}).supplierSiteCode,
              supplierSiteId: (_item.productionLine || {}).supplierSiteId,
              supplierSiteName: (_item.productionLine || {}).supplierSiteName,
            };
            _item.prodLineManufacturing.issuedLocator = {
              locatorCode: (_item.prodLineManufacturing || {}).issuedLocatorCode,
              locatorId: (_item.prodLineManufacturing || {}).issuedLocatorId,
              locatorName: (_item.prodLineManufacturing || {}).issuedLocatorName,
            };
            _item.prodLineManufacturing.completionLocator = {
              locatorCode: (_item.prodLineManufacturing || {}).completionLocatorCode,
              locatorId: (_item.prodLineManufacturing || {}).completionLocatorId,
              locatorName: (_item.prodLineManufacturing || {}).completionLocatorName,
            };
            _item.prodLineManufacturing.inventoryLocator = {
              locatorCode: (_item.prodLineManufacturing || {}).inventoryLocatorCode,
              locatorId: (_item.prodLineManufacturing || {}).inventoryLocatorId,
              locatorName: (_item.prodLineManufacturing || {}).inventoryLocatorName,
            };
            return { rows: _item };
          }
          return { rows: {} };
        },
      };
    },
    submit: ({ dataSet }) => {
      const { operation } = dataSet.children;
      const operationId = [];
      const operationData = operation.toData();
      if (Array.isArray(operationData)) {
        operationData.forEach(item => {
          if (item.operationId) {
            operationId.push(item.operationId);
          }
        });
      }
      const {
        productionLine = {},
        prodLineSchedule = {},
        prodLineManufacturing = {},
        _tls,
        description,
        prodLineName,
        ...others
      } = dataSet.toData()[0];

      const { supplier = {}, supplierSite = {} } = productionLine;

      productionLine.supplierId = (supplier || {}).supplierId;
      productionLine.supplierName = (supplier || {}).supplierName;
      productionLine.supplierCode = (supplier || {}).supplierCode;
      productionLine.supplierSiteId = (supplierSite || {}).supplierSiteId;
      productionLine.supplierSiteName = (supplierSite || {}).supplierSiteName;
      productionLine.supplierSiteCode = (supplierSite || {}).supplierSiteCode;
      productionLine.description = description;
      productionLine.prodLineName = prodLineName;
      productionLine._tls = _tls;

      delete productionLine.supplier;
      delete productionLine.supplierSite;

      const _productionLine = {
        ...others,
        ...productionLine,
      }

      const {
        issuedLocator = {},
        completionLocator = {},
        inventoryLocator = {},
      } = prodLineManufacturing;

      prodLineManufacturing.completionLocatorCode = (completionLocator || {}).locatorCode;
      prodLineManufacturing.completionLocatorId = (completionLocator || {}).locatorId;
      prodLineManufacturing.completionLocatorName = (completionLocator || {}).locatorName;
      prodLineManufacturing.inventoryLocatorCode = (inventoryLocator || {}).locatorCode;
      prodLineManufacturing.inventoryLocatorId = (inventoryLocator || {}).locatorId;
      prodLineManufacturing.inventoryLocatorName = (inventoryLocator || {}).locatorName;
      prodLineManufacturing.issuedLocatorCode = (issuedLocator || {}).locatorCode;
      prodLineManufacturing.issuedLocatorId = (issuedLocator || {}).locatorId;
      prodLineManufacturing.issuedLocatorName = (issuedLocator || {}).locatorName;
      prodLineManufacturing.operationId = operationId;

      delete prodLineManufacturing.issuedLocator;
      delete prodLineManufacturing.completionLocator;
      delete prodLineManufacturing.inventoryLocator;

      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-production-line/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.PROLINE_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.PROLINE`,
        data: {
          prodLineManufacturing,
          prodLineSchedule,
          productionLine: _productionLine,
        },
        method: 'POST',
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
});

export { tableDS, detailDS, operationTableDS };
