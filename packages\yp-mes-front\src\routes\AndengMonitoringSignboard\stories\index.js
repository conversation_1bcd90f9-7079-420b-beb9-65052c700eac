import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/yp-report-38283`;
const Host = `${BASIC.TARZAN_REPORT}`;
const modelPrompt = 'tarzan.hmes.FeedTankCapacityAdjustment';

const searchDS = () => {
  return {
    autoQuery: false,
    autoCreate: true,
    paging: false,
    cacheSelection: true,
    queryFields: [
      {
        name: 'shiftCodeLov',
        lovCode: 'HME.CLASS_EQUIPMENT',
        type: 'object',
        label: intl.get(`${modelPrompt}.shiftCodeLov`).d('班组名称'),
      },
      {
        name: 'classCode',
        bind: 'shiftCodeLov.classCode',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-lamp-monitor-board/list/ui`,
          method: 'GET',
        };
      },
    },
  }
};

const tableDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    paging: false,
    selection: false,
    dataKey: 'rows',
    fields: [
      {
        name: 'type',
        type: 'string',
        label: intl.get(`${modelPrompt}.type`).d('分类'),
      },
      {
        name: 'statusName',
        type: 'string',
        label: intl.get(`${modelPrompt}.statusName`).d('状态'),
      },
      {
        name: 'statusCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.statusCode`).d('状态代码'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-lamp-monitor-board/status/list/ui`,
          method: 'GET',
        };
      },
    },
  }
};

const timeDS = () => {
  return {
    autoQuery: false,
    autoCreate: true,
    paging: false,
    selection: false,
    fields: [{
      name: 'time',
      type: 'number',
    }],
  }
}


export { searchDS, tableDS, timeDS };
