/**
 * @feature 生产版本维护
 * @date 2021-8-31
 * <AUTHOR> <<EMAIL>>
 */

import React, { useEffect, useMemo } from 'react';
import {
  Table,
  DataSet,
  Modal,
  Form,
  TextField,
  Button,
  DateTimePicker,
  Lov,
} from 'choerodon-ui/pro';
import moment from 'moment';
import { Button as PermissionButton } from 'components/Permission';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import myInstance from '@utils/myAxios';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { C7nFormItemSort } from '@components/tarzan-ui';
import { entranceDS } from './stories/EntranceDs';
import { detailDS } from './stories/DetailDs';

const tenantId = getCurrentOrganizationId();

const entrance = props => {
  const {
    match: { path },
  } = props;
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const columns = [
    {
      name: 'productionVersionCode',
      align: 'left',
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => {
              addDetail(record.data);
            }}
          >
            {record.data.productionVersionCode}
          </a>
        );
      },
    },
    {
      name: 'productionVersionDesc',
      align: 'left',
    },
    {
      name: 'originalProversionCode',
      align: 'left',
    },
    {
      name: 'remark',
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => {
              const id = record.data.bomId;
              props.history.push(`/hmes/product/assembly-list/dist/${id}`);
            }}
          >
            {record.data.bomName}
            {record.data.bomName && record.data.bomRevision && '/'}
            {record.data.bomRevision}
          </a>
        );
      },
    },
    {
      name: 'valueAllowMissing',
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => {
              const id = record.data.routerId;
              props.history.push(`/hmes/new/process/routes-c7n/dist/${id}`);
            }}
          >
            {record.data.routerName}
            {record.data.routerName && record.data.routerRevision && '/'}
            {record.data.routerRevision}
          </a>
        );
      },
    },
    {
      name: 'dateFrom',
      align: 'center',
      width: 150,
    },
    {
      name: 'dateTo',
      align: 'center',
      width: 150,
    },
  ];

  let detailDrawer;

  const addDetail = (data: object) => {
    let title;
    let disabled;
    // @ts-ignore
    if (data.productionVersionId) {
      detailDs.loadData([{ ...data }]);
      title = intl.get('tarzan.product.productionVersion.title.edit').d('编辑生产版本');
      disabled = true;
    } else {
      const currentTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
      detailDs.loadData([{ dateFrom: currentTime }]);
      detailDs.reset();
      title = intl.get('tarzan.product.productionVersion.title.add').d('新建生产版本');
      disabled = false;
    }

    detailDrawer = Modal.open({
      key: Modal.key(),
      title,
      drawer: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal copy-drawer-modal',
      children: (
        <>
          <Form dataSet={detailDs} columns={1} labelWidth={112}>
            <TextField name="productionVersionCode" disabled={disabled} />
            <TextField name="productionVersionDesc" />
            <C7nFormItemSort name="bom" itemWidth={['70%', '30%']}>
              <Lov name="bom" />
              <TextField name="bomRevision" />
            </C7nFormItemSort>
            <C7nFormItemSort name="router" itemWidth={['70%', '30%']}>
              <Lov name="router" />
              <TextField name="routerRevision" />
            </C7nFormItemSort>
            <DateTimePicker name="dateFrom" />
            <DateTimePicker name="dateTo" />
          </Form>
        </>
      ),
      footer: (
        <>
          <div style={{ float: 'right' }}>
            <Button
              onClick={() => {
                detailDrawer.close();
              }}
            >
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button
              type={ButtonType.submit}
              onClick={() => {
                detailDrawerSave();
              }}
              color={ButtonColor.primary}
            >
              {intl.get('tarzan.common.button.confirm').d('确定')}
            </Button>
          </div>
        </>
      ),
    });
  };

  const detailDrawerSave = async () => {
    const validate = await detailDs.validate(false, true);
    if (validate) {
      const detailData = detailDs.toData();
      const url = `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-production-version/prod-version-save/ui`;
      myInstance
        .post(url, {
          ...detailData[0],
        })
        .then(res => {
          if (res.data.success) {
            detailDrawer.close();
            notification.success({});
            props.dataSet.query();
          } else {
            notification.error({
              message: res.data.message,
            });
          }
        });
    }
  };

  useEffect(() => {
    props.dataSet.query(props.dataSet.currentPage);
  }, []);

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get('tarzan.product.productionVersion.title.list').d('生产版本维护')}
        header={
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="add"
              onClick={addDetail}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.create').d('新建')}
            </PermissionButton>
          </>
        }
      >
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={props.dataSet}
          columns={columns as ColumnProps[]}
          searchCode="ProductionVersion"
          customizedCode="ProductionVersion"
        />
      </PageHeaderWrapper>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.product.productionVersion', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...entranceDS(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(entrance),
);
