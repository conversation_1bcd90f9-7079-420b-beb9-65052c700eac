/**
 * @Description: 事务尾数报表平台
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-25 18:09:22
 * @LastEditors: <<EMAIL>>
 */

import React, { useCallback, useMemo } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { isNil } from 'lodash';
import { useDataSetEvent } from 'utils/hooks';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from './stories';
import { lineTableDS } from './stories/lineTableDS';
import { TransBack, TransDelete } from './services';
import './index.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.event.transactionTailReportPlatform';
// 手工回传按钮
const EchoBtn = observer(({ ds }: { ds: DataSet }) => {
  const selectedRows = ds.selected;

  const { run: runTransBack, loading } = useRequest(TransBack(), { manual: true });

  const handleEcho = () => {
    const transBackList = selectedRows.map(item => {
      return item.toData();
    });
    runTransBack({
      params: transBackList,
      onSuccess: () => {
        const currentDate = new Date();
        currentDate.setMinutes(currentDate.getMinutes() + 1);
        ds!.queryDataSet!.current!.set('lastUpdateDateTo', currentDate);
      },
    });
  };

  return (
    <Button
      onClick={handleEcho}
      loading={loading}
      disabled={
        !selectedRows.length || !selectedRows.some(row => row.toData().status === 'N' || row.toData().status === 'E')
      }
    >
      {intl.get(`${modelPrompt}.echoButton`).d('手工回传')}
    </Button>
  );
});

const TransactionReportPlatform = observer(props => {
  const { tableDs, history } = props;
  const { run: transDelete, loading: transDeleteLoading } = useRequest(TransDelete(), {
    manual: true,
    needPromise: true,
  });
  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'materialId':
        record.set('revisionCode', null);
        break;
      case 'sourceOrderType':
        record.set('sourceOrderLov', null);
        break;
      case 'sourceOrderLov':
        record.set('sourceOrderLineLov', null);
        break;
      default:
        break;
    }
  });

  useDataSetEvent(tableDs, 'indexChange', ({ record }) => {
    if (record) {
      lineTableDs.setQueryParameter('materialTransIfaceIds', record.get('materialTransIfaceId'));
      lineTableDs.query();
    }
  });

  useDataSetEvent(tableDs, 'query', () => {
    lineTableDs.setQueryParameter('materialTransIfaceIds', null);
    lineTableDs.loadData([]);
  });

  const lineTableDs = useMemo(() => new DataSet(lineTableDS()), []);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'materialTransIfaceId',
        lock: ColumnLock.left,
        width: 80,
      },
      {
        name: 'materialTransMantissaIfaceId',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'transTypeCode',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'transCode',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'status',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'message',
        width: 150,
      },
      {
        name: 'transferTimes',
        width: 90,
      },
      {
        name: 'erpMaterialDoc',
        width: 150,
      },
      {
        name: 'erpMaterialDocLine',
        width: 180,
      },
      {
        name: 'batchId',
        width: 150,
      },
      {
        name: 'plantCode',
        width: 150,
      },
      {
        name: 'locatorCode',
        width: 150,
      },
      {
        name: 'ifaceId',
      },
      {
        name: 'sourcePlantCode',
        width: 150,
      },
      {
        name: 'sourceLocatorCode',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'revisionCode',
        width: 150,
      },
      {
        name: 'primaryUomQty',
        width: 150,
      },
      {
        name: 'primaryUomCode',
        width: 150,
      },
      {
        name: 'secondaryUomQty',
        width: 150,
      },
      {
        name: 'secondaryUomCode',
        width: 150,
      },
      {
        name: 'transTime',
        width: 150,
      },
      {
        name: 'accountTime',
        width: 150,
      },
      {
        name: 'transAccount',
        width: 150,
      },
      {
        name: 'transReasonCode',
        width: 150,
      },
      {
        name: 'materialLotCode',
        width: 150,
      },
      {
        name: 'containerCode',
        width: 150,
      },
      {
        name: 'lot',
        width: 150,
      },
      {
        name: 'workcellCode',
        width: 150,
      },
      {
        name: 'prodLineCode',
        width: 150,
      },
      {
        name: 'sourceOrderType',
        width: 150,
      },
      {
        name: 'sourceOrder',
        width: 150,
      },
      {
        name: 'sourceOrderLineNum',
        width: 150,
      },
      {
        name: 'sourceOrderCompLineNum',
        width: 150,
      },
      {
        name: 'instructionDocTypeDesc',
        width: 150,
      },
      {
        name: 'instructionDocNum',
        width: 150,
      },
      {
        name: 'lineNumber',
        width: 150,
      },
      {
        name: 'instructionNum',
        width: 150,
      },
      {
        name: 'supplierCode',
        width: 150,
      },
      {
        name: 'supplierSiteCode',
        width: 150,
      },
      {
        name: 'customerCode',
        width: 150,
      },
      {
        name: 'customerSiteCode',
        width: 150,
      },
      {
        name: 'operationSequence',
        width: 150,
      },
      {
        name: 'rsnum',
        width: 150,
      },
      {
        name: 'rspos',
        width: 150,
      },
      {
        name: 'ownerTypeDesc',
        width: 150,
      },
      {
        name: 'specStockType',
        width: 180,
      },
      {
        name: 'ownerCode',
        width: 150,
      },
      {
        name: 'ownerLineCode',
        width: 150,
      },
      {
        name: 'sourceOwnerTypeDesc',
        width: 150,
      },
      {
        name: 'sourceSpecStockType',
        width: 180,
      },
      {
        name: 'sourceOwnerCode',
        width: 150,
      },
      {
        name: 'sourceOwnerLineCode',
        width: 150,
      },
      {
        name: 'reservedObjectTypeDesc',
        width: 150,
      },
      {
        name: 'reservedObjectCode',
        width: 150,
      },
      {
        name: 'sourceReservedObjectTypeDesc',
        width: 150,
      },
      {
        name: 'sourceReservedObjectCode',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
      {
        name: 'erpPreMaterialDoc',
        width: 150,
      },
      {
        name: 'erpPreMaterialDocLine',
        width: 180,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'createdByName',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 150,
      },
      {
        name: 'attribute1',
        width: 150,
      },
      {
        name: 'attribute2',
        width: 150,
      },
      {
        name: 'attribute3',
        width: 150,
      },
      {
        name: 'attribute4',
        width: 150,
      },
      {
        name: 'attribute5',
        width: 150,
      },
    ];
  }, []);

  const listTableColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'transTypeCode',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'transCode',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'sumFlagDesc',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'materialTransIfaceId',
        width: 120,
      },
      {
        name: 'eventId',
        width: 120,
      },
      {
        name: 'eventTypeCode',
        width: 120,
      },
      {
        name: 'eventTypeCodeDesc',
        width: 120,
      },
      {
        name: 'businessTypeCodeDesc',
        width: 120,
      },
      {
        name: 'message',
        width: 150,
      },
      {
        name: 'plantCode',
        width: 150,
      },
      {
        name: 'siteCode',
        width: 150,
      },
      {
        name: 'locatorCode',
        width: 150,
      },
      {
        name: 'infoLocatorCode',
        width: 150,
      },
      {
        name: 'sourcePlantCode',
        width: 150,
      },
      {
        name: 'sourceSiteCode',
        width: 150,
      },
      {
        name: 'sourceLocatorCode',
        width: 150,
      },
      {
        name: 'infoSourceLocatorCode',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'revisionCode',
        width: 150,
      },
      {
        name: 'primaryUomQty',
        width: 150,
      },
      {
        name: 'primaryUomCode',
        width: 150,
      },
      {
        name: 'secondaryUomQty',
        width: 150,
      },
      {
        name: 'secondaryUomCode',
        width: 150,
      },
      {
        name: 'transTime',
        width: 150,
      },
      {
        name: 'accountTime',
        width: 150,
      },
      {
        name: 'transAccount',
        width: 150,
      },
      {
        name: 'transReasonCode',
        width: 150,
      },
      {
        name: 'materialLotCode',
        width: 150,
      },
      {
        name: 'containerCode',
        width: 150,
      },
      {
        name: 'lot',
        width: 150,
      },
      {
        name: 'workcellCode',
        width: 150,
      },
      {
        name: 'prodLineCode',
        width: 150,
      },
      {
        name: 'sourceOrderType',
        width: 150,
      },
      {
        name: 'sourceOrder',
        width: 150,
      },
      {
        name: 'sourceOrderLineNum',
        width: 150,
      },
      {
        name: 'sourceOrderCompLineNum',
        width: 150,
      },
      {
        name: 'instructionDocTypeDesc',
        width: 150,
      },
      {
        name: 'instructionDocNum',
        width: 150,
      },
      {
        name: 'lineNumber',
        width: 150,
      },
      {
        name: 'instructionNum',
        width: 150,
      },
      {
        name: 'supplierCode',
        width: 150,
      },
      {
        name: 'supplierSiteCode',
        width: 150,
      },
      {
        name: 'customerCode',
        width: 150,
      },
      {
        name: 'customerSiteCode',
        width: 150,
      },
      {
        name: 'operationSequence',
        width: 150,
      },
      {
        name: 'rsnum',
        width: 150,
      },
      {
        name: 'rspos',
        width: 150,
      },
      {
        name: 'ownerTypeDesc',
        width: 150,
      },
      {
        name: 'specStockType',
        width: 180,
      },
      {
        name: 'ownerCode',
        width: 150,
      },
      {
        name: 'ownerLineCode',
        width: 180,
      },
      {
        name: 'sourceOwnerTypeDesc',
        width: 150,
      },
      {
        name: 'sourceSpecStockType',
        width: 180,
      },
      {
        name: 'sourceOwnerCode',
        width: 150,
      },
      {
        name: 'sourceOwnerLineCode',
        width: 180,
      },
      {
        name: 'reservedObjectTypeDesc',
        width: 150,
      },
      {
        name: 'reservedObjectCode',
        width: 150,
      },
      {
        name: 'sourceReservedObjectTypeDesc',
        width: 150,
      },
      {
        name: 'sourceReservedObjectCode',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'createdByName',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 150,
      },
      {
        name: 'attribute1',
        width: 150,
      },
      {
        name: 'attribute2',
        width: 150,
      },
      {
        name: 'attribute3',
        width: 150,
      },
      {
        name: 'attribute4',
        width: 150,
      },
      {
        name: 'attribute5',
        width: 150,
      },
    ];
  }, []);

  const handleJumpToTransactionDetailReport = useCallback(() => {
    history.push(`/hmes/transaction-report/transaction-detail-report`);
  }, []);

  const handleJumpToMobileEventDetailReport = useCallback(() => {
    history.push(`/hmes/transaction-report/mobile-event-detail-report`);
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };
  const handleManualAbolition = () => {
    transDelete({
      params: tableDs.selected.map(item => item.data),
    }).then(res => {
      if (res && res.success) {
        const currentDate = new Date();
        currentDate.setMinutes(currentDate.getMinutes() + 1);
        tableDs!.queryDataSet!.current!.set('lastUpdateDateTo', currentDate);
        tableDs.query();
      }
    });
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.mes.event.transactionTailReportPlatform.title.list')
          .d('事务尾数报表平台')}
      >
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-material-trans-mantissa-iface/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <EchoBtn ds={tableDs} />
        <Button
          loading={transDeleteLoading}
          disabled={
            !tableDs.selected.length || tableDs.selected.some(item => item.get('status') !== 'E')
          }
          onClick={handleManualAbolition}
        >
          {intl.get(`${modelPrompt}.manualAbolition`).d('手工废除')}
        </Button>
        <Button onClick={handleJumpToTransactionDetailReport}>
          {intl.get(`${modelPrompt}.jumpToTransactionDetailReport`).d('事务明细查看')}
        </Button>
        <Button onClick={handleJumpToMobileEventDetailReport}>
          {intl.get(`${modelPrompt}.jumpToMobileEventDetailReport`).d('移动事件明细查看')}
        </Button>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          style={{ height: 400 }}
          dataSet={tableDs}
          columns={columns}
          searchCode="TransactionReportPlatform"
          customizedCode="TransactionReportPlatform"
        />
        <Collapse bordered={false} defaultActiveKey={['transDetail']}>
          <Panel
            header={intl
              .get('tarzan.mes.event.transactionTailReportPlatform.title.transDetail')
              .d('事务明细信息')}
            key="transDetail"
          >
            <Table style={{ height: 400 }} dataSet={lineTableDs} columns={listTableColumns} />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.mes.event.transactionTailReportPlatform', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(TransactionReportPlatform),
);
