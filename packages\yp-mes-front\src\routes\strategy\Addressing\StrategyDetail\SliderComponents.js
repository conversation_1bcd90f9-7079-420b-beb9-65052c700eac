/**
 * @Description: 寻址策略坐标系滑块
 * @Author: <<EMAIL>>
 * @Date: 2021-09-08 14:56:42
 * @LastEditTime: 2021-10-09 13:55:58
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import intl from 'utils/intl';
import { Form } from 'choerodon-ui/pro';
// import { Slider } from 'choerodon-ui-1.4.3';
import Slider from 'choerodon-ui-1.4.3/lib/slider';

import styles from '../index.module.less';

const modelPrompt = 'tarzan.addressing.strategy';

const SliderComponents = (props, ref) => {
  const { canEdit, coordinateList } = props;

  // 滑块缓存
  const [newCoordinateList, setNewCoordinateList] = useState([]);

  useEffect(() => {
    setNewCoordinateList(coordinateList);
  }, [coordinateList]);

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    resetMarks: () => {
      setNewCoordinateList([]);
    },
    getCoordinateList: () => {
      return newCoordinateList;
    },
  }));

  const sliderChange = (key, value) => {
    const _newCoordinateList = [];
    newCoordinateList.forEach(item => {
      const _item = { ...item };
      if (item.key === key) {
        _item.value = value;
      }
      _newCoordinateList.push(_item);
    });
    setNewCoordinateList(_newCoordinateList);
  };

  const formatSliderMarks = value => {
    return {
      [value[0]]: `${value[0]}`,
      [value[1]]: `${value[1]}`,
    };
  };

  return (
    <Form
      disabled={!canEdit}
      columns={1}
      labelLayout="horizontal"
      labelWidth={110}
      className={styles['strategy-self-form']}
    >
      {newCoordinateList.map(item => (
        <Slider
          disabled={!canEdit}
          onChange={event => {
            sliderChange(item.key, event);
          }}
          marks={formatSliderMarks(item.value)}
          min={item.range[0]}
          max={item.range[1]}
          tipFormatter={null}
          label={`${intl.get(`${modelPrompt}.coordinate`).d('坐标')}${item.key}`}
          range
          defaultValue={item.value && item.value.length === 2 ? item.value : [0, 0]}
          value={item.value && item.value.length === 2 ? item.value : [0, 0]}
        />
      ))}
    </Form>
  );
};

export default forwardRef(SliderComponents);
