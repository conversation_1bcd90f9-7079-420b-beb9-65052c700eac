import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.cassetteMaterial';

const historyFactory = () =>
    new DataSet({
        primaryKey: 'hisKeyId',
        selection: false,
        paging: true,
        autoQuery: false,
        dataKey: 'rows.content',
        totalKey: 'rows.totalElements',
        queryDataSet: new DataSet({
            fields: [
                {
                    name: 'creationFrom',
                    type: FieldType.dateTime,
                    label: intl.get(`${modelPrompt}.form.creationFrom`).d('创建时间从'),
                },
                {
                    name: 'creationTo',
                    type: FieldType.dateTime,
                    label: intl.get(`${modelPrompt}.form.creationTo`).d('创建时间至'),
                },]
        }),
        fields: [
            {
                name: 'objectType',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.objectType`).d('关联对象类型'),
            },
            {
                name: 'objectCode',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.objectCode`).d('对象编码'),
            },
            {
                name: 'objectDesc',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.objectDesc`).d('对象描述'),
            },
            {
                name: 'objectRevision',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.objectRevision`).d('对象版本'),
            },
            {
                name: 'revisionFlag',
                lookupCode: 'MT.YES_NO',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.revisionFlag`).d('是否开启物料版本'),
            },
            {
                name: 'enableFlag',
                lookupCode: 'MT.YES_NO',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.enableFlag`).d('有效性'),
            },
            {
                name: 'creationDate',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.creationDate`).d('创建时间'),
            },
            {
                name: 'createdByRealName',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.createdByRealName`).d('创建人'),
            },
        ],
        transport: {
            read: (config: AxiosRequestConfig): AxiosRequestConfig => {
                return {
                    ...config,
                    url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-his-Assembly-select/assign/list/ui`,
                };
            },
        },
    });

export default historyFactory;
