import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { openTab, closeTab } from 'utils/menuTab';
import queryString from 'querystring';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.hmes.wipImport';

const WipImport = observer(() => {
  useEffect(() => {
    openTab({
      key: `/himp/commentImport/HME.WIP.IMPORT`,
      title: intl.get(`${modelPrompt}.title`).d('在制品导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
    setTimeout(() => {
      closeTab('/hmes/wip-import')
    }, 100)
  }, [])
  return (
    <></>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.wipImport'],
})(WipImport);
