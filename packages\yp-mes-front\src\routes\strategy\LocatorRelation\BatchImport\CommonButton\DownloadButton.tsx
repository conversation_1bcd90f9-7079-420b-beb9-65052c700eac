import React, { FC } from 'react';
import { Button as PermissionButton } from 'components/Permission';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import intl from 'utils/intl';
/**
 * @feature 物料库位关系维护-批量导入页面删除按钮
 * @date 2021-12-15
 * <AUTHOR>
 */
import { BASIC, API_HOST } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.strategy.locatorRelation';

interface DownloadButtonProps {
  path: string;
  disabled: boolean;
}

const DownloadButton: FC<DownloadButtonProps> = props => {
  const { disabled, path } = props;
  const download = () => {
    const elink = document.createElement('a');
    elink.style.display = 'none';
    elink.href = `${API_HOST}${
      BASIC.HMES_BASIC
    }/v1/${tenantId}/mt-locator-material-rel/template/download/ui?access_token=${getAccessToken()}`;
    document.body.appendChild(elink);
    elink.click();
    document.body.removeChild(elink);
  };
  return (
    <PermissionButton
      type="c7n-pro"
      icon="get_app"
      onClick={download}
      disabled={disabled}
      permissionList={[
        {
          code: `${path}.button.tempalte`,
          type: 'button',
          meaning: '列表页-模板下载按钮',
        },
      ]}
    >
      {intl.get(`${modelPrompt}.button.download.tempalte`).d('模板下载')}
    </PermissionButton>
  );
};
export default DownloadButton;
