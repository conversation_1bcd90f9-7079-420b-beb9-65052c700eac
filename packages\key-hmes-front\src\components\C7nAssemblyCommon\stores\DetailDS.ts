/**
 * @Description: 装配清单 - 详情页面DS（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/7/25 15:55
 * @LastEditTime: 2023-05-11 11:05:21
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import uuid from 'uuid/v4';
import notification from 'utils/notification';
import { queryAssemblyDetail } from '../services';

const modelPrompt = 'tarzan.product.bom.model.bom';
const tenantId = getCurrentOrganizationId();

// 站点列表DS数据源
const siteListDS: (uiServerCode: string) => DataSetProps = uiServerCode => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${uiServerCode}/v1/${tenantId}/mt-component/user/distribution/site/list/ui`,
        method: 'GET',
        transformResponse: data => {
          if (Array.isArray(data)) {
            return data;
          }
          const datas = JSON.parse(data);
          // 处理接口报错
          if (datas && !datas.success) {
            if (datas.message) {
              notification.error({ message: datas.message });
            }
            return {
              rows: [],
            };
          }
          datas.rows.forEach(i => {
            i.displayName = `${i.typeDesc}-${i.siteCode}-${i.siteName}`;
          });
          return datas.rows;
        },
      };
    },
  },
});

// 组件类型DS数据源
const bomComponentTypeDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=BOM&typeGroup=BOM_COMPONENT_TYPE`,
        method: 'GET',
        transformResponse: data => {
          if (Array.isArray(data)) {
            return data;
          }
          const datas = JSON.parse(data);
          // 处理接口报错
          if (datas && !datas.success) {
            if (datas.message) {
              notification.error({ message: datas.message });
            }
            return {
              rows: [],
            };
          }
          return datas.rows;
        },
      };
    },
  },
});

// 状态DS数据源
const bomStatusListDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=BOM&statusGroup=BOM_STATUS`,
        method: 'GET',
        transformResponse: data => {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      };
    },
  },
});

// 装配清单基础信息DS
const detailDS: (uiServerCode: string, typeGroup: string, siteListDs: DataSet) => DataSetProps = (
  uiServerCode,
  typeGroup,
  siteListDs,
) => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'bomId',
      type: FieldType.number,
    },
    {
      name: 'bomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomName`).d('编码'),
      required: true,
    },
    {
      name: 'revision',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
      dynamicProps: {
        required: ({ record }) =>
          !record.get('autoRevisionFlag') || record.get('autoRevisionFlag') === 'N',
        disabled: ({ record }) => record.get('autoRevisionFlag') === 'Y',
      },
    },
    {
      name: 'currentFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'siteIds',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.assignSite`).d('站点'),
      options: siteListDs,
      textField: 'displayName',
      valueField: 'siteId',
      multiple: true,
      required: true,
    },
    {
      name: 'usageFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.releasedFlag`).d('使用状态标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      disabled: true,
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'bomStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomStatus`).d('状态'),
      required: true,
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'NEW',
      dynamicProps: {
        options: ({ record, dataSet }) => {
          const bomStatusList = bomStatusListDs.toData();
          let actualBomStatusList: any = [];
          // 装配清单的状态，在不同情况下可选值不同
          // 新建的装配清单只能为NEW
          // NEW状态可修改为除了HOLD的任何状态
          // USABLE可修改为除了NEW，HOLD的任何状态
          // FREEZE可修改为除了NEW，HOLD的任何状态
          // HOLD状态前台不可手动修改
          // 改为ABANDON状态后整体页面不可修改
          if (!record?.get('bomId')) {
            // 新建的装配清单
            actualBomStatusList = bomStatusList.filter((item: any) => item.statusCode === 'NEW');
          } else {
            switch (dataSet?.getState('originBomStatus')) {
              case 'NEW':
                actualBomStatusList = bomStatusList.filter(
                  (item: any) => item.statusCode !== 'HOLD',
                );
                break;
              case 'USABLE':
              case 'FREEZE':
                actualBomStatusList = bomStatusList.filter(
                  (item: any) => !['NEW', 'HOLD'].includes(item.statusCode),
                );
                break;
              case 'HOLD':
                actualBomStatusList = bomStatusList.filter(
                  (item: any) => item.statusCode === 'HOLD',
                );
                break;
              default:
                actualBomStatusList = bomStatusList;
            }
          }
          return new DataSet({
            data: [...actualBomStatusList],
          });
        },
      },
    },
    {
      name: 'assembleAsMaterialFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.assembleAsMaterialFlag`).d('允许强制装配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'bomType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomType`).d('类型'),
      required: true,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=BOM&typeGroup=${typeGroup}`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        defaultValue: () => (uiServerCode === '/tznd' ? 'MATERIAL' : undefined),
      },
    },
    {
      name: 'primaryQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('基本数量'),
      required: true,
      precision: 6,
      min: 0,
      step: 1,
      nonStrictStep: true,
      defaultValue: 1,
      validator: value => {
        if (value <= 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('基本数量必须大于0!');
        }
        return true;
      },
    },
    {
      name: 'autoRevisionFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.autoRevisionFlag`).d('自动升版本标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => Boolean(record.get('bomId')),
      },
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
      max: 'dateTo',
      required: true,
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
      min: 'dateFrom',
    },
    {
      name: 'copiedFromBomId',
      type: FieldType.number,
    },
    {
      name: 'copiedFromBomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.copiedFromBomId`).d('来源装配清单'),
      disabled: true,
    },
    {
      name: 'copiedFromBomRevision',
      type: FieldType.string,
      disabled: true,
    },
  ],
  transport: {
    read: queryAssemblyDetail(uiServerCode),
  },
});

// 组件行DS
const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  autoLocateFirst: true,
  queryFields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('组件编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('组件描述'),
    },
  ],
  fields: [
    {
      name: 'uuid',
      type: FieldType.string,
    },
    {
      name: 'bomComponentId',
      type: FieldType.number,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('排序号'),
      required: true,
      unique: true,
      defaultValidationMessages: {
        uniqueError: intl.get(`${modelPrompt}.info.lineNumberExisted`).d('行号已存在'), // 行号重复时的报错信息
      },
      min: 0,
      step: 1,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('组件编码'),
      lovCode: 'MT.METHOD.BOM_MATERIAL',
      required: true,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteIds: dataSet.parent?.current?.get('siteIds'),
          };
        },
        disabled: ({ record }) => record?.get('bomComponentId'),
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('组件编码'),
      bind: 'materialLov.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'materialLov.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentRevision`).d('组件版本'),
      bind: 'materialLov.currentRevisionCode',
      textField: 'description',
      valueField: 'description',
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: item?.kid ? item.kid : uuid(),
                  description: item?.description ? item?.description : item,
                };
              });
            }
            if (record && firstlyQueryData.length > 0 && !record?.get('revisionCode')) {
              record?.init('revisionCode', firstlyQueryData[0].description);
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('bomComponentId') || record?.get('revisionFlag') !== 'Y';
        },
        required: ({ record }) => {
          return record?.get('revisionFlag') === 'Y' && record?.get('materialId');
        },
        lovPara: ({ record, dataSet }) => {
          return {
            tenantId,
            siteIds: dataSet.parent?.current?.get('siteIds') || undefined,
            materialId: record?.get('materialId') || undefined,
          };
        },
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('组件描述'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('组件单位'),
      bind: 'materialLov.uomName',
      disabled: true,
    },
    {
      name: 'unitQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.perQty`).d('单位用量'),
      min: 0,
      step: 1,
      precision: 6,
      nonStrictStep: true,
      required: true,
    },
    {
      name: 'bomComponentType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomComponentTypeDesc`).d('组件类型'),
      required: true,
      options: bomComponentTypeDs,
      valueField: 'typeCode',
      textField: 'description',
    },
    {
      name: 'bomComponentTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomComponentTypeDesc`).d('组件类型'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryQty`).d('数量'),
      min: 0,
      step: 1,
      precision: 6,
      nonStrictStep: true,
    },
    {
      name: 'assembleMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMethod`).d('装配方式'),
      required: true,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MATERIAL&typeGroup=ASSY_METHOD`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'attritionPolicy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attritionPolicy`).d('损耗策略'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=BOM&typeGroup=ATTRITION_POLICY`,
      valueField: 'typeCode',
      textField: 'description',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'attritionChance',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attritionChance`).d('损耗百分比(%)'),
      min: 0,
      step: 1,
      precision: 6,
      nonStrictStep: true,
      dynamicProps: {
        required: ({ record }) => {
          return ['2', '3'].includes(record.get('attritionPolicy'));
        },
        disabled: ({ record }) => {
          return !['2', '3'].includes(record.get('attritionPolicy'));
        },
      },
    },
    {
      name: 'attritionQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.attritionQty`).d('固定损耗值'),
      min: 0,
      step: 1,
      precision: 6,
      nonStrictStep: true,
      dynamicProps: {
        required: ({ record }) => {
          return ['1', '3'].includes(record?.get('attritionPolicy'));
        },
        disabled: ({ record }) => {
          return !['1', '3'].includes(record?.get('attritionPolicy'));
        },
      },
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
      max: 'dateTo',
      required: true,
    },
    {
      name: 'dateTo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
      min: 'dateFrom',
    },
    {
      name: 'issuedLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.issuedLocatorId`).d('投料库位编码'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            locatorCategoryList: ['LOCATION', 'INVENTORY'],
            siteIds: dataSet.parent?.current?.get('siteIds'),
          };
        },
      },
    },
    {
      name: 'issuedLocatorId',
      type: FieldType.number,
      bind: 'issuedLocatorLov.locatorId',
    },
    {
      name: 'issuedLocatorCode',
      type: FieldType.string,
      bind: 'issuedLocatorLov.locatorCode',
    },
    {
      name: 'issuedLocatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.issuedLocatorDesc`).d('投料库位描述'),
      bind: 'issuedLocatorLov.locatorName',
      disabled: true,
    },
    {
      name: 'keyMaterialFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.keyMaterialFlag`).d('关键件标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'assembleAsReqFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.assembleAsReqFlag`).d('按需求数量装配'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
  ],
});

// 替代物料DS
const substituteDrawerDS: () => DataSetProps = () => ({
  selection: false,
  paging: false,
  queryFields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdTo`).d('替代物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialNameTo`).d('替代物料描述'),
    },
  ],
  fields: [
    {
      name: 'bomSubstituteId',
      type: FieldType.number,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialIdTo`).d('替代物料编码'),
      lovCode: 'MT.METHOD.BOM_MATERIAL',
      required: true,
      unique: true,
      defaultValidationMessages: {
        uniqueError: intl.get(`${modelPrompt}.info.materialExisted`).d('替代物料编码已存在'), // 替代物料编码重复时的报错信息
      },
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          const parentDs = dataSet.parent?.parent;
          return {
            tenantId,
            siteIds: parentDs?.current?.get('siteIds'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdTo`).d('替代物料编码'),
      bind: 'materialLov.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'materialLov.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.substituteRevision`).d('替代物料版本'),
      bind: 'materialLov.currentRevisionCode',
      textField: 'description',
      valueField: 'description',
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: item?.kid ? item.kid : uuid(),
                  description: item?.description ? item?.description : item,
                };
              });
            }
            if (record && firstlyQueryData.length > 0 && !record?.get('revisionCode')) {
              record?.init('revisionCode', firstlyQueryData[0].description);
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('revisionFlag') !== 'Y';
        },
        required({ record }) {
          return record?.get('revisionFlag') === 'Y' && record?.get('materialId');
        },
        lovPara: ({ record, dataSet }) => {
          const parentDs = dataSet.parent?.parent;
          return {
            tenantId,
            siteIds: parentDs?.current?.get('siteIds') || undefined,
            materialId: record?.get('materialId') || undefined,
          };
        },
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialNameTo`).d('替代物料描述'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'substituteValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.substituteValue`).d('替代值'),
      min: 0,
      step: 1,
      nonStrictStep: true,
      required: true,
    },
    {
      name: 'substituteUsage',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.substituteUsage`).d('替代用量'),
      min: 0,
      step: 1,
      precision: 6,
      nonStrictStep: true,
      required: true,
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
      max: 'dateTo',
      required: true,
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
      min: 'dateFrom',
    },
  ],
});

// 参考点DS
const referencePointDrawerDS = (): DataSetProps => ({
  selection: false,
  paging: false,
  fields: [
    {
      name: 'uuid',
      type: FieldType.string,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('排序号'),
      required: true,
      min: 0,
      step: 1,
      validator: (value, name, record: any) => {
        const currentData = record?.dataSet || [];
        let existFlag = false;
        currentData.forEach(currentRecord => {
          if (
            record?.get('uuid') !== currentRecord?.get('uuid') &&
            value === currentRecord?.get(name)
          ) {
            existFlag = true;
          }
        });
        if (existFlag) {
          return intl.get(`${modelPrompt}.info.lineNumberExisted`).d('行号已存在');
        }
        return true;
      },
    },
    {
      name: 'referencePoint',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.referencePoint`).d('参考点'),
      required: true,
      validator: (value, name, record: any) => {
        const currentData = record?.dataSet || [];
        let existFlag = false;
        currentData.forEach(currentRecord => {
          if (
            record?.get('uuid') !== currentRecord?.get('uuid') &&
            value === currentRecord?.get(name)
          ) {
            existFlag = true;
          }
        });
        if (existFlag) {
          return intl.get(`${modelPrompt}.info.referencePointExisted`).d('参考点已存在');
        }
        return true;
      },
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryQty`).d('数量'),
      required: true,
      min: 0,
      step: 1,
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
  ],
});

// 复制抽屉DS
const copyDrawerDS: (typeGroup: string) => DataSetProps = typeGroup => ({
  autoCreate: true,
  fields: [
    {
      name: 'bomNameNew',
      label: intl.get(`${modelPrompt}.bomNameNew`).d('目标编码'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'revisionNew',
      label: intl.get(`${modelPrompt}.revisionNew`).d('目标版本'),
      type: FieldType.string,
      required: true,
    },
    {
      name: 'bomTypeNew',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomTypeNew`).d('目标类型'),
      required: true,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=BOM&typeGroup=${typeGroup}`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
  ],
});

export {
  siteListDS,
  bomComponentTypeDs,
  detailDS,
  lineDS,
  substituteDrawerDS,
  referencePointDrawerDS,
  copyDrawerDS,
};
