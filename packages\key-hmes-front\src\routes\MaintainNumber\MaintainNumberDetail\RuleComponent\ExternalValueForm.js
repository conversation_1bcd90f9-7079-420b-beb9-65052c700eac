import React, { Component } from 'react';
import { Form, Row, Col, InputNumber } from 'hzero-ui';
import {
  FORM_COL_3_LAYOUT,
  SEARCH_FORM_ROW_LAYOUT,
  DRAWER_FORM_ITEM_LAYOUT,
} from '@utils/constants';
import intl from 'utils/intl';
import { connect } from 'dva';

const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';

let timeout;
@connect(({ maintainNumber }) => ({
  maintainNumber,
}))
@Form.create({ fieldNameProp: null })
export default class ExternalValueForm extends Component {
  constructor(props) {
    super(props);
    props.onRef(this);
  }

  handleChange = (useKey, cleanKey, value) => {
    const { dataSource, setUsingRuleDetail, form } = this.props;
    const usingRuleDetail = {
      ...dataSource,
      [useKey]: value > 40 ? 40 : value,
      [cleanKey]: null,
    };

    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }

    timeout = setTimeout(() => {
      setUsingRuleDetail(usingRuleDetail);
      form.setFieldsValue({
        [cleanKey]: undefined,
      });
    }, 300);
  };

  render() {
    const {
      form,
      canEdit,
      dataSource,
      maintainNumber: { maintainNumberDetail = {}, userRole = 'N' },
    } = this.props;
    const { initialFlag = 'N' } = maintainNumberDetail;
    const { getFieldDecorator } = form;
    const { incomeValueLength, incomeValueLengthLimit } = dataSource;
    return (
      <Row {...SEARCH_FORM_ROW_LAYOUT}>
        <Col {...FORM_COL_3_LAYOUT}>
          <Form.Item
            {...DRAWER_FORM_ITEM_LAYOUT}
            label={intl.get(`${modelPrompt}.incomeValueLength`).d('输入值固定长度')}
          >
            {getFieldDecorator('incomeValueLength', {
              initialValue: incomeValueLength,
              rules: [
                {
                  required: !incomeValueLengthLimit,
                  message: intl.get('hzero.common.validation.notNull', {
                    name: intl.get(`${modelPrompt}.incomeValueLength`).d('输入值固定长度'),
                  }),
                },
              ],
            })(
              <InputNumber
                max={40}
                min={1}
                style={{ width: '100%' }}
                onChange={values =>
                  this.handleChange('incomeValueLength', 'incomeValueLengthLimit', values)
                }
                disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
              />,
            )}
          </Form.Item>
        </Col>
        <Col {...FORM_COL_3_LAYOUT}>
          <Form.Item
            {...DRAWER_FORM_ITEM_LAYOUT}
            label={intl.get(`${modelPrompt}.incomeValueLengthLimit`).d('输入值最大长度')}
          >
            {getFieldDecorator('incomeValueLengthLimit', {
              initialValue: incomeValueLengthLimit,
              rules: [
                {
                  required: !incomeValueLength,
                  message: intl.get('hzero.common.validation.notNull', {
                    name: intl.get(`${modelPrompt}.incomeValueLengthLimit`).d('输入值最大长度'),
                  }),
                },
              ],
            })(
              <InputNumber
                max={40}
                min={1}
                style={{ width: '100%' }}
                onChange={values =>
                  this.handleChange('incomeValueLengthLimit', 'incomeValueLength', values)
                }
                disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
              />,
            )}
          </Form.Item>
        </Col>
      </Row>
    );
  }
}
