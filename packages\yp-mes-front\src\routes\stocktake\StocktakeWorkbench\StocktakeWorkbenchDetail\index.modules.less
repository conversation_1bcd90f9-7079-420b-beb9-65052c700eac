.stocktake-workbench-page {
  :global {
    .c7n-card-head {
      .c7n-card-extra {
        padding: 0;
        display: inline-flex;
      }
    }
  }

  .select-box-view-type {
    :global {
      .c7n-pro-radio-label {
        padding-left: 10px !important;
        padding-right: 10px !important;
      }

      .c7n-pro-radio-disabled.c7n-pro-radio-button
        .c7n-pro-radio:checked
        + .c7n-pro-radio-inner
        + span {
        color: #333;
      }

      .c7n-pro-radio-disabled.c7n-pro-radio-button .c7n-pro-radio + .c7n-pro-radio-inner + span {
        color: rgba(0, 0, 0, 0.25);
      }

      .c7n-pro-select-box
        .c7n-pro-radio-disabled.c7n-pro-radio-button:last-child
        .c7n-pro-radio-inner,
      .c7n-pro-select-box .c7n-pro-radio-button:last-child {
        border-color: rgba(0, 0, 0, 0.25);
      }

      .c7n-pro-select-box
        .c7n-pro-checkbox-disabled.c7n-pro-checkbox-button
        .c7n-pro-checkbox:checked
        + .c7n-pro-checkbox-inner
        + span {
        color: #333;
      }

      .c7n-pro-checkbox:disabled + .c7n-pro-checkbox-inner + span {
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }
}

.barcode-drawer-title-wapper {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-around;
  margin-bottom: 20px;
  // padding-right: 30px;

  .barcode-drawer-title {
    flex: 1;
  }

  .barcode-drawer-title-content {
    display: inline-flex;
    align-items: center;
    justify-content: flex-end;

    .content-item {
      font-size: 12px;
      margin-right: 8px;
    }
  }
}
