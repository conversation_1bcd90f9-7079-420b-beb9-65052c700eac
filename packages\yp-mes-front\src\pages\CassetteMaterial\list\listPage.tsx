import React, { FC, useEffect, useState, } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Modal, Icon, TextField, Form, Col, Button, Row, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useDataSet, } from 'utils/hooks';
import listPageFactory from '../stores/listPageDs';
import detailPageFactory from '../stores/detailPageDs';
import productHistoryFactory from '../stores/productHistoryDs';
import materialHistoryFactory from '../stores/materialHistoryDs';
import LovModal from '@/components/BatchInput/LovModal';
import { ButtonColor, } from 'choerodon-ui/pro/es/button/enum';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import { Collapse } from 'choerodon-ui';

const Panel = Collapse.Panel;

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.cassetteMaterial';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {

  const detailDs = useDataSet(detailPageFactory, 'cassetteMaterialDetail');
  const productHistoryDs = useDataSet(productHistoryFactory, 'productHistory');
  const materialHistoryDs = useDataSet(materialHistoryFactory, 'materialHistory');
  const inputLovDS = new DataSet(InputLovDS());

  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);

  useEffect(() => {
    listDs.query();
    detailDs.query()
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'boxCode',
    },
    {
      name: 'identification',
      width: 230
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'bindTime',
      width: 150,
    },
    {
      name: 'productHistory',
      width: 150,
      renderer: ({ record }) => {
        return <a onClick={() => onProductHistory(record)}>{intl.get(`${modelPrompt}.form.productHistory`).d('产品料盒绑定历史')}</a>
      }
    },
    {
      name: 'history',
      width: 150,
      renderer: ({ record }) => {
        return <a onClick={() => onHistory(record)}>{intl.get(`${modelPrompt}.form.history`).d('料盒物料绑定历史')}</a>
      }
    },
  ];

  const columnModal: ColumnProps[] = [
    {
      name: 'boxCode',
      width: 230
    },
    {
      name: 'materialLotCode',
      width: 170
    },
    {
      name: 'materialCode',
      width: 170
    },
    {
      name: 'materialName',
      width: 170
    },
    {
      name: 'primaryUomQty',
    },
    {
      name: 'uomCode',
    },
    {
      name: 'loadTime',
      width: 150
    },
  ]

  const columnHistory: ColumnProps[] = [
    {
      name: 'boxCode',
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'enableFlag',
    },
    {
      name: 'creationDate',
      width: 150,
    },
  ]

  const columnProduct: ColumnProps[] = [
    {
      name: 'boxCode',
      width:150
    },
    {
      name: 'identification',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'enableFlag',
    },
    {
      name: 'creationDate',
      width: 150,
    },
  ]

  const onProductHistory = (record) => {
    const { boxCode } = record.toData()
    productHistoryDs.setQueryParameter('boxCode', boxCode)
    productHistoryDs.query()
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.productHistory`).d('产品料盒绑定历史'),
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table
        dataSet={productHistoryDs}
        columns={columnProduct}
      />,
    });
  }

  const onHistory = (record) => {
    const { boxCode } = record.toData()
    materialHistoryDs.setQueryParameter('boxCode', boxCode)
    materialHistoryDs.query()
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history`).d('料盒物料绑定历史'),
      drawer: true,
      style: {
        width: 720,
      },
      children: <Table
        dataSet={materialHistoryDs}
        columns={columnHistory}
      />,
    });
  }


  const handleSearch = async () => {
    listDs.query();
    console.log(listDs.queryDataSet?.toData()[0])
    detailDs.queryDataSet?.loadData(listDs.queryDataSet?.toData())
    detailDs.query()
  };

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: listDs,
    onOpenInputModal,
  };

  const renderQueryBar = ({ buttons, queryDataSet, queryFields, dataSet }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField
                name="boxCodes"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'boxCodes', '料盒编码')}
                    />
                  </div>
                }
              />
              <TextField
                name="identifications"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'identifications', '物料批标识')}
                    />
                  </div>
                }
              />
              <TextField
                name="materialLotCodes"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() => onOpenInputModal(true, 'materialLotCodes', '物料批编码')}
                    />
                  </div>
                }
              />

            </Form>
          </Col>
          <Col span={6}>
            <div>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                  queryDataSet.loadData([]);
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button onClick={handleSearch} color={ButtonColor.primary}>
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.cassetteMaterial`).d('料盒物料查询')}>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="cassetteMaterial"
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={5} // 头部显示的查询字段的数量
          searchCode="cassetteMaterial" // 动态筛选条后端接口唯一编码
          customizedCode="cassetteMaterial" // 个性化编码
        />
        <Collapse bordered={false} defaultActiveKey={['1']} >
          <Panel header={intl.get(`${modelPrompt}.title.cassetteMaterialDetail`).d('料盒装载明细')} key="1">
            <Table
              dataSet={detailDs}
              columns={columnModal}
              key="materialLotDetail"
              queryBar={TableQueryBarType.none}
              queryBarProps={{
                fuzzyQuery: false, // 是否开启模糊查询
              }}
              queryFieldsLimit={5} // 头部显示的查询字段的数量
              searchCode="materialLotDetail" // 动态筛选条后端接口唯一编码
              customizedCode="materialLotDetail" // 个性化编码
            />
          </Panel>
        </Collapse>
      </Content>
      <LovModal {...lovModalProps} />
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.cassetteMaterial'],
})(ListPage);
