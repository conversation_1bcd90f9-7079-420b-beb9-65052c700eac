/**
 * @Description: 库存查询（重构） - 预留详情DS
 * @Author: <EMAIL>
 * @Date: 2022/7/8 10:52
 * @LastEditTime: 2022/7/8 10:52
 * @LastEditors: <EMAIL>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';
const tenantId = getCurrentOrganizationId();

const drawerHeadDS = (): DataSetProps => ({
  autoCreate: true,
  paging: false,
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      disabled: true,
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
      disabled: true,
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      disabled: true,
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
      disabled: true,
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      disabled: true,
    },
    {
      name: 'locatorDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorDesc`).d('库位描述'),
      disabled: true,
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
      disabled: true,
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者'),
      disabled: true,
    },
    {
      name: 'lotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCode`).d('批次'),
      disabled: true,
    },
    {
      name: 'holdQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.holdQtyTotal`).d('预留总量'),
      disabled: true,
    },
  ],
});

const drawerTableDS = (): DataSetProps => {
  return {
    selection: false,
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    autoLocateFirst: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryFields: [
      {
        name: 'locatorId',
        type: FieldType.number,
      },
      {
        name: 'lotCode',
        type: FieldType.string,
      },
      {
        name: 'materialId',
        type: FieldType.number,
      },
      {
        name: 'ownerId',
        type: FieldType.number,
      },
      {
        name: 'ownerType',
        type: FieldType.string,
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
      },
      {
        name: 'siteId',
        type: FieldType.number,
      },
    ],
    fields: [
      {
        name: 'holdTypeDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.holdType`).d('预留类型'),
      },
      {
        name: 'orderTypeDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.orderType`).d('预留指令类型'),
      },
      {
        name: 'orderCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.orderId`).d('预留指令编码'),
      },
      {
        name: 'holdQuantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.holdQuantity`).d('预留数量'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inv-onhand-hold/limit-property/list/ui`,
          method: 'POST',
        };
      },
    },
  };
};

export { drawerHeadDS, drawerTableDS };
