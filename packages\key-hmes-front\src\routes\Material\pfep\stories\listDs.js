import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { getCurrentSiteInfo } from '@utils/utils';

const modelPrompt = 'tarzan.material-pfep-manager.model.dataItem';
const tenantId = getCurrentOrganizationId();

/**
 * 列表
 */
const listDS = () => ({
  primaryKey: 'tagCode',
  queryUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-assemble-group/list/ui`,
  autoQuery: true,
  autoCreate: true,
  dataKey: 'content',
  totalKey: 'totalElements',
  // selection: 'single',
  selection: false,
  queryFields: [
    {
      name: 'siteIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.siteId`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        defaultValue: () => {
          const siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo };
          }
          return undefined;
        },
      },
      required: false,
    },
    {
      name: 'siteIdList',
      bind: 'siteIdObj.siteId',
    },

    {
      name: 'materialIdObj',
      type: 'object',
      label: intl.get(`${modelPrompt}.materialId`).d('物料'),
      lovCode: 'MT.MATERIAL',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      required: false,
    },
    {
      name: 'materialIdList',
      bind: 'materialIdObj.materialId',
    },

    {
      name: 'makeBuyCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.makeBuyCode`).d('制造采购标识'),
      lookupCode: 'MT.METHOD.MAKE_BUY_CODE1',
    },
    {
      name: 'highPriceFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.highPriceFlag`).d('高价值物料标识'),
      lookupCode: 'WMS.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'coaApproveGlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.coaApproveGlag`).d('COA自动审核标识'),
      lookupCode: 'WMS.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'snpControlFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.snpControlFlag`).d('SNP管控标识'),
      lookupCode: 'WMS.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'pickFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.pickFlag`).d('取货标识'),
      lookupCode: 'WMS.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'materialCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: 'string',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'makeBuyCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.makeBuyCode`).d('制造采购标识'),
      lookupCode: 'MT.METHOD.MAKE_BUY_CODE1',
    },
    {
      name: 'highPriceFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.highPriceFlag`).d('高价值物料标识'),
    },
    {
      name: 'coaApproveGlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.coaApproveGlag`).d('COA自动审核标识'),
    },
    {
      name: 'pickFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.pickFlag`).d('取货标识'),
    },
    {
      name: 'minRemainShelfLife',
      type: 'string',
      label: intl.get(`${modelPrompt}.minRemainShelfLife`).d('期望最小剩余寿命'),
    },
    {
      name: 'shelfLife',
      type: 'string',
      label: intl.get(`${modelPrompt}.shelfLife`).d('保质期'),
    },
    {
      name: 'extendedShelfLife',
      type: 'string',
      label: intl.get(`${modelPrompt}.extendedShelfLife`).d('延保期'),
    },
    {
      name: 'dullPeriod',
      type: 'string',
      label: intl.get(`${modelPrompt}.dullPeriod`).d('呆滞期'),
    },
    {
      name: 'dullPeriodSecondary',
      type: 'string',
      label: intl.get(`${modelPrompt}.dullPeriodSecondary`).d('二级呆滞期'),
    },
    {
      name: 'earlyWarningLeadTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.earlyWarningLeadTime`).d('预警提前期'),
    },
    {
      name: 'shelfLifeUomCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.shelfLifeUomCode`).d('时间单位编码'),
    },
    {
      name: 'minStockQty',
      type: 'string',
      label: intl.get(`${modelPrompt}.minStockQty`).d('最小存储库存'),
    },
    {
      name: 'maxStockQty',
      type: 'string',
      label: intl.get(`${modelPrompt}.maxStockQty`).d('最大存储库存'),
    },
    {
      name: 'minPackageQty',
      type: 'string',
      label: intl.get(`${modelPrompt}.minPackageQty`).d('最小单包量'),
    },
    {
      name: 'packMinQty',
      type: 'string',
      label: intl.get(`${modelPrompt}.packMinQty`).d('领料最小包装数'),
    },
    {
      name: 'mareqPackType',
      type: 'string',
      label: intl.get(`${modelPrompt}.mareqPackType`).d('领料包装类型'),
      lookupCode:'WMS.MAREQ_PACK_TYPE'
    },
    {
      name: 'packageWeight',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageWeight`).d('存储包装重量'),
    },
    {
      name: 'weightUomCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.weightUomCode`).d('重量单位'),
    },
    {
      name: 'packageLength',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageLength`).d('单位包装长'),
    },
    {
      name: 'packageWidth',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageWidth`).d('单位包装宽'),
    },
    {
      name: 'packageHeight',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageHeight`).d('单位包装高'),
    },
    {
      name: 'packageSizeUomCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageSizeUomCode`).d('包装尺寸单位'),
    },
    {
      name: 'loadingArea',
      type: 'string',
      label: intl.get(`${modelPrompt}.loadingArea`).d('单托装载面积'),
    },
    {
      name: 'loadingFloor',
      type: 'string',
      label: intl.get(`${modelPrompt}.loadingFloor`).d('装载堆叠层数'),
    },
    {
      name: 'packageType',
      type: 'string',
      label: intl.get(`${modelPrompt}.packageType`).d('包装类型'),
    },
    {
      name: 'singleBoxes',
      type: 'string',
      label: intl.get(`${modelPrompt}.singleBoxes`).d('单托箱数'),
    },
    {
      name: 'deliverGroup',
      type: 'string',
      label: intl.get(`${modelPrompt}.deliverGroup`).d('送货是否组托'),
    },
    {
      name: 'receivedGroup',
      type: 'string',
      label: intl.get(`${modelPrompt}.receivedGroup`).d('上架是否组托'),
    },
    {
      name: 'mixLotFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.mixLotFlag`).d('是否混批'),
    },
    {
      name: 'lotControlFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.lotControlFlag`).d('是否批次限制'),
    },
    {
      name: 'containerType',
      type: 'string',
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
    },
    {
      name: 'snpControlFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.snpControlFlag`).d('SNP管控标识'),
    },
    {
      name: 'aPointSupplier',
      type: 'string',
      label: intl.get(`${modelPrompt}.aPointSupplier`).d('A点供应商'),
    },
    {
      name: 'poLineCreationFlag',
      lookupCode: 'MT.YES_NO',
      type: 'string',
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.poLineCreationFlag`).d('PO行创建标识'),
    },
    {
      name: 'mrpType',
      type: 'string',
      label: intl.get(`${modelPrompt}.mrpType`).d('MRP类型'),
      lookupCode: 'WMS_MRP_TYPE',
    },
    {
      name: 'mrpController',
      type: 'string',
      label: intl.get(`${modelPrompt}.mrpController`).d('MRP控制者'),
      lookupCode: 'WMS_MRP_CONTROLLER',
    },
    {
      name: 'batchProgram',
      type: 'string',
      label: intl.get(`${modelPrompt}.batchProgram`).d('批量程序'),
      lookupCode: 'WMS_MRP_PROGRAM',
    },
    {
      name: 'maxBatch',
      type: 'string',
      label: intl.get(`${modelPrompt}.maxBatch`).d('最大批量'),
    },
    {
      name: 'minBatch',
      type: 'string',
      label: intl.get(`${modelPrompt}.minBatch`).d('最小批量'),
    },
    {
      name: 'fixedBatch',
      type: 'string',
      label: intl.get(`${modelPrompt}.fixedBatch`).d('固定批量'),
    },
    {
      name: 'safetyInventory',
      type: 'string',
      label: intl.get(`${modelPrompt}.safetyInventory`).d('安全库存'),
    },
    {
      name: 'receiveLocatorCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.receiveLocatorId`).d('默认收货仓库'),
    },
    {
      name: 'seifmadeProductionTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.seifmadeProductionTime`).d('自制生产时间'),
    },
    {
      name: 'receiveProcessTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.receiveProcessTime`).d('收货处理时间'),
    },
    {
      name: 'procureAdvance',
      type: 'string',
      label: intl.get(`${modelPrompt}.procureAdvance`).d('采购提前期'),
    },
    {
      name: 'longCycleFlag',
      type: 'string',
      label: intl.get(`${modelPrompt}.longCycleFlag`).d('长周期标识'),
      defaultValue: '禁用',
      lookupCode: 'WMS.ENABLE_FLAG',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/wms-mrp-inventorys`,
        method: 'GET',
      };
    },
  },
});

export { listDS };
