/**
 * @Description: 执行作业管理详情
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:40:22
 * @LastEditTime: 2023-05-18 14:43:08
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState, useRef } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { isEmpty } from 'lodash';
import {
  DataSet,
  Form,
  Select,
  Lov,
  TextField,
  NumberField,
  DateTimePicker,
  Icon,
  Dropdown,
  Menu,
  Modal,
  Switch,
} from 'choerodon-ui/pro';
import { Radio, Collapse } from 'choerodon-ui';
import myInstance from '@utils/myAxios';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import { AttributeDrawer, drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { C7nFormItemSort } from '@/components/tarzan-ui';
import { getResponse } from '@utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import notification from 'utils/notification';
import uuid from 'uuid/v4';

import { formDS, defaultBomRouterMaterialDS, bomAndRouterDS } from '../stores/ExecuteDetailDS';

import styles from '../index.module.less';

import TotalList from './TotalList';
import DetailList from './DetailList';
import SplitWoDrawer from './SplitWoDrawer';
import MergeWoDrawer from './MergeWoDrawer';
import BomAndRouterDrawer from './BomAndRouterDrawer';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.workshop.execute';

const ExecuteDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    custConfig,
    customizeForm,
  } = props;

  // 加工实绩装配实绩组件
  const detailListRef = useRef();
  // 编辑开关
  const [canEdit, setCanEdit] = useState(false);
  // 实绩信息初始化
  const [isFirst, setIsFirst] = useState(false);
  // 基础信息&实绩信息切换
  const [pageSwitch, setPageSwitch] = useState('left');
  // 实绩信息进度数据
  const [listData, setListData] = useState({});
  // 抽屉编辑开关
  const [splitVisible, setSplitVisible] = useState(false);
  const [mergeVisible, setMergeVisible] = useState(false);

  const [linkRe, setLinkRe] = useState(1);
  const [status, setStatus] = useState(undefined);

  // 记录初始装配清单/工艺路线/生产线版本/生产版本信息
  const [defaultBomRouter, setDefaultBomRouter] = useState({});
  // 根据站点、物料、生产线、生产版本自动带出来的bom
  const [autoBroughtOutBomInfo, setAutoBroughtOutBomInfo] = useState({});
  // 根据站点、物料、生产线、生产版本自动带出来的router
  const [autoBroughtOutRouterInfo, setAutoBroughtOutRouterInfo] = useState({});

  // 表单信息DS
  const formDs = useMemo(() => {
    return new DataSet(formDS());
  }, []);

  const defaultBomRouterMaterialDs = useMemo(() => {
    return new DataSet(defaultBomRouterMaterialDS());
  }, []);

  // 装配清单和工艺路线弹窗的DS
  const bomAndRouterDs = useMemo(() => {
    return new DataSet(bomAndRouterDS());
  }, []);

  const { run } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/save/ui`,
      method: 'POST',
    },
    { manual: true, needPromise: true },
  );

  useEffect(() => {
    if (formDs) {
      formDs.addEventListener('update', handleQueryDataSetUpdate);
    }
    return () => {
      if (formDs) {
        formDs.removeEventListener('update', handleQueryDataSetUpdate);
      }
    };
  });

  useEffect(() => {
    if (id === 'create') {
      formDs.current.set('kid', uuid());
      setCanEdit(true);
    } else {
      // 编辑开关
      setCanEdit(false);
      setPageSwitch('left');
      setSplitVisible(false);
      setMergeVisible(false);
      queryForm(id);
    }
  }, [id]);

  const handleQueryDataSetUpdate = () => {
    setLinkRe(linkRe + 1);
  };

  // 查询上方表单,下方表格
  const queryForm = eoId => {
    formDs.setQueryParameter('eoId', eoId);
    formDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.EO.DETAIL.BASIC`);
    formDs.query().then(res => {
      if (res && res.rows) {
        const {
          eoBomId,
          eoBomName,
          eoBomRevision,
          makeOrderNum,
          siteId,
          materialId,
          eoRouterId,
          eoRouterName,
          eoRouterRevision,
          productionLineId,
          productionVersionCode,
        } = res.rows;
        setDefaultBomRouter({
          eoBomId,
          eoBomName,
          eoBomRevision,
          eoRouterId,
          eoRouterName,
          eoRouterRevision,
          productionLineId,
          productionVersionCode,
        });
        setStatus(res.rows.status);
        formDs.current.set('bomType', 'MATERIAL');
        formDs.current.set('routerType', 'MATERIAL');
        if (makeOrderNum) {
          formDs.current.set('productionVersionRequire', 'N');
          formDs.current.set('productionVersionDisable', 'Y');
        } else {
          const url = `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-production-version/prod-version-edit-verify/ui`;
          myInstance
            .post(url, {
              siteId,
              materialId,
            })
            .then(response => {
              if (response.data.success) {
                if (response.data.rows === 'Y') {
                  formDs.current.set('productionVersionRequire', 'Y');
                  formDs.current.set('productionVersionDisable', 'Y');
                } else if (response.data.rows === 'N') {
                  formDs.current.set('productionVersionRequire', 'N');
                  formDs.current.set('productionVersionDisable', 'N');
                }
              }
            });
        }
      }
      setLinkRe(linkRe + 1);
    });

    totalListQuery(eoId);
  };

  const totalListQuery = async eoId => {
    const response = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/actual-qty/collect/ui`,
      {
        method: 'GET',
        query: {
          eoId,
        },
      },
    );
    const res = getResponse(response);
    if (res && res.rows) {
      const stepWipQty =
        (res.rows.stepWipQty || 0) +
        (res.rows.stepWipScrapQty || 0) +
        (res.rows.stepWipHoldQty || 0) || 0;
      const other =
        (res.rows.unStartedQty || 0) +
        (res.rows.stepWipQty || 0) +
        (res.rows.completedQty || 0) +
        (res.rows.scrapConfirmQty || 0) -
        (res.rows.qty || 0);
      const totalListData = {
        list1: [
          {
            name: 'unStartedQty',
            title: intl.get(`${modelPrompt}.unStartedQty`).d('未开始数量'),
            value: (res.rows.unStartedQty || 0).toFixed(3) - 0,
          },
          {
            name: 'stepWipQty',
            title: intl.get(`${modelPrompt}.stepWipQty`).d('在制数量'),
            value: stepWipQty.toFixed(3) - 0,
            max: stepWipQty.toFixed(3) - 0,
            children: [
              {
                name: 'stepWipQty',
                title: intl.get(`${modelPrompt}.stepWipQty`).d('在制数量'),
                value: (res.rows.stepWipQty || 0).toFixed(3) - 0,
              },
              {
                name: 'stepWipScrapQty',
                title: intl.get(`${modelPrompt}.stepWipScrapQty`).d('在制报废数量'),
                value: (res.rows.stepWipScrapQty || 0).toFixed(3) - 0,
              },
              // {
              //   name: 'stepWipHoldQty',
              //   title: intl.get(`${modelPrompt}.stepWipHoldQty`).d('在制保留数量'),
              //   value: (res.rows.stepWipHoldQty || 0).toFixed(3) - 0,
              // },
            ],
          },
          {
            name: 'completedQty',
            title: intl.get(`${modelPrompt}.completedQty`).d('作业已完工数量'),
            value: (res.rows.completedQty || 0).toFixed(3) - 0,
          },
          {
            name: 'scrapConfirmQty',
            title: intl.get(`${modelPrompt}.scrapConfirmQty`).d('报废确认数量'),
            value: (res.rows.scrapConfirmQty || 0).toFixed(3) - 0,
          },
        ],
        list2: [
          {
            name: 'completedQty',
            title: intl.get(`${modelPrompt}.completedQty`).d('完成数量'),
            value: (res.rows.completedQty || 0).toFixed(3) - 0,
          },
          {
            name: 'kitQty',
            title: intl.get(`${modelPrompt}.kitQty`).d('齐套数量'),
            value: (res.rows.kitQty || 0).toFixed(3) - 0,
          },
        ],
        max: res.rows.qty,
        other: other.toFixed(3) - 0,
      };
      setListData(totalListData);
    } else {
      setListData({});
    }
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/workshop/execute-operation-management/list');
    } else {
      setCanEdit(false);
      formDs.reset();
      queryForm(id);
    }
  };

  const revisionLinkBom = () => {
    const _id = formDs.current.get('eoBomId');
    if (_id > 0) {
      props.history.push(`/hmes/product/manufacture-list/dist/${_id}`);
    }
  };

  const revisionLinkRouter = () => {
    const _id = formDs.current.get('eoRouterId');
    if (_id > 0) {
      props.history.push(`/hmes/new/manufacture-process/routes-c7n/dist/${_id}`);
    }
  };

  const handleSave = async () => {
    formDs.current.set({ nowDate: new Date().getTime() });
    const validate = await formDs.validate();
    if (
      (defaultBomRouter.productionLineId !== formDs.current.get('productionLineId') ||
        defaultBomRouter.productionVersionCode !== formDs.current.get('productionVersionCode')) &&
      (autoBroughtOutRouterInfo.routerName || autoBroughtOutBomInfo.bomName)
    ) {
      // 编辑生产指令时，修改过生产线或者生产版本，弹确认框
      await showConfirmModal();
      return;
    }
    // bom和router没有发生改变时的保存,bomType和routerType为true，意味着保存的是EO类型的bom和router
    formDs.current.set('bomType', 'EO');
    formDs.current.set('routerType', 'EO');
    if (validate) {
      await formDs.submit().then(res => {
        const { rows = [] } = res || {};
        if (!isEmpty(rows) && rows[0].success) {
          setCanEdit(false);
          const newId = res.rows[0].rows;
          if (id === 'create') {
            props.history.push(`/hmes/workshop/execute-operation-management/detail/${newId}`);
          } else {
            queryForm(newId);
          }
        }
      });
    }
  };

  // 确认保存弹窗
  const showConfirmModal = async () => {
    const _saveData = {};
    _saveData.eoBomId = autoBroughtOutBomInfo.bomId;
    _saveData.eoBomName = autoBroughtOutBomInfo.bomName;
    _saveData.eoBomRevision = autoBroughtOutBomInfo.revision;
    _saveData.eoRouterId = autoBroughtOutRouterInfo.routerId;
    _saveData.eoRouterName = autoBroughtOutRouterInfo.routerName;
    _saveData.eoRouterRevision = autoBroughtOutRouterInfo.revision;
    await run({
      params: {
        ...formDs.current.toData(),
        ..._saveData,
      },
    }).then(res => {
      if (res && res.success) {
        setCanEdit(false);
        notification.success();
        if (id === 'create') {
          props.history.push(`/hmes/workshop/execute-operation-management/detail/${res.rows}`);
        } else {
          queryForm(res.rows);
        }
      }
    });
  };

  const handleChangeBomAndRouter = async () => {
    const {
      siteId,
      materialId,
      productionLineId,
      revisionCode,
      productionVersionCode,
    } = formDs.current.toData();
    const queryParams = {
      siteId,
      materialId,
      revisionCode,
      productionVersionCode,
    };
    bomAndRouterDs.create({
      ...queryParams,
    });
    // 获取重读使用的bom和router
    const url = `${BASIC.HMES_BASIC
      }/v1/${tenantId}/mt-work-order/default/bom/router?materialId=${materialId ||
      ''}&prodLineId=${productionLineId || ''}&siteId=${siteId || ''}&revisionCode=${revisionCode ||
      ''}`;
    let querySuccess = false;
    await myInstance.get(url).then(response => {
      const { rows, success } = getResponse(response.data);
      if (success) {
        querySuccess = true;
        const productionVersion = {
          productionVersionId: rows.productionVersionId,
          productionVersionCode: rows.productionVersionCode,
          productionVersionDesc: rows.productionVersionDesc,
        };
        const bom = {
          bomId: rows.bomId,
          bomName: rows.bomName,
          revision: rows.bomRevision,
        };
        const router = {
          routerId: rows.routerId,
          routerName: rows.routerName,
          revision: rows.routerRevision,
        };
        bomAndRouterDs.current.set('designProductionVersion', productionVersion);
        bomAndRouterDs.current.set('designBom', bom);
        bomAndRouterDs.current.set('designRouter', router);
      }
    });
    if (!querySuccess) {
      return;
    }
    if (formDs.current.get('productionVersionDisable') === 'Y') {
      // 有生产版本————感觉之前设置的disable不对，此时应该为N才对
      bomAndRouterDs.current.set('ownProductionVersion', true);
    }
    await Modal.open({
      ...drawerPropsC7n({ ds: bomAndRouterDs }),
      drawer: false,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.bomAndRouter`).d('装配清单/工艺路线'),
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      children: <BomAndRouterDrawer ds={bomAndRouterDs} />,
      onOk: async () => {
        if (!(await bomAndRouterDs.validate())) {
          return false;
        }
        const data = bomAndRouterDs.current.toData();
        const _saveData = {};
        if (data.selectType === 'designChange') {
          // 设计变更
          if (data.ownProductionVersion) {
            // formDs.current.set('productionVersion', data.designProductionVersion);
            _saveData.productionVersionId = data.designProductionVersion.productionVersionId;
            _saveData.productionVersionCode = data.designProductionVersion.productionVersionCode;
          }
          // formDs.current.set('bom', data.designBom);
          // formDs.current.set('router', data.designRouter);
          _saveData.eoBomId = data.designBom.bomId;
          _saveData.eoBomName = data.designBom.bomName;
          _saveData.eoBomRevision = data.designBom.revision;
          _saveData.eoRouterId = data.designRouter.routerId;
          _saveData.eoRouterName = data.designRouter.routerName;
          _saveData.eoRouterRevision = data.designRouter.revision;
        }
        if (data.selectType === 'textMaterial') {
          // 物料
          if (data.ownProductionVersion) {
            // formDs.current.set('productionVersion', data.productionVersion);
            _saveData.productionVersionId = data.productionVersion.productionVersionId;
            _saveData.productionVersionCode = data.productionVersion.productionVersionCode;
          }
          // formDs.current.set('bom', data.bom);
          // formDs.current.set('router', data.router);
          _saveData.eoBomId = data.bom.bomId;
          _saveData.eoBomName = data.bom.bomName;
          _saveData.eoBomRevision = data.bom.revision;
          _saveData.eoRouterId = data.router.routerId;
          _saveData.eoRouterName = data.router.routerName;
          _saveData.eoRouterRevision = data.router.revision;
        }
        if (data.selectType === 'textOrder') {
          // 指令
          // formDs.current.set('bom', data.bom);
          // formDs.current.set('router', data.router);
          _saveData.eoBomId = data.bom.bomId;
          _saveData.eoBomName = data.bom.bomName;
          _saveData.eoBomRevision = data.bom.revision;
          _saveData.bomType = data.bom.bomType;
          _saveData.eoRouterId = data.router.routerId;
          _saveData.eoRouterName = data.router.routerName;
          _saveData.eoRouterRevision = data.router.revision;
          _saveData.routerType = data.router.routerType;
        }
        return run({
          params: {
            ...formDs.current.toData(),
            ..._saveData,
          },
        }).then(res => {
          if (res && res.success) {
            notification.success();
            if (id === 'create') {
              props.history.push(`/hmes/workshop/execute-operation-management/detail/${res.rows}`);
            } else {
              queryForm(res.rows);
            }
          } else {
            return Promise.resolve(false);
          }
        });
      },
    });
  };

  const handlePageChange = e => {
    if (!isFirst) {
      setIsFirst(true);
    }
    setPageSwitch(e.target.value);
    modalRefresh();
  };

  const clickSplitMergeMenu = ({ key }) => {
    switch (key) {
      case 'WOSPLIT':
        setSplitVisible(true);
        break;
      case 'WOMERGE':
        setMergeVisible(true);
        break;
      default:
        break;
    }
  };

  const drawerCancel = () => {
    setSplitVisible(false);
    setMergeVisible(false);
  };

  const modalRefresh = () => {
    queryForm(id);
    if (detailListRef.current) {
      detailListRef.current.queryWorking();
      detailListRef.current.queryBom();
    }
  };

  const getRevisionColor = name => {
    if (linkRe) {
      if (formDs.current.get(name) > 0) {
        return '#29bece';
      }
      return `rgba(0,0,0,0.25)`;
    }
  };

  // const textAfter = (type) => {
  //   return type
  //     ? intl.get(`${modelPrompt}.textOrder`).d('指令')
  //     : intl.get(`${modelPrompt}.textMaterial`).d('物料');
  // };

  const clickMenu = async ({ key }) => {
    const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/status/update/ui`, {
      method: 'POST',
      body: {
        eoIds: [id],
        operationType: key,
      },
    });
    const res = getResponse(response);
    if (res) {
      notification.success();
      queryForm(id);
    }
  };

  const productionLineChange = () => {
    formDs.current.init('productionVersion', undefined);
    formDs.current.init('bom', undefined);
    formDs.current.init('router', undefined);
    getDefaultBomRouterMaterial();
  };

  const getDefaultBomRouterMaterial = () => {
    const { siteId, materialId, productionLineId, revisionCode } = formDs.current.toData();

    if (siteId && materialId && productionLineId) {
      defaultBomRouterMaterialDs.queryParameter = {
        materialId,
        prodLineId: productionLineId,
        siteId,
        revisionCode: revisionCode || '',
      };
      defaultBomRouterMaterialDs.query().then(res => {
        if (res && res.rows) {
          const {
            completeControlQty,
            completeControlType,
            completionLocatorCode,
            completionLocatorId,
            completionLocatorName,
          } = res.rows;
          formDs.current.set('completeControlType', completeControlType);
          formDs.current.set('completeControlQty', completeControlQty);
          formDs.current.set('locator', {
            locatorId: completionLocatorId,
            locatorCode: completionLocatorCode,
            locatorName: completionLocatorName,
          });
          searchDefaultProductionVersion(res.rows);
        } else {
          searchDefaultProductionVersion({});
        }
      });
    }
  };

  const searchDefaultProductionVersion = (detail = {}) => {
    const { siteId, materialId, productionLineId, revisionCode } = formDs.current.toData();

    if (siteId && materialId && productionLineId) {
      const url = `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-production-version/material-limit-prod-version-get/ui`;
      myInstance
        .post(url, {
          siteId,
          materialId,
          organizationId: productionLineId,
          revisionCode,
        })
        .then(res => {
          if (res.data.success) {
            const { productionVersionId, productionVersionCode } = res.data.rows;
            let _detail = detail;
            if (productionVersionId) {
              _detail = res.data.rows;
            }
            const { bomName, bomId, bomRevision, routerId, routerName, routerRevision } = _detail;
            if (bomName || bomId) {
              setAutoBroughtOutBomInfo({
                bomName,
                bomId,
                revision: bomRevision,
              });
              // formDs.current.set('bom', {
              //   bomName,
              //   bomId,
              //   revision: bomRevision,
              // });
            } else {
              // formDs.current.set('bom', undefined);
              setAutoBroughtOutBomInfo({});
            }
            if (routerId || routerName) {
              setAutoBroughtOutRouterInfo({
                routerId,
                routerName,
                revision: routerRevision,
              });
              // formDs.current.set('router', {
              //   routerId,
              //   routerName,
              //   revision: routerRevision,
              // });
            } else {
              setAutoBroughtOutRouterInfo({});
              // formDs.current.set('router', undefined);
            }
            formDs.current.set('productionVersionCode', productionVersionCode);
            formDs.current.set('productionVersionId', productionVersionId);
            if (
              id !== 'create' &&
              productionLineId === defaultBomRouter.productionLineId &&
              productionVersionCode === defaultBomRouter.productionVersionCode
            ) {
              formDs.current.set('bom', {
                bomId: defaultBomRouter.bomId,
                bomName: defaultBomRouter.bomName,
                revision: defaultBomRouter.bomRevision,
              });
              formDs.current.set('router', {
                routerId: defaultBomRouter.routerId,
                routerName: defaultBomRouter.routerName,
                revision: defaultBomRouter.routerRevision,
              });
            }
          }
        });
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.list`).d('执行作业管理')}
        backPath="/hmes/workshop/execute-operation-management/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              disabled={!status || status === 'CLOSED' || status === 'ABANDON'}
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton type="c7n-pro" icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            disabled={!status || status === 'CLOSED' || status === 'ABANDON'}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <Dropdown
          overlay={
            <Menu
              onClick={clickSplitMergeMenu}
              className={styles['split-menu']}
              style={{ width: '100px' }}
            >
              <Menu.Item key="WOSPLIT">
                <a>{intl.get(`${modelPrompt}.splitWo`).d('执行作业拆分')}</a>
              </Menu.Item>
              <Menu.Item key="WOMERGE">
                <a>{intl.get(`${modelPrompt}.mergeWo`).d('执行作业合并')}</a>
              </Menu.Item>
            </Menu>
          }
          trigger={['click']}
          disabled={id === 'create' || canEdit}
        >
          <PermissionButton
            type="c7n-pro"
            icon="keyboard_tab"
            disabled={
              id === 'create' ||
              canEdit ||
              // ['NEW', 'WORKING', 'RELEASED', 'HOLD'].indexOf(status) === -1 ||
              !status
            }
            permissionList={[
              {
                code: `${path}.button.splitMerge`,
                type: 'button',
                meaning: '详情页-拆分合并按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.splitMerge`).d('拆分合并')}
          </PermissionButton>
        </Dropdown>
        <PermissionButton
          type="c7n-pro"
          icon="cached"
          disabled={
            id === 'create' ||
            canEdit ||
            ['NEW', 'WORKING', 'RELEASED', 'HOLD'].indexOf(status) === -1 ||
            !status
          }
          onClick={handleChangeBomAndRouter}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.changeBomAndRouter`).d('修改装配清单/工艺路线')}
        </PermissionButton>
        <AttributeDrawer
          serverCode={BASIC.HMES_BASIC}
          className="org.tarzan.mes.domain.entity.MtEo"
          kid={id}
          canEdit={canEdit}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.EO.DETAIL.ATTR`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        {!canEdit && (
          <div>
            <Radio.Group onChange={handlePageChange} value={pageSwitch} style={{ marginBottom: 8 }}>
              <Radio.Button value="left">
                {intl.get(`${modelPrompt}.basicInfo`).d('基础信息')}
              </Radio.Button>
              <Radio.Button value="right">
                {intl.get(`${modelPrompt}.achieveInfo`).d('实绩信息')}
              </Radio.Button>
            </Radio.Group>
          </div>
        )}
        <div style={{ display: pageSwitch === 'left' || canEdit ? 'block' : 'none' }}>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location', 'produce']}>
            <Panel
              header={intl.get(`${modelPrompt}.title.basic`).d('基本属性')}
              key="basicInfo"
              dataSet={formDs}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.EO.DETAIL.BASIC`,
                },
                <Form
                  disabled={!canEdit}
                  dataSet={formDs}
                  columns={3}
                  labelLayout="horizontal"
                  labelWidth={110}
                >
                  <TextField name="eoNum" />
                  <Lov name="site" />
                  <TextField name="siteName" />
                  <Select name="eoType" />
                  <Select name="status" />
                  <TextField name="workOrderNum" />
                  <TextField name="identification" />
                  <TextField name="qualityStatusDesc" />
                </Form>,
              )}
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.requirement`).d('需求属性')}
              key="location"
              dataSet={formDs}
            >
              <Form
                disabled={!canEdit}
                dataSet={formDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <C7nFormItemSort name="material" itemWidth={['70%', '30%']}>
                  <Lov name="material" />
                  <Select
                    name="revisionCode"
                    dropdownMatchSelectWidth={false}
                    dropdownMenuStyle={{ width: '200px' }}
                  />
                </C7nFormItemSort>
                <TextField name="materialName" />
                <NumberField name="qty" />
                <TextField name="uomCode" />
                <TextField name="uomName" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.produce`).d('生产属性')}
              key="produce"
              dataSet={formDs}
            >
              <Form
                disabled={!canEdit}
                dataSet={formDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <DateTimePicker name="planStartTime" />
                <DateTimePicker name="planEndTime" />
                <TextField name="productionVersionCode" />
                <Lov name="productionLine" onChange={productionLineChange} />
                <TextField name="productionLineName" />
                <C7nFormItemSort name="bom" itemWidth={['68%', '17%', '15%']}>
                  <TextField name="eoBomName" />
                  <TextField name="eoBomRevision" />
                  <Icon
                    type="link2"
                    itemType="link"
                    onClick={revisionLinkBom}
                    style={{ color: getRevisionColor('eoBomId') }}
                    iconDisabled
                  />
                </C7nFormItemSort>
                <Switch name="reworkFlag" />
                <Switch name="concessiveInterceptionFlag" />
                <C7nFormItemSort name="router" itemWidth={['68%', '17%', '15%']}>
                  <TextField name="eoRouterName" />
                  <TextField name="eoRouterRevision" />
                  <Icon
                    type="link2"
                    itemType="link"
                    onClick={revisionLinkRouter}
                    style={{ color: getRevisionColor('eoRouterId') }}
                    iconDisabled
                  />
                </C7nFormItemSort>
                <Switch name="overOrderInterceptionFlag" />
                <Switch name="dischargeFlag" />
                <Switch name="degradeFlag" />
              </Form>
            </Panel>
          </Collapse>
        </div>
        {isFirst && (
          <div style={{ display: pageSwitch === 'right' && !canEdit ? 'block' : 'none' }}>
            <TotalList listData={listData} />
            <DetailList ref={detailListRef} id={id} />
          </div>
        )}
      </Content>
      <SplitWoDrawer
        visible={splitVisible}
        handleRefresh={modalRefresh}
        handleCancel={drawerCancel}
        eoId={id}
        history={props.history}
        status={status}
      />
      <MergeWoDrawer
        visible={mergeVisible}
        handleRefresh={modalRefresh}
        handleCancel={drawerCancel}
        eoId={id}
        history={props.history}
        status={status}
      />
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.workshop.execute', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.EO.DETAIL.ATTR`, `${BASIC.CUSZ_CODE_BEFORE}.EO.DETAIL.BASIC`],
  })(ExecuteDetail),
);
