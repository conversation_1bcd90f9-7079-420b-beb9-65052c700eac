// 物料PFEP属性维护
import React, { useEffect } from 'react';
import { Badge } from 'choerodon-ui';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import ExcelExport from 'components/ExcelExport';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { isNil } from 'lodash';
import { openTab } from 'utils/menuTab';
import { getCurrentOrganizationId } from 'utils/utils';
import queryString from 'querystring';
import { listDS } from '../stories/listDs';

const page = props => {
  const {
    match: { path },
    listDs,
  } = props;

  const flagRender = ({ value,record },attr) =>{

    return <Badge
      status={value === 'Y' ? 'success' : 'error'}
      text={
        value==='Y'?record.get(attr):record.getField('longCycleFlag').getText('N')
      }
    />
  }

  const columns = [
    { name: 'siteCode' },
    {
      name: 'materialCode',
      width: 150,
      renderer: ({ record, text }) => {
        return (
          <a
            onClick={() => {
              const materialSiteId = record.get('materialSiteId') || 0;
              const pfepInventoryId = record.get('pfepInventoryId') || 0;
              props.history.push(
                `/hmes/product/material-pfep-manager/detail/${materialSiteId},${pfepInventoryId}`,
              );
            }}
          >
            {text}
          </a>
        );
      },
    },
    { name: 'materialName' },
    { name: 'makeBuyCode', width: 114 },
    { name: 'highPriceFlag', width: 128, renderer:({value,record})=> flagRender({value,record},'highPriceFlagValue') },
    { name: 'coaApproveGlag', width: 150, renderer: ({value,record},)=> flagRender({value,record},'coaApproveGlagValue') },
    { name: 'pickFlag', renderer: ({value,record})=> flagRender({value,record},'pickFlagValue') },
    { name: 'minRemainShelfLife', width: 142 },
    { name: 'shelfLife' },
    { name: 'extendedShelfLife' },
    { name: 'dullPeriod' },
    { name: 'dullPeriodSecondary', width: 100 },
    { name: 'earlyWarningLeadTime', width: 100 },
    { name: 'shelfLifeUomCode', width: 114 },
    { name: 'minStockQty', width: 114 },
    { name: 'maxStockQty', width: 114 },
    { name: 'minPackageQty', width: 100 },
    {  name: 'packMinQty',width: 120  },
    { name: 'mareqPackType',  width: 130 },
    { name: 'packageWeight', width: 114 },
    { name: 'weightUomCode' },
    { name: 'packageLength', width: 100 },
    { name: 'packageWidth', width: 100 },
    { name: 'packageHeight', width: 100 },
    { name: 'packageSizeUomCode', width: 114 },
    { name: 'loadingArea' },
    { name: 'loadingFloor', width: 114 },
    { name: 'packageType' },
    { name: 'singleBoxes' },
    { name: 'deliverGroup', width: 114 },
    { name: 'receivedGroup', width: 114 },
    { name: 'mixLotFlag', renderer: ({value,record})=> flagRender({value,record},'mixLotFlagValue') },
    { name: 'lotControlFlag', width: 114, renderer: ({value,record},)=> flagRender({value,record},'lotControlFlagValue') },
    { name: 'containerType' },
    { name: 'snpControlFlag', width: 122, renderer: ({value,record},)=> flagRender({value,record},'snpControlFlagValue') },
    { name: 'aPointSupplier',  },
    { name: 'poLineCreationFlag', width: 150,},
    { name: 'mrpType' },
    { name: 'mrpController', width: 108 },
    { name: 'batchProgram' },
    { name: 'maxBatch' },
    { name: 'minBatch' },
    { name: 'fixedBatch' },
    { name: 'safetyInventory' },
    { name: 'receiveLocatorCode', width: 114 },
    { name: 'seifmadeProductionTime', width: 114 },
    { name: 'receiveProcessTime', width: 114 },
    { name: 'procureAdvance', width: 100 },
    { name: 'longCycleFlag', width: 100, renderer: ({value,record},)=> flagRender({value,record},'longCycleFlagValue') },
  ];

  const goDetail = () => {
    props.history.push(`/hmes/product/material-pfep-manager/detail/create`);
  };

  useEffect(() => {
    listDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.TAG_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.TAG_LIST.LIST`,
    );
    listDs.query(listDs.currentPage);
  }, []);

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/WMS_MRP_INVENTORY_IMPORT`,
      title: intl.get(`material-pfep-manager.title.import`).d('物料PFEP属性维护导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const getExportQueryParams = () => {
    if (!listDs.queryDataSet || !listDs.queryDataSet.current) {
      return {};
    }
    const queryParams = listDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return {
      ...queryParams,
    };
  };

  return (
    <PageHeaderWrapper
      title={intl.get('tarzan.material-pfep-manager.title.list').d('物料PFEP属性维护')}
      header={
        <>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={goDetail}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-新建',
              },
            ]}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            onClick={handleImport}
            permissionList={[
              {
                code: `${path}.button.import`,
                type: 'button',
                meaning: '列表页-导入',
              },
            ]}
          >
            {intl.get('tarzan.common.button.import').d('导入')}
          </PermissionButton>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${
              BASIC.TARZAN_METHOD
            }/v1/${getCurrentOrganizationId()}/wms-mrp-inventorys/export`}
            queryParams={getExportQueryParams}
            buttonText="导出"
          />
        </>
      }
    >
      <Table
        queryBar="filterBar"
        searchCode="materialPrepTableList"
        queryBarProps={{
          fuzzyQuery: false,
        }}
        dataSet={listDs}
        columns={columns}
      />
    </PageHeaderWrapper>
  );
};

export default formatterCollections({
  code: ['tarzan.product.materialPrepTableList', 'tarzan.common'],
})(
  withProps(
    () => {
      const listDs = new DataSet({
        ...listDS(),
      });
      return {
        listDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(page),
);
