/**
 * 工艺组件-抽屉
 */
// @ts-nocheck
import React, { FC, useEffect } from 'react';
import {
  Table,
  DataSet,
} from 'choerodon-ui/pro';
import { Card, Badge } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import '../index.module.less';
import { operationDrawerDS } from '../stories/OperationDrawerDS';

export interface OperationComponentDrawerProps {
  currentDataSource: any,
  description: string,
  stepName: string,
}

const OperationComponentDrawer: FC<OperationComponentDrawerProps> = ({currentDataSource,description, stepName}) => {

  const operationDrawerDs =  new DataSet({ ...operationDrawerDS() });

  useEffect(() => {
    operationDrawerDs.loadData(currentDataSource?.mtRouterOperationDTO?.mtRouterOperationComponentDTO || []);
  }, [currentDataSource?.mtRouterOperationDTO?.mtRouterOperationComponentDTO]);

  /**
   * 工艺组件-表格-列配置
   */
  const operationDrawerColumn = [
    {
      name: 'lineNumber',
      renderer: ({ record }) => {
        return record.get('lineNumber') || record.get('sequence');
      },
    },
    {
      name: 'bomComponentId',
      renderer: ({ record }) => {
        return record.get('bomComponentMaterialCode') || record.get('materialCode');
      },
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'bomComponentMaterialDesc',
      renderer: ({ record }) => {
        return record.get('bomComponentMaterialDesc') || record.get('materialName');
      },
    },
    {
      name: 'perQty',
    },
    {
      name: 'enableFlag',
      align: "center",
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
  ]

  return (
    <div className="hmes-style">
      <Card
        title={stepName ? `${description}/${stepName}` : description}
        bordered={false}
      >
        <Table dataSet={operationDrawerDs} columns={operationDrawerColumn as ColumnProps[]} />
      </Card>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.process.routes', 'tarzan.common'],
})(OperationComponentDrawer);
