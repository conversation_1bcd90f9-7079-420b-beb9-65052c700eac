/**
 * @Description: 执行作业管理列表页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2023-04-13 18:56:01
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
// import { BASIC } from '@utils/config';
import { BASIC } from '../../../../utils/config';

const modelPrompt = 'tarzan.workshop.execute';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: 'multiple',
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-eo/list/ui`,
        method: 'GET',
        data: {
          ...data,
          eoType: (data.eoType && data.eoType.length) > 0 ? data.eoType.join(',') : undefined,
          status: (data.status && data.status.length) > 0 ? data.status.join(',') : undefined,
        },
      };
    },
    submit: ({ dataSet }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/save/attr?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.EO.LIST.TABLE`,
        data: dataSet.toJSONData(),
        method: 'POST',
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'eoId',
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'eoNums',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoNum`).d('执行作业编码'),
      multiple: ',',
    },
    {
      name: 'identifications',
      type: FieldType.string,
      multiple: ',',
      label: intl.get(`${modelPrompt}.model.execute.identifications`).d('执行作业标识'),
      dynamicProps: {
        required: ({ record }) => !record.get('material')
      }
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.materialId`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        required: ({ record }) => !record.get('identifications').length
      }
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'eoType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoType`).d('执行作业类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=EO_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.status`).d('执行作业状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'productionLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.productionLineId`).d('生产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'productionLineId',
      type: FieldType.string,
      bind: 'productionLine.prodLineId',
    },
    {
      name: 'workOrder',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.workOrderNum`).d('生产指令编码'),
      lovCode: 'MT.WORK_ORDER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workOrderId',
      type: FieldType.string,
      bind: 'workOrder.workOrderId',
    },
    {
      name: 'startTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.startTimeFrom`).d('开始时间从'),
      max: 'startTimeTo',
    },
    {
      name: 'startTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.startTimeTo`).d('开始时间至'),
      min: 'startTimeFrom',
    },
    {
      name: 'endTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.endTimeFrom`).d('结束时间从'),
      max: 'endTimeTo',
    },
    {
      name: 'endTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.endTimeTo`).d('结束时间至'),
      min: 'endTimeFrom',
    },
  ],
  fields: [
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoNum`).d('执行作业编码'),
    },
    {
      name: 'nowOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.nowOperationName`).d('当前工艺'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.identification`).d('执行作业标识'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.siteName`).d('站点名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialRevision`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialName`).d('物料名称'),
    },
    {
      name: 'eoTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoType`).d('执行作业类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=EO_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.status`).d('执行作业状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'productionLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.productionLineCode`).d('生产线编码'),
    },
    {
      name: 'productionLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.productionLineName`).d('生产线短描述'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.qty`).d('执行作业数量'),
    },
    {
      name: 'completedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.completedQty`).d('完成数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.scrappedQty`).d('报废数量'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'reworkFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkFlag`).d('返修标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'concessiveInterceptionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.concessiveInterceptionFlag`).d('让步拦截标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'overOrderInterceptionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overOrderInterceptionFlag`).d('跨工单拦截标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'dischargeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dischargeFlag`).d('在制品排出标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'degradeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeFlag`).d('降级标识'),
      lovPara: { tenantId },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

const historyDS = () => ({
  selection: false,
  autoQuery: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  fields: [
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
    },
    {
      name: 'eventTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeDescription`).d('事件类型'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元'),
    },
    {
      name: 'eventRequestId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventRequestId`).d('事件请求ID'),
    },
    {
      name: 'requestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('请求类型编码'),
    },
    {
      name: 'requestTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeDescription`).d('请求类型'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('事件时间'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.realName`).d('操作人'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
      // textField: 'description',
      // valueField: 'statusCode',
      // lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
      //     if (data instanceof Array) {
      //       return data;
      //     }
      //     const { rows } = JSON.parse(data);
      //     return rows;
      //   },
      // },
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('执行作业状态'),
      // textField: 'description',
      // valueField: 'statusCode',
      // noCache: true,
      // lovPara: { tenantId },
      // lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
      // lookupAxiosConfig: {
      //   transformResponse(data) {
      //     if (data instanceof Array) {
      //       return data;
      //     }
      //     const { rows } = JSON.parse(data);
      //     return rows;
      //   },
      // },
    },
    {
      name: 'specifiedLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specifiedLevel`).d('等级'),
    },
    {
      name: 'partialScrapFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partialScrapFlag`).d('部分报废标识'),
      lookupCode: 'MT.FLAG',
    },
    {
      name: 'partialScrapShiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partialScrapShiftCode`).d('部分报废班次'),
      lookupCode: 'HME.HANDOVER_TIME',
    },
    {
      name: 'partialScrapShiftDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.partialScrapShiftDate`).d('部分报废日期'),
    },
    {
      name: 'reworkStartFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkStartFlag`).d('返修开始标识'),
      lookupCode: 'MT.FLAG',
    },
    {
      name: 'reworkWorkcell',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkWorkcell`).d('返修工序编码'),
    },
    {
      name: 'reworkOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkOperationName`).d('返修工艺'),
    },
    {
      name: 'reworkEndOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reworkEndOperationName`).d('返修结束工艺'),
    },
    {
      name: 'overTimeReleaseFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overTimeReleaseFlag`).d('超期放行标识'),
      lookupCode: 'MT.FLAG',
    },
    {
      name: 'overTimeReleaseTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overTimeReleaseTime`).d('超期放行时间'),
    },
    {
      name: 'concesInterOperationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.concesInterOperationName`).d('让步拦截工艺'),
    },
    {
      name: 'disposalFunctionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalFunctionCode`).d('处置方法编码'),
    },
    {
      name: 'degradeLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.degradeLevel`).d('降级等级'),
    },
    {
      name: 'traceLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.traceLevel`).d('追溯层级'),
      lookupCode: 'HME.TRACE_LEVEL',
    },
    {
      name: 'manPowerSupplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.manPowerSupplierName`).d('主粉供应商'),
    },
    {
      name: 'marking',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.marking`).d('标记'),
    },
    {
      name: 'concessiveInterceptionOp',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.concessiveInterceptionOp`).d('拦截工艺名称'),
    },
    {
      name: 'electrolyteSupplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.electrolyteSupplierName`).d('电解液供应商'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-eo/list/his`,
        method: 'GET',
      };
    },
  },
});

export { tableDS, historyDS };
