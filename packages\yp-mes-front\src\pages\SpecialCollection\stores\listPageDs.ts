import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentTenantId } from 'utils/utils';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.specialCollection';
const tenantId = getCurrentOrganizationId();
console.log(getCurrentTenantId());

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'keyId',
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'tagLov',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          label: intl.get(`${modelPrompt}.ncCode`).d('收集项编码'),
          lovCode: 'MT.TAG',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'tagId',
          bind: 'tagLov.tagId'
        },
        {
          name: 'equipmentLov',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          label: intl.get(`${modelPrompt}.equipmentCode`).d('关联设备'),
          lovCode: 'MT.MODEL.EQUIPMENT',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'equipmentId',
          bind: 'equipmentLov.equipmentId'
        },
        {
          name: 'materialLov',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          label: intl.get(`${modelPrompt}.materialCode`).d('关联物料'),
          lovCode: 'MT.MATERIAL.PERMISSION',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'materialId',
          bind: 'materialLov.materialId'
        },
      ]
    }),
    fields: [
      {
        name: 'tagCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.ncCode`).d('收集项编码'),
      },
      {
        name: 'tagName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.tagName`).d('收集项描述'),
      },
      {
        name: 'equipmentCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.equipmentCode`).d('关联设备'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('关联物料'),
      },
      {
        name: 'missingNcCode',
        label: intl.get(`${modelPrompt}.form.missingNcCode`).d('缺失值不良代码'),
        type: FieldType.string,
      },
      {
        name: 'defaultNcCode',
        label: intl.get(`${modelPrompt}.form.defaultNcCode`).d('默认不良代码'),
        type: FieldType.string,
      },
      {
        name: 'userName',
        label: intl.get(`${modelPrompt}.form.userName`).d('最后更新人'),
        type: FieldType.string,
      },
      {
        name: 'lastUpdateDate',
        label: intl.get(`${modelPrompt}.form.lastUpdateDate`).d('最后更新时间'),
        type: FieldType.dateTime,
      },
      {
        name: 'remark',
        label: intl.get(`${modelPrompt}.form.remark`).d('备注'),
        type: FieldType.string,
      },
      {
        name: 'conditionTagCode',
        label: intl.get(`${modelPrompt}.form.conditionTagCode`).d('条件收集项编码'),
        type: FieldType.string,
      },
      {
        name: 'conditionTagName',
        label: intl.get(`${modelPrompt}.form.conditionTagName`).d('条件收集项描述'),
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-look-process/head/query`,
        };
      },
    },
  });

export default listPageFactory;
