import { Host } from '@/utils/config';
import { FieldIgnore, FieldType } from 'choerodon-ui/dataset/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.ProductionProcessConversion';

const tableDS = () => {
  return {
    name: 'tableDS',
    paging: true,
    autoQuery: false,
    selection: 'multiple',
    queryFields: [
      {
        name: 'siteObj',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.siteObj`).d('站点查询'),
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'siteId',
        type: FieldType.number,
        bind: 'siteObj.siteId',
      },
      {
        name: 'materialObj',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialObj`).d('物料编号'),
        lovCode: 'MT.MATERIAL',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'materialId',
        type: FieldType.number,
        bind: 'materialObj.materialId',
      },
      {
        name: 'operationObj',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.operationObj`).d('工艺查询'),
        lovCode: 'MT.OPERATION_CONVERSION',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'operationId',
        type: FieldType.number,
        bind: 'operationObj.operationId',
      },
      {
        name: 'equipmentObj',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.equipmentObj`).d('设备编码'),
        lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'equipmentId',
        type: FieldType.number,
        bind: 'equipmentObj.equipmentId',
      },
    ],
    fields: [
      {
        name: 'operationConversionId',
        type: FieldType.number,
      },
      {
        name: 'siteObj',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.siteObj`).d('站点编码'),
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
        required: true,
        defaultValidationMessages: {
          patternMismatch: intl
            .get(`${modelPrompt}.error.siteCode.null`)
            .d(`输入参数站点编码为空，请检查`), // 正则不匹配的报错信息
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'siteId',
        type: FieldType.number,
        bind: 'siteObj.siteId',
      },
      {
        name: 'siteCode',
        type: FieldType.string,
        bind: 'siteObj.siteCode',
      },
      {
        name: 'materialObj',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
        lovCode: 'HME.ASSEMBL_EPOINT_SITE_MATERIAL',
        dynamicProps: {
          lovPara: ({ record }) => {
            const siteId = record.get('siteId');
            return {
              tenantId: getCurrentOrganizationId(),
              siteId,
            };
          },
        },
        required: true,
        defaultValidationMessages: {
          patternMismatch: intl
            .get(`${modelPrompt}.error.materialCode.null`)
            .d(`输入参数物料编码为空，请检查！`), // 正则不匹配的报错信息
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'materialId',
        type: FieldType.number,
        bind: 'materialObj.materialId',
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        bind: 'materialObj.materialCode',
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
        bind: 'materialObj.materialName',
      },
      {
        name: 'materialSiteId',
        type: FieldType.number,
        bind: 'materialObj.materialSiteId',
        ignore: FieldIgnore.always,
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        bind: 'materialObj.revisionCode',
        textField: 'revisionCode',
        valueField: 'revisionCode',
        lookupUrl: `${Host}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
        lookupAxiosConfig: ({ record }) => {
          const _params = record?.toData() || {};
          if (_params && _params.materialSiteId) {
            return {
              params: {
                materialSiteId: _params.materialSiteId,
              },
              transformResponse(data) {
                // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
                if (data instanceof Array) {
                  return data;
                }
                if (data.failed) {
                  return [];
                }
                const rows = JSON.parse(data);
                return rows;
              },
            };
          }
        },
        dynamicProps: {
          disabled: record => {
            const _params = record?.record.data || {};
            if (
              (_params.materialObj &&
                _params.materialObj.revisionFlag &&
                _params.materialObj.revisionFlag === 'Y') ||
              _params.revisionFlag === 'Y'
            ) {
              return false;
            }
            return true;
          },
          required: record => {
            const _params = record?.record.data || {};
            if (
              (_params.materialObj &&
                _params.materialObj.revisionFlag &&
                _params.materialObj.revisionFlag === 'Y') ||
              _params.revisionFlag === 'Y'
            ) {
              return true;
            }
            return false;
          },
        },
      },
      {
        name: 'operationObj',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.operationObj`).d('工艺编码'),
        lovCode: 'MT.OPERATION_CONVERSION',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
        required: true,
        defaultValidationMessages: {
          patternMismatch: intl
            .get(`${modelPrompt}.error.operation.null`)
            .d(`输入参数工艺编码为空，请检查！`), // 正则不匹配的报错信息
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'operationId',
        type: FieldType.number,
        bind: 'operationObj.operationId',
      },
      {
        name: 'operationName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
        bind: 'operationObj.operationName',
      },
      {
        name: 'description',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.description`).d('工艺名称'),
        bind: 'operationObj.description',
      },
      {
        name: 'materialConsumption',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.materialConsumption`).d('余料扣减上限'),
        defaultValidationMessages: {
          rangeUnderflow: intl.get(`${modelPrompt}.view.validator`).d('必须大于0'),
        },
        min: 0.001,
      },
      {
        name: 'equipmentObj',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.equipmentObj`).d('设备编码'),
        lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
        required: true,
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'equipmentId',
        type: FieldType.number,
        bind: 'equipmentObj.equipmentId',
      },
      {
        name: 'equipmentCode',
        type: FieldType.string,
        bind: 'equipmentObj.equipmentCode',
      },
      {
        name: 'equipmentName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
        bind: 'equipmentObj.equipmentName',
      },
      {
        name: 'singleUsage',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.singleUsage`).d('单片用量'),
      },
      {
        name: 'eaQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.eaQty`).d('极片个数'),
      },
      {
        name: 'slittingMultiple',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.slittingMultiple`).d('分切倍数'),
      },
      {
        name: 'stripeNumber',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.stripeNumber`).d('条纹数量'),
      },
      {
        name: 'maxCompletedQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.maxCompletedQty`).d('最大完工数量'),
      },
      {
        name: 'stretchNumber',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.stretchNumber`).d('拉伸系数'),
        required: true,
        validator: value => {
          if (value <= 0) {
            return intl
              .get(`${modelPrompt}.error.stretchNumber.zero`)
              .d(`拉伸系数必须大于0!`);
          }
        },
      },
      {
        name: 'weightConversionFactorForSingle',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.weightConversionFactorForSingle`).d('重量换算系数(单面)'),
        min: 0,
      },
      {
        name: 'meterConversionFactorForSingle',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.meterConversionFactorForSingle`).d('米数换算系数(单面)'),
        min: 0,
      },
      {
        name: 'weightConversionFactor',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.weightConversionFactor`).d('重量换算系数(双面)'),
        min: 0,
      },
      {
        name: 'meterConversionFactor',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.meterConversionFactor`).d('米数换算系数(双面)'),
        min: 0,
      },
      {
        name: 'weightConversionFactorForBright',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.weightConversionFactorForBright`).d('重量换算系数(光箔)'),
        min: 0,
      },
      {
        name: 'meterConversionFactorForBright',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.meterConversionFactorForBright`).d('米数换算系数(光箔)'),
        min: 0,
      },
      {
        name: 'weightConversionFactorForComplete',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.weightConversionFactorForComplete`).d('重量换算系数（完工品）'),
        min: 0,
      },
      {
        name: 'meterConversionFactorForComplete',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.meterConversionFactorForComplete`).d('米数换算系数（完工品）'),
        min: 0,
      },
      {
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        type: FieldType.string,
        trueValue: 'Y',
        falseValue: 'N',
        required: true,
        defaultValue: 'Y',
        defaultValidationMessages: {
          patternMismatch: intl
            .get(`${modelPrompt}.error.enableFlag.null`)
            .d(`输入参数有效性为空，请检查！`), // 正则不匹配的报错信息
        },
      },
      {
        name: 'createdName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.createdName`).d('创建人'),
      },
      {
        name: 'creationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'lastUpdatedName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lastUpdatedName`).d('最后更新人'),
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-operation-conversions/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

export { tableDS };
