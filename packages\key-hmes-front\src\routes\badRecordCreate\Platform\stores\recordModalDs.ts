import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSet } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment';

const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';

const tenantId = getCurrentOrganizationId();
const RecordFactory = () =>
  new DataSet({
    autoQuery: false,
    autoCreate: false,
    autoLocateFirst: true,
    selection: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    fields: [
      {
        name: 'ncCodeLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.ncCodeDesc`).d('不良代码'),
        lovCode: 'MT.NC_CODE',
        lovPara: { tenantId },
        required: true,
        multiple: true,
        ignore: FieldIgnore.always,
        textField: 'description',
      },
      {
        name: 'ncCodeId',
        bind: 'ncCodeLov.ncCodeId',
      },
      {
        name: 'ncCodeDesc',
        bind: 'ncCodeLov.description',
      },
      {
        name: 'ncCodeStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.ncCodeStatus`).d('不良代码状态'),
        textField: 'description',
        valueField: 'statusCode',
        lovPara: { tenantId },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_CODE_STATUS`,
        lookupAxiosConfig: {
          transformResponse(data) {
            // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
        disabled: true,
      },
      {
        name: 'rootCauseOperationLov',
        type: FieldType.object,
        lovCode: 'MT.OPERATION',
        required: true,
        lovPara: {
          tenantId,
        },
        label: intl.get(`${modelPrompt}.rootCauseOperationLov`).d('不良产生工艺'),
        dynamicProps: {
          // disabled: ({ record }) => {
          //   return record.get('rootCauseOperationId');
          // },
          required: ({ record }) => {
            return !record.get('rootCauseOperationId');
          },
        },
      },
      {
        name: 'rootCauseOperationId',
        bind: 'rootCauseOperationLov.operationId',
      },
      {
        name: 'rootCauseOperationCode',
        bind: 'rootCauseOperationLov.operationName',
      },
      {
        name: 'rootCauseWorkcellLov',
        type: FieldType.object,
        required: true,
        label: intl.get(`${modelPrompt}.rootCauseWorkcellName`).d('不良产生工作单元'),
        lovCode: 'HME.POOR_WORK_CELL',
        ignore: FieldIgnore.always,
        dynamicProps: {
          lovPara: ({ record }) => ({
            tenantId,
            operationId: record.get('rootCauseOperationId'),
          }),
        },
        textField: 'workcellName',
      },
      {
        name: 'rootCauseWorkcellName',
        bind: 'rootCauseWorkcellLov.workcellName',
      },
      {
        name: 'rootCauseWorkcellId',
        bind: 'rootCauseWorkcellLov.workcellId',
      },
      {
        name: 'rootCauseEquipmentCodeLov',
        type: FieldType.object,
        // lovCode: 'MT.MODEL.EQUIPMENT',
        lovCode: 'HME.POOR_EQUIPMENT',
        dynamicProps: {
          lovPara: ({ record }) => ({
            tenantId,
            // workCellType: 'STATION',
            workcellId: record.get('rootCauseWorkcellId'),
          }),
          disabled: ({ record }) => {
            if (record.get('rootCauseWorkcellId')) {
              return false;
            }
            return true;
          },
        },
        label: intl.get(`${modelPrompt}.rootCauseEquipmentCodeLov`).d('不良产生设备'),
      },
      {
        name: 'rootCauseEquipmentId',
        bind: 'rootCauseEquipmentCodeLov.equipmentId',
      },
      {
        name: 'rootCauseEquipmentCode',
        bind: 'rootCauseEquipmentCodeLov.equipmentCode',
      },
      {
        name: 'responsibleUserLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.responsibleUserName`).d('不良责任人'),
        lovCode: 'YP.QIS.USER.ORG',
        ignore: FieldIgnore.always,
        textField: 'realName',
        lovPara: { tenantId },
      },
      {
        name: 'responsibleUserId',
        bind: 'responsibleUserLov.userId',
      },
      {
        name: 'responsibleUserName',
        bind: 'responsibleUserLov.realName',
      },
      {
        name: 'responsibleApartment',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.responsibleApartment`).d('不良责任部门'),
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          data: {
            ...data,
            shiftDate: data.shiftDate ? moment(data.shiftDate).format('yyyy-MM-DD') : undefined,
          },
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-nc-record/platform/head/query/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_LIST.LIST`,
          method: 'POST',
        };
      },
    },
  });
export default RecordFactory;
