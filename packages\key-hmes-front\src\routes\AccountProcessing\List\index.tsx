import React, { FC, useMemo } from 'react';
import { observer } from 'mobx-react';
import { RouteComponentProps } from 'react-router';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import {  Tag } from 'choerodon-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { tableDS } from '../stores/platformListDS';
import {RecordCancel} from '../services';

const modelPrompt = 'tarzan.mes.event.accountProcessing';

interface BadRecordPlatformProps extends RouteComponentProps {
  headDs: DataSet;
  customizeTable: any;
}

const BadRecordPlatform: FC<BadRecordPlatformProps> = observer(props => {

  const { history, headDs,  } = props;

  const { run: cancel, loading: cancelLoading } = useRequest(
    RecordCancel(),
    {
      needPromise: true,
      manual: true,
    },
  );


  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    if (dataSet?.current?.toData().message) {
      notification.error({
        message: dataSet?.current?.toData().message,
      });
      headDs.loadData([]);
      return;
    }
  };

  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const openDetail = id => {
    history.push(`/hmes/accountProcessing/detail/${id}`);
  };

  const renderNcRecordTag = (value, record) => {
    switch (record.get('ncIncidentStatus')) {
      case 'NEW':
        return <Tag color="green">{value}</Tag>;
      case 'RELEASED':
        return <Tag color="blue">{value}</Tag>;
      case 'COMPLETED':
        return <Tag color="red">{value}</Tag>;
      case 'CANCEL':
        return <Tag color="gray">{value}</Tag>;
      case 'WORKING':
        return <Tag color="volcano">{value}</Tag>;
      default:
        return null;
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'ncIncidentNum',
        width: 180,
        lock: ColumnLock.left,
        renderer: ({ record }) => {
          return (
            <a
              onClick={() => {
                openDetail(record?.get('ncIncidentId'));
              }}
            >
              {record?.get('ncIncidentNum')}
            </a>
          );
        },
      },
      {
        name: 'ncIncidentStatusDesc',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value, record }) => renderNcRecordTag(value, record),
      },
      { name: 'ncRecordTypeDesc', width: 120 },
      { name: 'siteCode' },
      { name: 'materialName' },
      { name: 'revisionCode' },
      { name: 'operationName' },
      { name: 'description' },
      { name: 'locatorName' },
      { name: 'ncStartTime', width: 150, align: ColumnAlign.center },
      { name: 'ncStartUserName' },
      { name: 'ncCloseTime', width: 150, align: ColumnAlign.center },
      { name: 'ncCloseUserName' },
      { name: 'remark', width: 200 },
    ];
  }, []);


  const clickMenu = async () => {
    cancel({
      params: headDs?.selected?.map(item => item?.get('ncIncidentId')),
    }).then(res => {
      if (res?.success) {
        headDs.batchUnSelect(headDs.selected);
        headDs.clearCachedSelected();
        headDs.query(props.headDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  return (
    <div className="hmes-style" style={{height: '98%', overflow: 'auto'}}>
        <Header title={intl.get(`${modelPrompt}.title`).d('账务处理平台')}>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={() => openDetail('create')}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
          <Button
            color={ButtonColor.primary}
            onClick={clickMenu}
            disabled={
              !headDs.selected.length ||
            headDs.selected.some(item => item.get('ncIncidentStatus') !== 'NEW')
            }
          >
            {intl.get(`tarzan.common.button.cancel`).d('取消')}
          </Button>
        </Header>
        <Content>
          <Table
            highLightRow
            queryFieldsLimit={4}
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={headDs}
            columns={columns}
            searchCode="accountProcessing"
            customizedCode="accountProcessing"
          />
        </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(tableDS());
      return {
        headDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
      ],
    })
    (BadRecordPlatform as any),
  ),
);
