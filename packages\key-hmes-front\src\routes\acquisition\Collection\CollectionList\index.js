/**
 * @Description: 数据收集组-列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-03-01 10:47:34
 * @LastEditTime: 2023-05-18 14:38:38
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState, useRef } from 'react';
import { DataSet, Table, Button, Modal } from 'choerodon-ui/pro';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Button as PermissionButton } from 'components/Permission';
// import { Tag } from 'choerodon-ui';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import ExcelExport from 'components/ExcelExport';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { BASIC } from '@utils/config';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { observer } from 'mobx-react';
import { getCurrentOrganizationId } from 'utils/utils';
import { tableDS } from '../stores/CollectionDS';


const modelPrompt = 'tarzan.hmes.acquisition.collection';
// const tagClassName = {
//   NEW: 'green',
//   RELEASE: 'blue',
//   LOCK: 'orange',
//   CLOSED: 'red',
// };

const DataCollectionGroupMaintenance = observer(props => {
  const modal = useRef(null); // 当前模态框

  const [canEdit, setCanEdit] = useState(false);
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;
  useDataSetEvent(tableDs, 'query', () => {
    setCanEdit(false);
  });
  useEffect(() => {
    tableDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_LIST.LIST`,
    );

    tableDs.query(props.tableDs.currentPage);
  }, []);
  const columns = [
    {
      name: 'tagGroupCode',
      width: 300,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(
                `/hmes/acquisition/data-collection-new/detail/${record.data.tagGroupId}`,
              );
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'tagGroupDescription',
      width: 250,
    },
    {
      name: 'missingNcCode',
      align: 'left',
      width: 200,
    },
    {
      name: 'status',
      align: 'center',
      editor: () => canEdit,
      width: 120,
      // renderer: ({ value, record }) =>
      //   value && <Tag color={tagClassName[record.get('status')]}>{record?.get('statusDesc')}</Tag>,
    },
    {
      name: 'tagGroupTypeDesc',
      align: 'left',
      width: 120,
    },
    {
      name: 'businessTypeDesc',
      align: 'center',
      width: 120,
    },
    {
      name: 'collectionTimeControlDesc',
      align: 'left',
      width: 120,
    },
    {
      name: 'defaultNcCode',
      align: 'left',
      width: 120,
    },
    {
      name: 'lastUpdateDate',
      width: 150,
    },
    
  ];

  const handleCreate = () => {
    props.history.push('/hmes/acquisition/data-collection-new/detail/create');
  };

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MT.MES.TAG_GROUP`,
      title: intl.get('tarzan.hmes.acquisition.collection.collectionGroupMaintenance.import').d('数据收集组数据导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }
  const handleEdit = () => {
    setCanEdit(true);
  }
  const handleSave = async () => {
    const p = tableDs.selected.map(item => item?.validate())
    const resp = await Promise.all(p)
    if(resp.some(item => !item))return
    const res = await tableDs.submit()
    if(res&&res.success){
      notification.success({
        message: intl.get(`${modelPrompt}.success`).d('操作成功'),
      });
    }
    setCanEdit(false)
    await tableDs.query()
  }
  const handleCancel = () => {
    setCanEdit(false)
    tableDs.query();
  }
  const handleExport = () => {
    modal.current = Modal.open({
      key: `ModalKey`,
      title: intl.get('tarzan.common.button.export').d('导出'),
      className: 'hcmp-modal',
      destroyOnClose: false,
      drawer: false,
      closable: true,
      okButton: false,
      children: <div>
        <ExcelExport
          method="GET"
          requestUrl={`${
            BASIC.HMES_BASIC
          }/v1/${getCurrentOrganizationId()}/mt-tag-group/export/tag/group`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.tagGroup`).d('数据收集组导出')}
        />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <ExcelExport
          method="GET"
          requestUrl={`${
            BASIC.HMES_BASIC
          }/v1/${getCurrentOrganizationId()}/mt-tag-group/export/tag/group/and/assign`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.groupAndAssign`).d('收集组+收集项导出')}
        />
      </div>,
    })
  }

  const getExportQueryParams = () => {
    return {
      mtTagGroupIds: tableDs.selected.map(item => item.get('tagGroupId')),
    }
  }
  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get(`${modelPrompt}.collectionGroupMaintenance`)
          .d('数据收集组维护')}
      >
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button color='primary' icon="daorucanshu" onClick={handleImport}>{intl.get('tarzan.common.button.import').d('导入')}</Button>
        <Button color='primary' disabled={!tableDs.selected.length} onClick={handleExport}>{intl.get('tarzan.common.button.export').d('导出')}</Button>

        {canEdit?(<>
          <Button color='primary' onClick={handleSave}>{intl.get('tarzan.common.button.save').d('保存')}</Button>
          <Button onClick={handleCancel}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
        </>):(<Button color='primary' icon="edit-o" onClick={handleEdit}>{intl.get('tarzan.common.button.edit').d('编辑')}</Button>)}
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_LIST.LIST`,
          },
          <Table
            searchCode="sjsjzwh1"
            customizedCode="sjsjzwh1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
          />,
        )}
      </Content>
    </div>
  );
});
export default formatterCollections({
  code: ['tarzan.hmes.acquisition.collection', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.TAG_GROUP_LIST.LIST`],
    })(DataCollectionGroupMaintenance),
  ),
);
