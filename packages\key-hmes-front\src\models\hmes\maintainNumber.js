/**
 * @date 2019-8-21
 * <AUTHOR> <<EMAIL>>
 */

import { createPagination } from 'utils/utils';
import { get as chainGet } from 'lodash';
import { fetchGenTypeList, getUserRole } from '@services/api';
import { getResponse } from '@utils/utils';
import {
  fetchMaintainNumberList,
  fetchMaintainNumberDetail,
  fetchCodingObject,
  saveMaintainNumber,
  fetchObjectColumnCodeList,
  fetchNumCurrentInfo,
  updateNumCurrent,
} from '../../services/hmes/maintainNumberService';

//  显示值会有以下几种类型
const INPUT_MAP = ['FIXED_VALUE', 'SERIAL', 'DATE', 'TIME', 'SPECIFIC_OBJECT', 'INPUT_VALUE'];

export default {
  namespace: 'maintainNumber',
  state: {
    maintainNumberList: [], // 号码段表格数据源
    maintainNumberPagination: {}, // 号码段表格分页
    maintainNumberParams: {}, // 号码段表格查询参数
    maintainNumberDetail: {}, //  号码段详细数据
    maintainNumberDetailRules: [], //  号码段详细数据中的规则
    usingObjectColumnCodeArray: [], //  在使用的编码对象属性数组

    dateFormatList: [], // 日期格式下拉框数据
    timeFormatList: [], // 时间格式下拉框数据
    numberLevelList: [], // 序列号层级下拉框数据
    numAlertTypeList: [], // 号段预警下拉框数据
    numRadixList: [], // 号段进制下拉框数据
    numResetTypeList: [], // 序列号重置周期下拉框数据
    userRole: 'N', // 用户权限
  },
  effects: {
    // 获取用户权限
    *getUserRole({ payload }, { call, put }) {
      const res = yield call(getUserRole, payload);
      const result = getResponse(res);
      if (result) {
        yield put({
          type: 'updateState',
          payload: {
            userRole: result.rows,
          },
        });
      }
    },

    // 获取号码段表数据
    *fetchMaintainNumberList({ payload }, { call, put }) {
      const res = yield call(fetchMaintainNumberList, payload);
      const data = getResponse(res);
      if (data) {
        yield put({
          type: 'updateState',
          payload: {
            maintainNumberList: chainGet(data, 'rows.content', []),
            maintainNumberPagination: createPagination(data.rows),
          },
        });
      }
    },

    // 获取号码段详细数据
    *fetchMaintainNumberDetail({ payload }, { call, put }) {
      const res = yield call(fetchMaintainNumberDetail, payload);
      const data = getResponse(res);
      if (data) {
        //   _backUpRule保留初始数据，因为在修改序列号段时，如果编辑的规则，
        //   当前与现在【序列号】层级相同，不允许修改序列号上线
        let ruleList = [null, null, null, null, null];
        chainGet(data, 'rows.rules', []).forEach(item => {
          let numRule;
          if (item.ruleType && INPUT_MAP.indexOf(item.ruleType) > -1) {
            numRule = `${INPUT_MAP.indexOf(item.ruleType) + 1}`;
          }
          ruleList[item.ruleSequence - 1] = {
            ...item,
            numRule,
            _backUpRule: {
              ...item,
              numRule,
            },
          };
        });

        ruleList = ruleList.map(item => {
          if (item) {
            return {
              ...item,
            };
          }
          return {};
        });
        const usingObjectColumnCodeArray = ruleList.map(item => {
          if (item && item.callStandardObject) {
            return item.callStandardObject;
          }
          return null;
        });
        yield put({
          type: 'updateState',
          payload: {
            maintainNumberDetail: chainGet(res, 'rows', {}),
            maintainNumberDetailRules: ruleList,
            usingObjectColumnCodeArray,
          },
        });
      }
      return data;
    },

    //  保存号码段规则详情
    *saveMaintainNumber({ payload }, { call }) {
      const res = yield call(saveMaintainNumber, payload);
      return getResponse(res);
    },

    //  编码对象下拉模糊查询
    *fetchCodingObject({ payload }, { call }) {
      const res = yield call(fetchCodingObject, payload);
      return getResponse(res);
    },

    //  编码对象下的编码对象属性查询
    *fetchObjectColumnCodeList({ payload }, { call, put }) {
      const res = yield call(fetchObjectColumnCodeList, payload);
      const data = getResponse(res);
      if (data) {
        yield put({
          type: 'updateState',
          payload: {
            usingObjectColumnCodeArray: [],
          },
        });
      }
      return data;
    },

    // 获取日期格式下拉框数据
    *fetchDateFormatList({ payload }, { call, put }) {
      const res = yield call(fetchGenTypeList, payload);
      const data = getResponse(res);
      if (data) {
        yield put({
          type: 'updateState',
          payload: {
            dateFormatList: chainGet(data, 'rows', []),
          },
        });
      }
    },

    // 获取时间格式下拉框数据
    *fetchTimeFormatList({ payload }, { call, put }) {
      const res = yield call(fetchGenTypeList, payload);
      const data = getResponse(res);
      if (data) {
        yield put({
          type: 'updateState',
          payload: {
            timeFormatList: chainGet(data, 'rows', []),
          },
        });
      }
    },

    // 获取序列号层级下拉框数据
    *fetchNumLevelList({ payload }, { call, put }) {
      const res = yield call(fetchGenTypeList, payload);
      const data = getResponse(res);
      if (data) {
        yield put({
          type: 'updateState',
          payload: {
            numberLevelList: chainGet(data, 'rows', []),
          },
        });
      }
    },

    // 获取号段预警类型下拉框数据
    *fetchNumAlertTypeList({ payload }, { call, put }) {
      const res = yield call(fetchGenTypeList, payload);
      const data = getResponse(res);
      if (data) {
        yield put({
          type: 'updateState',
          payload: {
            numAlertTypeList: chainGet(data, 'rows', []),
          },
        });
      }
    },

    // 获取号段进制下拉框数据
    *fetchNumRadixList({ payload }, { call, put }) {
      const res = yield call(fetchGenTypeList, payload);
      const data = getResponse(res);
      if (data) {
        yield put({
          type: 'updateState',
          payload: {
            numRadixList: chainGet(data, 'rows', []),
          },
        });
      }
    },

    // 获取序列号重置周期下拉框数据
    *fetchNumResetTypeList({ payload }, { call, put }) {
      const res = yield call(fetchGenTypeList, payload);
      const data = getResponse(res);
      if (data) {
        yield put({
          type: 'updateState',
          payload: {
            numResetTypeList: chainGet(data, 'rows', []),
          },
        });
      }
    },

    // 当前序列号相关数据查询
    *fetchNumCurrentInfo({ payload }, { call }) {
      const res = yield call(fetchNumCurrentInfo, payload);
      return getResponse(res);
    },

    // 更新当前序列号
    *updateNumCurrent({ payload }, { call }) {
      const res = yield call(updateNumCurrent, payload);
      return getResponse(res);
    },
  },

  reducers: {
    updateState(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },

    clear(state) {
      return {
        ...state,
        maintainNumberList: [], // 号码段表格数据源
        maintainNumberDetail: {}, //  号码段详细数据
        maintainNumberDetailRules: [], //  号码段详细数据中的规则
        usingObjectColumnCodeArray: [], //  在使用的编码对象属性数组

        dateFormatList: [], // 日期格式下拉框数据
        timeFormatList: [], // 时间格式下拉框数据
        numberLevelList: [], // 序列号层级下拉框数据
        numAlertTypeList: [], // 号段预警下拉框数据
        numRadixList: [], // 号段进制下拉框数据
        numResetTypeList: [], // 序列号重置周期下拉框数据
      };
    },
  },
};
