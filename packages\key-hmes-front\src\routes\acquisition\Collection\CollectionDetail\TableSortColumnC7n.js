/**
 * @Description: 表格方法
 * @Author: <<EMAIL>>
 * @Date: 2021-04-25 14:37:55
 * @LastEditTime: 2021-04-25 15:24:02
 * @LastEditors: <<EMAIL>>
 */
import React, { useState } from 'react';
import { Icon } from 'choerodon-ui';
import './Table.module.less';

const SortColumnC7n = ({ title, name, ds }) => {
  const [sortType, setSortType] = useState('asc');

  const handleChangeSortDesc = () => {
    if (sortType === 'asc') {
      const newDsList = ds.records.sort((a, b) => b.get(name) - a.get(name));
      ds.loadData(newDsList);
    }
    setSortType('desc');
  };

  const handleChangeSortAsc = () => {
    if (sortType === 'desc') {
      const newDsList = ds.records.sort((a, b) => a.get(name) - b.get(name));
      ds.loadData(newDsList);
    }
    setSortType('asc');
  };

  const renderArrow = () => {
    if (sortType === 'asc') {
      return <Icon type="arrow_upward" onClick={handleChangeSortDesc} />;
    } 
    return <Icon type="arrow_downward" onClick={handleChangeSortAsc} />;
    
  };

  return (
    <div className="hcm-table-column-sort">
      <span>{title}</span>
      {renderArrow()}
    </div>
  );
};

export { SortColumnC7n };
