/**
 * @Description: 事件查询-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-27 16:24:34
 * @LastEditTime: 2023-02-01 14:56:08
 * @LastEditors: <<EMAIL>>
 */

import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import moment from 'moment';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
// import { BASIC } from 'hcm-components-front/lib/utils/config';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.event.eventQuery.model.eventQuery';

const tableDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  paging: 'server',
  primaryKey: 'kid',
  parentField: 'eventRequestId',
  idField: 'kid',
  // expandField: 'expand',
  childrenField: 'children',
  transport: {
    read: ({ data }) => {
      const _data = {
        ...data,
        shiftDate: data.shiftDate ? moment(data.shiftDate).format('yyyy-MM-DD') : null,
      };
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-event/property/list/ui`,
        method: 'POST',
        data: _data,
      };
    },
  },
  queryFields: [
    {
      name: 'eventTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eventTypeId`).d('事件类型'),
      ignore: FieldIgnore.always,
      lovCode: 'YP_MES.EVENT_TYPE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'eventTypeId',
      bind: 'eventTypeLov.eventTypeId',
    },
    {
      name: 'eventTypeCode',
      bind: 'eventTypeLov.eventTypeCode',
    },
    {
      name: 'startTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
      max: 'endTime',
      dynamicProps: {
        required: ({record}) =>{
          return !record.get('requestIdList')[0] && !record.get('eventIdList')[0]
        },
        min: ({record}) => {
          // console.log( moment(undefined), record.get('endTime'),moment(record.get('endTime'))?.subtract(3,'day'));
          return record.get('endTime') ? moment(record.get('endTime'))?.subtract(3,'day') : undefined
        },
      },
    },
    {
      name: 'endTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
      min: 'startTime',
      dynamicProps: {
        required: ({record}) =>{
          return !record.get('requestIdList')[0] && !record.get('eventIdList')[0]
        },
        max: ({record}) => {
          // console.log(moment(record.get('startTime'))?.add(3,'day'));
          return record.get('startTime') ? moment(record.get('startTime'))?.add(3,'day') : undefined
        },
      },
    },
    {
      name: 'requestIdList',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.requestIdList`).d('事件请求主键'),
      multiple: true,
      dynamicProps: {
        required: ({record}) =>{
          return !record.get('startTime') && !record.get('eventIdList')[0]
        },
      },
    },
    {
      name: 'eventIdList',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventIdList`).d('事件主键'),
      multiple: true,
      dynamicProps: {
        required: ({record}) =>{
          return !record.get('startTime') && !record.get('requestIdList')[0]
        },
      },
    },
    {
      name: 'requestTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.requestTypeId`).d('事件请求类型'),
      ignore: FieldIgnore.always,
      lovCode: 'YP_MES.EVENT_REQUEST',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'requestTypeId',
      bind: 'requestTypeLov.requestTypeId',
    },
    {
      name: 'operationByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationBy`).d('操作人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'operationBy',
      bind: 'operationByLov.id',
    },
    {
      name: 'shiftDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.shiftDate`).d('班次日期'),
      format: 'yyyy-MM-DD',
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
    },
    {
      name: 'workcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellId`).d('工作单元查询'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.WORKCELL',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workcellId',
      bind: 'workcellLov.workcellId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorId`).d('库位查询'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
  ],
  fields: [
    { name: 'kid', type: FieldType.number },
    // @ts-ignore
    { name: 'eventRequestId', type: FieldType.number, parentFieldName: 'kid' },
    {
      name: 'rowStyle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rowStyle`).d('行类型'),
    },
    {
      name: 'kid',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.kid`).d('事件/请求主键'),
    },
    {
      name: 'typeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.typeCode`).d('事件/请求类型编码'),
    },
    {
      name: 'calendarCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.calendarCode`).d('所属日历'),
    },
    {
      name: 'typeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.typeDesc`).d('事件/请求类型描述'),
    },
    {
      name: 'operationTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationTime`).d('事件时间'),
    },
    {
      name: 'operationUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationBy`).d('操作人'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
    {
      name: 'shiftDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftDate`).d('班次日期'),
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
    },
    {
      name: 'parentEventId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.parentEventId`).d('父事件主键'),
    },
  ],
});

const parentEventDrawerDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-event/limit-parent/list/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件主键'),
      disabled: true,
    },
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件编码'),
      disabled: true,
    },
    {
      name: 'eventTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeDescription`).d('事件描述'),
      disabled: true,
    },
    {
      name: 'eventTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTime`).d('事件时间'),
      disabled: true,
    },
    {
      name: 'eventTypeUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeUserName`).d('操作人'),
      disabled: true,
    },
    {
      name: 'requestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('事件请求编码'),
      disabled: true,
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元'),
      disabled: true,
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
      disabled: true,
    },
    {
      name: 'shiftDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftDate`).d('班次日期'),
      disabled: true,
    },
    {
      name: 'shiftCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftCode`).d('班次编码'),
      disabled: true,
    },
  ],
});

export { tableDS, parentEventDrawerDS };
