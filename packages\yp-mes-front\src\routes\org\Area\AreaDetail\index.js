/**
 * @Description: 新区域维护-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 13:19:18
 * @LastEditTime: 2023-05-18 11:29:47
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useRef } from 'react';
import { Button } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Button as PermissionButton } from 'components/Permission';
import { BASIC } from '@utils/config';
import { AttributeDrawer } from '@components/tarzan-ui';
import Detail from '../components/Detail';

// const TABLENAME = 'mt_mod_area_attr';
const modelPrompt = 'tarzan.model.org.area';

const AreaDetail = props => {
  const {
    match,
    custConfig,
  } = props;
  const {
    path,
    params: { areaId },
  } = match;
  const [canEdit, setCanEdit] = useState(areaId === 'create');
  const childRef = useRef();

  const handleSave = async () => {
    const { success, newKid } = await childRef.current.submit();
    if (success) {
      setCanEdit(prev => !prev);
      props.history.push(`/hmes/organization-modeling/area/dist/${newKid}`);
    }
  };

  const handleCancel = () => {
    if (areaId === 'create') {
      props.history.push('/hmes/organization-modeling/area/list');
    } else {
      childRef.current.reset();
      setCanEdit(prev => !prev);
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.componentName`).d('区域维护')}
        backPath="/hmes/organization-modeling/area/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <AttributeDrawer
          // tablename={TABLENAME}
          className="org.tarzan.model.domain.entity.MtModArea"
          kid={areaId}
          canEdit={canEdit}
          disabled={areaId === 'create'}
          serverCode={BASIC.TARZAN_MODEL}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.AREA_DETAIL.BUTTON`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <Detail canEdit={canEdit} ref={childRef} kid={areaId} columns={3} componentType="AREA" />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.model.org.area', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.AREA_DETAIL.BUTTON`],
})(AreaDetail));
