/**
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-16
 * @description 不良记录平台新建form表
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';

// const Host = '/yp-mes-20000'
const Host = `${BASIC.HMES_BASIC}`;
const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  paging: false,
  autoCreate: true,
  selection: false,
  primaryKey: 'ncRecordId',
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-nc-record/platform/detail/query/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows = {}, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          return {
            ...rows,
            // disposalList,
          };
        },
      };
    },
  },
  fields: [
    {
      name: 'labelColor',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.labelColor`).d('标签颜色'),
      lookupCode: 'HME.LABEL_COLOUR',
      required: true,
    },
    {
      name: 'labelType',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.labelType`).d('标签类型'),
      computedProps: {
        lookupCode: ({ record }) => {
          if (record?.get('labelColor') === '红') {
            return 'HME.LABEL_RED_MAPPING';
          }
          if (record?.get('labelColor') === '黄') {
            return 'HME.LABEL_YELLOW_MAPPING';
          }
          if (record?.get('labelColor') === '绿') {
            return 'HME.LABEL_GREEN_MAPPING';
          }
        },
      },
    },
    {
      name: 'codes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.codes`).d('条码号'),
    },
  ],
});


const detailLineDS: () => DataSetProps = () => ({
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows',
  primaryKey: 'ncRecordId',
  paging: false,
  fields: [
    {
      name: 'materialLotId',
    },
    {
      name: 'workOrderId',
    },
    {
      name: 'workOrderNum',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'eoId',
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
    },
    {
      name: 'ncRecordId',
    },
    {
      name: 'uomId',
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'workOrderId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'workOrderQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.workOrderQty`).d('生产指令数量'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'supplierId',
      type: FieldType.number,
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'locatorId',
      type: FieldType.number,
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    },
    {
      name: 'containerId',
      type: FieldType.number,
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadContainerCode`).d('装载容器'),
    },
    {
      name: 'prodLineId',
      type: FieldType.number,
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线'),
    },
    {
      name: 'routerId',
    },
    {
      name: 'routerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线'),
    },
    {
      name: 'routerStepId',
    },
    {
      name: 'workcellId',
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元'),
    },
    {
      name: 'operationId',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'equipmentId',
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备'),
    },
    {
      name: 'reviewNum',
      type: FieldType.string,
      maxLength: 100,
    },
    {
      name: 'defectDescription',
      type: FieldType.string,
      maxLength: 100,
    },
    {
      name: 'reworkDate',
      type: FieldType.dateTime,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      name: 'reworkUserName',
      type: FieldType.string,
      maxLength: 100,
    },
    {
      name: 'restrictedUse',
      type: FieldType.string,
      maxLength: 100,
    },
    {
      name: 'currentUserName',
      type: FieldType.string,
      maxLength: 100,
    },
    {
      name: 'currentDate',
    },
  ],
});



export { detailDS, detailLineDS };
