import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentSiteInfo } from '@utils/utils';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import moment from 'moment';

const modelPrompt = 'tarzan.hmes.equipmentMaintenance';
const tenantId = getCurrentOrganizationId();
console.log(getCurrentSiteInfo())
const historyPageFactory = () =>
  new DataSet({
    primaryKey: 'transRoutesId',
    selection: false,
    paging: true,
    autoQuery: true,
    validateBeforeQuery: true,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    queryDataSet: new DataSet({
      forceValidate: true,
      validateBeforeQuery: true,
      fields: [
        {
          name: 'equipmentLov',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          label: intl.get(`${modelPrompt}.equipmentLov`).d('设备编码'),
          lovCode: 'MT.MODEL.EQUIPMENT',
          multiple: true,
          dynamicProps: {
            required: ({ record }) => {
              if (record.get('materialLotIds').length || record.get('materialIds').length) {
                return false
              } else {
                return true
              }
            }
          },
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'equipmentIds',
          bind: 'equipmentLov.equipmentId',
        },
        {
          name: 'type',
          lookupCode: 'HME.FEEDING_TYPE',
          label: intl.get(`${modelPrompt}.type`).d('类型'),
          type: FieldType.string,
        },
        {
          name: 'materialLotCode',
          label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
          ignore: FieldIgnore.always,
          type: FieldType.object,
          multiple: true,
          lovCode: 'YP_MES.MATERIAL_LOT',
          lovPara: {
            tenantId,
          },
          dynamicProps: {
            required: ({ record }) => {
              if (record.get('equipmentIds').length || record.get('materialIds').length) {
                return false
              } else {
                return true
              }
            }
          },
        },
        {
          name: 'materialLotIds',
          bind: 'materialLotCode.materialLotId',
        },
        {
          name: 'materialLov',
          ignore: FieldIgnore.always,
          type: FieldType.object,
          multiple: true,
          label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
          lovCode: 'MT.MATERIAL',
          lovPara: {
            tenantId,
          },
          dynamicProps: {
            required: ({ record }) => {
              if (record.get('equipmentIds').length || record.get('materialLotIds').length) {
                return false
              } else {
                return true
              }
            }
          },
        },
        {
          name: 'materialIds',
          bind: 'materialLov.materialId',
        },
        {
          name: 'assemblePointLov',
          ignore: FieldIgnore.always,
          type: FieldType.object,
          multiple: true,
          label: intl.get(`${modelPrompt}.assemblePoint`).d('装配点'),
          lovCode: 'HME.HME_ASSEMBLE_POINT',
          lovPara: {
            tenantId,
            siteId: getCurrentSiteInfo().siteId || undefined,
          },
        },
        {
          name: 'assemblePointIds',
          bind: 'assemblePointLov.assemblePointId',
        },
        {
          name: 'startTime',
          label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
          type: FieldType.dateTime,
          max: 'endTime',
          dynamicProps: {
            required: ({ record }) => {
              if (record.get('equipmentIds').length || record.get('materialIds').length) {
                return true
              } else {
                return false
              }
            },
            min: ({ record }) => {
              if (record.get('materialLotIds').length) {
                return undefined
              } else {
                if (record?.get('endTime')) {
                  return moment(record?.get('endTime')).subtract(3, 'months')
                }
              }
            },
          },
        },
        {
          name: 'endTime',
          required: true,
          label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
          type: FieldType.dateTime,
          min: 'startTime',
          dynamicProps: {
            required: ({ record }) => {
              if (record.get('equipmentIds').length || record.get('materialIds').length) {
                return true
              } else {
                return false
              }
            },
            max: ({ record }) => {
              if (record.get('materialLotIds').length) {
                return undefined
              } else {
                if (record?.get('startTime')) {
                  return moment(record?.get('startTime')).add(3, 'months')
                }
              }
            },
          },
        },
        {
          name: 'loginName',
          label: intl.get(`${modelPrompt}.loginName`).d('操作人'),
          type: FieldType.string,
        },
      ],
    }),
    fields: [
      {
        name: 'equipmentCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      },
      {
        name: 'type',
        lookupCode: 'HME.FEEDING_TYPE',
        label: intl.get(`${modelPrompt}.type`).d('类型'),
        type: FieldType.string,
      },
      {
        name: 'materialLotCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'operationTime',
        label: intl.get(`${modelPrompt}.operationTime`).d('操作时间'),
        type: FieldType.string,
      },
      {
        name: 'userName',
        label: intl.get(`${modelPrompt}.userName`).d('操作人'),
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-workcell-material-lots-his/query`,
        };
      },
    },
  });

export default historyPageFactory;
