import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.andengMonitor';

const detailPageFactory = () =>
  new DataSet({
    primaryKey: 'lampStatusId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [{
        name: 'lampStatusId',
        type: FieldType.string,
      },]
    }),
    fields: [
      {
        name: 'processBarcode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.processBarcode`).d('产品条码'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'GET',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-lamp-status-watch/detail/ui`,
        };
      },
    },
  });

export default detailPageFactory;
