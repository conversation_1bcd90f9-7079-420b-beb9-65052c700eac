import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;
const modelPrompt = 'tarzan.hmes.QueryProductReworkRecords';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'traceRelId',
    // dataKey: 'rows.content',
    // totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      },
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('执行作业标识'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'ncCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      },
      {
        name: 'ncCodeDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCodeDescription`).d('不良代码名称'),
      },
      {
        name: 'qualityStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
      },
      {
        name: 'nowOperationDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.nowOperationDescription`).d('当前工艺'),
      },
      {
        name: 'causeOperationDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.causeOperationDescription`).d('判定工艺'),
      },
      {
        name: 'reworkOperationDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.reworkOperationDescription`).d('返修工艺'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
      },
      {
        name: 'reworkStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.reworkStatus`).d('返修状态'),
      },
      {
        name: 'reworkResultDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.reworkResultDescription`).d('返修结果'),
      },
      {
        name: 'reworkDisposalTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.reworkDisposalTime`).d('返修时间'),
      },
      {
        name: 'disposalUser',
        type: 'string',
        label: intl.get(`${modelPrompt}.disposalUser`).d('操作人'),
      },
    ],
    queryFields: [
      {
        name: 'identificationstr',
        type: 'string',
        label: intl.get(`${modelPrompt}.identificationstr`).d('条码号'),
      },
      {
        name: 'materialLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
        lovCode: 'MT.MATERIAL',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'materialCode',
        multiple: true,
      },
      {
        name: 'materialIds',
        bind: 'materialLov.materialId',
      },
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('返修工艺'),
        lovCode: 'MT.OPERATION',
        labelWidth: 150,
        ignore: 'always',
        lovPara: { tenantId },
        multiple: true,
      },
      {
        name: 'operationIds',
        bind: 'operationObj.operationId',
      },
      {
        name: 'workOrderNums',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNums`).d('生产指令'),
      },
      {
        name: 'defaultNcCodeLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.defaultNcCodeLov`).d('不良代码'),
        lovCode: 'MT.METHOD.NC_CODE',
        ignore: 'always',
        textField: 'ncCode',
        valueField: 'ncCodeId',
        lovPara: {
          tenantId,
        },
        multiple: true,
      },
      {
        name: 'ncCodeIds',
        bind: 'defaultNcCodeLov.ncCodeId',
      },
      {
        name: 'dateFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.dateFrom`).d('时间开始'),
        max: 'dateTo',
        dynamicProps: {
          min: ({ record }) => record?.get('dateTo')?moment(record?.get('dateTo')).subtract(3, 'M'):null,
        },
      },
      {
        name: 'dateTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.dateTo`).d('时间结束'),
        min: 'dateFrom',
        dynamicProps: {
          max: ({ record }) => record?.get('dateFrom')?moment(record?.get('dateFrom')).add(3, 'M'):null,
        },
      },
    ],
    transport: {
      read: ({ data }) => {
        const code = data.identificationstr;
        // const code2 = data.workOrderNumstr;
        const dataParams = {
          ...data,
          identifications: data.identificationstr ? code.split(',') : null,
          // workOrderNums: data.workOrderNumstr ? code2.split(',') : null,
        };
        if (!code) {
          delete dataParams.identifications;
        }
        // if (!code2) {
        //   delete dataParams.workOrderNums;
        // }
        return {
          url: `${Host}/v1/${tenantId}/hme-product-rework-query/list/query`,
          data: dataParams,
          method: 'GET',
        };
      },
    },
  };
};

const drawerDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'traceRelId',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      },
      {
        name: 'ncCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
      },
      {
        name: 'ncCodeDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncOperation`).d('不良工艺'),
      },
      {
        name: 'ncCodeStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCodeStatus`).d('不良代码状态'),
      },
      {
        name: 'ncRecordTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordTime`).d('不良记录时间'),
      },
      {
        name: 'ncRecordUser',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordUser`).d('记录人'),
      },
      {
        name: 'ncRecordClosedTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordClosedTime`).d('不良关闭时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-product-rework-query/detail/query`,
          method: 'GET',
        };
      },
    },
  };
};

export { tableDS, drawerDS };
