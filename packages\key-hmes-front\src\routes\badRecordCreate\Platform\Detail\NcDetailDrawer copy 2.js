import React from 'react';
import {
  Form,
  Lov,
  Modal,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import uuid from 'uuid/v4';


const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';

const NcDetailDrawer = ({ formDs, handleSubmit, selectionType, handleModalQuery, btnType }) => {

  const handleChangeWorkcell = (value) => {
    handleModalQuery(value.workcellId);
  }
  
  Modal.open({
    maskClosable: false,
    destroyOnClose: true,
    title: intl.get(`${modelPrompt}.detail.title`).d('不良记录明细'),
    style: {
      width: '800px',
    },
    children: (
      <>
        <Form dataSet={formDs} columns={1} labelWidth={200}>
          <Lov name="ncCodeLov" />
          <Lov name="rootCauseWorkcellLov" onChange={handleChangeWorkcell} />
          <TextField name="rootCauseEquipmentCode" />
          <TextField name="rootCauseOperationCode" />
          <Lov name="responsibleUserLov" />
          <TextField name="responsibleApartment" />
          <TextArea name="remark"/>
        </Form>
      </>
    ),
    drawer: true,
    onOk: async () => {
      if(!await formDs.current?.validate())return false;
      const temp = formDs.toData();
      if(selectionType !== 'edit'){
        const data = [];
        temp[0].ncCodeLov.forEach((item, index) => {
          const obj = {
            lineNumber:index+1,
            ...temp[0],
            ncCodeLov: item,
            ncCodeId: item.ncCodeId,
            ncCodeDesc: item.ncCodeDesc,
            enclosure: uuid(),
          }
          data.push(obj)
        })
        handleSubmit(data, selectionType, btnType||null)
      }else{
        handleSubmit(temp, selectionType, btnType||null)
      }
      return true
    },
  });
};

export default NcDetailDrawer;
