import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentTenantId } from 'utils/utils';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { searchCopy } from '@utils/utils';

const modelPrompt = 'tarzan.hmes.weighingScrap';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'stackSequenceId',
    selection: DataSetSelection.multiple,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    queryDataSet: new DataSet({
      events: {
        update({ record, name, value }) {
          searchCopy(['docNums', 'workOrderNums', 'materialLotCodes'], name, record, value);
        },
      },
      fields: [
        {
          name: 'docNums',
          multiple: true,
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.docNum`).d('单据编码'),
        },
        {
          name: 'docType',
          lookupCode: 'HME_WEIGHING_SCRAP_DOC_TYPE',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
        },
        {
          name: 'docStatus',
          // lookupCode: 'HME_WEIGHING_SCRAP_DOC_TYPE',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.docStatus`).d('单据状态'),
          textField: 'description',
          valueField: 'statusCode',
          lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
          lookupAxiosConfig: {
            transformResponse(data) {
              if (data instanceof Array) {
                return data;
              }
              const { rows } = JSON.parse(data);
              return rows;
            },
          },
        },
        {
          name: 'workOrderNums',
          multiple: true,
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
        },
        {
          name: 'workOrderNums',
          multiple: true,
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
        },
        {
          name: 'equipmentLov',
          multiple: true,
          ignore: FieldIgnore.always,
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.equipmentObj`).d('设备编码'),
          lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
          dynamicProps: {
            lovPara: () => {
              return {
                tenantId: getCurrentOrganizationId(),
              };
            },
          },
        },
        {
          name: 'equipmentIds',
          bind: 'equipmentLov.equipmentId',
        },
        {
          name: 'materialLotCodes',
          multiple: true,
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.materialLotCodes`).d('物料批编码'),
        },
        {
          name: 'siteLov',
          ignore: FieldIgnore.always,
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
          lovCode: 'MT.MODEL.SITE',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'siteId',
          bind: 'siteLov.siteId',
        },
        {
          name: 'createdFromDate',
          max: 'createdEndDate',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.createdFromDate`).d('创建时间从'),
        },
        {
          name: 'createdEndDate',
          min: 'createdFromDate',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.createdEndDate`).d('创建时间至'),
        },
        {
          name: 'createdBy',
          label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
          type: FieldType.string,
        },
      ],
    }),
    fields: [
      {
        name: 'docNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.docNums`).d('单据编码'),
      },
      {
        name: 'docType',
        lookupCode: 'HME_WEIGHING_SCRAP_DOC_TYPE',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.docType`).d('单据类型'),
      },
      {
        name: 'docStatus',
        // lookupCode: 'HME_WEIGHING_SCRAP_DOC_TYPE',
        type: FieldType.string,
        textField: 'description',
        valueField: 'statusCode',
        label: intl.get(`${modelPrompt}.docStatus`).d('单据状态'),
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'siteCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      },
      {
        name: 'workOrderNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      },
      {
        name: 'equipmentCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'weightFactor',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.weightFactor`).d('重量换算系数'),
      },
      {
        name: 'meterFactor',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.meterFactor`).d('米数换算系数'),
      },
      {
        name: 'scarpWeight',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.scarpWeight`).d('报废重量'),
      },
      {
        name: 'creationDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'createdByRealName',
        label: intl.get(`${modelPrompt}.createdByRealName`).d('创建人'),
        type: FieldType.string,
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
      {
        name: 'scrapTime',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.scrapTime`).d('报废时间'),
      },
      {
        name: 'lastUpdatedByRealName',
        label: intl.get(`${modelPrompt}.lastUpdatedByRealName`).d('更新人'),
        type: FieldType.string,
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'POST',
          url: `${BASIC.HMES_BASIC
            }/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/query/ui`,
        };
      },
    },
  });

export default listPageFactory;
