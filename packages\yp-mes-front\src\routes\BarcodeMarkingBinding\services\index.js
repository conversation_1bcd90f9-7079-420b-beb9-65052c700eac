import { getCurrentOrganizationId } from 'utils/utils';
import { Host } from '@/utils/config';

const tenantId = getCurrentOrganizationId();
// const Host = '/yp-mes-33202'
export function UnBind() {
  return {
    url: `${Host}/v1/${tenantId}/hme-identification-markings/unbinding`,
    method: 'POST',
  };
}

export function QueryInfo() {
  return {
    url: `${Host}/v1/${tenantId}/hme-identification-markings/marking/data`,
    method: 'GET',
  };
}
