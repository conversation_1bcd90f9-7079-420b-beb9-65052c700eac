import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId, } from 'utils/utils';
import {  FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.product.processShelf.maintenance';

const historyPageFactory = () =>
  new DataSet({
    primaryKey: 'materialOpExpirationHisId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'startDate',
          label: intl.get(`${modelPrompt}.startDate`).d('创建时间从'),
          type: FieldType.dateTime,
          max: 'endDate',
        },
        {
          name: 'endDate',
          label: intl.get(`${modelPrompt}.endDate`).d('创建时间至'),
          type: FieldType.dateTime,
          min: 'startDate',
        },
      ],
    }),
    fields: [
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'minExpiration',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.minExpiration`).d('最短保质期（小时）'),
      },
      {
        name: 'maxExpiration',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.maxExpiration`).d('最长保质期（小时）'),
      },
      {
        name: 'crossLineFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.crossLineFlag`).d('跨工段标识'),
      },
      {
        name: 'fromOperationName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.fromOperationName`).d('起始工艺'),
      },
      {
        name: 'toOperationName',
        label: intl.get(`${modelPrompt}.toOperationName`).d('截止工艺'),
        type: FieldType.string,
      },
      {
        name: 'crossLineFlagMeaning',
        label: intl.get(`${modelPrompt}.crossLineFlagMeaning`).d('是否跨工段'),
        type: FieldType.string,
      },
      {
        name: 'enableFlag',
        lookupCode: 'MT.YES_NO',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      },
      {
        name: 'revisionFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionFlag`).d('版本标记'),
      },
      {
        name: 'transferExpirationFlag',
        lookupCode: 'MT.YES_NO',
        label: intl.get(`${modelPrompt}.transferExpirationFlag`).d('转罐保质期标识'),
        type: FieldType.string,
      },
      {
        name: 'creationDate',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
        type: FieldType.string,
      },
      {
        name: 'createdRealName',
        label: intl.get(`${modelPrompt}.createdRealName`).d('创建人'),
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-material-op-expirations/his/list/ui`,
        };
      },
    },
  });

export default historyPageFactory;
