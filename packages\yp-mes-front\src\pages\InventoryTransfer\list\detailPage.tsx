import React, { useEffect, useState, } from 'react';
import {
  Form,
  Row,
  Col,
  TextField,
  Button,
  Select,
  Table,
  Lov,
  TextArea,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { useDataSet, } from 'utils/hooks';
import { RouteComponentProps } from 'react-router';
import { Collapse, Popconfirm } from 'choerodon-ui';
import detailPageFactory from '../stores/detailPageDs';
import detailTable from '../stores/detailTableDs';
import { ButtonColor, } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import { differenceBy, intersectionBy } from 'lodash'
import { getCurrentOrganizationId } from 'utils/utils';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import axios from 'axios';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
const { Panel } = Collapse;

const modelPrompt = 'tarzan.mes.inventoryTransfer';
interface RouterId {
  id: string;
}

const DetailPage: React.FC<RouteComponentProps<RouterId>> = ({ match: { params, path }, history }) => {

  const [edit, setEdit] = useState(false)

  const [transferDocStatus, setTransferDocStatus] = useState('NEW')

  const detailDs = useDataSet(detailPageFactory, 'inventoryTransferDetail');

  const detailTableDs = useDataSet(detailTable, 'inventoryTransferTable');

  useEffect(() => {
    if (params.id === 'create') {
      setEdit(true)
      detailDs.loadData([{
        transferDocStatus: 'NEW'
      }])
    } else {
      detailQuery()
    }
    return () => {
      detailDs.loadData([])
      detailTableDs.loadData([])
    }
  }, [params]);

  const detailQuery = () => {
    detailDs.setQueryParameter('invTransferDocId', params.id);
    detailDs.query().then(res => {
      if (res) {
        const { lineList, } = res.rows
        setTransferDocStatus(res.rows.transferDocStatus)
        detailTableDs.loadData(lineList || []);
      }
    });
    setEdit(false);
  }


  const columns: ColumnProps[] = [
    {
      name: 'editColumn',
      align: ColumnAlign.center,
      lock: ColumnLock.left,
      width: 80,
      hideable: false,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            disabled={!(edit && ['NEW', 'REJECTED'].includes(transferDocStatus))}
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
    },
    {
      name: 'materialLotCode',
      width: 250
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
    },
    {
      name: 'transferStatus',
    },
    {
      name: 'qty',
      width: 70,
    },
    {
      name: 'uomCode',
      width: 70,
    },
    {
      name: 'lot',
    },
    {
      name: 'fromLocatorCode',
    },
    {
      name: 'toLocatorCode',
    },
  ]

  // 删除表格某一行的回调
  const deleteRecord = async record => {
    detailTableDs.remove(record);
  };

  const onHandleSave = async () => {
    const validate = await detailDs.validate()
    const validateTable = await detailTableDs.validate()
    if (detailTableDs.toData().length === 0) {
      notification.error({
        message: intl.get(`${modelPrompt}.title.invTransferDocNum`).d('请至少维护一条单据明细')
      });
      return true
    }
    if (validate && validateTable) {
      const data: any = detailDs.toData()[0]
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-inv-transfer/save/ui`;
      const res: any = await axios.post(url, {
        ...data,
        lineList: detailTableDs.toData()
      })
      if (res && res.success) {
        if (params.id === 'create') {
          history.push(`/hmes/inventoryTransfer/detail/${res.rows}`)
        } else {
          detailQuery()
        }
        notification.success({});
      } else {
        notification.error({
          message: res.message
        });
      }
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.title.required`).d('请输入必填项')
      });
    }
  }

  const onHandleSubmit = async () => {
    const validate = await detailDs.validate()
    const validateTable = await detailTableDs.validate()
    if (!validate && !validateTable) {
      return true
    }
    const data: any = detailDs.toData()[0]
    const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-inv-transfer/submit/ui`;
    const res: any = await axios.post(url, [{
      ...data,
      lineList: detailTableDs.toData()
    }])
    if (res && res.success) {
      if (params.id === 'create') {
        history.push(`/hmes/inventoryTransfer/detail/${res.rows}`)
      } else {
        detailQuery()
      }
      notification.success({});
    } else {
      notification.error({
        message: res.message,
      });
    }
  }


  const onHandleEdit = () => {
    setEdit(true)
  }

  const onHandleCancel = () => {
    if (params.id === 'create') {
      history.push(`/hmes/inventoryTransfer/list`)
    } else {
      setEdit(false)
      detailDs.reset()
      detailTableDs.reset()
    }
  }

  const handleMaterialLots = async () => {
    const { materialLotCodes, invTransferDocId, toLocatorId }: any = detailDs.toData()[0]
    if (materialLotCodes) {
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-inv-transfer/lot/query/ui`
      const res: any = await axios.post(url, {
        invTransferDocId,
        toLocatorId,
        materialLotCodes,
      })
      if (res && res.success) {
        const list = intersectionBy((res.rows || []), detailTableDs.toData(), 'materialLotCode')
        const noList = differenceBy((res.rows || []), detailTableDs.toData(), 'materialLotCode')
        const listMap = new Map()
        const listKey = list.map(item => item.materialLotCode)
        list.map(item => {
          listMap.set(item.materialLotCode, item)
        })
        const updateList = detailTableDs.toData().map((item: any) => {
          if (listKey.includes(item.materialLotCode)) {
            return listMap.get(item.materialLotCode)
          } else {
            return item
          }
        })
        detailTableDs.loadData([...updateList, ...noList])
      } else {
        notification.error({
          message: res.message,
        });
      }
    }
  }

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.inventoryTransfer`).d('库存调拨平台')}
        backPath={history.location.pathname.includes('/pub') ? null : `/hmes/inventoryTransfer/list`}
      >
        {edit ? (
          <div>
            <Button disabled={!['NEW', 'REJECTED'].includes(transferDocStatus)} color={ButtonColor.primary} onClick={onHandleSubmit}>
              {intl.get(`${modelPrompt}.title.submit`).d('提交')}
            </Button>
            <Button onClick={() => onHandleCancel()}>
              {intl.get(`${modelPrompt}.title.cancel`).d('取消')}
            </Button>
            <Button color={ButtonColor.primary} onClick={onHandleSave}>
              {intl.get(`${modelPrompt}.title.save`).d('保存')}
            </Button>
          </div>
        ) : (
          <>
            {!history.location.pathname.includes('/pub') && <Button disabled={!['NEW', 'REJECTED'].includes(transferDocStatus)} color={ButtonColor.primary} onClick={() => onHandleEdit()}>
              {intl.get(`${modelPrompt}.title.edit`).d('编辑')}
            </Button>}
          </>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['1']}>
          <Panel key="1" header={intl.get(`${modelPrompt}.title.basicInfo`).d('基本信息')}>
            <Row type="flex" align="top" justify="space-between">
              <Col span={20}>
                <Form dataSet={detailDs} disabled={!edit} columns={3} labelWidth={112}>
                  <TextField name="invTransferDocNum" />
                  <Select name="transferDocStatus" />
                  <Lov name="siteLov" />
                  <Lov name="locatorLov" />
                  <TextField name="createdByRealName" />
                  <Lov name="approveLov" colSpan={2} rowSpan={2} />
                  <TextArea name="remark" colSpan={2} rowSpan={2} />
                </Form>
              </Col>
            </Row>
          </Panel>
        </Collapse>
        <Collapse bordered={false} defaultActiveKey={['2']}>
          <Panel key="2" header={intl.get(`${modelPrompt}.title.numDetail`).d('单据明细')}>
            <Row>
              <Col span={16}>
                <Form dataSet={detailDs} disabled={!edit} columns={1} >
                  <TextField name="materialLotCodes" onChange={handleMaterialLots} />
                </Form>
              </Col>
              <Col span={6}>
                <Button
                  onClick={() => {
                    detailDs.current!.set('materialLotCodes', [])
                  }}
                >
                  {intl.get('hzero.common.button.reset').d('重置')}
                </Button>
              </Col>
            </Row>
            <Table
              dataSet={detailTableDs}
              columns={columns}
              key="inventoryTransferTable"
              queryBar={TableQueryBarType.none}
              searchCode="inventoryTransferTable" // 动态筛选条后端接口唯一编码
              customizedCode="inventoryTransferTable" // 个性化编码
            />
          </Panel>
        </Collapse>

      </Content>
    </div>
  );
};

export default DetailPage;
