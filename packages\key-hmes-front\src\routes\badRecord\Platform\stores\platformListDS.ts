import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment';

const modelPrompt = 'tarzan.mes.event.badRecordPlatform';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'ncStartTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncStartTimeFrom`).d('不良发生时间从'),
      max: 'ncStartTimeTo',
      defaultValue: moment(moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'ncStartTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncStartTimeTo`).d('不良发生时间至'),
      min: 'ncStartTimeFrom',
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'ncIncidentNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncIncidentNum`).d('不良记录编码'),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
      name: 'ncRecordType',
      lookupCode: 'HME.NC_RECORD_TYPE',
      valueField: 'value',
      textField: 'meaning',
      lovPara: { tenantId },
    },
    {
      name: 'ncIncidentStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncIncidentStatus`).d('不良记录状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_RECORD_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      name: 'siteLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    // {
    //   name: 'ncReviewStatus',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncReviewStatusDesc`).d('审核状态'),
    //   lookupCode: 'MT.MES.REVIEW_STATUS',
    //   lovPara: { tenantId },
    // },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('产品物料'),
      lovCode: 'MT.MATERIAL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      textField: 'materialName',
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
    },

    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncOperationName`).d('不良发现工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      textField: 'operationName',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'ncStartUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncStartUserName`).d('不良记录人'),
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
      ignpre: FieldIgnore.always,
    },
    {
      name: 'ncStartUserId',
      bind: 'ncStartUserLov.id',
    },
    {
      name: 'ncCloseTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncCloseTimeFrom`).d('不良关闭日期自'),
      max: 'ncCloseTimeTo',
    },
    {
      name: 'ncCloseTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncCloseTimeTo`).d('不良关闭日期至'),
      min: 'ncCloseTimeFrom',
    },
    {
      name: 'ncCloseUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCloseUserName`).d('不良关闭人'),
      lovCode: 'HIAM.USER.ORG',
      lovPara: { tenantId },
      textField: 'realName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'ncCloseUserId',
      bind: 'ncCloseUserLov.id',
    },
  ],
  fields: [
    {
      name: 'ncIncidentNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncIncidentNum`).d('不良记录编码'),
    },
    {
      name: 'ncIncidentStatus',
      type: FieldType.string,
    },
    {
      name: 'ncIncidentStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordStatus`).d('不良记录状态'),
    },
    {
      name: 'ncRecordTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
    },
    {
      name: 'ncReviewStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncReviewStatusDesc`).d('审核状态'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('产品物料'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialVersion`).d('物料版本'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('工艺描述'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    },
    {
      name: 'ncStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.ncStartTime`).d('不良发生时间'),
    },
    {
      name: 'ncStartUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncStartUserName`).d('不良记录人'),
    },
    {
      name: 'ncCloseTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCloseTime`).d('不良关闭日期'),
    },
    {
      name: 'ncCloseUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCloseUserName`).d('不良关闭人'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },

  ],
  transport: {
    read: ({ data }) => {
      const code = data.identification;
      const dataParams = {
        ...data,
        identification: data.identification ? code.split(' ') : null,
      };
      if (!code) {
        delete dataParams.identification;
      }
      return {
        data: {
          ...dataParams,
        },
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-nc-incidents/platform/head/query/ui`,
        method: 'GET',
      };
    },
  },
});

const lineTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifications`).d('条码号'),
    },
    {
      name: 'intendedDisposalDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.intendedDisposalDesc`).d('欲处置结果'),
    },
    {
      name: 'disposalFunctionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.disposalFunctionDesc`).d('评审结果'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'ncRecordStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncRecordStatus`).d('不良记录行状态'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('不良数量'),
    },
    // {
    //   name: 'ncCodeAndDefectQty',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.ncCodeAndDefectQty`).d('不良代码及缺陷数'),
    // },
    // {
    //   name: 'disposalDescAndQty',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.disposalDescAndQty`).d('处置方法及处置数'),
    // },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('生产线'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元'),
    },
    {
      name: 'operationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationCode`).d('工艺'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('工艺描述'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    },

    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
    },
    {
      name: 'ncStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncStartTime`).d('不良发生时间'),
    },
    {
      name: 'ncStartUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncStartUserName`).d('不良记录人'),
    },
    {
      name: 'ncCloseTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCloseTime`).d('不良关闭日期'),
    },
    {
      name: 'ncCloseUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCloseUserName`).d('不良关闭人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-nc-incidents/platform/line/query/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS, lineTableDS };
