/*
 * Config - 全局统一配置
 * @Author: ji<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-05-09 14:31:38
 * @Last Modified by: xu xia<PERSON>u
 * @Last Modified time: 2023-06-15 14:28:53
 */
// import { API_HOST as host } from 'utils/config';
// import { API_HOST as host } from 'hzero-front/lib/utils/config';

export const CLIENT_ID = `${process.env.CLIENT_ID}`;
export const API_HOST = `${process.env.API_HOST}`;
// export const API_HOST = host;
export const HZERO_OAUTH = '/oauth';
export const HZERO_PLATFORM = '/hpfm';
export const HZERO_IAM = '/iam';
export const HZERO_HFLE = '/hfle';
export const AUTH_HOST = `${API_HOST}${HZERO_OAUTH}`;
export const AUTH_URL = `${AUTH_HOST}/oauth/authorize?response_type=token&client_id=${CLIENT_ID}`;
export const AUTH_SELF_URL = `${API_HOST}${HZERO_IAM}/hzero/v1/users/self`;

export const BASIC = {
  HMES_BASIC: '/mes',
  TARZAN_COMMON: '/tznc',
  // HRPT_COMMON: '/key-ne-focus-report',
};

export const Host = BASIC.HMES_BASIC;
//   process.env.NODE_ENV === 'production' ? `${host}/mes` : 'http://dev.hzero.org:8080/mes';
