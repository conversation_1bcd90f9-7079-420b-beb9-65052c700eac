/**
 * @Description:  领退料工作台-入口页
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
// import { Button as PermissionButton } from 'components/Permission';
import { Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
// import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
// import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
// import request from 'utils/request';
// import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
// import notification from 'utils/notification';
import { headerTableDS, lineTableDS } from './stores/ListDS';
import styles from './index.module.less';
// import MaterialLotDrawer from './MaterialLotDrawer';

const { Panel } = Collapse;
// const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.receiveReturn';
// let modalAssembly;

// const lugeUrl = '';
// const HMES_BASIC = BASIC.HMES_BASIC;

const Order = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { params },
    customizeTable,
  } = props;

  // 判断头搜索条件切换
  const [siteId, setSiteId] = useState();
  // const [selectedStatus, setSelectedStatus] = useState(undefined);
  // const [printIds, setPrintIds] = useState([]); // 头表格选择的id
  // const [loading, setLoading] = useState(false);

  // 头选中行instructionDocType
  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    // if (props?.history?.action === 'PUSH') {
    if (params && params.code) {
      headerTableDs.setQueryParameter('instructionDocNum', params.code);
    }
    headerTableDs.query(props.headerTableDs.currentPage);
    handleLineTableChange({
      dataSet: headerTableDs,
    });
    // }
  }, []);

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.queryDataSet.addEventListener
        : headerTableDs.queryDataSet.removeEventListener;
      handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
      handler.call(headerTableDs, 'batchSelect', handleLineTableChange);
      handler.call(headerTableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (data.siteId !== siteId) {
      setSiteId(data.siteId);
      record.set('prodLine', null);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 列表刷新清除头单选状态
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable();
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    const _selectedStatus = [];
    const _printIds = [];
    const completedList = ['1_PROCESSING', '1_COMPLETED', '2_PROCESSING', 'COMPLETED'];
    dataSet.selected.forEach(item => {
      const instructionDocStatus = item?.data?.instructionDocStatus;
      _printIds.push(item?.data?.instructionDocId);
      if (completedList.indexOf(instructionDocStatus) > -1) {
        if (_selectedStatus.indexOf('COMPLETED') === -1) {
          _selectedStatus.push('COMPLETED');
        }
      } else if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    // setSelectedStatus(_selectedStatus.length === 1 ? _selectedStatus[0] : undefined);
    // setPrintIds(_printIds);
  };

  // 行列表数据查询
  const queryLineTable = data => {
    lineTableDs.setQueryParameter('instructionDocId', data?.instructionDocId);
    lineTableDs.setQueryParameter('instructionDocType', data?.instructionDocType);
    lineTableDs.setQueryParameter('workOrderId', data?.workOrderId);
    lineTableDs.setQueryParameter('prodLineId', data?.prodLineId);
    lineTableDs.setQueryParameter('materialId', data?.materialId);
    lineTableDs.setQueryParameter('revisionCode', data?.revisionCode);
    lineTableDs.query();
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'instructionDocNum',
      width: 200,
      lock: 'left',
    },
    {
      name: 'instructionDocType',
      // width: 140,
    },
    {
      name: 'instructionDocStatusDesc',
      // width: 140,
    },
    {
      name: 'siteCode',
      // width: 140,
    },
    {
      name: 'demandTime',
      // width: 140,
      align: 'center',
    },
    {
      name: 'remark',
      // width: 140,
    },
    {
      name: 'printTimes',
      width: 100,
    },
    {
      name: 'realName',
      // width: 140,
    },
    {
      name: 'creationDate',
      // width: 140,
      align: 'center',
    },
  ];

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'lineNumber',
      width: 100,
      lock: 'left',
    },
    {
      name: 'materialCode',
      width: 140,
      lock: 'left',
    },
    {
      name: 'revisionCode',
      width: 100,
      lock: 'left',
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'quantity',
      width: 100,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'instructionStatusDesc',
      width: 100,
    },
    {
      name: 'soNum',
      title: intl.get(`${modelPrompt}.soNumSoLineNumber`).d('销单/行号'),
      width: 140,
      renderer: ({ record }) => {
        if (record.data.soNumber) {
          return `${record.data.soNumber || ''}/${record.data.soLineNum || ''}`;
        }
        return '';
      },
    },
    {
      name: 'sourceOrderNum',
      width: 140,
    },
    {
      name: 'productionLineCode',
      width: 140,
    },
    {
      name: 'receivedQty',
      width: 100,
    },
    {
      name: 'signedQty',
      width: 100,
    },
    {
      name: 'returnedQty',
      width: 100,
    },
    {
      name: 'fromIdentifyType',
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        }
        if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'fromLocatorCode',
      width: 140,
    },
    {
      name: 'sourceSubLocatorCode',
      width: 140,
    },
    {
      name: 'toIdentifyType',
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        }
        if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'toLocatorCode',
      width: 140,
    },
    {
      name: 'subLocatorCode',
      width: 140,
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceTypeDesc',
      width: 140,
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
    },
    {
      name: 'specifiedLot',
      width: 120,
    },
    {
      name: 'specifiedLevel',
      width: 120,
    },
  ];

  const headerRowClick = record => {
    queryLineTable(record?.toData());
  };

  return (
    <div className="hmes-style">
      {/* <Header title=''>
      </Header> */}
      <Content>
        {customizeTable(
          {
            // ${BASIC.CUSZ_CODE_BEFORE}
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.HEAD`,
          },
          <Table
            // searchCode="ltlgzt1"
            customizedCode="ltlgzt1"
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            // queryBar="filterBar"
            // queryBarProps={{
            //   fuzzyQuery: false,
            // }}
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            {lineTableDs &&
              customizeTable(
                {
                  // ${BASIC.CUSZ_CODE_BEFORE}
                  code: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.LINE`,
                },
                <Table
                  customizedCode="ltlgzt2"
                  className={styles['expand-table']}
                  dataSet={lineTableDs}
                  highLightRow={false}
                  columns={lineTableColumns}
                />,
              )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.receive.receiveReturn', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    //
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_MATERIAL_LOT.QUERY`,
    ],
  }),
)(Order);
