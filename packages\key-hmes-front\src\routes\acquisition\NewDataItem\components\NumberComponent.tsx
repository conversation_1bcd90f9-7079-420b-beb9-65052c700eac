/**
 * @Description: 检验项目维护-详情页
 * @Author: <EMAIL>
 * @Date: 2023/1/10 11:09
 */
import React from 'react';
import { Button, Col, DataSet, NumberField, Row, Tooltip, Lov } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { Size } from 'choerodon-ui/lib/_util/enum';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import styles from './index.module.less';

const modelPrompt = 'tarzan.hwms.inspectItemMaintain';

interface NumberComponentProps {
  name: string;
  parentDs: DataSet | undefined;
  dataSet: DataSet;
  disabled: boolean;
  showStandard: boolean;
  showNcCode: boolean;
  canEdit: boolean;
  dataType?: string | undefined;
  handleUpdateDisabled: () => void;
  handleUpdateWarning?: () => void;
}

const NumberComponent = observer((props: NumberComponentProps) => {
  const {
    dataSet,
    disabled,
    canEdit,
    handleUpdateDisabled,
    showStandard,
    showNcCode,
  } = props;

  const currentDisabled = disabled || !canEdit;

  const updateDataValue = record => {
    let _val: any = null;
    if (record.get('valueType') === 'single') {
      _val = record.get('multipleValue');
    } else if (!isNaN(record.get('leftValue')) || !isNaN(record.get('rightValue'))) {
      const leftNum = !isNaN(record.get('leftValue')) ? record.get('leftValue') : '-∞'
      const rightNum = !isNaN(record.get('rightValue')) ? record.get('rightValue') : '+∞'
      _val = `${record.get('leftChar')}${leftNum},${rightNum}${record.get('rightChar')}`;
    }
    record.set('dataValue', _val);
    record.set('standard', '');
    handleUpdateDisabled();
    // if (name === 'trueValue' && handleUpdateWarning) {
    //   handleUpdateWarning();
    // }
  };

  // 切换单值/范围的回调
  const handleChangeTag = record => {
    record.set('valueType', record.get('valueType') === 'single' ? 'section' : 'single');
    updateDataValue(record);
  };

  // 切换区间开闭的回调
  const handleChangeChar = (record, name) => {
    switch (record.get(name)) {
      case '(':
        record.set(name, '[');
        break;
      case '[':
        record.set(name, '(');
        break;
      case ')':
        record.set(name, ']');
        break;
      default:
        record.set(name, ')');
    }
    updateDataValue(record);
  };

  const handleCreate = () => {
    dataSet.create();
    handleUpdateDisabled();
    // if (name === 'trueValue' && handleUpdateWarning) {
    //   handleUpdateWarning();
    // }
  };

  const handleRemove = record => {
    dataSet.remove(record);
    handleUpdateDisabled();
    // if (name === 'trueValue' && handleUpdateWarning) {
    //   handleUpdateWarning();
    // }
  };

  const handleStandard = (value, oldValue, index) => { };

  return (
    <Row>
      {dataSet.map((record, index) => (
        <>
          <Col className={styles['number-col']} key={record.key}>
            {!currentDisabled && (
              <Tooltip
                title={
                  record.get('valueType') === 'single'
                    ? intl.get(`${modelPrompt}.switching.range`).d('点击切换范围值')
                    : intl.get(`${modelPrompt}.switch.single.value`).d('点击切换单值')
                }
              >
                <div
                  className={styles['tag-change-button']}
                  onClick={() => handleChangeTag(record)}
                >
                  {record.get('valueType') === 'single' ? 'X' : '(X)'}
                </div>
              </Tooltip>
            )}
            <div className={styles['input-number-inner']}>
              <NumberField
                name="multipleValue"
                record={record}
                disabled={currentDisabled}
                style={{ width: '100%' }}
                onChange={() => updateDataValue(record)}
                prefix={
                  record.get('valueType') === 'section' &&
                  (!currentDisabled ? (
                    <Tooltip
                      title={
                        record.get('rightChar') === '('
                          ? intl.get(`${modelPrompt}.contain`).d('点击切换为包含')
                          : intl.get(`${modelPrompt}.not.contain`).d('点击切换为不包含')
                      }
                    >
                      <div
                        className={styles['char-style']}
                        onClick={() => handleChangeChar(record, 'leftChar')}
                      >
                        {record.get('leftChar')}
                      </div>
                    </Tooltip>
                  ) : (
                    <div>{record.get('leftChar')}</div>
                  ))
                }
                suffix={
                  record.get('valueType') === 'section' &&
                  (!currentDisabled ? (
                    <Tooltip
                      title={
                        record.get('rightChar') === ')'
                          ? intl.get(`${modelPrompt}.contain`).d('点击切换为包含')
                          : intl.get(`${modelPrompt}.not.contain`).d('点击切换为不包含')
                      }
                    >
                      <div
                        className={styles['char-style']}
                        onClick={() => handleChangeChar(record, 'rightChar')}
                      >
                        {record.get('rightChar')}
                      </div>
                    </Tooltip>
                  ) : (
                    <div>{record.get('rightChar')}</div>
                  ))
                }
              />
              {showStandard && record.get('valueType') === 'section' && (
                <NumberField
                  placeholder={intl.get(`${modelPrompt}.standard`).d('标准值')}
                  name="standard"
                  record={dataSet.get(index)}
                  onChange={(value, oldValue) => handleStandard(value, oldValue, index)}
                />
              )}
              {showNcCode && (
                <Lov
                  placeholder={intl.get(`${modelPrompt}.ncCodeLov`).d('不良代码')}
                  name="ncCodeLov"
                  disabled={currentDisabled}
                  record={dataSet.get(index)}
                  onChange={(value, oldValue) => handleStandard(value, oldValue, index)}
                />
              )}

            </div>
            {!currentDisabled && (
              <Button
                icon="remove"
                size={Size.small}
                funcType={FuncType.flat}
                disabled={dataSet.length === 1}
                onClick={() => handleRemove(record)}
              />
            )}
            {!currentDisabled && (
              <Button
                icon="add"
                size={Size.small}
                funcType={FuncType.flat}
                style={{ visibility: index === 0 ? 'visible' : 'hidden' }}
                onClick={handleCreate}
              />
            )}
          </Col>
          {record.get('valueType') === 'section' && (
            <div
              className={styles['detail-shows']}
              style={{ paddingLeft: currentDisabled ? 0 : '25px' }}
            >
              {record.get('valueShow')}
            </div>
          )}
        </>
      ))}
    </Row>
  );
});

export default NumberComponent;
