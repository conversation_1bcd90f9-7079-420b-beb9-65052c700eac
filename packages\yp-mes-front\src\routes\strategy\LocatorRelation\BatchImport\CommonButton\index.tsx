/**
 * @feature 物料库位关系维护-批量导入按钮组件
 * @date 2021-12-15
 * <AUTHOR>
 */
import React, { useState, useEffect } from 'react';
import { Upload } from 'choerodon-ui';
import { DataSet } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import myInstance from '@utils/myAxios';
import { BASIC, API_HOST } from '@utils/config';
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import notification from 'utils/notification';
import DownloadButton from './DownloadButton';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.strategy.locatorRelation';

export interface CommonButtonProps {
  path: string;
  dataSet: DataSet;
  siteId: any;
  deleteFlag: boolean;
  setDeleteFlag: any;
}

export const CommonButton = props => {
  const [confirmStatus, changeConfirm] = useState(true); // 控制“数据导入”按钮的禁用
  const [validateStatus, changeValidate] = useState(true); // 控制“数据验证”按钮的禁用
  const [importLoading, setImportLoading] = useState(false); // “数据上传”按钮的loading
  const [validateLoading, setValidateLoading] = useState(false); // “数据验证”按钮的loading
  const [confirmLoading, setConfirmLoading] = useState(false); // “数据导入”按钮的loading
  const { path, siteId } = props;

  useEffect(() => {
    props.dataSet.query().then(res => {
      if (res?.success) {
        changeValidate((res.rows || {}).totalElements === 0);
        changeConfirm((res.rows || {}).allFlag !== 'Y');
      }
    });
  }, []);

  useDataSetEvent(props.dataSet, 'load', () => {
    changeValidate(props.dataSet.totalCount === 0);
  });

  // 对文件在上传之前进行校验操作
  const beforeUpload = file => {
    if (!['.csv', '.xlsx', '.xls'].some(child => file.name.indexOf(child) >= 0)) {
      notification.warning({
        description: '',
        message: intl.get(`${modelPrompt}.notification.import.validate`).d('上传文本格式不正确'),
      });
      return false;
    }
    changeConfirm(true);
    setImportLoading(true);
    return true;
  };

  // 上传成功的回调
  const uploadFile = info => {
    setImportLoading(false);
    // @ts-ignore
    if (info?.success) {
      props.dataSet.query().then(res => {
        // @ts-ignore
        if (res?.success) {
          changeValidate((res.rows || {}).totalElements === 0);
        }
      });
    } else if (info.message) {
      notification.error({
        message: info.message,
        description: '',
      });
    }
  };

  // 数据验证
  const validateAction = () => {
    setValidateLoading(true);
    const validateUrl = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-locator-material-rel/validate/excel/import/ui`;
    myInstance.post(validateUrl).then(res => {
      const { success } = res.data;
      if (success) {
        // eslint-disable-next-line no-unused-expressions
        notification?.success({});
        changeConfirm(false);
        props.dataSet.query().then(ress => {
          if (ress?.success && ress.rows) {
            changeConfirm((ress.rows || {}).allFlag !== 'Y');
          }
        });
      } else if (res.data.message) {
        notification.error({
          message: res.data.message,
          description: '',
        });
      }
      setValidateLoading(false);
    });
  };

  // 数据上传
  const confirmDatas = () => {
    setConfirmLoading(true);
    const importUrl = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-locator-material-rel/import/data/save/ui`;
    myInstance.post(importUrl, {}).then(res => {
      const { success } = res.data;
      if (success) {
        // eslint-disable-next-line no-unused-expressions
        notification?.success({
          message: intl.get(`${modelPrompt}.notification.import?.success`).d('导入成功'),
          description: '',
        });
        props.dataSet.query();
        changeConfirm(true);
        changeValidate(true);
        setConfirmLoading(false);
      } else if (res.data.message) {
        notification.error({
          message: res.data.message,
          description: '',
        });
        setConfirmLoading(false);
      } else {
        setConfirmLoading(false);
      }
    });
  };

  // 删除
  const onDelete = async () => {
    const ress = await props.dataSet.delete(props.dataSet.selected);
    if (ress?.success) {
      props.dataSet.query().then(res => {
        if (res?.success) {
          changeValidate((res.rows || {}).totalElements === 0);
        }
      });
      props.dataSet.clearCachedSelected();
      props.setDeleteFlag(true);
    }
  };

  const url = `${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-locator-material-rel/excel/import/ui`;
  const uploadProps = {
    name: 'file',
    beforeUpload,
    showUploadList: false,
    headers: {
      authorization: `Bearer ${getAccessToken()}`,
    },
    accept: '[.xlsx]',
    action: url,
    onSuccess: uploadFile,
    showUploadBtn: false,
    data: { siteId },
  };
  return (
    <>
      <span style={{ margin: '-4px 0 0 8px' }}>
        <Upload {...uploadProps} disabled={false}>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            loading={importLoading}
            disabled={!siteId}
            permissionList={[
              {
                code: `${path}.button.upload`,
                type: 'button',
                meaning: '列表页-数据上传按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.data.import`).d('数据上传')}
          </PermissionButton>
        </Upload>
      </span>
      <DownloadButton disabled={!siteId} path={path} />
      <PermissionButton
        type="c7n-pro"
        icon="spellcheck"
        onClick={validateAction}
        disabled={validateStatus || !siteId}
        loading={validateLoading}
        permissionList={[
          {
            code: `${path}.button.validatas`,
            type: 'button',
            meaning: '列表页-数据验证按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.button.data.validate`).d('数据验证')}
      </PermissionButton>
      <PermissionButton
        type="c7n-pro"
        icon="file_upload"
        onClick={confirmDatas}
        disabled={confirmStatus || !siteId}
        loading={confirmLoading}
        permissionList={[
          {
            code: `${path}.button.confirm`,
            type: 'button',
            meaning: '列表页-数据导入按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.button.data.confirm`).d('数据导入')}
      </PermissionButton>
      <PermissionButton
        type="c7n-pro"
        icon="delete"
        onClick={onDelete}
        disabled={props.deleteFlag || !siteId}
        permissionList={[
          {
            code: `${path}.button.edit`,
            type: 'button',
            meaning: '列表页-编辑新建删除复制按钮',
          },
        ]}
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
    </>
  );
};
