/**
 * @Description: 寻址策略列表
 * @Author: <<EMAIL>>
 * @Date: 2021-09-06 10:52:19
 * @LastEditTime: 2023-01-10 11:01:35
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { DataSet, Table, Icon } from 'choerodon-ui/pro';
import { Badge, Tag } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Button as PermissionButton } from 'components/Permission';

import { tableDS } from '../stores/StrategyListDS';
import styles from '../index.module.less';

const modelPrompt = 'tarzan.addressing.strategy';

const colorMap = {
  STOCKED: 'orange', // 待发货
  OVERDISPATCH: 'blue', // 超发
  RECEIVE_PENDING: 'orange', // 待上架
  STORE: 'blue', // 存放
  PICK: 'orange', // 捡取
  1: 'orange', // 待收
  2: 'purple', // 完工
  Y: 'blue', // 完工
  3: 'yellow', // 呆滞
  Z: 'orange', // 呆滞
  4: 'blue', // 存储
  X: 'green', // 存储
  5: 'yellow', // 配料
  6: 'red', // 不合格
};

const StrategyList = props => {
  const {
    dataSet,
    match: { path },
  } = props;

  useEffect(() => {
    dataSet.query(props.dataSet.currentPage);
  }, []);

  const orderDetail = id => {
    props.history.push(`/hmes/strategy/addressing/detail/${id}`);
  };

  const tagFormat = record => {
    if (record.data && record.data.addressingRange) {
      return record.data.addressingRange.map(item => {
        const itemKeys = Object.keys(item);
        if (itemKeys.length > 0) {
          return <Tag color={colorMap[itemKeys[0]]}>{item[itemKeys]}</Tag>;
        }
        return null;
      });
    }
    return null;
  };
  const retrievalOrderFormat = record => {
    const {
      processOrder,
      // judgmentConditionDesc,
    } = record.data || {};
    const { priority, priorityDesc, coordinate, locatorCapacity, inSiteTime, inLocatorTime, productionDate, expirationDate, lotCode } =
      processOrder || {};
    const showList = [];
    if (priority && priorityDesc) {
      priority.forEach((item, index) => {
        if (index > 0) {
          showList.push({
            desc: '/  ',
            capacity: undefined,
          });
        }
        if (item === 'COORDINATE') {
          const { coordinatePriority, coordinateValue } = coordinate || {};
          if (coordinatePriority) {
            coordinatePriority.forEach(_item => {
              showList.push({
                desc: _item,
                capacity: coordinateValue[(_item || '').toLowerCase()],
              });
            });
          }
        }
        if (item === 'LOCATOR_CAPACITY') {
          showList.push({
            desc: priorityDesc[index] || priority[index],
            capacity: locatorCapacity,
          });
        }
        if (item === 'IN_SITE_TIME') {
          showList.push({
            desc: priorityDesc[index] || priority[index],
            capacity: inSiteTime,
          });
        }
        if (item === 'IN_LOCATOR_TIME') {
          showList.push({
            desc: priorityDesc[index] || priority[index],
            capacity: inLocatorTime,
          });
        }
        if (item === 'PRODUCTION_DATE') {
          showList.push({
            desc: priorityDesc[index] || priority[index],
            capacity: productionDate,
          });
        }
        if (item === 'EXPIRATION_DATE') {
          showList.push({
            desc: priorityDesc[index] || priority[index],
            capacity: expirationDate,
          });
        }
        if (item === 'LOT_CODE') {
          showList.push({
            desc: priorityDesc[index] || priority[index],
            capacity: lotCode,
          });
        }
      });
    }
    return (
      <span>
        {showList.length > 0 &&
          showList.map(item => {
            return (
              <span>
                {item.desc}
                {item.capacity && (
                  <Icon
                    className={item.capacity === 'ASC' ? styles['icon-up'] : styles['icon-down']}
                    type="play_arrow"
                  />
                )}
              </span>
            );
          })}
        {/* {showList.length === 0 &&
          judgmentConditionDesc[0] &&
          intl.get(`${modelPrompt}.noOrder`).d('无次序')}
        {showList.length === 0 &&
          !judgmentConditionDesc[0] &&
          intl.get(`${modelPrompt}.noJudgment`).d('无判定')} */}
      </span>
    );
  };

  const judgmentConditionDescFormat = record => {
    const { judgmentConditionDesc } = record.toData() || {};
    if (judgmentConditionDesc && judgmentConditionDesc.length > 0) {
      return <span>{judgmentConditionDesc.join('/')}</span>;
    }
    return null;
  };

  const columns = [
    {
      name: 'addressingStrategyCode',
      lock: 'left',
      width: 180,
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => {
              orderDetail(record.data.addressingStrategyId);
            }}
          >
            {record.data.addressingStrategyCode}
          </a>
        );
      },
    },
    {
      name: 'addressingStrategyTypeDesc',
      width: 120,
      renderer: ({ value, record }) =>
        value && <Tag color={colorMap[record.get('addressingStrategyType')]}>{value}</Tag>,
    },
    {
      name: 'addressingStrategyLevelDesc',
      width: 120,
    },
    {
      name: 'addStrategyLocationLimitDesc',
      width: 120,
    },
    {
      name: 'descendantStrategyCode',
      width: 120,
    },
    {
      name: 'areaAddressingTrigger',
      width: 120,
    },
    {
      name: 'addressingRangeDesc',
      width: 180,
    },
    {
      name: 'addressingRangeType',
      width: 180,
      renderer: ({ record }) => {
        if (record.data && record.data.addressingRangeType === 'LOCATOR_TYPE') {
          return intl.get(`${modelPrompt}.locatorLevel`).d('库位层级');
        }
        if (record.data && record.data.addressingRangeType === 'COORDINATE') {
          return intl.get(`${modelPrompt}.coordinateSystem`).d('坐标系');
        }
        return null;
      },
    },
    {
      name: 'addressingRange',
      width: 300,
      renderer: ({ record }) => {
        if (record.data && record.data.addressingRange && record.data.addressingRange.length > 0) {
          return tagFormat(record);
        }
        return null;
      },
    },
    {
      name: 'screeningConditionDesc',
      width: 180,
      renderer: ({ record }) => {
        const { screeningConditionDesc } = record.toData() || {};
        return screeningConditionDesc && screeningConditionDesc.length > 0
          ? screeningConditionDesc.join('/')
          : intl.get(`${modelPrompt}.unlimited`).d('无限制');
      },
    },
    {
      name: 'retrievalOrder',
      width: 230,
      renderer: ({ record }) => {
        return retrievalOrderFormat(record);
      },
    },
    {
      name: 'judgmentConditionDesc',
      width: 130,
      renderer: ({ record }) => {
        return judgmentConditionDescFormat(record);
      },
    },
    {
      name: 'enableFlag',
      width: 130,
      renderer: ({ record }) => (
        <Badge
          status={record.get('enableFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('enableFlag') === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
      align: 'center',
    },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.detail.title`).d('寻址策略')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => orderDetail('create')}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          highLightRow={false}
          dataSet={dataSet}
          columns={columns}
          searchCode="StrategyList"
          customizedCode="StrategyList"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.addressing.strategy', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...tableDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(StrategyList),
);
