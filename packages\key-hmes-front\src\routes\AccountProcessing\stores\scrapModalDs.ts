import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inventory.marking';


const scrapFactory = () =>
  new DataSet({
    autoQuery: false,
    primaryKey: 'lineNumber',
    selection: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    fields: [
      {
        name: 'materialLotId',
        type: FieldType.string,
      },
      {
        name: 'cancelReason',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.cancelReason`).d('报废取消原因'),
      },
      {
        name: 'workNumOrderLov',
        type: FieldType.object,
        required: true,
        lovCode: 'HME.WO_NOT_CLOSED',
        textField: 'workOrderNum',
        label: intl.get(`${modelPrompt}.workNumOrder`).d('工单'),
        dynamicProps: {
          lovPara: ({ dataSet, record }) => {
            const ncRecordType = dataSet.getState('ncRecordType');
            if (ncRecordType === 'RM_NC') {
              return {
                tenantId,
                materialLotId: record?.get('materialLotId'),
              };
            }
            return { tenantId };
          },
        },
      },
      {
        name: 'workOrderId',
        bind: 'workNumOrderLov.workOrderId',
      }, {
        name: 'workOrderNum',
        bind: 'workNumOrderLov.workOrderNum',
      },
      {
        name: 'identification',
        label: intl.get(`${modelPrompt}.identification`).d('条码'),
        type: FieldType.string,
      },
      {
        name: 'cancelReason',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.cancelReason`).d('报废取消原因'),
      },
      {
        name: 'realName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.realName`).d('操作人'),
      },
      {
        name: 'creationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.creationDate`).d('操作时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-nc-scrap-cancels/query`,
          method: 'GET',
        };
      },
    },
  });

export default scrapFactory;
