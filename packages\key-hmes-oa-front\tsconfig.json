{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "outDir": "./lib",                        /* Redirect output structure to the directory. */
    "rootDir": "./src",                       /* Specify the root directory of input files. Use to control the output directory structure with --outDir. */
    "baseUrl": "./",                       /* Base directory to resolve non-absolute module names. */
  },
  "include": [
    "src"
  ],
  "exclude": [
    "lib",
    "node_modules",
    "build",
    "scripts",
    "acceptance-tests",
    "webpack",
    "jest",
    "src/setupTests.ts",
    "tslint:latest",
    "tslint-config-prettier"
  ]
}
