/**
 * @Description: 生产指令管理拆分抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:40:22
 * @LastEditTime: 2022-11-10 16:04:42
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Form, TextField, Lov, Icon, Table } from 'choerodon-ui/pro';
import { Tabs, Tooltip } from 'choerodon-ui';
import { isEmpty } from 'lodash';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { TarzanDrawer } from '@components/tarzan-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';

import styles from '../../index.module.less';
import { mergeDS, mergeListDS } from '../../stores/ProductionOrderMgtDrawerMergeDS';

const { TabPane } = Tabs;

const modelPrompt = 'tarzan.workshop.productionOrderMgt';

const ProductionOrderMgtMergeWoForm = props => {
  const { workOrderId, visible, handleRefresh = () => {}, handleCancel = () => {}, status } = props;

  const [drawerEdit, setDrawerEdit] = useState(false);

  const mergeDs = useMemo(() => {
    return new DataSet(mergeDS());
  }, []);

  const mergeListDs = useMemo(() => {
    return new DataSet(mergeListDS());
  }, []);

  useEffect(() => {
    if (visible) {
      splitQuery(workOrderId);
    }
  }, [visible]);

  const splitQuery = id => {
    mergeDs.queryParameter = {
      workOrderId: id,
    };
    mergeDs.query();
    mergeListDs.queryParameter = {
      parentWorkOrderId: id,
    };
    mergeListDs.query();
  };

  const resetData = () => {
    mergeDs.loadData([]);
  };

  const resetSplits = () => {
    splitQuery(workOrderId);
    setDrawerEdit(false);
  };

  const handleSave = async () => {
    mergeDs.current.set({ nowDate: new Date().getTime() });
    const validate = await mergeDs.validate(false, true);
    if (validate) {
      await mergeDs.submit().then(res => {
        const { rows = [] } = res || {};
        if (!isEmpty(rows) && rows[0].success) {
          setDrawerEdit(false);
          splitQuery(workOrderId);
          handleRefresh('WOSPLIT');
        }
      });
    }
  };

  const handleClose = async () => {
    setDrawerEdit(false);
    handleCancel();
    resetData();
  };

  const orderDetail = id => {
    props.history.push(`/hmes/workshop/production-order-mgt/detail/${id}`);
  };

  const columns = [
    {
      name: 'workOrderNum',
      lock: 'left',
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              orderDetail(record.data.workOrderId);
            }}
          >
            {value}
          </a>
        );
      },
      width: 180,
    },
    {
      name: 'materialCode',
      width: 100,
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'statusDesc',
      width: 70,
    },
    {
      name: 'qty',
      width: 100,
    },
    {
      name: 'creationDate',
      align: 'center',
      width: 160,
    },
  ];

  return (
    <>
      <TarzanDrawer
        visible={visible}
        canEdit={false}
        onOk={handleClose}
        onClose={handleClose}
        width={720}
        title={
          <div className={styles['modal-title-container']}>
            <div>{intl.get(`${modelPrompt}.model.productionOrderMgt.mergeWo`).d('WO合并')}</div>
            <div className={styles['modal-title-btn-box']}>
              {drawerEdit && (
                <>
                  <PermissionButton type="c7n-pro" onClick={resetSplits}>
                    {intl.get('tarzan.common.button.cancel').d('取消')}
                  </PermissionButton>
                  <PermissionButton type="c7n-pro" color={ButtonColor.primary} onClick={handleSave}>
                    {intl.get('tarzan.common.button.save').d('保存')}
                  </PermissionButton>
                </>
              )}
              {!drawerEdit && (
                <PermissionButton
                  type="c7n-pro"
                  // color={ButtonColor.primary}
                  disabled={
                    !status || ['NEW', 'RELEASED', 'EORELEASED', 'HOLD'].indexOf(status) === -1
                  }
                  onClick={() => {
                    setDrawerEdit(prev => !prev);
                  }}
                >
                  {intl.get(`${modelPrompt}.merge`).d('合并')}
                </PermissionButton>
              )}
            </div>
          </div>
        }
      >
        <Form
          disabled={!drawerEdit}
          dataSet={mergeDs}
          columns={2}
          labelLayout="horizontal"
          labelWidth={110}
        >
          <div name="targetworkOrderId" className={styles['split-qty-box']}>
            <TextField name="targetworkOrderId" />
            <Tooltip
              placement="right"
              title={intl
                .get(`${modelPrompt}.mergeNote`)
                .d(
                  '指定需要生成的目标生产指令编码，若输入存在的生产指令编码则合并到该生产指令上，若不存在则按照该编码生成新的生产指令',
                )}
            >
              <Icon
                className={styles['split-qty-icon']}
                type="help_outline"
                style={{
                  color: 'rgba(0,0,0,0.2)',
                  fontSize: 14,
                  cursor: 'pointer',
                }}
              />
            </Tooltip>
          </div>
          <TextField name="workOrderNum" />
          <Lov name="secondaryWorkOrders" />
        </Form>
        <Tabs>
          <TabPane tab={intl.get(`${modelPrompt}.mergeOrigin`).d('合并来源')}>
            <Table dataSet={mergeListDs} columns={columns} />
          </TabPane>
        </Tabs>
      </TarzanDrawer>
    </>
  );
};

export default ProductionOrderMgtMergeWoForm;
