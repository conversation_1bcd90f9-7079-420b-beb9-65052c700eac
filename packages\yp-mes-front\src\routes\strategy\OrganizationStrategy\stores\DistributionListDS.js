/**
 * @Description: 策略组织关系列表查询 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-09-13 09:43:15
 * @LastEditTime: 2023-07-19 14:50:17
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.addressing.strategy';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-add-strategy-assign/list/ui`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'eoId',
  cacheSelection: true,
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'locator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'locatorId',
      type: FieldType.string,
      bind: 'locator.locatorId',
    },
    {
      name: 'strategy',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.strategyCode`).d('策略编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.ADDRESSING_STRATEGY`,
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'addressingStrategyId',
      type: FieldType.string,
      bind: 'strategy.addressingStrategyId',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'materialCategory',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialTypeCode`).d('物料类别编码'),
      lovCode: 'MT.METHOD.MATERIAL_CATEGORY_SITES',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialCategoryId',
      type: FieldType.string,
      bind: 'materialCategory.materialCategoryId',
    },
    {
      name: 'businessTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessTypeCode`).d('业务类型'),
      lovCode: 'MT.COMMON.BUSINESS_TYPE',
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'businessType',
      type: FieldType.string,
      bind: 'businessTypeObj.typeCode',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.enable`).d('启用') },
          { value: 'N', key: intl.get(`tarzan.common.label.disable`).d('禁用') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      type: FieldType.string,
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorDesc`).d('库位说明'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
    },
    {
      name: 'materialCategoryCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialTypeCode`).d('物料类别编码'),
    },
    {
      name: 'materialCategoryName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialTypeDesc`).d('物料类别说明'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessTypeCode`).d('业务类型'),
    },
    {
      name: 'addressingStrategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addressingStrategy`).d('寻址策略'),
    },
    {
      name: 'addressingStrategyId',
      type: FieldType.string,
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

export { tableDS };
