import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.cassetteMaterial';

const materialHistoryFactory = () =>
  new DataSet({
    primaryKey: 'hisKeyId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'creationDateFrom',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.creationDateFrom`).d('创建时间从'),
        },
        {
          name: 'creationDateTo',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.creationDateTo`).d('创建时间至'),
        },
      ]
    }),
    fields: [
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
      },
      {
        name: 'uomName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.uomName`).d('物料单位'),
      },
      {
        name: 'maxQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.maxQty`).d('最大装载量'),
      },
      {
        name: 'enableFlag',
        lookupCode: 'MT.YES_NO',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.enableFlag`).d('有效性'),
      },
      {
        name: 'createdByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.createdByName`).d('创建人'),
      },
      {
        name: 'creationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.creationDate`).d('创建时间'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'GET',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-his-assembly-point/assembly-point/material-his/ui`,
        };
      },
    },
  });

export default materialHistoryFactory;
