import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import DataSet, { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { searchCopy } from '@utils/utils';
const modelPrompt = 'tarzan.mes.barcodeWorkstationBinding';
const tenantId = getCurrentOrganizationId();
import notification from 'utils/notification';

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  events: {
    query: ({ dataSet, }) => {
      const { equipmentId, workcellId, assemblePointIds, materialLotCodes, materialIds } = dataSet.queryDataSet.toData()[0]
      if (equipmentId || workcellId || assemblePointIds.length || materialLotCodes.length || materialIds.length) {
        return true
      } else {
        notification.error({
          message: intl.get(`${modelPrompt}.form.select`).d('请至少输入一条查询条件')
        })
        return false
      }
    }
  },
  queryDataSet: new DataSet({
    events: {
      update({ record, name, value }) {
        searchCopy(
          [
            'materialLotCodes',
          ],
          name,
          record,
          value,
        );
      }
    },
    fields: [
      {
        name: 'equipmentLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.equipmentLov`).d('设备编码'),
        lovCode: 'MT.MODEL.EQUIPMENT',
        ignore: FieldIgnore.always,
      },
      {
        name: 'equipmentId',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'workCellLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.workCellLov`).d('工位编码'),
        lovCode: 'HME.WORKCELL_SITE',
        ignore: FieldIgnore.always,
      },
      {
        name: 'workcellId',
        bind: 'workCellLov.workcellId',
      },
      {
        name: 'assemblePointLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.assemblePointLov`).d('装配点编码'),
        lovCode: 'HME.HME_ASSEMBLE_POINT',
        ignore: FieldIgnore.always,
        multiple: true,
      },
      {
        name: 'assemblePointIds',
        bind: 'assemblePointLov.assemblePointId',
      },
      {
        name: 'materialLotCodes',
        multiple: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'materialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
        lovCode: 'MT.MATERIAL',
        ignore: FieldIgnore.always,
        multiple: true,
      },
      {
        name: 'materialIds',
        bind: 'materialLov.materialId',
      },
    ],
  }),
  fields: [
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('工位名称'),
    },
    {
      name: 'assemblePointCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
    },
    {
      name: 'assemblePointName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assemblePointName`).d('装配点名称'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'materialLotSequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotSequence`).d('顺序'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'createdByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByRealName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByRealName`).d('最近更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最近更新时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-workcell-material-lots/query-by-condition`,
        method: 'POST',
      };
    },
  },
});
