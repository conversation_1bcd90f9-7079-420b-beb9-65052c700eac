import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.receive.confirmationTeamWorkingHours';
const tenantId = getCurrentOrganizationId();
// ${BASIC.HMES_BASIC}
const HMES_BASIC = BASIC.HMES_BASIC;

const headerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: 'multiple',
  dataKey: 'content',
  totalKey: 'totalElements',
  cacheSelection: true,
  primaryKey: 'handoverId',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${HMES_BASIC}/v1/${tenantId}/hme-handovers/list/ui`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'site.siteId',
    },
    {
      name: 'handoverNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.handoverNum`).d('交班号'),
    },
    {
      name: 'prodLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLov`).d('产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'productionLineId',
      type: FieldType.number,
      bind: 'prodLov.prodLineId',
    },
    {
      name: 'shiftType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftType`).d('班次'),
      lookupCode: 'HME.HANDOVER_TIME',
      valueField: 'value',
      textField: 'meaning',
      lovPara: {
        tenantId,
      },
    },
    { name: 'creationDateFrom',
      type: 'date',
      max: 'creationDateTo',
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
    },
    { name: 'creationDateTo', type: 'date',
      min: 'creationDateFrom',

      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
    },
    {
      name: 'userLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.userLov`).d('交班人'),
      lovCode: 'MT.USER.ORG',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'shiftUser',
      type: FieldType.number,
      bind: 'userLov.id',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'handoverNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.handoverNum`).d('交班号'),
    },
    {
      name: 'shiftTypeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftTypeMeaning`).d('班次'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线描述'),
    },
    {
      name: 'shiftDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftDate`).d('日期'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.realName`).d('交班人'),
    },
  ],
});


export { headerTableDS };
