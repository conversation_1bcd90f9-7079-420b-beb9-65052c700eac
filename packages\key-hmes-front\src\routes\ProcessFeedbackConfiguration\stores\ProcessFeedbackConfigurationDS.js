import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.hmes.ProcessFeedbackConfiguration';

const tenantId = getCurrentOrganizationId();

// const Host1 = `/key-ne-focus-mes-38510`;

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'operationTagConfigId',
    paging: true,
    autoQuery: false,
    selection: 'single',
    fields: [
      {
        name: 'siteObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.siteObj`).d('站点'),
        textField: 'siteCode',
        ignore: 'always',
        required: true,
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        labelWidth: 150,
      },
      {
        name: 'siteId',
        type: 'number',
        bind: 'siteObj.siteId',
      },
      {
        name: 'siteCode',
        type: 'string',
        bind: 'siteObj.siteCode',
        labelWidth: 150,
      },
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料'),
        lovCode: 'HME.PERMISSION_MATERIAL',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        textField: 'materialCode',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: getCurrentOrganizationId(),
              siteId: record?.get('siteId'),
            };
          },
        },
      },
      {
        name: 'materialCode',
        type: 'string',
        bind: 'materialObj.materialCode',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'materialObj.materialName',
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'materialSiteId',
        type: 'string',
        bind: 'materialObj.materialSiteId',
      },
      {
        name: 'materialSiteId',
        type: 'string',
        bind: 'materialObj.materialSiteId',
      },
      {
        name: 'revisionFlag',
        type: 'string',
        bind: 'materialObj.revisionFlag',
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        bind: 'materialObj.revisionCode',
        textField: 'revisionCode',
        valueField: 'revisionCode',
        lookupUrl: `${Host}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
        lookupAxiosConfig: ({ record }) => {
          const _params = record?.toData() || {};
          if (_params.siteId && _params.materialSiteId) {
            return {
              params: {
                materialSiteId: _params.materialSiteId,
                siteId: _params.siteId,
              },
              transformResponse(data) {
                // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
                if (data instanceof Array) {
                  return data;
                }
                if (data.failed) {
                  return [];
                }
                const rows = JSON.parse(data);
                return rows;
              },
            };
          }
        },
        dynamicProps: {
          required: record => {
            const _params = record?.record.data.materialObj || {};
            if (_params && _params.revisionFlag && _params.revisionFlag === 'Y') {
              return true;
            }
            return false;
          },
        },
      },
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('工艺名称'),
        lovCode: 'MT.OPERATION_CONVERSION',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        textField: 'operationName',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'operationName',
        type: 'string',
        bind: 'operationObj.operationName',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('工艺描述'),
        bind: 'operationObj.description',
      },
      {
        name: 'operationId',
        type: 'number',
        bind: 'operationObj.operationId',
      },
      {
        name: 'prodLineObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineObj`).d('生产线编码'),
        lovCode: 'HME.PERMISSION_PROD_LINE',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        textField: 'prodLineCode',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: getCurrentOrganizationId(),
              siteId: record?.get('siteId'),
            };
          },
        },
      },
      {
        name: 'prodLineCode',
        type: 'string',
        bind: 'prodLineObj.prodLineCode',
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线描述'),
        bind: 'prodLineObj.prodLineName',
      },
      {
        name: 'prodLineId',
        type: 'number',
        bind: 'prodLineObj.prodLineId',
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        required: true,
        lookupCode: 'MT.ENABLE_FLAG',
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('更新人'),
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('更新时间'),
      },
    ],
    queryFields: [
      {
        name: 'materialCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
        lovCode: 'MT.MATERIAL',
        labelWidth: 150,
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialCode.materialId',
      },
      {
        name: 'operationCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationCode`).d('工艺'),
        lovCode: 'MT.OPERATION_CONVERSION',
        labelWidth: 150,
      },
      {
        name: 'operationId',
        type: 'number',
        bind: 'operationCode.operationId',
      },
      {
        name: 'prodLineCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineCode`).d('产线'),
        lovCode: 'MT.MODEL.PRODLINE',
        labelWidth: 150,
      },
      {
        name: 'prodLineId',
        type: 'number',
        bind: 'prodLineCode.prodLineId',
      },
      // {
      //   type: 'string',
      //   name: 'enableFlag',
      //   label: '有效性',
      //   lookupCode: 'MT.ENABLE_FLAG',
      //   // defaultValue: 'Y',
      // },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-operation-tag-configs/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const assObjectsDS = () => {
  return {
    name: 'assObjectsDS',
    primaryKey: 'operationTagConfigLineId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'operationObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationObj`).d('参数项工艺'),
        lovCode: 'MT.OPERATION_CONVERSION',
        labelWidth: 150,
        ignore: 'always',
        required: true,
        textField: 'operationName',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'operationId',
        type: 'number',
        bind: 'operationObj.operationId',
      },
      {
        name: 'operationName',
        type: 'string',
        bind: 'operationObj.operationName',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('工艺名称'),
        bind: 'operationObj.description',
      },
      {
        name: 'tagObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.tagObj`).d('采集项'),
        ignore: 'always',
        required: true,
        textField: 'tagCode',
        lovCode: 'HME_MT_TAG',
        labelWidth: 150,
      },
      {
        name: 'tagId',
        type: 'number',
        bind: 'tagObj.tagId',
      },
      {
        name: 'tagCode',
        type: 'string',
        bind: 'tagObj.tagCode',
      },
      {
        name: 'tagDescription',
        type: 'string',
        bind: 'tagObj.tagDescription',
        label: intl.get(`${modelPrompt}.tagDescription`).d('采集项描述'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        required: true,
        lookupCode: 'MT.ENABLE_FLAG',
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('更新人'),
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('更新时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-operation-tag-config-lines/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

export { tableDS, assObjectsDS };
