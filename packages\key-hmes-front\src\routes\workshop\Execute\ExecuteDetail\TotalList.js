/**
 * @Description: 实绩信息组件
 * @Author: <<EMAIL>>
 * @Date: 2021-07-26 15:34:14
 * @LastEditTime: 2022-09-08 16:33:12
 * @LastEditors: <<EMAIL>>
 */

import React, { useState } from 'react';
import intl from 'utils/intl';
import { Row, Col, Popover, Tooltip, Collapse } from 'choerodon-ui';

import styles from '../index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.workshop.execute';

const ProductionOrderMgtTotalList = props => {
  const { listData } = props;

  const { list1 = [], list2 = [], max, other } = listData || {};

  const [colorMap1] = useState(['#6CADFF', '#3990FF', '#146EE2', '#004CAA']);
  // ['#A7CEFF', '#6CADFF', '#3990FF', '#146EE2', '#004CAA']
  const [colorMap2] = useState(['#00CBBB', '#A2E2E8']);

  const formatOther = val => {
    if (val > 0) {
      return `+${val}`;
    } if (val < 0) {
      return `-${val * -1}`;
    } 
    return '';
    
  };

  const formatItemWidth = (children = []) => {
    let _itemLength = 0;
    children.forEach(item => {
      if (item && item.value > 0) {
        _itemLength += item.value.toString().length;
        _itemLength++;
      }
    });
    return _itemLength;
  };

  return (
    <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
      <Panel header={intl.get(`${modelPrompt}.achieveInfo`).d('实绩信息')} key="basicInfo">
        <Row>
          <Col span={4} className={styles['list-label']}>
            <div>
              <Popover
                className="list-data-pop"
                content={intl.get(`${modelPrompt}.eoQty`).d('执行作业数量')}
              >
                {intl.get(`${modelPrompt}.eoQty`).d('执行作业数量')}
              </Popover>
            </div>
          </Col>
          <Col span={18}>
            <div className={styles['pro-list-row']}>
              {list1.map((item, index) => {
                if (!item.value || item.value === 0) {
                  return null;
                } if (item.name === 'stepWipQty' && item.children) {
                  return (
                    <div
                      className={styles['pro-list-row-sub']}
                      style={{
                        backgroundColor: `${colorMap1[index]}`,
                        width: `${(item.value / max) * 100}%`,
                        minWidth: `${formatItemWidth(item.children) * 10}px`,
                        flexGrow: (item.value / max) * 100 > 10 ? '1' : 0,
                      }}
                    >
                      <Tooltip
                        placement="top"
                        title={item.children.map(subItem => {
                          if (subItem.value > 0) {
                            return <div>{`${subItem.title}: ${subItem.value}`}</div>;
                          } 
                          return null;
                          
                        })}
                      >
                        {item.children.map(subItem => {
                          if (subItem.value > 0) {
                            return (
                              <div
                                style={{
                                  width: `${(subItem.value / item.max) * 100}%`,
                                  minWidth: `${formatItemWidth([subItem]) * 10}px`,
                                }}
                              >
                                {subItem.value}
                              </div>
                            );
                          } 
                          return null;
                          
                        })}
                      </Tooltip>
                    </div>
                  );
                } 
                return (
                  <div
                    style={{
                      backgroundColor: `${colorMap1[index]}`,
                      width: `${(item.value / max) * 100}%`,
                      minWidth: `${formatItemWidth([item]) * 10}px`,
                      flexGrow: (item.value / max) * 100 > 10 ? '1' : 0,
                    }}
                  >
                    <Tooltip placement="top" title={<div>{`${item.title}: ${item.value}`}</div>}>
                      {item.value}
                    </Tooltip>
                  </div>
                );
                
              })}
            </div>
          </Col>
          <Col span={2} className={styles['list-max']}>
            {max}
            {formatOther(other)}
          </Col>
        </Row>
        <Row style={{ overflow: 'hidden' }}>
          <Col span={4} className={styles['list-label']}>
            <div>
              <Popover
                className="list-data-pop"
                type="dark"
                content={intl.get(`${modelPrompt}.completeProgress`).d('完成进度')}
              >
                {intl.get(`${modelPrompt}.completeProgress`).d('完成进度')}
              </Popover>
            </div>
          </Col>
          <Col span={18}>
            <div className={styles['pro-list-row']}>
              {list2.map((item, index) => {
                if (!item.value || item.value === 0) {
                  return null;
                } 
                return (
                  <div
                    className={
                      index > 0 && list2[index - 1].value < list2[index].value
                        ? styles['pro-list-col-ab']
                        : `${styles['pro-list-col-ab']} ${styles['pro-list-col-ab-short']}`
                    }
                    style={{
                      backgroundColor: `${colorMap2[index]}`,
                      width: `${(item.value / max) * 100}%`,
                      minWidth: `${formatItemWidth([item]) * 10}px`,
                      zIndex: `${list2.length - index}`,
                      textIndent:
                          index > 0 && list2[index - 1].value < list2[index].value
                            ? `${(list2[index - 1].value / list2[index].value) * 100}%`
                            : `0`,
                      height: `${16 + index * 6}px`,
                      lineHeight: `${16 + index * 6}px`,
                    }}
                  >
                    <Tooltip placement="top" title={<div>{`${item.title}: ${item.value}`}</div>}>
                      {item.value}
                    </Tooltip>
                  </div>
                );
                
              })}
            </div>
          </Col>
          <Col span={2} className={styles['list-max']}>
            {max}
          </Col>
        </Row>
      </Panel>
    </Collapse>
  );
};

export default ProductionOrderMgtTotalList;
