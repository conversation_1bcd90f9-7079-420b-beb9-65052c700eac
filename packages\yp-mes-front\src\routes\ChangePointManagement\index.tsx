import React, { useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Spin, TextField, DateTimePicker } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import { PostMethod } from './services';
import { tableDS } from './stores';

const modelPrompt = 'tarzan.hmes.changePointManagement';

const ChangePointManagement = observer(props => {
  const {
    // match: { path },
    tableDs,
    // history,
  } = props;
  const [disabledFlag, setDisabledFlag] = useState(false);
  // const [createFlag, setCreateFlag] = useState(true);
  // const [loading, setLoading] = useState(false);

  const { run: postMethod, loading: postLoading } = useRequest(PostMethod(), {
    manual: true,
    // needPromise: true,
  }); // 手动查询，需要promise

  const columns: ColumnProps[] = useMemo(
    () => [
      // 记录编号
      {
        name: 'changePointRecordCode',
        align: ColumnAlign.center,
      },
      {
        name: 'type',
        align: ColumnAlign.center,
        editor: record => {
          return record.getState('editing')
        },
      },
      {
        name: 'operationLov',
        align: ColumnAlign.center,

        editor: record => {
          return record.getState('editing')
        },
      },
      // 人
      {
        name: 'man',
        align: ColumnAlign.center,
        editor: record => {
          return (
            record.getState('editing') && <TextField dataSet={tableDs} name="man" maxLength={255} />
          );
        },
      },
      // 机
      {
        name: 'machine',
        align: ColumnAlign.center,
        editor: record => {
          return (
            record.getState('editing') && (
              <TextField dataSet={tableDs} name="machine" maxLength={255} />
            )
          );
        },
      },
      // 料
      {
        name: 'material',
        align: ColumnAlign.center,
        editor: record => {
          return (
            record.getState('editing') && (
              <TextField dataSet={tableDs} name="material" maxLength={255} />
            )
          );
        },
      },
      // 法
      {
        name: 'method',
        align: ColumnAlign.center,
        editor: record => {
          return (
            record.getState('editing') && (
              <TextField dataSet={tableDs} name="method" maxLength={255} />
            )
          );
        },
      },
      // 环
      {
        name: 'environment',
        align: ColumnAlign.center,
        editor: record => {
          return (
            record.getState('editing') && (
              <TextField dataSet={tableDs} name="environment" maxLength={255} />
            )
          );
        },
      },
      // 变化时点
      {
        name: 'changeDate',
        align: ColumnAlign.center,
        editor: record => {
          return (
            record.getState('editing') && (
              <DateTimePicker dataSet={tableDs} name="changeDate" required />
            )
          );
        },
      },
      {
        name: 'reason',
        align: ColumnAlign.center,
        editor: record => {
          return record.getState('editing')
        },
      },
      {
        name: 'solution',
        align: ColumnAlign.center,
        editor: record => {
          return record.getState('editing')
        },
      },
      // 记录人
      {
        name: 'createdRealName',
        width: 120,
        align: ColumnAlign.center,
      },
      // 记录时间
      {
        name: 'creationDate',
        width: 120,
        align: ColumnAlign.center,
      },
      // 更新人
      {
        name: 'lastUpdatedRealName',
        width: 120,
        align: ColumnAlign.center,
      },
      // 更新时间
      {
        name: 'lastUpdateDate',
        width: 120,
        align: ColumnAlign.center,
      },
    ],
    [],
  );
  // 编辑按钮
  const handelEdit = () => {
    tableDs.records.forEach(item => {
      item.setState('editing', true);
    });
    setDisabledFlag(true);
    // setCreateFlag(false);
  };

  // 取消按钮
  const handelCancel = () => {
    tableDs.records.forEach(item => {
      item.setState('editing', false);
    });
    setDisabledFlag(false);
    // setCreateFlag(true);
    tableDs.query();
  };

  const handleCreate = () => {
    tableDs.create({}, 0);
    tableDs.current.setState('editing', true);
    setDisabledFlag(true);
  };

  const handleSave = async () => {
    const validateFlag = await tableDs.validate();
    if (!validateFlag) {
      return false;
    }
    const params = tableDs.toJSONData();
    postMethod({
      params,
      onSuccess: () => {
        notification.success({});
        // setCreateFlag(true);
        setDisabledFlag(false);
        tableDs.query();
      },
    });
  };

  return (
    <div className="hmes-style">
      <Spin spinning={postLoading}>
        <Header title={intl.get(`${modelPrompt}.title`).d('变化点管理')}>
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={handleCreate}
            permissionList={[
              {
                code: `path.button.add`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
          {!disabledFlag && (
            <>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="edit"
                disabled={tableDs.records.length === 0}
                onClick={handelEdit}
                permissionList={[
                  {
                    code: `path.button.add`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </PermissionButton>
            </>
          )}
          {disabledFlag && (
            <>
              <PermissionButton
                type="c7n-pro"
                onClick={handelCancel}
                permissionList={[
                  {
                    code: `path.button.cancel`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </PermissionButton>
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
                permissionList={[
                  {
                    code: `path.button.save`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </PermissionButton>
            </>
          )}
        </Header>
        <Content>
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="ChangePointManagement"
            customizedCode="ChangePointManagement"
          />
        </Content>
      </Spin>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ChangePointManagement),
);
