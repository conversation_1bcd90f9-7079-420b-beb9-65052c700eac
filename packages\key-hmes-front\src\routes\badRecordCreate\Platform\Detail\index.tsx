import React, { useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  Modal,
  TextArea,
  Select,
  Table,
  TextField,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm } from 'choerodon-ui';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import request from 'utils/request';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { BASIC } from '@utils/config';
import { TarzanSpin, C7nFormItemSort } from '@components/tarzan-ui';
import { getCurrentOrganizationId, getCurrentRole } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { getActiveTabKey } from 'utils/menuTab';
import { observer } from 'mobx-react';
import scanImg from '@/assets/icons/scan-o.svg';
import axios from 'axios';
import uuid from 'uuid/v4';
import { useDataSet } from 'utils/hooks';
// import openNcDetailDrawer from './NcDetailDrawer';
import createDrawer from './createDrawer';
import { detailDS, detailLineDS, detailObjectDS } from '../stores/detailDS';
// import { formDS } from '../stores/detailCreateDS';
import InputLovDS from '../stores/InputLovDS';
import openBatchBarCodeModal from './BatchBarCodeModal';
import { fetchDefaultSite } from '../../../../services/api';
import {
  FetchEoRelatedInfo,
  ScanEoRelatedInfo,
  FetchMaterialLotRelatedInfo,
  ScanMaterialLotRelatedInfo,
  FetchUserSiteList,
  SaveNcRecord,
  FetchDetailLine,
  FetchOperation,
} from '../services';
import RecordFactoryDs from '../stores/recordModalDs';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.mes.event.badRecordPlatformCreate';

const BadRecordDetail = observer(props => {
  const {
    history,
    match: { params },
  } = props;
  const { id } = params as any;

  // 不良对象类型
  const [ncRecordType, setNcRecordType] = useState<string>('');
  // 是否能编辑
  const [canEdit, setCanEdit] = useState<boolean>(id === 'create');
  // 折叠activeKey
  const [activeKey, setActiveKey] = useState<any>([
    'basicInfo',
    'badRecordDetail',
    'badRecordInfoObject',
  ]);
  const [showFlagLov, setShowFlagLov] = useState<boolean>(true);
  // 多输入框
  const inputLovDS = new DataSet({ ...InputLovDS() });

  // 不良记录明细Ds
  const detailLineDs = useMemo(() => new DataSet(detailLineDS()), []);
  // 抽屉
  const formDs = useDataSet(RecordFactoryDs, 'RecordFormDs');

  // 详情界面Ds
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );

  const detailObjectDs = useMemo(
    () =>
      new DataSet({
        ...detailObjectDS(),
      }),
    [],
  );
  const { run: saveNcRecord, loading: saveLoading } = useRequest(SaveNcRecord(), {
    manual: true,
  });
  // 获取用户有权限的站点
  const { run: fetchUserSiteList, loading: fetchUserSiteLoading } = useRequest(
    FetchUserSiteList(),
    {
      manual: true,
    },
  );
  // 获取物料批关联信息
  const { run: fetchMaterialLotRelatedInfo, loading: fetchMaterialLotLoading } = useRequest(
    FetchMaterialLotRelatedInfo(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: fetchDetailLine, loading: fetchDetailLineLoading } = useRequest(FetchDetailLine(), {
    manual: true,
    needPromise: true,
  });

  const { run: scanMaterialLotRelatedInfo, loading: scanMaterialLotLoading } = useRequest(
    ScanMaterialLotRelatedInfo(),
    {
      manual: true,
    },
  );

  // 获取EO关联信息
  const { run: fetchEoRelatedInfo, loading: fetchEoLoading } = useRequest(FetchEoRelatedInfo(), {
    manual: true,
    needPromise: true,
  });
  const { run: scanEoRelatedInfo, loading: scanEoLoading } = useRequest(ScanEoRelatedInfo(), {
    manual: true,
  });
  const { run: fetchOperation, loading: fetchOperationLoading } = useRequest(FetchOperation(), {
    manual: true,
    needPromise: true,
  });

  // const handleModalQuery = workcellId => {
  //   fetchOperation({
  //     params: {
  //       workcellId,
  //     },
  //   }).then(res => {
  //     formDs.current?.set('rootCauseEquipmentCode', res.rows?.equipmentCode);
  //     formDs.current?.set('rootCauseEquipmentId', res.rows?.equipmentId);
  //     formDs.current?.set('rootCauseOperationId', res.rows?.operationId);
  //     formDs.current?.set('rootCauseOperationCode', res.rows?.operationName);
  //   });
  // };

  const handleQueryDetailLine = ncRecordId => {
    fetchDetailLine({
      params: {
        ncRecordId,
      },
    }).then(res => {
      const temp = res.rows.content || [];
      console.log('temp', temp);

      detailObjectDs.current?.set('queryList', temp);
      detailLineDs.loadData(
        temp.concat(detailObjectDs.current?.toData()?.ncRecordDetailList || []),
      );
    });
  };

  useEffect(() => {
    const currentRole = getCurrentRole();
    request(`/hpfm/v1/${tenantId}/lovs/value/batch`, {
      method: 'GET',
      query: {
        lookupCode: 'HME.ROLE_CONTROL_NC',
      },
    }).then(res => {
      if (res.lookupCode?.length === 0) {
        setShowFlagLov(false);
      } else {
        const filterData = res.lookupCode.filter(item => item?.value === currentRole?.code);
        if (filterData.length) {
          setShowFlagLov(true);
        } else {
          setShowFlagLov(false);
        }
      }
    });
  }, []);

  useEffect(() => {
    if (id === 'create') {
      fetchUserSiteList({
        onSuccess: res => {
          // 若用户有权限的站点仅一个，则自动带出
          if (res?.length === 1) {
            detailDs.current?.set('siteId', res[0].siteId);
            detailDs.current?.set('siteName', res[0].siteName);
          }
        },
      });
      return;
    }
    handleQueryDetail(id);
    setCanEdit(false);
  }, [id]);
  useEffect(() => {
    fetchDefaultSite().then(res => {
      if (res && res.success) {
        detailDs.current?.set('siteLov', res.rows);
      }
    });
  }, []);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('ncRecordId', id);
    detailDs.query().then(res => {
      const { ncRecordType, ncRecordList } = res || {};
      setNcRecordType(ncRecordType);
      detailObjectDs.loadData(ncRecordList || []);
      if (ncRecordList && ncRecordList.length) {
        handleQueryDetailLine(ncRecordList[0]?.ncRecordId);
      }
    });
  };

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    const ncRecordList: any = detailObjectDs.toData();
    if (detailObjectDs.toData().length === 0) {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.info.noBadRecordObject`)
          .d('请至少选择一条不良对象创建不良记录！'),
      });
      return false;
    }
    if (
      ncRecordList.some(
        (item: any) => !item?.ncRecordDetailList || item?.ncRecordDetailList.length === 0,
      )
    ) {
      const findIndex: number = ncRecordList.findIndex(
        (item: any) => !item?.ncRecordDetailList || item?.ncRecordDetailList.length === 0,
      );
      notification.warning({
        message: intl
          .get(`${modelPrompt}.info.noBadRecordDetail`)
          .d(
            `${ncRecordList[findIndex]?.materialLotCode ||
              ncRecordList[findIndex]?.eoNum}不良记录对象没有不良记录明细`,
          ),
      });
      return false;
    }
    // 处理保存数据
    const data = detailDs.current?.toData();
    // 处理shiftDate格式
    data.shiftDate = data.shiftDate?.split(' ')[0] || undefined;
    const params = {
      ...data,
      ncRecordList: detailObjectDs.toData(),
    };
    saveNcRecord({
      params,
      onSuccess: res => {
        notification.success({});
        // history.push(`/hmes/bad-record/platform-new/list`);
        Modal.open({
          title: intl.get(`tarzan.common.title.tips`).d('提示'),
          children: (
            <div>
              <p style={{ textAlign: 'center', fontSize: '22px' }}>是否打印标签？</p>
            </div>
          ),
          onOk: () => {
            let list;
            if (ncRecordType === 'EO_ALL_NC') {
              list = detailObjectDs.toData().map((item: any) => item?.identification);
            } else {
              list = detailObjectDs.toData().map((item: any) => item?.materialLotCode);
            }
            props.history.push({
              pathname: `/hmes/bad-record/platform-new/print/${res[0]}`,
              state: {
                list,
              },
            });
          },
          onCancel: () => {
            history.push(`/hmes/bad-record/platform-new/detail/${res[0]}`);
          },
        });
      },
    });
  };

  const handleChangeNcRecordType = (value, oldVal) => {
    setNcRecordType(value);
    if (oldVal && detailObjectDs?.toData().length) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.clearData`)
              .d('不良记录对象/不良记录明细会清空，确定更换不良记录类型？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          setNcRecordType(value);
          detailObjectDs.loadData([]);
          detailLineDs.loadData([]);
          if (detailDs.current?.get('eoId')) {
            detailDs.current?.set('eoLov', undefined);
          } else if (detailDs.current?.get('materialLotId')) {
            detailDs.current?.set('materialLotLov', undefined);
          }
        } else {
          setNcRecordType(oldVal);
          detailDs.current?.set('ncRecordType', oldVal);
        }
      });
    }
  };

  const handleChangeSite = () => {
    detailDs.current?.init('workcellLov', undefined);
  };

  const handleChangeMaterialLot = value => {
    if (!value) {
      return;
    }
    fetchMaterialLotRelatedInfo({
      params: detailDs?.current?.get('materialLotId'),
    }).then(res => {
      if (res && res.success) {
        // 清空数据
        detailDs.current?.init('materialLotLov', undefined);
        const temp = detailObjectDs.toData();
        const list = detailObjectDs
          .toData()
          .concat(
            uniqueArray(temp.concat(res.rows || []), detailObjectDs.toData(), 'materialLotCode'),
          );
        const sortList = list.sort((a: any, b: any) => a.operationId - b.operationId);
        detailObjectDs.loadData(sortList);
        detailObjectDs.current = detailObjectDs.get(0);
      }
    });
  };

  const handleChangeEo = value => {
    if (!value) {
      return;
    }
    fetchEoRelatedInfo({
      params: detailDs?.current?.get('eoId'),
    }).then(res => {
      if (res && res.success) {
        detailDs.current?.init('eoLov', undefined);
        const temp = detailObjectDs.toData();
        const list = detailObjectDs
          .toData()
          .concat(
            uniqueArray(temp.concat(res.rows || []), detailObjectDs.toData(), 'identification'),
          );
        const sortList = list.sort((a: any, b: any) => a.operationId - b.operationId);
        detailObjectDs.loadData(sortList);
      }
    });
  };

  const deleteLineObject = () => {
    detailObjectDs.remove(detailObjectDs.selected);
    detailLineDs.loadData(
      (detailObjectDs.current?.toData().ncRecordDetailList || []).concat(
        detailObjectDs.current?.toData()?.queryList || [],
      ),
    );
  };

  const cancelEdit = () => {
    if (id === 'create') {
      history.push('/hmes/bad-record/platform-new/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(id);
    }
  };

  const createBadRecord = () => {
    const { ncRecordType, ncRecordTypeDesc, siteLov } = detailDs.current?.toData();
    history.push({
      pathname: `/hmes/bad-record/platform/detail/create`,
      state: {
        ncRecordType,
        ncRecordTypeDesc,
        siteLov,
        detailLineDsList: detailLineDs.toData(),
        detailObjectDsList: detailObjectDs.toData(),
        sourceKey: getActiveTabKey(),
        isFromListPage: true,
      },
    });
  };

  const detailColumn: any[] = useMemo(
    () => [
      {
        name: 'lineNumber',
        align: ColumnAlign.left,
        width: 80,
        lock: ColumnLock.left,
        renderer: ({ record }) => record.index + 1,
      },
      {
        name: 'ncCodeLov',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'ncCodeStatusDesc',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'rootCauseWorkcellName',
        width: 150,
      },
      {
        name: 'rootCauseEquipmentCode',
        width: 150,
      },
      {
        name: 'rootCauseOperationCode',
        width: 150,
      },
      {
        name: 'responsibleUserLov',
      },
      {
        name: 'responsibleApartment',
        width: 130,
      },
      {
        name: 'remark',
      },
      {
        name: 'enclosure',
        width: 120,
        editor: canEdit,
        align: 'center',
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        lock: 'right',
        align: 'center',
        hidden: id !== 'create',
        width: 120,
        renderer: ({ record }) => (
          <Button
            color={ButtonColor.primary}
            disabled={!canEdit}
            icon="edit-o"
            funcType={FuncType.flat}
            onClick={() => {
              formDs?.getField('ncCodeLov')?.set('multiple', false);
              formDs?.getField('ncCodeLov')?.set('required', true);
              formDs.loadData([record.data]);
              createDrawer({
                formDs,
                handleSubmit,
                selectionType: 'edit',
                record,
              });
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </Button>
        ),
      },
    ],
    [ncRecordType, canEdit],
  );

  const detailObjectColumn: any[] = useMemo(
    () => [
      ncRecordType === 'EO_ALL_NC' && {
        name: 'identification',
        lock: ColumnLock.left,
      },
      ncRecordType === 'RM_NC' && {
        name: 'materialLotCode',
        width: 200,
        lock: ColumnLock.left,
      },
      {
        name: 'materialCode',
        lock: ColumnLock.left,
      },
      {
        name: 'revisionCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'qty',
      },
      {
        name: 'uomName',
      },
      {
        name: 'locatorName',
      },
      {
        name: 'containerCode',
      },
      {
        name: 'supplierName',
      },
      ncRecordType === 'EO_ALL_NC' && {
        name: 'eoNum',
        width: 200,
      },
      {
        name: 'prodLineName',
        width: 150,
      },
      {
        name: 'routerName',
        width: 150,
      },
      {
        name: 'operationName',
        width: 150,
      },
      {
        name: 'equipmentCode',
        width: 150,
      },
      {
        name: 'workcellName',
        width: 150,
      },
      {
        name: 'workOrderNum',
        width: 120,
      },
      {
        name: 'workOrderQty',
        width: 120,
      },
    ],
    [ncRecordType, canEdit],
  );

  const buttons = [
    <PermissionButton
      type="c7n-pro"
      icon="add"
      disabled={!detailObjectDs.current}
      onClick={() => {
        formDs?.getField('ncCodeLov')?.set('multiple', true);
        formDs?.getField('ncCodeLov')?.set('required', true);
        const { operationId, operationName } = detailObjectDs.current?.toData();
        formDs.loadData([
          {
            ncCodeStatus: 'NEW',
            rootCauseOperationId: operationId,
            rootCauseOperationCode: operationName,
          },
        ]);
        createDrawer({ formDs, handleSubmit, selectionType: 'single' });
      }}
      funcType="flat"
      shape="circle"
      size="small"
    >
      {intl.get('hzero.common.button.add').d('新增')}
    </PermissionButton>,
    <Popconfirm
      title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
      onConfirm={() => handleDeleteLine()}
      okText={intl.get('tarzan.common.button.confirm').d('确认')}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
    >
      <PermissionButton
        type="c7n-pro"
        icon="delete_black-o"
        disabled={detailLineDs.selected?.length < 1}
        funcType="flat"
        shape="circle"
        size="small"
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
    </Popconfirm>,
  ];

  const handleDeleteLine = () => {
    detailLineDs.remove(detailLineDs.selected);
    detailObjectDs?.current?.set('ncRecordDetailList', detailLineDs.toData());
  };

  const handleCreateDetail = type => {
    if (type === 'equipment') {
      formDs?.getField('ncCodeLov')?.set('required', false);
      createDrawer({
        formDs,
        handleSubmit,
        selectionType: 'equipment',
        btnType: type,
      });
    } else {
      formDs?.getField('ncCodeLov')?.set('required', true);
      createDrawer({
        formDs,
        handleSubmit,
        selectionType: 'section',
        btnType: type,
      });
    }
    formDs?.getField('ncCodeLov')?.set('multiple', true);
    const { operationId, operationName } = detailObjectDs.current?.toData();

    formDs.loadData([
      {
        rootCauseOperationId: operationId,
        rootCauseOperationCode: operationName,
      },
    ]);
  };

  // 更新工艺/工作单元/设备
  const updateOption = (data, record) => {
    const {
      rootCauseWorkcellId,
      rootCauseWorkcellName,
      rootCauseEquipmentId,
      rootCauseEquipmentCode,
      rootCauseOperationId,
      rootCauseOperationCode,
    } = data;
    record.set('workcellId', rootCauseWorkcellId);
    record.set('workcellName', rootCauseWorkcellName);
    record.set('equipmentId', rootCauseEquipmentId);
    record.set('equipmentCode', rootCauseEquipmentCode);
    record.set('operationId', rootCauseOperationId);
    record.set('operationName', rootCauseOperationCode);
  };

  const handleSubmit = async (data, selectionType, btnType) => {
    // 单个新增
    if (selectionType === 'single') {
      const temp = detailObjectDs.current?.toData()?.ncRecordDetailList || [];
      detailObjectDs.current?.set(
        'ncRecordDetailList',
        temp.concat(uniqueArray(data, temp, 'ncCodeId')),
      );
      if (ncRecordType === 'EO_ALL_NC') {
        const currentDetailList = detailObjectDs.current?.toData()?.ncRecordDetailList || [];
        const isFirstRecord = currentDetailList.findIndex(item => item.uuid === data[0].uuid) === 0;

        if (isFirstRecord) {
          const { rootCauseOperationId, rootCauseOperationCode } = data[0];
          detailObjectDs.current?.set('operationId', rootCauseOperationId);
          detailObjectDs.current?.set('operationName', rootCauseOperationCode);
          // 更新头上的工艺
          detailDs.current?.set('operationId', rootCauseOperationId);
          detailDs.current?.set('operationName', rootCauseOperationCode);
        }
      }
      detailLineDs.loadData(
        (detailObjectDs.current?.toData().ncRecordDetailList || []).concat(
          detailObjectDs.current?.toData()?.queryList || [],
        ),
      );
    } else if (selectionType === 'edit') {
      // 编辑
      const temp = detailObjectDs.current?.toData()?.ncRecordDetailList;
      const list = temp.map(item => {
        if (item.uuid === data[0].uuid) {
          return data[0];
        }
        return item;
      });
      detailObjectDs.current?.set('ncRecordDetailList', list);
      if (ncRecordType === 'EO_ALL_NC') {
        const currentDetailList = detailObjectDs.current?.toData()?.ncRecordDetailList || [];
        const isFirstRecord = currentDetailList.findIndex(item => item.uuid === data[0].uuid) === 0;

        if (isFirstRecord) {
          const { rootCauseOperationId, rootCauseOperationCode } = data[0];
          detailObjectDs.current?.set('operationId', rootCauseOperationId);
          detailObjectDs.current?.set('operationName', rootCauseOperationCode);
          // 更新头上的工艺
          detailDs.current?.set('operationId', rootCauseOperationId);
          detailDs.current?.set('operationName', rootCauseOperationCode);
        }
      }
      detailLineDs.loadData(
        (detailObjectDs.current?.toData().ncRecordDetailList || []).concat(
          detailObjectDs.current?.toData()?.queryList || [],
        ),
      );
    } else if (btnType === 'update') {
      // 批量更新
      detailObjectDs.selected.forEach(item => {
        item.set('ncRecordDetailList', data);
        updateOption(data[0], item);
      });
      detailLineDs.loadData(
        (detailObjectDs.current?.toData().ncRecordDetailList || []).concat(
          detailObjectDs.current?.toData()?.queryList || [],
        ),
      );
    } else if (btnType === 'equipment') {
      const {
        rootCauseWorkcellId,
        rootCauseWorkcellName,
        rootCauseEquipmentId,
        rootCauseEquipmentCode,
        rootCauseOperationId,
        rootCauseOperationCode,
      } = data[0];
      // 批量更新
      detailObjectDs.selected.forEach(item => {
        const temp = item.get('ncRecordDetailList') || [];
        const list = temp.map(item => {
          return {
            ...item,
            rootCauseWorkcellId,
            rootCauseWorkcellName,
            rootCauseEquipmentId,
            rootCauseEquipmentCode,
            rootCauseOperationId,
            rootCauseOperationCode,
            uuid: uuid(),
          };
        });
        updateOption(data[0], item);
        item.set('ncRecordDetailList', list);
      });
      detailLineDs.loadData(
        (detailObjectDs.current?.toData().ncRecordDetailList || []).concat(
          detailObjectDs.current?.toData()?.queryList || [],
        ),
      );
    } else {
      // 头新增
      const ids = detailObjectDs.selected.map(item => {
        if (ncRecordType === 'EO_ALL_NC') {
          return item.get('eoId');
        }
        if (ncRecordType === 'RM_NC') {
          return item.get('materialLotId');
        }
      });
      const {
        rootCauseWorkcellId,
        rootCauseEquipmentId,
        rootCauseOperationId,
        rootCauseOperationCode,
      } = data[0];
      // 检查是否是在制品类型且是第一条数据

      if (ncRecordType === 'EO_ALL_NC') {
        // 只在第一条不良明细时同步工艺到不良记录信息
        const currentDetailList = detailObjectDs.current?.toData()?.ncRecordDetailList || [];

        // 只有当是第一条数据时才更新工艺信息
        if (currentDetailList.length === 0) {
          detailObjectDs.current?.set('operationId', rootCauseOperationId);
          detailObjectDs.current?.set('operationName', rootCauseOperationCode);
          // 更新头上的工艺
          detailDs.current?.set('operationId', rootCauseOperationId);
          detailDs.current?.set('operationName', rootCauseOperationCode);
        }
      }
      const params = {
        ids,
        ncRecordType,
        operationId: rootCauseOperationId,
        workcellId: rootCauseWorkcellId,
        equipmentId: rootCauseEquipmentId,
      };
      // 批量新增
      const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-nc-record/detailed/query`;
      const res: any = await axios.post(url, params);
      if (res && res.success) {
        // 如果匹配到默认走这个逻辑，否则走原逻辑
        if (res.rows.length > 0) {
          const dataMap = new Map();
          res.rows.map(item => {
            dataMap.set(item.id, item.detailedList);
          });
          detailObjectDs.selected.forEach(item => {
            const temp = item.toData()?.ncRecordDetailList || [];
            /**
             * 取数据的优先级
             * 1.弹框中有了工艺并且有了工作单元，就可以弹框上的数据为准，更新头信息以行信息
             * 2.弹框中只有工艺，首先以接口查询出的默认值为准
             * 3.以上都没有（没有接口的数据，也没有工艺及单元），以弹框为准以弹框上的数据为准，更新头信息以行信息
             */
            // 如果匹配到的detailedList是有值的,那就遍历默认值，否则取页面上显示的数据
            if (ncRecordType === 'EO_ALL_NC') {
              if (
                !data[0].rootCauseWorkcellId &&
                dataMap.get(item.get('eoId')) &&
                dataMap.get(item.get('eoId')).length > 0
              ) {
                // 代表弹框中没有工艺并且没有工作单元，以接口返回的数据为准
                const array: Array<any> = [];
                data.forEach(prev => {
                  dataMap.get(item.get('eoId')).forEach(next => {
                    array.push({ ...next, ...prev, uuid: uuid() });
                  });
                });
                // 只在第一条记录时更新工艺等信息
                if (temp.length === 0) {
                  updateOption(dataMap.get(item.get('eoId'))[0], item);
                }
                item.set('ncRecordDetailList', temp.concat(array));
              } else {
                // 只在第一条记录时更新工艺等信息
                if (temp.length === 0) {
                  updateOption(data[0], item);
                }
                item.set('ncRecordDetailList', temp.concat(uniqueArray(data, temp, 'ncCodeId')));
              }
            }
            // 不同类型的取值，逻辑同上
            if (ncRecordType === 'RM_NC') {
              if (
                !data[0].rootCauseWorkcellId &&
                dataMap.get(item.get('materialLotId')) &&
                dataMap.get(item.get('materialLotId')).length > 0
              ) {
                const array: Array<any> = [];
                data.forEach(prev => {
                  dataMap.get(item.get('materialLotId')).forEach(next => {
                    array.push({ ...next, ...prev, uuid: uuid() });
                  });
                });
                updateOption(dataMap.get(item.get('materialLotId'))[0], item);
                item.set('ncRecordDetailList', temp.concat(array));
              } else {
                const temp = item.toData()?.ncRecordDetailList || [];
                updateOption(data[0], item);
                item.set('ncRecordDetailList', temp.concat(uniqueArray(data, temp, 'ncCodeId')));
              }
            }
          });
          detailLineDs.loadData(
            (detailObjectDs.current?.toData().ncRecordDetailList || []).concat(
              detailObjectDs.current?.toData()?.queryList || [],
            ),
          );
        } else {
          // 批量新增
          detailObjectDs.selected.forEach(item => {
            const temp = item.toData()?.ncRecordDetailList || [];
            item.set('ncRecordDetailList', temp.concat(uniqueArray(data, temp, 'ncCodeId')));
          });
          detailLineDs.loadData(
            (detailObjectDs.current?.toData().ncRecordDetailList || []).concat(
              detailObjectDs.current?.toData()?.queryList || [],
            ),
          );
        }
      } else {
        notification.error({
          message: res.message,
        });
      }
    }
  };

  const buttonsObject = [
    <Button
      icon="update"
      disabled={detailObjectDs.selected?.length === 0}
      onClick={() => handleCreateDetail('equipment')}
    >
      {intl.get(`${modelPrompt}.title.button.detailLine.new`).d('更新不良设备')}
    </Button>,
    <Button
      icon="update"
      disabled={detailObjectDs.selected?.length === 0 || id !== 'create'}
      onClick={() => handleCreateDetail('update')}
    >
      {intl.get(`${modelPrompt}.title.button.detailLine.update`).d('更新不良明细')}
    </Button>,
    <Button
      icon="add"
      disabled={detailObjectDs.selected?.length === 0 || id !== 'create'}
      onClick={handleCreateDetail}
    >
      {intl.get(`${modelPrompt}.title.button.detailLine.new`).d('新增不良明细')}
    </Button>,
    <Popconfirm
      title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
      onConfirm={() => deleteLineObject()}
      okText={intl.get('tarzan.common.button.confirm').d('确认')}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
    >
      <PermissionButton
        type="c7n-pro"
        icon="delete_black-o"
        // disabled={!canEdit || detailObjectDs?.selected.length === 0 || ncRecordStatus !== 'NEW'}
        disabled={!canEdit || detailObjectDs?.selected.length === 0}
        funcType="flat"
        shape="circle"
        size="small"
      >
        {intl.get('tarzan.common.button.delete').d('删除')}
      </PermissionButton>
    </Popconfirm>,
  ];

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    openBatchBarCodeModal({
      inputLovDS,
      inputLovFlag,
      inputLovTitle,
      inputLovVisible,
      targetDS: detailDs,
      submit: handleScan,
    });
    inputLovDS.queryDataSet?.current?.set('code', '');
    inputLovDS.data = [];
    inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
  };

  const handleScan = inputLovFlag => {
    if (inputLovFlag === 'materialLots' || inputLovFlag === 'materialLotList') {
      scanMaterialLot(inputLovFlag);
    } else if (inputLovFlag === 'eos') {
      scanEo();
    }
  };

  const scanMaterialLot = inputLovFlag => {
    scanMaterialLotRelatedInfo({
      params: {
        materialLots:
          inputLovFlag === 'materialLots'
            ? detailDs.current?.get('materialLots')
            : detailDs.current?.get('materialLotList'),
        siteId: detailDs.current?.get('siteId'),
        equipmentCode: detailDs.current?.get('equipmentCode'),
        operationId: detailDs.current?.get('operationId'),
      },
      onSuccess: res => {
        if (res) {
          detailDs.current?.init('materialLots', null);
          detailDs.current?.init('materialLotList', null);
          const list = detailObjectDs
            .toData()
            .concat(uniqueArray(res, detailObjectDs.toData(), 'materialLotCode'));
          const sortList = list.sort((a: any, b: any) => a.operationId - b.operationId);
          detailObjectDs.loadData(sortList);
          detailObjectDs.current = detailObjectDs.get(0);
        }
      },
    });
  };

  // 去重
  const uniqueArray = (res, tableList, field) => {
    // tableList为原值 res为新增数据 field为去重字段
    const temp: any = [];
    res.forEach(i => {
      if (tableList.every((j: any) => j[field] !== i[field])) {
        temp.push(i);
      }
    });
    return temp;
  };

  const scanEo = () => {
    scanEoRelatedInfo({
      params: {
        identifications: detailDs.current?.get('eos'),
        siteId: detailDs.current?.get('siteId'),
      },
      onSuccess: res => {
        if (res) {
          detailDs.current?.init('eos', null);
          const list = detailObjectDs
            .toData()
            .concat(uniqueArray(res, detailObjectDs.toData(), 'identification'));
          const sortList = list.sort((a: any, b: any) => a.operationId - b.operationId);
          detailObjectDs.loadData(sortList);
        }
      },
    });
  };

  const handleClick = record => {
    if (record.data?.ncRecordId) {
      handleQueryDetailLine(record.data?.ncRecordId);
    } else {
      detailLineDs.loadData(
        (detailObjectDs?.current?.toData()?.ncRecordDetailList || []).concat(
          detailObjectDs?.current?.toData()?.queryList || [],
        ),
      );
    }
  };

  return (
    <div style={{ overflow: 'auto', height: '100%' }}>
      <TarzanSpin
        dataSet={detailDs}
        spinning={
          saveLoading ||
          fetchUserSiteLoading ||
          fetchMaterialLotLoading ||
          fetchEoLoading ||
          scanMaterialLotLoading ||
          scanEoLoading ||
          fetchDetailLineLoading ||
          fetchOperationLoading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('不良记录创建')}
          backPath="/hmes/bad-record/platform-new/list"
        >
          {canEdit ? (
            <>
              <Button
                color={ButtonColor.primary}
                icon="save"
                loading={saveLoading}
                onClick={() => handleSave()}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button color={ButtonColor.default} onClick={cancelEdit}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <>
              <Button
                disabled={detailDs.current?.get('ncRecordStatus') !== 'NEW'}
                color={ButtonColor.default}
                onClick={createBadRecord}
              >
                {'生成不良记录评审单'}
              </Button>
              {/* <Button
                color={ButtonColor.primary}
                onClick={() => setCanEdit(true)}
                disabled={ncRecordStatus !== 'NEW'}
                loading={saveLoading}
                icon="edit-o"
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button> */}
            </>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} activeKey={activeKey} onChange={value => setActiveKey(value)}>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecord`).d('不良记录信息')}
              key="basicInfo"
            >
              <Form columns={3} labelWidth={112} dataSet={detailDs} disabled={!canEdit}>
                <Select name="ncRecordType" onChange={handleChangeNcRecordType} />
                <Select name="ncRecordStatus" disabled />
                <Lov name="siteLov" onChange={handleChangeSite} />
                <Lov name="operation" />
                {ncRecordType === 'RM_NC' && id === 'create' && (
                  <>
                    <TextField
                      name="materialLots"
                      onEnterDown={() => {
                        scanMaterialLot('materialLots');
                      }}
                      readOnly
                      clearButton={false}
                      suffix={
                        <img
                          alt=""
                          style={{ width: '20px', paddingRight: '5px' }}
                          src={scanImg}
                          onClick={() => onOpenInputModal(true, 'materialLots', '物料批编码')}
                        />
                      }
                    />
                    {showFlagLov && (
                      <Lov name="materialLotLov" onChange={handleChangeMaterialLot} />
                    )}
                  </>
                )}
                {ncRecordType === 'EO_ALL_NC' && id === 'create' && (
                  <>
                    <TextField
                      name="eos"
                      onEnterDown={scanEo}
                      readOnly
                      clearButton={false}
                      suffix={
                        <img
                          alt=""
                          style={{ width: '20px', paddingRight: '5px' }}
                          src={scanImg}
                          onClick={() => onOpenInputModal(true, 'eos', '执行作业编码')}
                        />
                      }
                    />
                    {showFlagLov && <Lov name="eoLov" onChange={handleChangeEo} />}
                  </>
                )}
                <DateTimePicker name="ncStartTime" />
                <TextField name="ncRecordNum" />
                {ncRecordType === 'RM_NC' && id === 'create' && (
                  <C7nFormItemSort
                    disabled={id !== 'create'}
                    name="equipmentLov"
                    itemWidth={['60%', '40%']}
                  >
                    <Lov name="equipmentLov" disabled={id !== 'create'} />
                    <TextField
                      name="materialLotList"
                      onEnterDown={() => {
                        scanMaterialLot('materialLotList');
                      }}
                      readOnly
                      clearButton={false}
                      suffix={
                        <img
                          alt=""
                          style={{ width: '20px', paddingRight: '5px' }}
                          src={scanImg}
                          onClick={() => onOpenInputModal(true, 'materialLotList', '物料批编码')}
                        />
                      }
                    />
                  </C7nFormItemSort>
                )}
                <TextArea name="remark" newLine colSpan={3} />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecordInfoObject`).d('不良对象信息')}
              key="badRecordInfoObject"
            >
              {!!ncRecordType && (
                <Table
                  dataSet={detailObjectDs}
                  // buttons={id === 'create' ? buttonsObject : []}
                  buttons={buttonsObject}
                  columns={detailObjectColumn}
                  onRow={({ record }) => {
                    return {
                      onClick: () => handleClick(record),
                    };
                  }}
                />
              )}
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecordDetail`).d('不良记录明细')}
              key="badRecordDetail"
            >
              <Table
                dataSet={detailLineDs}
                style={{ height: '350px' }}
                buttons={id === 'create' ? buttons : []}
                columns={detailColumn}
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(BadRecordDetail as any);
