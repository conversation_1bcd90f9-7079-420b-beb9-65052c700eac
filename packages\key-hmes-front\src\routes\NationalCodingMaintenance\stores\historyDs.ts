import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.hmes.equipmentMaintenance';

const historyPageFactory = () =>
  new DataSet({
    primaryKey: 'hisId',
    selection: false,
    paging: true,
    autoQuery: false,
    validateBeforeQuery: true,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet:new DataSet({
      fields: [
        {
          name: 'startDate',
          type: FieldType.dateTime,
          max:'endDate',
          label: intl.get(`${modelPrompt}.form.startDate`).d('开始时间'),
        },
        {
          name: 'endDate',
          min:'startDate',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.endDate`).d('结束时间'),
        },
      ]
    }),
    fields: [
      {
        name: 'ruleCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.ruleCode`).d('规则编码'),
      },
      {
        name: 'description',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.description`).d('规则描述'),
      },
      {
        name: 'numberBit',
        label: intl.get(`${modelPrompt}.numberBit`).d('位数'),
        type: FieldType.string,
      },
      {
        name: 'siteCode',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
        type: FieldType.string,
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'revisionFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionFlag`).d('版本标记'),
      },
      {
        name: 'workOrderType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.workOrderType`).d('生产类型'),
      },
      {
        name: 'enableFlag',
        lookupCode: 'MT.ENABLE_FLAG',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      },
      {
        name: 'prodLineCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
      },
      {
        name: 'prodLineName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.prodLineName`).d('生产线名称'),
      },
      {
        name: 'workOrderTypeDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.workOrderTypeDesc`).d('生产指令类型描述'),
      },
      {
        name: 'creationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.creationDate`).d('创建日期'),
      },
      {
        name: 'createdByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.endDate`).d('创建人'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-gb-coding-rules/his/list/ui`,
        };
      },
    },
  });

export default historyPageFactory;
