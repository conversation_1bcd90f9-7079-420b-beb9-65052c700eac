/**
 * @Description: C7N 表单项多值组件 样式 
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 15:05:44
 * @LastEditTime: 2021-08-16 16:15:34
 * @LastEditors: <<EMAIL>>
 */

.c7n-sort-wrapper {
  position: relative;
  flex-grow: 1;
  display: flex;
  flex-wrap: nowrap;
  :global {
    .c7n-pro-select-empty {
      z-index: 11;
    }
    .c7n-pro-input-wrapper,
    .c7n-pro-select-wrapper {
      width: 100%;
    }
    .c7n-pro-select,
    .c7n-pro-input {
      border-radius: 0;
    }
    .c7n-sort-item {
      position: relative;
      flex-grow: 1;
      flex-shrink: 1;
      overflow: hidden;
      .link-box {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        z-index: 10;
        display: flex;
        justify-content: center;
        align-items: center;
        & > i {
          font-size: 16px;
          color: rgba(0, 0, 0, 0.5);
          &:hover {
            cursor: pointer;
          }
        }
      }
    }
    .c7n-sort-item.c7n-sort-item-border {
      .c7n-pro-select,
      .c7n-pro-input {
        border-left: none;
      }
      .border-box {
        position: absolute;
        left: 1px;
        top: 18%;
        bottom: 18%;
        width: 1px;
        background-color: rgba(0, 0, 0, 0.2);
        z-index: 10;
      }
    }
    .c7n-sort-item:first-of-type {
      .c7n-pro-select,
      .c7n-pro-input {
        border-radius: 4px 0 0 4px;
      }
    }
    .c7n-sort-item:nth-last-of-type(n + 2) {
      .c7n-pro-select,
      .c7n-pro-input {
        border-right: none;
      }
    }
    .c7n-sort-item:last-of-type {
      .c7n-pro-select,
      .c7n-pro-input {
        border-radius: 0 4px 4px 0;
      }
    }
  }
}
