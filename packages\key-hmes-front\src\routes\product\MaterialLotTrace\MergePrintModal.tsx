import React, { useEffect, useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';

export default ({ ds, mergeedList }) => {  
  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'mergeProcessBarcode',
      },
      {
        name: 'qty',
      },
      {
        name: 'lot',
      },
    ];
  }, []);

  useEffect(() => {
    ds.loadData(mergeedList);
  }, [mergeedList]);

  return (
    <>
      <Table customizedCode="wlpglpt3" dataSet={ds} columns={columns} />
    </>
  );
};
