/**
 * @Description: 盘点工作台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-02-08 16:50:04
 * @LastEditTime: 2023-05-18 16:09:44
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Table, Dropdown, Menu, Modal, Lov, Form, Button } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExport';
// import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST, BASIC } from '@utils/config';
import { observer } from 'mobx-react';
import { tableOADS, batchCreateDS, batchCreateTableDS, costCenterDS } from '../stores/StocktakeWorkbenchListDS';
import { ChangeStatus, BatchAdd, Approve } from '../services';

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';
const tenantId = getCurrentOrganizationId();



const ControlChartList = observer((props: any) => {
  const {
    tableDs,
    match: { params },
    customizeTable,
  } = props;


  // DS事件监听
  useEffect(() => {
    if (params && params.code) {
      props.tableDs.setQueryParameter('stocktakeNum', params.code);
    }
    props.tableDs.query();
  }, []);



  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'stocktakeNum',
        width: 240,
        lock: ColumnLock.left,
      },
      {
        name: 'identification',
        width: 240,
      },
      {
        name: 'remark',
        width: 240,
      },
      {
        name: 'stocktakeStatusDesc',
        width: 100,
        align: ColumnAlign.center,
      },
      {
        name: 'openFlag',
        width: 130,
        align: ColumnAlign.center,
        renderer: ({ value, record }) => {
          return (
            <Tag color={value === 'Y' ? 'orange' : 'blue'}>
              {record?.addField('openFlag').getText(value)}
            </Tag>
          );
        },
      },
      {
        name: 'siteCode',
        width: 200,
      },
      {
        name: 'areaLocatorCode',
        width: 200,
      },
      {
        name: 'createByName',
        width: 180,
      },
      {
        name: 'createDate',
        align: ColumnAlign.center,
        width: 180,
      },
      {
        name: 'lastUpdateByName',
        width: 180,
      },
      {
        name: 'lastUpdateDate',
        align: ColumnAlign.center,
        width: 180,
      },
    ],
    [],
  );

  return (
    <div className="hmes-style">
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.LIST`,
          },
          <Table
            // queryBar={TableQueryBarType.filterBar}
            // queryBarProps={{
            //   fuzzyQuery: false,
            // }}
            dataSet={tableDs}
            columns={columns}
            // searchCode="pdgzt1"
            customizedCode="pdgzt1"
          />,
        )}
      </Content>
    </div>
  );
});

export default flow(
  formatterCollections({ code: ['tarzan.hmes.stocktake.stocktakeWorkbench', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableOADS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.LIST`] }),
)(ControlChartList);
