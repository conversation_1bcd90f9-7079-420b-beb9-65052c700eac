/**
 * @Description: 工艺维护-接口
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 10:27:17
 * @LastEditTime: 2023-05-18 15:07:20
 * @LastEditors: <<EMAIL>>
 */



import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 工艺维护保存
export function saveOperationConfig() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.OPERATION_DETAIL.BASIC`,
    method: 'POST',
  };
}

// 工艺维护基础信息
export function getOperationConfig() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation/detail/ui`,
    method: 'GET',
  };
}

// 工艺维护子步骤
export function getOperationSubSetpConfig() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation-substep/list/ui`,
    method: 'GET',
  };
}

// 工艺维护子步骤删除
export function deleteOperationSubSetpConfig() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-operation-substep/remove/ui`,
    method: 'POST',
  };
}
