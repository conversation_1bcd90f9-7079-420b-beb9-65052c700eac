/**
 * @Description: 生产指令管理抽屉 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2022-03-31 19:47:57
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.execute';
const tenantId = getCurrentOrganizationId();

const splitDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-eo/detail/ui`,
        method: 'GET',
      };
    },
    submit: ({ dataSet }) => {
      const splitMap = [
        'splitQty0',
        'splitQty1',
        'splitQty2',
        'splitQty3',
        'splitQty4',
        'splitQty5',
        'splitQty6',
        'splitQty7',
        'splitQty8',
        'splitQty9',
      ];
      const { eoId, ...data } = dataSet.current.toData();
      const splitQtys = [];
      splitMap.forEach(item => {
        const value = data[item];
        if (value > 0) {
          splitQtys.push(value);
        }
      });
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/split/for/ui `,
        method: 'POST',
        data: {
          sourceEoId: eoId,
          splitQtyList: splitQtys,
        },
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
  fields: [
    // 基本属性
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      disabled: true,
    },
    {
      name: 'eoId',
      type: FieldType.number,
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eoQty`).d('执行作业数量'),
      disabled: true,
    },
    {
      name: 'splitQty0',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      required: true,
      min: 0,
      precision: 2,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
    },
    {
      name: 'splitQty1',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      min: 0,
      precision: 2,
      defaultValue: undefined,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
      dynamicProps: {
        required({ record }) {
          return record.get('flagSplitQty1');
        },
      },
    },
    {
      name: 'flagSplitQty1',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'splitQty2',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      min: 0,
      precision: 2,
      defaultValue: undefined,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
      dynamicProps: {
        required({ record }) {
          return record.get('flagSplitQty2');
        },
      },
    },
    {
      name: 'flagSplitQty2',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'splitQty3',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      min: 0,
      precision: 2,
      defaultValue: undefined,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
      dynamicProps: {
        required({ record }) {
          return record.get('flagSplitQty3');
        },
      },
    },
    {
      name: 'flagSplitQty3',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'splitQty4',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      min: 0,
      precision: 2,
      defaultValue: undefined,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
      dynamicProps: {
        required({ record }) {
          return record.get('flagSplitQty4');
        },
      },
    },
    {
      name: 'flagSplitQty4',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'splitQty5',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      min: 0,
      precision: 2,
      defaultValue: undefined,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
      dynamicProps: {
        required({ record }) {
          return record.get('flagSplitQty5');
        },
      },
    },
    {
      name: 'flagSplitQty5',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'splitQty6',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      min: 0,
      precision: 2,
      defaultValue: undefined,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
      dynamicProps: {
        required({ record }) {
          return record.get('flagSplitQty6');
        },
      },
    },
    {
      name: 'flagSplitQty6',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'splitQty7',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      min: 0,
      precision: 2,
      defaultValue: undefined,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
      dynamicProps: {
        required({ record }) {
          return record.get('flagSplitQty7');
        },
      },
    },
    {
      name: 'flagSplitQty7',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'splitQty8',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      min: 0,
      precision: 2,
      defaultValue: undefined,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
      dynamicProps: {
        required({ record }) {
          return record.get('flagSplitQty8');
        },
      },
    },
    {
      name: 'flagSplitQty8',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'splitQty9',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
      min: 0,
      precision: 2,
      defaultValue: undefined,
      validator: value => {
        if (value === 0) {
          return intl.get(`${modelPrompt}.validation.moreThanZero`).d('数量必须大于0!');
        }
      },
      dynamicProps: {
        required({ record }) {
          return record.get('flagSplitQty9');
        },
      },
    },
    {
      name: 'flagSplitQty9',
      type: FieldType.boolean,
      defaultValue: false,
    },
  ],
});

const splitListDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'eoId',
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/split/detail/for/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialRevision`).d('物料版本'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('执行作业状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eoQty`).d('数量'),
      required: true,
      min: 0,
      precision: 2,
      defaultValue: 0,
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.splitTime`).d('拆分时间'),
    },
  ],
});

export { splitDS, splitListDS };
