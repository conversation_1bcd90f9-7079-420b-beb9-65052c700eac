// import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { TARZAN_REPORT} from '@/utils/config';

const modelPrompt = 'tarzan.inventory.initial.model';
const tenantId = getCurrentOrganizationId();

// const prefix = '/yp-mes-38546'

const initialDs = () => ({
  autoQuery: false,
  primaryKey: 'traceRelId',
  parentField: 'parentTraceRelId',
  expandField: 'expand',
  dataKey: 'rows',
  queryUrl: `${TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-trace-rel-priors/one/query`,
  idField: 'traceRelId',
  queryFields: [
    {
      name: 'processBarcodeListStr',
      type: 'string',
      label: intl.get(`${modelPrompt}.processBarcodeListStr`).d('产品条码'),
    },
    {
      name: 'orderType',
      lookupCode: 'HME.TRACE_DIRECATION',
      label: intl.get(`${modelPrompt}.orderType`).d('追溯方向'),
      type: 'string',
    },
  ],
  fields: [
    { name: 'traceRelId', type: 'number' },
    { name: 'expand', type: 'boolean' },
    { name: 'parentTraceRelId', type: 'number' },
  ],
});

const formDS = () => {
  return {
    name: 'formDS',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'status',
        type: 'string',
        label: intl.get(`${modelPrompt}.status`).d('质量状态'),
      },
      {
        name: 'workStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.workStatus`).d('生产状态'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      },
      {
        name: 'prodLine',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLine`).d('产线编码'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线描述'),
      },
      {
        name: 'workDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.workDate`).d('生产开始时间'),
      },
    ],
  };
};

const productHistoryDS = () => {
  return {
    name: 'productHistoryDS',
    primaryKey: 'traceRelId',
    paging: true,
    autoQuery: false,
    selection: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
      },
      {
        name: 'eoQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.eoQty`).d('数量'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'workOrderCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderCode`).d('工单编码'),
      },
      {
        name: 'sequence',
        type: 'string',
        label: intl.get(`${modelPrompt}.sequence`).d('工序号'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
      },
      {
        name: 'operationDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDesc`).d('工艺描述'),
      },
      {
        name: 'wipStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.wipStatusDesc`).d('移动类型'),
      },
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('移动数量'),
      },
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备描述'),
      },
      {
        name: 'workcellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
      },
      {
        name: 'workcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellName`).d('工位描述'),
      },
      {
        name: 'reworkFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.reworkFlag`).d('是否返修'),
      },
      {
        name: 'shiftCodeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.shiftCodeDesc`).d('班次'),
      },
      {
        name: 'shiftDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.shiftDate`).d('日期'),
      },
      {
        name: 'createdByRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.createdByRealName`).d('操作人'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('移动时间'),
      },
      {
        name: 'prodLineCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线描述'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${TARZAN_REPORT}/v1/${tenantId}/hme-product-step-actual/query/list`,
          method: 'POST',
        };
      },
    },
  };
};

const rawMaterialBarcodeDS = () => {
  return {
    name: 'rawMaterialBarcodeDS',
    primaryKey: 'traceRelId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'parentIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.parentIdentification`).d('产品条码'),
      },
      {
        name: 'parentMaterialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.parentMaterialCode`).d('产品物料编码'),
      },
      {
        name: 'parentMaterialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.parentMaterialName`).d('产品物料描述'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('原材料条码'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('原材料物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('原材料物料描述'),
      },
      {
        name: 'materialLotIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotIdentification`).d('原材料批次'),
      },
      {
        name: 'materialLotSupplier',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotSupplier`).d('原材料供应商批次'),
      },
      {
        name: 'assembleQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.assembleQty`).d('装配数量'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('装配工序'),
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('工序描述'),
      },
      {
        name: 'workcellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellCode`).d('装配工作单元'),
      },
      {
        name: 'workcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellName`).d('工作单元描述'),
      },
      {
        name: 'loginName',
        type: 'string',
        label: intl.get(`${modelPrompt}.loginName`).d('操作人'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('记录时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${TARZAN_REPORT}/v1/${tenantId}/hme-trace-rels/report/ui`,
          method: 'POST',
        };
      },
    },
  };
};

const productParameterDS = () => {
  return {
    name: 'productParameterDS',
    primaryKey: 'ncRecordId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
      },
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'tagCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagCode`).d('收集项编码'),
      },
      {
        name: 'tagDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagDescription`).d('收集项描述'),
      },
      {
        name: 'tagValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagValue`).d('收集值'),
      },
      {
        name: 'tagCalculateResultMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagCalculateResultMeaning`).d('判定结果'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('收集日期'),
      },
      {
        name: 'createByName',
        type: 'string',
        label: intl.get(`${modelPrompt}.createByName`).d('收集人'),
      },
      {
        name: 'tagGroupCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupCode`).d('收集组编码'),
      },
      {
        name: 'tagGroupDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupDescription`).d('收集组描述'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
      },
      {
        name: 'operationDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDescription`).d('工艺名称'),
      },
      {
        name: 'workCellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workCellCode`).d('工位编码'),
      },
      {
        name: 'workCellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workCellName`).d('工位名称'),
      },
      {
        name: 'recordRemark',
        type: 'string',
        label: intl.get(`${modelPrompt}.recordRemark`).d('收集值备注'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-product-param-collect/list/ui`,
          method: 'POST',
        };
      },
    },
  };
};

const ncRecordQueryDS = () => {
  return {
    name: 'ncRecordQueryDS',
    primaryKey: 'ncRecordDetailId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('条码标识'),
      },
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      },
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'ncRecodeTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecodeTypeDesc`).d('不良记录类型'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
      },
      {
        name: 'ncCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCode`).d('不良代码编码'),
      },
      {
        name: 'ncCodeName',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCodeName`).d('不良代码名称'),
      },
      {
        name: 'ncCodeStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncCodeStatusDesc`).d('不良代码状态'),
      },
      {
        name: 'workcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellName`).d('不良产生工作单元'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('不良产生设备'),
      },
      {
        name: 'operationDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDesc`).d('不良产生工艺'),
      },
      {
        name: 'ncRecordTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordTime`).d('不良产生时间'),
      },
      {
        name: 'ncRecordUserIdRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordUserIdRealName`).d('不良记录人'),
      },
      {
        name: 'ncRecordClosedTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordClosedTime`).d('不良关闭时间'),
      },
      {
        name: 'ncRecordClosedRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.ncRecordClosedRealName`).d('不良关闭人'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-nc-record/list/record-query`,
          method: 'POST',
        };
      },
    },
  };
};

const equipmentParamCollectDS = () => {
  return {
    name: 'equipmentParamCollectDS',
    primaryKey: 'ncRecordId',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      },
      {
        name: 'tagCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagCode`).d('收集项编码'),
      },
      {
        name: 'tagDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagDescription`).d('收集项描述'),
      },
      {
        name: 'tagValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagValue`).d('收集值'),
      },
      {
        name: 'tagCalculateResultMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagCalculateResultMeaning`).d('判定结果'),
      },
      {
        name: 'trueValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
      },
      {
        name: 'falseValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
      },
      {
        name: 'recordDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.recordDate`).d('收集日期'),
      },
      {
        name: 'createByName',
        type: 'string',
        label: intl.get(`${modelPrompt}.createByName`).d('收集人'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
      },
      {
        name: 'tagGroupCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupCode`).d('收集组编码'),
      },
      {
        name: 'tagGroupDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupDescription`).d('收集组描述'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupDescription`).d('单位编码'),
      },
      {
        name: 'uomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupDescription`).d('单位描述'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-product-param-collect/equipment/bycode/list/ui`,
          method: 'POST',
        };
      },
    },
  };
};

const inspectReportDS = () => {
  return {
    name: 'inspectReportDS',
    dataKey: 'content',
    totalKey: 'totalElements',
    primaryKey: 'inspectionReportResultId',
    selection: false,
    paging: true,
    autoQuery: false,
    queryFields: [],
    fields: [
      {
        name: 'inspectionReportResultId',
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'sourceObjectTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源对象类型'),
      },
      {
        name: 'sourceObjectCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.sourceObjectCode`).d('来源对象编码'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
      },
      {
        name: 'supplierName',
        type: 'string',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
      },
      {
        name: 'processWorkcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.processWorkcellName`).d('工序'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('检验时间'),
      },
      {
        name: 'lineInspectResultDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineInspectResultDesc`).d('项目检验结果'),
      },
      {
        name: 'dtlInspectResultDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.dtlInspectResultDesc`).d('条码/明细检验结果'),
      },
      {
        name: 'inspectorName',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectorName`).d('检验员'),
      },
      {
        name: 'inspectValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectValue`).d('检验值'),
      },
      {
        name: 'inspectDocNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectDocNum`).d('来源检验单编码'),
      },
      {
        name: 'inspectBusinessTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectBusinessTypeDesc`).d('检验业务类型'),
      },
      {
        name: 'inspectItemCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectItemCode`).d('检验项目编码'),
      },
      {
        name: 'inspectItemDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectItemDesc`).d('检验项目描述'),
      },
      {
        name: 'inspectItemType',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectItemType`).d('检验项目类型'),
        textField: 'description',
        valueField: 'typeCode',
        lovPara: { tenantId },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=INSPECT_ITEM_TYPE`,
        lookupAxiosConfig: {
          transformResponse(data) {
            // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'subItemType',
        type: 'string',
        label: intl.get(`${modelPrompt}.subItemType`).d('检验项目次级类型'),
        lookupCode: 'YP.QIS.SUB_ITEM_TYPE',
        lovPara: { tenantId },
      },
      {
        name: 'featureType',
        type: 'string',
        label: intl.get(`${modelPrompt}.featureType`).d('特性类型'),
        lookupCode: 'YP.QIS.ITEM_CHARACTER',
        lovPara: { tenantId },
      },
      {
        name: 'qualityCharacteristicDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityCharacteristic`).d('质量特性'),
        // lookupCode: 'MT.QMS.QUALITY_CHARACTERISTIC_TYPE',
        // lovPara: { tenantId },
      },
      {
        name: 'inspectToolDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectToolDesc`).d('检验工具'),
      },
      {
        name: 'inspectMethodDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectMethodDesc`).d('检验方法'),
      },
      {
        name: 'dataType',
        type: 'string',
      },
      {
        name: 'dataTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.dataTypeDesc`).d('数据类型'),
      },
      {
        name: 'trueValueList',
        type: 'string',
        label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
      },
      {
        name: 'falseValueList',
        type: 'string',
        label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
      },
      {
        name: 'warningValueList',
        type: 'string',
        label: intl.get(`${modelPrompt}.warningValue`).d('预警值'),
      },
      {
        name: 'inspectFrequency',
        type: 'string',
      },
      {
        name: 'inspectFrequencyDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.inspectFrequency`).d('频次'),
      },
      {
        // 频率参数M值
        name: 'm',
        type: 'number',
        label: intl.get(`${modelPrompt}.m`).d('频率参数M'),
      },
      {
        // 频率参数N值
        name: 'n',
        type: 'number',
        label: intl.get(`${modelPrompt}.n`).d('频率参数N'),
      },
    ],
    transport: {
      read: ({ data }) => {
        const { productCode } = data;
        return {
          url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qis-report/list`,
          method: 'GET',
          data: {
            productCode: productCode,
          },
        };
      },
    },
  };
};

export { initialDs, formDS, productHistoryDS, rawMaterialBarcodeDS, productParameterDS, ncRecordQueryDS, equipmentParamCollectDS, inspectReportDS };
