/**
 * @Description: 物料批管理平台-列表页-历史抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-01-26 00:37:15
 * @LastEditTime: 2022-07-04 17:46:32
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';

export default ({ ds }) => {
  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'eventId',
        align: ColumnAlign.left,
        width: 150,
      },
      {
        name: 'eventTypeCode',
        width: 150,
      },
      {
        name: 'eventTypeDesc',
        width: 150,
      },
      {
        name: 'eventRequestId',
        width: 150,
      },
      {
        name: 'eventRequestTypeCode',
        width: 150,
      },
      {
        name: 'eventRequestTypeDesc',
        width: 150,
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'realName',
      },
      {
        name: 'workOrderNum',
        width: 150,
      },
      {
        name: 'workOrderTypeDesc',
      },
      {
        name: 'prodLineCode',
      },
      {
        name: 'workcellName',
        width: 100,
      },
      {
        name: 'prodVersion',
      },
      {
        name: 'priority',
      },
      {
        name: 'qty',
      },
      {
        name: 'statusDesc',
      },
      {
        name: 'lastWoStatusDesc',
        width: 150,
      },
      {
        name: 'planStartTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'planEndTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'bomName',
      },
      {
        name: 'routerName',
        width: 100,
      },
      {
        name: 'remark',
      },
      // {
      //   name: 'trxQty',
      // },
      // {
      //   name: 'preCompletedQty',
      //   width: 150,
      // },
      {
        name: 'specifiedLevel',
        width: 120,
      },
      {
        name: 'caAttribute1',
        width: 150,
      },
      {
        name: 'caAttribute2',
      },
      {
        name: 'caAttribute3',
      },
      {
        name: 'caAttribute4',
        width: 150,
      },
    ],
    [],
  );

  return (
    <>
      <Table customizedCode="wlpglpt3" dataSet={ds} columns={columns} />
    </>
  );
};
