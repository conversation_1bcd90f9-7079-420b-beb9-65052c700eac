import { Host } from '@/utils/config';
import { getCurrentOrganizationId, getCurrentUserId } from 'utils/utils';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.hmes.rebateWorkOrderBarcodeBinding';
const tenantId = getCurrentOrganizationId();
// const Host = `/yp-mes-38283`
const headDS = () => {
  return {
    name: 'headDS',
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'approvingFlagDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.approvingFlagDesc`).d('是否审批中'),
      },
      {
        name: 'lastApprovalStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastApprovalStatusDesc`).d('上次审批状态'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      },
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'statusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.statusDesc`).d('工单状态'),
      },
      {
        name: 'workOrderTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderTypeDesc`).d('工单类型'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('产线'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),

      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),

      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('工单数量'),
      },
      {
        name: 'completedQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.completedQty`).d('完工数量'),
      },
      {
        name: 'pendingQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.pendingQty`).d('待加工数量'),
      },

    ],
    // queryFields: [
    //   {
    //     name: 'workCellObj',
    //     type: 'object',
    //     label: intl.get(`${modelPrompt}.workOrder`).d('工单'),
    //     lovCode: 'HME.REWORK_WORK_ORDER',
    //     labelWidth: 150,
    //     ignore: 'always',
    //   },
    //   {
    //     name: 'workOrderId',
    //     type: 'string',
    //     bind: 'workCellObj.workOrderId',
    //   },
    //   {
    //     name: 'prodLineObj',
    //     type: 'object',
    //     label: intl.get(`${modelPrompt}.prodLine`).d('产线'),
    //     lovCode: 'HME.PERMISSION_PROD_LINE',
    //     ignore: 'always',
    //     labelWidth: 150,
    //     dynamicProps: {
    //       lovPara() {
    //         return {
    //           tenantId: getCurrentOrganizationId(),
    //           userId: getCurrentUserId(),
    //         };
    //       },
    //     },
    //   },
    //   {
    //     name: 'prodLineId',
    //     type: 'string',
    //     bind: 'prodLineObj.prodLineId',
    //   },
    // ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-wo-barcode-bind/query/wo/for/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const lineDS = () => {
  return {
    name: 'lineDS',
    autoQuery: false,
    primaryKey: 'reworkBindingDtlId',
    selection: false,
    fields: [
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批条码'),
      },
      {
        name: 'reworkBindingFlagDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.reworkBindingFlagDesc`).d('返修绑定标识'),
      },
      {
        name: 'primaryUomQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
      },
      {
        name: 'primaryUomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.primaryUomCode`).d('单位'),
      },
      // {
      //   name: 'siteCode',
      //   type: 'string',
      //   label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      // },
      // {
      //   name: 'materialCode',
      //   type: 'string',
      //   label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      // },
      // {
      //   name: 'materialName',
      //   type: 'string',
      //   label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      // },
      // {
      //   name: 'revisionCode',
      //   type: 'string',
      //   label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      // },
      {
        type: 'string',
        name: 'enableFlag',
        trueValue: 'Y',
        label: intl.get('Default.enableFlag').d('有效性'),
        falseValue: 'N',
        defaultValue: 'Y',
      },
      {
        name: 'qualityStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单'),
      },
    ],
    queryFields: [
      // {
      //   name: 'materialLotCodes',
      //   type: 'string',
      //   label: intl.get(`${modelPrompt}.materialLotCode`).d('条码'),
      // },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-wo-barcode-bind/query/wo/bind/barcode/for/ui`,
          method: 'GET',
        };
      },
    },
  };
};


export { headDS, lineDS };
