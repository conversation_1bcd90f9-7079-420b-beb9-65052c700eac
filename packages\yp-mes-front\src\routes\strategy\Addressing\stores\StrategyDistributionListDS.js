/**
 * @Description: 策略组织分配列表页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-09-14 11:11:55
 * @LastEditTime: 2022-03-31 19:47:41
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.addressing.strategy';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: false,
  autoQueryAfterSubmit: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-add-strategy-assign/list/ui`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'addStrAssignId',
  autoLocateFirst: false,
  paging: false,
  fields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      valueField: 'siteId',
      noCache: true,
      required: true,
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'site.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'locator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      textField: 'locatorCode',
      valueField: 'locatorId',
      noCache: true,
      required: true,
      dynamicProps: {
        disabled({ record }) {
          return !record.get('siteId');
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteIds: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'locator.locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'locator.locatorCode',
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorDesc`).d('库位说明'),
      bind: 'locator.locatorName',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      textField: 'materialCode',
      valueField: 'materialId',
      noCache: true,
      dynamicProps: {
        disabled({ record }) {
          return !record.get('siteId') || record.get('materialCategoryId');
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteIds: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'material.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
      bind: 'material.materialName',
    },
    {
      name: 'materialCategory',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialTypeCode`).d('物料类别编码'),
      lovCode: 'MT.METHOD.MATERIAL_CATEGORY_SITES',
      textField: 'categoryCode',
      valueField: 'materialCategoryId',
      noCache: true,
      dynamicProps: {
        disabled({ record }) {
          return !record.get('siteId') || record.get('materialId');
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteIds: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialCategoryId',
      type: FieldType.number,
      bind: 'materialCategory.materialCategoryId',
    },
    {
      name: 'categoryCode',
      type: FieldType.string,
      bind: 'materialCategory.categoryCode',
    },
    {
      name: 'materialCategoryCode',
      type: FieldType.string,
      bind: 'categoryCode',
    },
    {
      name: 'materialCategoryName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialTypeDesc`).d('物料类别说明'),
      bind: 'materialCategory.description',
    },
    {
      name: 'businessTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessTypeCode`).d('业务类型'),
      lovCode: 'MT.COMMON.BUSINESS_TYPE',
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'businessType',
      type: FieldType.string,
      bind: 'businessTypeObj.typeCode',
    },
    {
      name: 'description',
      type: FieldType.string,
      bind: 'businessTypeObj.description',
    },
    {
      name: 'addressingStrategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.addressingStrategy`).d('寻址策略'),
    },
    {
      name: 'addressingStrategyId',
      type: FieldType.string,
      required: true,
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

export { tableDS };
