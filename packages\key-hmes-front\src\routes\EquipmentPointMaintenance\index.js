import React, { Component } from 'react';
import { DataSet, Table, Spin, Button } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { observer } from 'mobx-react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
// import { isEmpty } from 'lodash';
import { Header, Content } from 'components/Page';
import { Bind } from 'lodash-decorators';
import intl from 'utils/intl';
import { tableDs } from './stores/EquipmentPointMaintenanceDs';

const modelPrompt = 'tarzan.hmes.EquipmentPointMaintenance';

@connect()
@observer
export default class EquipmentPointMaintenance extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };
    this.tableDs = new DataSet({ ...tableDs() });
  }

  @Bind()
  create() {
    const { dispatch } = this.props;
    dispatch(
      routerRedux.push({
        pathname: `/hmes/equipment-point-maintenance/create`,
        payload: { flag: 'NEW' },
      }),
    );
  }

  @Bind()
  edit(record) {
    const { dispatch } = this.props;
    console.log(record.data)
    dispatch(
      routerRedux.push({
        pathname: `/hmes/equipment-point-maintenance/create`,
        payload: { ...record.data, flag: 'EDIT' },
      }),
    );
  }

  render() {
    const { loading } = this.state;
    const columns = [
      // 站点
      {
        name: 'siteCode',
        align: 'left',
      },
      // 装配点编码
      {
        name: 'assemblePointCode',
        align: 'left',
        renderer: ({ value, record }) => {
          return (
            <span className="action-link">
              <a onClick={() => this.edit(record)}>
                {value}
              </a>
            </span>
          );
        },
      },
      // 装配点描述
      {
        name: 'description',
        align: 'left',
      },
      // 有效性
      {
        name: 'enableFlag',
        width: 120,
        align: 'left',
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={value === 'Y' ? '有效' : '无效'}
          >
            {}
          </Badge>
        ),
      },
    ];
    return (
      <React.Fragment>
        <Header title={intl.get(`${modelPrompt}.title`).d('装配点维护')}>
          <Button onClick={this.create} style={{ marginRight: 15 }} icon="add" color="primary">
            {intl.get('tarzan.common.button.create').d('新建')}
          </Button>
        </Header>
        <Content>
          <Spin spinning={loading}>
            <Table
              dataSet={this.tableDs}
              columns={columns}
              style={{ height: 400 }}
              dragRow
              queryBar="filterBar"
              queryBarProps={{
                fuzzyQuery: false,
              }}
              queryFieldsLimit={4}
              searchCode="EquipmentPointMaintenance"
              customizedCode="EquipmentPointMaintenance"
            />
          </Spin>
        </Content>
      </React.Fragment>
    );
  }
}
