import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
// const Host = `/tznr-20000`;
const modelPrompt = 'tarzan.hmes.specialParameterQuery';

const tableDS = () => {
  return {
    name: 'tableDS',
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('产品条码'),
      },
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      },
      {
        name: 'equipmentName',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'tagCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagCode`).d('收集项编码'),
      },
      {
        name: 'tagDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagDescription`).d('收集项描述'),
      },
      {
        name: 'tagValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagValue`).d('收集值'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位编码'),
      },
      {
        name: 'uomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomName`).d('单位描述'),
      },
      {
        name: 'tagCalculateResultDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagCalculateResultDesc`).d('判定结果'),
      },
      {
        name: 'trueValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.trueValue`).d('符合值'),
      },
      {
        name: 'falseValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.falseValue`).d('不符合值'),
      },
      {
        name: 'recordDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.recordDate`).d('收集日期'),
      },
      {
        name: 'loginName',
        type: 'string',
        label: intl.get(`${modelPrompt}.loginName`).d('收集人'),
      },
      {
        name: 'tagGroupCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupCode`).d('收集组编码'),
      },
      {
        name: 'tagGroupDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagGroupDescription`).d('收集组描述'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('工艺'),
      },
      {
        name: 'operationDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationDescription`).d('工艺名称'),
      },
      {
        name: 'workcellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellCode`).d('工位编码'),
      },
      {
        name: 'workcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellName`).d('工位名称'),
      },
      {
        name: 'recordRemark',
        type: 'string',
        label: intl.get(`${modelPrompt}.recordRemark`).d('收集值备注'),
      },      
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    queryFields: [
      {
        name: 'identifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.identifications`).d('条码号'),
      },
      {
        name: 'startDate',
        type: 'dateTime',
        max: 'endDate',
        label: intl.get(`${modelPrompt}.startDate`).d('时间开始'),
      },
      {
        name: 'endDate',
        type: 'dateTime',
        min: 'startDate',
        label: intl.get(`${modelPrompt}.endDate`).d('时间结束'),
      },
      {
        name: 'areaLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.areaLov`).d('车间编码'),
        lovCode: 'HME.DATA_RECORD_AREA',
        lovPara: { tenantId },
        ignore: 'always',
        multiple: true,
      },
      {
        name: 'prodlineLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodlineLov`).d('产线编码'),
        lovCode: 'HME.DATA_RECORD_PRODLINE',
        lovPara: { tenantId },
        ignore: 'always',
        multiple: true,
        computedProps: {
          lovPara: ({ record }) => {
            const areaLov = record.get('areaLov');
            const areaIdList = areaLov?.map(e=>e.areaId);
            return {
              tenantId,
              areaIds: (areaIdList || []).join(','),
            }
          },
        },
      },
      {
        name: 'equipmentLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.equipmentLov`).d('设备编码'),
        lovCode: 'HME.DATA_RECORD_EQUIPMENT',
        lovPara: { tenantId },
        ignore: 'always',
        multiple: true,
        computedProps: {
          lovPara: ({ record }) => {            
            const prodlineLov = record.get('prodlineLov');
            const prodlineIdList = prodlineLov?.map(e=>e.prodlineId);
            return {
              tenantId,
              prodlineIds: (prodlineIdList || []).join(','),
            }
          },
          required: ({ record }) => {
            const areaLov = record.get('areaLov');
            const areaIdList = areaLov?.map(e=>e.areaId);
            const prodlineLov = record.get('prodlineLov');
            const prodlineIdList = prodlineLov?.map(e=>e.prodlineId);
            return (areaIdList || []).length || (prodlineIdList || []).length
          },
        },
      },
      {
        name: 'equipmentIdList',
        bind: 'equipmentLov.equipmentId',
      },
      {
        name: 'materialLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
        lovCode: 'MT.MATERIAL',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'materialCode',
      },
      {
        name: 'materialId',
        bind: 'materialLov.materialId',
      },
      {
        name: 'operationLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.operationLov`).d('工艺编码'),
        lovCode: 'MT.METHOD.OPERATION',
        lovPara: { tenantId },
        ignore: 'always',
        textField: 'operationName',
        multiple: true,
      },
      {
        name: 'operationIdList',
        bind: 'operationLov.operationId',
      },
      {
        name: 'tagValue',
        type: 'string',
        label: intl.get(`${modelPrompt}.tagValue`).d('收集值'),
      },
      {
        name: 'tagLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.tagLov`).d('收集项编码'),
        lovCode: 'YP_MES.TAG',
        ignore: 'always',
        textField: 'tagCode',
        multiple: true,
        lovPara: {
          tenantId,
        },
      },     
      {
        name: 'tagIdList',
        bind: 'tagLov.tagId',
      },
      {
        name: 'tagGroupLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.tagGroupLov`).d('收集组编码'),
        lovCode: 'YP_MES.TAG_GROUP',
        ignore: 'always',
        textField: 'tagGroupCode',
        lovPara: {
          tenantId,
        },
        multiple: true,
      },
      {
        name: 'tagGroupIdList',
        bind: 'tagGroupLov.tagGroupId',
      },
      {
        name: 'tagCalculateResult',
        type: 'string',
        lookupCode: 'HME.TAG_CAL_RESULT',
        label: intl.get(`${modelPrompt}.tagCalculateResult`).d('判定结果'),
      },
      {
        name: 'levelConfigTags',
        type: 'string',
        textField:'meaning',
        valueField:'meaning',
        multiple:true,
        lookupCode: 'HME.LEVEL_CONFIG_TAG',
        label: intl.get(`${modelPrompt}.levelConfigTags`).d('分档参数'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-special-param-query/list/ui`,
          method: 'POST',
        };
      },
    },
  };
};


export { tableDS };


