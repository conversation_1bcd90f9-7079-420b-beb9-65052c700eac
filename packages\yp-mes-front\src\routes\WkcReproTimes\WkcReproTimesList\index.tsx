/*
 * @Description: 复采次数配置-列表页
 * @Author: <<EMAIL>>
 * @Date: 2024-02-22 09:44:42
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-02-22 17:14:59
 */
import React, { useMemo } from 'react';
import { Table, DataSet, Lov, Button } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock } from 'choerodon-ui/pro/es/table/enum';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { openTab } from 'utils/menuTab';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import queryString from 'querystring';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from '../stores';
import { SaveReproTimes, QueryProdLineInfo } from '../services';

const modelPrompt = 'tarzan.hmes.wkcReproTimes';
const tenantId = getCurrentOrganizationId();

const WkcReproTimesList = props => {
  const {
    match: { path },
    tableDs,
  } = props;

  const { run: saveReproTimes } = useRequest(SaveReproTimes(), {
    manual: true,
    needPromise: true,
  });
  const { run: queryProdLineInfo } = useRequest(
    QueryProdLineInfo(),
    { manual: true },
  );

  const handleSave = record => {
    return new Promise(async resolve => {
      const valRes = await record.validate();
      if (!valRes) {
        return resolve(false);
      }
      const res = await saveReproTimes({
        params: [{
          ...record.toData(),
          insertFlag: record?.status === 'add' ? 'Y' : 'N',
        }],
      });
      if (res.success) {
        notification.success({});
        tableDs.query(tableDs.currentPage);
        return resolve(true);
      }
      return resolve(false);
    });
  };

  const handleChangeWorkcell = (value, record) => {
    if (!value) {
      record.set('organizationId', undefined);
      record.set('organizationCode', undefined);
      record.set('organizationName', undefined);
      return;
    }
    queryProdLineInfo({
      params: { organizationId: value.organizationId },
      onSuccess: res => {
        record.set('organizationCode', res.organizationCode);
        record.set('organizationName', res.organizationName);
      },
    });
  };

  const deleteRecord = record => {
    record.setState('deleteFlag', true);
    record.set('reProTimes', null);
    handleSave(record);
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'workcellLov',
        editor: record =>
          record.status === 'add' && (
            <Lov onChange={value => handleChangeWorkcell(value, record)} />
          ),
      },
      {
        name: 'workcellName',
      },
      {
        name: 'organizationCode',
      },
      {
        name: 'organizationName',
      },
      {
        name: 'reProTimes',
        editor: record => record.status === 'add' || record?.getState('editing'),
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 180,
        renderer: ({ record, dataSet }: { record?; dataSet? }) => {
          if (record?.status === 'add' || record?.getState('editing')) {
            return (
              <>
                <Button
                  funcType={FuncType.flat}
                  onClick={() => {
                    if (record.status === 'add') {
                      dataSet?.remove(record);
                    } else {
                      record.reset();
                      record.setState('editing', false);
                    }
                  }}
                >
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
                <Button funcType={FuncType.flat} onClick={() => handleSave(record)}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </Button>
              </>
            );
          }
          return (
            <>
              <Button
                funcType={FuncType.flat}
                onClick={() => {
                  record?.setState('editing', true);
                }}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
              <Popconfirm
                title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                onConfirm={() => deleteRecord(record)}
                okText={intl.get('tarzan.common.button.confirm').d('确认')}
                cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
              >
                <Button icon="delete" color={ButtonColor.red} funcType={FuncType.flat}>
                  {intl.get('tarzan.common.button.delete').d('删除')}
                </Button>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }, []);

  const handleAdd = () => {
    tableDs.create({}, 0);
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/HME.REPRO_TIMES',
      title: intl.get(`${modelPrompt}.list.import`).d('复采次数配置导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('复采次数配置')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
          permissionList={[
            {
              code: `${modelPrompt}.list.button.import`,
              type: 'button',
              meaning: '列表页-导入按钮',
            },
          ]}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="wkcReproTimes_searchCode"
          customizedCode="wkcReproTimes_customizedCode"
          filter={record => !record?.get('deleteFlag')}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(WkcReproTimesList),
);
