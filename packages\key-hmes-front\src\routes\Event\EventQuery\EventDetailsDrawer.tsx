/**
 * @Description: 事件查询-事件影响对象抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-10-31 14:19:51
 * @LastEditTime: 2023-05-25 14:16:02
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { PerformanceTable, Tooltip } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import uuid from 'uuid/v4';

const Panel = Collapse.Panel;
const modelPrompt = 'tarzan.event.eventQuery.model.eventQuery';

type CollpaseItem = {
  columnNames: string[],
  dataValues: string[][],
  eventDetails: any,
  objectDescription: string,
  objectTypeCode: string,
  objectTypeId: number,
  sumTrxQty: number,
}

const EventDetailsDrawer = (props) => {
  const { dataSource } = props;

  const [collpaseActiveKeys, setCollpaseActiveKeys] = useState<string[]>([]);
  const [panelList, setPanelList] = useState<{ key: string, title: string, sumTrxQty: number }[]>([]);
  const [wholdTableObj, setWholdTableObj] = useState<any>({});

  useEffect(() => {
    initCollapse();
  }, []);

  const initCollapse = () => {
    const _collpaseList: { key: string, title: string, sumTrxQty: number }[] = [];
    (dataSource as CollpaseItem[]).forEach((item) => {
      _collpaseList.push({
        title: item.objectDescription,
        key: item.objectTypeCode,
        sumTrxQty: item.sumTrxQty,
      })
      const _columns: any = [];
      const _data: any = [];
      item.columnNames.forEach((item, index) => {
        _columns.push({
          title: () => <Tooltip title={item} theme="light">{item}</Tooltip>,
          dataIndex: index,
          key: index.toString(),
          flexGrow: 1,
          minWidth: 100,
          render: ({ rowData, dataIndex }) => <Tooltip title={rowData[dataIndex]} theme="light">{rowData[dataIndex]}</Tooltip>,
        })
      });
      item.dataValues.forEach((item) => {
        const _lineData: any = {
          id: uuid(),
        };
        item.forEach((lineData, index) => {
          _lineData[index.toString()] = lineData;
        })
        _data.push(_lineData)
      })
      wholdTableObj[item.objectTypeCode] = {
        columns: _columns,
        data: _data,
      }
      setWholdTableObj(wholdTableObj);
    })
    setCollpaseActiveKeys(_collpaseList.map(item => item.key));
    setPanelList(_collpaseList);
  };

  const handleChangeCollapse = (data) => {
    setCollpaseActiveKeys(data);
  };

  return (
    <Collapse
      activeKey={collpaseActiveKeys}
      onChange={handleChangeCollapse}
      expandIconPosition="text-right"
    >
      {
        panelList.map(item => (
          <Panel header={item.title} key={item.key}>
            {
              ['MATERIAL_LOT', 'ONHAND_QTY'].includes(item.key) && (
                <>
                  <div>{`${intl.get(`${modelPrompt}.sumTrxQty`).d('事物数量汇总:')} ${item.sumTrxQty}`}</div>
                  <br />
                </>
              )
            }
            <PerformanceTable
              virtualized
              columns={wholdTableObj[item.key].columns}
              data={wholdTableObj[item.key].data}
              height={260}
            />
          </Panel>
        ))
      }
    </Collapse>
  );
}

export default EventDetailsDrawer;
