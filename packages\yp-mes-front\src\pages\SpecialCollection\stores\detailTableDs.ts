import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.ass.specialCollection';

const detailTableFactory = () =>
  new DataSet({
    primaryKey: 'lineId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    fields: [
      {
        name: 'ordinalLine',
        label: intl.get(`${modelPrompt}.form.ordinalLine`).d('序号'),
        type: FieldType.string,
      },
      {
        name:'valueType',
        type: FieldType.string,
        defaultValue:'VALUE',
      },
      {
        name: 'decide',
        required: true,
        label: intl.get(`${modelPrompt}.form.decide`).d('条件'),
        type: FieldType.string,
      },
      {
        name: 'trueValue',
        required: true,
        label: intl.get(`${modelPrompt}.form.trueValue`).d('符合值'),
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-look-process/line/query`,
        };
      },
    },
  });

export default detailTableFactory;
