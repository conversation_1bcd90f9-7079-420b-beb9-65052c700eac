/**
 * @Description: 返修工单条码绑定-列表页
 * @Author: <EMAIL>
 * @Date: 2023-06-15 10:19:56
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table, Button, Form, TextField, Spin } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { getResponse } from '@utils/utils';

import notification from 'hzero-front/lib/utils/notification';
import { headDS, lineDS } from './stores/TableDs';
import { Host } from '@/utils/config';

const modelPrompt = 'tarzan.hmes.rebateWorkOrderBarcodeBinding';

// const Host = `/yp-mes-38283`;
const Panel = Collapse.Panel;
const RebateWorkOrderBarcodeBinding = props => {
  const { headDs, lineDs } = props;
  const [bindDisabled, setBindDisabled] = useState(false);
  const [lineDisabled, setLineDisabled] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectLine, setSelectLine] = useState([]);
  useEffect(() => {
    headDs.addEventListener('load', () => {
      if (headDs.toData().length === 1) {
        setTimeout(() => {
          headDs.select(0);
        }, 0);
      } else {
        setBindDisabled(false);
        lineDs.queryDataSet.current.set('materialLotCodes', '');
        lineDs.loadData([]);
      }
    });
    headDs.addEventListener('select', handleDataSetSelect);
    headDs.addEventListener('unSelect', handleDataSetSelect);
    lineDs.addEventListener('select', handleDataSetLineSelect);
    lineDs.addEventListener('unSelect', handleDataSetLineSelect);
    lineDs.addEventListener('selectAll', handleDataSetLineSelect);
    lineDs.addEventListener('unSelectAll', handleDataSetLineSelect);
    lineDs.addEventListener('query', () => {
      setSelectLine([]);
      lineDs.unSelectAll();
    });

    lineDs.addEventListener('load', () => {
      if (
        lineDs.toData().length > 0 &&
        headDs.selected.length === 1 &&
        headDs.selected[0].toData().approvingFlag !== 'Y'
      ) {
        setBindDisabled(true);
      }
    });
  }, []);

  const handleDataSetSelect = () => {
    if (headDs.selected) {
      lineDs.setQueryParameter('workOrderId', headDs.selected[0].toData().workOrderId);
      lineDs.query().then(res => {
        if (res) {
          setBindDisabled(
            headDs.selected.length === 1 &&
            lineDs.toData().length > 0 &&
            headDs.selected[0].toData().approvingFlag !== 'Y',
          );
        }
      });
    } else {
      lineDs.loadData([]);
      setBindDisabled(false);
    }
  };

  const handleDataSetLineSelect = () => {
    setSelectLine(lineDs.selected);
    if (lineDs.selected.length) {
      const disabledData = [];
      lineDs.selected.forEach(item => {
        if (item.data.reworkBindingFlag) {
          if (item.data.reworkBindingFlag === 'Y' || item.data.reworkBindingFlag === 'S') {
            disabledData.push(item.data);
          }
        }
      });
      setLineDisabled(
        headDs.selected.length === 1 &&
        lineDs.toData().length > 0 &&
        headDs.selected[0].toData().approvingFlag !== 'Y' &&
        disabledData.length < 1,
      );
    } else {
      setLineDisabled(false);
    }
  };
  const headColumns = [
    {
      name: 'approvingFlagDesc',
    },
    {
      name: 'lastApprovalStatusDesc',
      width: 150,
    },
    {
      name: 'workOrderNum',
      width: 150,
    },
    {
      name: 'siteCode',
    },
    {
      name: 'statusDesc',
    },
    {
      name: 'workOrderTypeDesc',
    },
    {
      name: 'prodLineName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'qty',
    },
    {
      name: 'completedQty',
    },
    {
      name: 'pendingQty',
    },
  ];

  // 删除
  const handleDel = async () => {
    const deleteData = [];
    // const disabledData = [];
    selectLine.forEach(item => {
      if (item.data.reworkBindingFlag) {
        // if (item.data.reworkBindingFlag === 'N' || item.data.reworkBindingFlag === 'F') {
        deleteData.push(item.data);
        // }
        // if (item.data.reworkBindingFlag === 'Y' || item.data.reworkBindingFlag === 'S') {
        //   disabledData.push(item.data);
        // }
      } else {
        lineDs.remove(item);
      }
    });
    // if (disabledData.length > 0) {
    //   return notification.error({
    //     message: '选择数据中存在返修绑定标识为是或成功的数据，该条码不可删除！',
    //   });
    // }
    if (deleteData.length > 0) {
      const res = await request(
        `${Host}/v1/${getCurrentOrganizationId()}/hme-wo-barcode-bind/delete/for/ui`,
        {
          method: 'POST',
          body: {
            bindBarcodeList: deleteData,
          },
        },
      );
      const respone = getResponse(res);
      if (respone) {
        notification.success({
          message: intl.get('tarzan.aps.common.notification.success').d('操作成功'),
        });
        // 重新查询行
        lineDs.queryDataSet.current.set('materialLotCodes', '');
        lineDs.queryDataSet.current.set('workOrderId', headDs.selected[0].toData().workOrderId);
        lineDs.query();
      }
    } else {
      notification.success({
        message: intl.get('tarzan.aps.common.notification.success').d('操作成功'),
      });
    }
  };
  const lineColumns = [
    // {
    //   name: 'del',
    //   width: 80,
    //   align: 'center',
    //   renderer: ({record}) =>(<Icon onClick={() => handleDel(record)} type="delete_black-o" style={{ fontSize: 20, color: 'rgb(8, 64, 248)', cursor: 'pointer' }} />),
    // },
    {
      name: 'materialLotCode',
    },
    {
      name: 'reworkBindingFlagDesc',
    },
    {
      name: 'primaryUomQty',
    },
    {
      name: 'primaryUomCode',
    },
    // {
    //   name: 'siteCode',
    // },
    // {
    //   name: 'materialCode',
    // },
    // {
    //   name: 'materialName',
    // },
    // {
    //   name: 'revisionCode',
    // },
    {
      name: 'enableFlag',
      width: 140,
      align: 'center',
      renderer: ({ value }) => {
        return value === 'Y' ? '是' : '否';
      },
    },
    {
      name: 'qualityStatusDesc',
    },
    {
      name: 'workOrderNum',
    },
  ];
  const renderBar = () => {
    if (lineDs.queryDataSet) {
      return (
        <Form
          id="queryForm"
          loading={loading}
          dataSet={lineDs.queryDataSet}
          columns={4}
          labelWidth="120"
        >
          <TextField
            clearButton
            disabled={
              !headDs.selected.length ||
              loading ||
              headDs.selected[0].toData().approvingFlag === 'Y'
            }
            name="materialLotCodes"
            onChange={handleCode}
          />
        </Form>
      );
    }
    return null;
  };

  const handleCode = async () => {
    if (lineDs.queryDataSet.current.get('materialLotCodes')) {
      const temp = lineDs.queryDataSet.current
        .get('materialLotCodes')
        .split(' ')
        .filter(item => item !== '');
      const arr = temp.filter((item, index) => temp.indexOf(item) === index);
      const newTemp = lineDs.toData().map(item => item.materialLotCode);
      const newArr = [];
      arr.forEach(item => {
        if (newTemp.indexOf(item) === -1) {
          newArr.push(item);
        }
      });
      if (!newArr.length) {
        console.log(newArr)
        notification.error({
          message: `${lineDs.queryDataSet.current
            .get('materialLotCodes')} ${intl.get(`${modelPrompt}.title.materialLotNo`).d('当前返修条码已存在，请检查!')}`,
        });
        return
      };
      setLoading(true);
      const res = await request(
        `${Host}/v1/${getCurrentOrganizationId()}/hme-wo-barcode-bind/query/barcode/for/ui`,
        {
          method: 'GET',
          query: {
            materialLotCodes: newArr.join(','),
            workOrderId: headDs.selected[0].toData().workOrderId,
            materialId: headDs.selected[0].toData().materialId,
            revisionCode: headDs.selected[0].toData().revisionCode,
          },
        },
      );
      setLoading(false);
      const respone = getResponse(res);
      if (respone) {
        const data = lineDs.toData();
        const { content } = respone;
        const newLine = data
          .concat(content)
          .filter(
            (item, index) =>
              data.concat(content).findIndex(i => i.materialLotCode === item.materialLotCode) ===
              index,
          );
        lineDs.loadData(newLine);
      }
    }
  };

  const handleBind = async () => {
    const res = await request(
      `${Host}/v1/${getCurrentOrganizationId()}/hme-wo-barcode-bind/process`,
      {
        method: 'POST',
        body: {
          ...headDs?.selected[0]?.toJSONData(),
          bindBarcodeList: lineDs.toData(),
          materialLotCodes: lineDs
            .toData()
            .map(item => item.materialLotCode)
            .join(','),
        },
      },
    );
    const respone = getResponse(res);
    if (respone) {
      notification.success({
        message: intl.get('tarzan.aps.common.notification.success').d('操作成功'),
      });
      headDs.query(headDs.currentPage);
      // 清空数据
      lineDs.queryDataSet.current.set('materialLotCodes', '');
      lineDs.loadData([]);
    }
  };
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('返修工单条码绑定')}>
        <Button color="primary" onClick={handleBind} disabled={!bindDisabled}>
          {intl.get('hzero.common.button.bindApply').d('绑定申请')}
        </Button>
      </Header>
      <Content>
        <Collapse defaultActiveKey={['1', '2']}>
          <Panel header={intl.get(`${modelPrompt}.tab.title1`).d('工单')} key="1">
            <Table
              queryFieldsLimit={6}
              searchCode="rebateWorkOrderBarcodeBindingHead"
              customizedCode="rebateWorkOrderBarcodeBindingHead"
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={headDs}
              columns={headColumns}
            />
          </Panel>
          <Panel header={intl.get(`${modelPrompt}.tab.title2`).d('返修条码')} key="2">
            <Spin spinning={loading}>
              <Button
                onClick={handleDel}
                disabled={!lineDisabled}
                color="primary"
                style={{ marginBottom: '-45px', float: 'right' }}
              >
                {intl.get(`${modelPrompt}.button.delete`).d('删除')}
              </Button>
              <Table
                searchCode="rebateWorkOrderBarcodeBindingLine"
                customizedCode="rebateWorkOrderBarcodeBindingLine"
                queryBar={renderBar}
                queryBarProps={{
                  fuzzyQuery: false,
                }}
                loading={loading}
                dataSet={lineDs}
                columns={lineColumns}
              />
            </Spin>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.hmes.rebateWorkOrderBarcodeBinding', 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet({
        ...headDS(),
      });
      const lineDs = new DataSet({
        ...lineDS(),
      });

      return {
        headDs,
        lineDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(RebateWorkOrderBarcodeBinding),
);
