import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.process.unitWork.model.unitWork';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    autoQuery: true,
    autoCreate: false,
    selection: DataSetSelection.multiple,
    dataKey: 'content',
    totalKey: 'numberOfElements',
    primaryKey: 'id',
    queryFields: [
      {
        name: 'tableName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.tableName `).d('表名'),
      },
      {
        name: 'tableDatasource',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.tableDatasource`).d('表所属数据源'),
        lookupCode: 'HME_DATA_SOURCE',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'tableType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.tableType`).d('表类型'),
        lookupCode: 'HME_TABLE_TYPE',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'archiveWay',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.archiveWay`).d('归档⽅式'),
        lookupCode: 'HME_ARCHIVE_WAY',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'clearUom',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.clearUom`).d('清理单位（表/分区/时间（天）'),
        lookupCode: 'HME_CLEAR_UOM',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'enableFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.enableFlag`).d('是否启⽤（Y/N）'),
        lookupCode: 'HSCS.YES_NO',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'preType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.preType `).d('上次执行类型'),
        lookupCode: 'HME_CLEAR_ARCHIVE_OPERATION_TYPE',
        textField: 'meaning',
        valueField: 'value',
        lovPara: { tenantId },
      },
      {
        name: 'preStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.preStatus `).d('上次执行状态'),
        lookupCode: 'HME_CLEAR_ARCHIVE_STATUS',
        textField: 'meaning',
        valueField: 'value',
        lovPara: { tenantId },
      },
    ],
    fields: [
      {
        name: 'subFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.subFlag`).d('是否分表'),
        lookupCode: 'HSCS.YES_NO',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'subRule',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.subRule`).d('分表规则'),
        dynamicProps: {
          disabled: ({ record }) => record.get('subFlag') === 'N',
        },
      },
      {
        name: 'tableName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.tableName`).d('表名'),
      },
      {
        name: 'tableDatasource',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.tableDatasource`).d('表所属数据源'),
        lookupCode: 'HME_DATA_SOURCE',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'tableType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.tableType`).d('表类型'),
        lookupCode: 'HME_TABLE_TYPE',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'archiveFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.archiveFlag`).d('归档标识'),
        lookupCode: 'HSCS.YES_NO',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'archiveWay',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.archiveWay`).d('归档⽅式'),
        lookupCode: 'HME_ARCHIVE_WAY',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'clearUom',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.clearUom`).d('清理单位（表/分区/时间（天）'),
        lookupCode: 'HME_CLEAR_UOM',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'reserveCycle',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.reserveCycle`).d('保留周期'),
      },
      {
        name: 'preExcute',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.preExcute`).d('上次执⾏时间'),
      },
      {
        name: 'preType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.preType`).d('上次执⾏'),
        lookupCode: 'HME_CLEAR_ARCHIVE_OPERATION_TYPE',
        textField: 'meaning',
        valueField: 'value',
        lovPara: { tenantId },
      },
      {
        name: 'preStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.preStatus`).d('上⼀次执⾏状态（S/E）'),
        lookupCode: 'HME_CLEAR_ARCHIVE_STATUS',
        textField: 'meaning',
        valueField: 'value',
        lovPara: { tenantId },
      },
      {
        name: 'status',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.status`).d('运⾏状态'),
        lookupCode: 'HME_CLEAR_ARCHIVE_RUN',
        textField: 'meaning',
        valueField: 'value',
        lovPara: { tenantId },
      },
      {
        name: 'enableFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.enableFlag`).d('是否启⽤（Y/N）'),
        lookupCode: 'HSCS.YES_NO',
        lovPara: { tenantId },
        valueField: 'value',
        textField: 'meaning',
      },
      {
        name: 'createdNameBy',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.createdBy`).d('创建⼈'),
      },
      {
        name: 'creationDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'lastUpdatedNameBy',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lastUpdatedBy`).d('更新⼈'),
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('更新时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `/archive/v1/${tenantId}/hme-clean-archive/ui`,
          method: 'GET',
        };
      },
    },
  });

const relationListPageFactory = () =>
  new DataSet({
    autoQuery: true,
    autoCreate: false,
    selection: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    primaryKey: 'id',
    queryFields: [
      {
        name: 'type',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.type`).d('类型'),
        lookupCode: 'HME_CLEAR_ARCHIVE_OPERATION_TYPE',
        textField: 'meaning',
        valueField: 'value',
        lovPara: { tenantId },
      },
      {
        name: 'status',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.status`).d('状态'),
        lookupCode: 'HME_CLEAR_ARCHIVE_STATUS',
        textField: 'meaning',
        valueField: 'value',
        lovPara: { tenantId },
      },
      {
        name: 'startLastUpdateDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.startLastUpdateDate`).d('更新开始时间'),
        max: 'LastUpdateDate',
      },
      {
        name: 'LastUpdateDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.LastUpdateDate`).d('更新结束时间'),
        min: 'startLastUpdateDate',
      },
    ],
    fields: [
      {
        name: 'dataRange',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.dataRange`).d('数据范围'),
      },
      {
        name: 'type',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.type`).d('类型'),
        lookupCode: 'HME_CLEAR_ARCHIVE_OPERATION_TYPE',
        textField: 'meaning',
        valueField: 'value',
        lovPara: { tenantId },
      },
      {
        name: 'status',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.status`).d('状态'),
        lookupCode: 'HME_CLEAR_ARCHIVE_STATUS',
        textField: 'meaning',
        valueField: 'value',
        lovPara: { tenantId },
      },
      {
        name: 'msg',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.msg`).d('结果'),
      },
      {
        name: 'createdNameBy',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.createdBy`).d('创建⼈'),
      },
      {
        name: 'creationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'lastUpdatedNameBy',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lastUpdatedBy`).d('更新⼈'),
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('更新时间'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `/archive/v1/${tenantId}/hme-clean-archive/lines/ui`,
          method: 'GET',
        };
      },
    },
  });

export { listPageFactory, relationListPageFactory };
