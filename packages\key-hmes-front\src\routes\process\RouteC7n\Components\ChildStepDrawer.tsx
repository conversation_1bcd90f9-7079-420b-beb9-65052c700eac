/**
 * 工艺子步骤-抽屉
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */
import React, { FC, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Table, DataSet, Button, TextField } from 'choerodon-ui/pro';
import { Card, Popconfirm } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import { Button as PermissionButton } from 'components/Permission';
import '../index.module.less';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.process.routes.model.routes';

export interface ChildStepDrawerProps {
  currentDataSource: any;
  description: string;
  stepName: string;
  path: string;
  canEdit: boolean;
  closeModal: any;
  childStepDrawerDs: DataSet;
  operateData?: any;
  detailTableDs?: DataSet;
  UpdateStepList?: any;
  ref: any;
}

const ChildStepDrawer: FC<ChildStepDrawerProps> = (
  {
    currentDataSource,
    description,
    stepName,
    path,
    canEdit,
    closeModal,
    childStepDrawerDs,
    operateData,
    detailTableDs,
    UpdateStepList,
  },
  ref,
) => {
  // 通过ref调用的方法
  useImperativeHandle(ref, () => ({
    handleOK,
  }));

  useEffect(() => {
    const tableList = (currentDataSource.mtRouterOperationDTO.mtRouterSubstepDTO || []).map(
      item => {
        return {
          ...item,
          uuid: uuid(),
        };
      },
    );
    tableList.sort((a, b) => a.sequence - b.sequence);

    childStepDrawerDs.loadData(tableList);
  }, [currentDataSource.routerStepId]);

  /**
   * 工艺子步骤-表格-确定保存
   */
  const handleOK = async () => {
    let editing = false;
    childStepDrawerDs.forEach(record => {
      if (record.getState('editing')) {
        editing = true;
      }
    });
    const validate = await childStepDrawerDs?.validate();
    if (validate && !editing) {
      const data = detailTableDs?.toData() || [];
      data.forEach((i: any) => {
        if (i.routerStepId === currentDataSource.routerStepId) {
          i.mtRouterOperationDTO.mtRouterSubstepDTO = childStepDrawerDs.toData();
        }
      });
      UpdateStepList(operateData(data));
      notification.success({});
      closeModal();
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.unSaveSitesList`).d('请保存所有行'),
      });
    }
  };

  /**
   * 工艺子步骤-表格-新增行
   */
  const handleCreate = () => {
    let maxNumber = 0;
    childStepDrawerDs.toData().forEach(item => {
      const { sequence } = item as any;
      if (sequence > maxNumber) {
        maxNumber = sequence;
      }
    });
    const record = childStepDrawerDs.create(
      {
        uuid: uuid(),
        sequence: parseInt(String(maxNumber / 10), 10) * 10 + 10,
      },
      0,
    );
    record.setState('editing', true);
  };

  /**
   * 工艺子步骤-表格-删除行
   */
  const handleDelete = record => {
    childStepDrawerDs.remove(record);
  };

  /**
   * 工艺子步骤-表格-列配置
   */
  const childStepColumn = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!canEdit}
          funcType="flat"
          onClick={handleCreate}
          shape="circle"
          size="small"
          // permissionList={[
          //   {
          //     code: `${path}.button.edit`,
          //     type: 'button',
          //     meaning: '详情页-编辑新建删除复制按钮',
          //   },
          // ]}
        />
      ),
      align: 'center',
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => {
            handleDelete(record);
          }}
        >
          <Button icon="remove" funcType={FuncType.flat} size={Size.small} disabled={!canEdit} />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'sequence',
      editor: record => record.getState('editing'),
    },
    {
      name: 'substep',
      editor: record => record.getState('editing'),
    },
    {
      name: 'substepDesc',
      editor: record => record.getState('editing') && <TextField />,
    },
    {
      header: intl.get(`${modelPrompt}.operation`).d('操作'),
      renderer: ({ record }) => {
        return (
          <span>
            {record.getState('editing') ? (
              <>
                <a onClick={() => handleCancel(record)}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </a>
                &nbsp;&nbsp;
                <PermissionButton
                  type="text"
                  permissionList={[
                    {
                      code: `${path}.button.edit`,
                      type: 'button',
                      meaning: '详情页-编辑新建删除复制按钮',
                    },
                  ]}
                  onClick={() => handleSaveLine(record)}
                >
                  {intl.get('tarzan.common.button.save').d('保存')}
                </PermissionButton>
              </>
            ) : (
              <PermissionButton
                type="text"
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '详情页-编辑新建删除复制按钮',
                  },
                ]}
                disabled={!canEdit}
                onClick={() => handleEdit(record)}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </PermissionButton>
            )}
          </span>
        );
      },
    },
  ];

  /**
   * 工艺子组件行，取消按钮
   * @param record
   */
  const handleCancel = record => {
    if (record.status === 'add') {
      childStepDrawerDs.remove(record);
    } else {
      record.reset();
      record.setState('editing', false);
    }
  };

  /**
   * 工艺子组件行，编辑按钮
   * @param record
   */
  const handleEdit = record => {
    record.setState('editing', true);
  };

  /**
   * 工艺子组件行，保存按钮
   */
  const handleSaveLine = async record => {
    const validate = await record?.validate();
    if (validate) {
      const tableList = childStepDrawerDs.toData();
      if (
        tableList.some(
          (ele: any) => ele.sequence === record.get('sequence') && ele.uuid !== record.get('uuid'),
        )
      ) {
        notification.error({
          message: intl.get(`${modelPrompt}.sameSequence`).d('顺序重复'),
        });
      } else if (
        tableList.some(
          (ele: any) =>
            ele.substepId === record.get('substepId') && ele.uuid !== record.get('uuid'),
        )
      ) {
        notification.error({
          message: intl.get(`${modelPrompt}.sameSubstepId`).d('编码重复'),
        });
      } else {
        record.setState('editing', false);
      }
    }
  };

  return (
    <div className="hmes-style">
      <Card title={stepName ? `${description}/${stepName}` : description} bordered={false}>
        <Table
          key="uuid"
          dataSet={childStepDrawerDs}
          columns={childStepColumn as ColumnProps[]}
          filter={record => {
            return record.status !== 'delete';
          }}
        />
      </Card>
    </div>
  );
};
// @ts-ignore
export default forwardRef(ChildStepDrawer);
