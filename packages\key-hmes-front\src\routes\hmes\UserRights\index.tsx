/**
 * @Description: 用户权限维护
 * @Author: <<EMAIL>>
 * @Date: 2022-10-09 14:43:54
 * @LastEditTime: 2022-10-17 14:06:43
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useCallback, useState } from 'react';
import { toJS } from 'mobx';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { EnableRender } from '@components/tarzan-ui';
import ExcelExport from 'components/ExcelExport';
import { BASIC } from '@utils/config';
import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import { getCurrentOrganizationId } from 'utils/utils';
import UserRightsDrawer from './UserRightsDrawer';
import { isNil } from 'lodash';
import { tableDS } from './stores';

const MaterialPlanList = (props) => {
  const {
    match: { path },
    tableDs,
  } = props;

  const [visible, setVisible] = useState(false);
  const [editingUserInfo, setEditingUserInfo] = useState<{ userId: number, userName: string, userDesc: string } | null>(null);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'userLov',
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                setEditingUserInfo({
                  ...toJS(value),
                  userDesc: record!.get('userDesc'),
                });
                setVisible(true);
              }}
            >
              {record!.get('userName')}
            </a>
          );
        },
      },
      { name: 'userDesc' },
      { name: 'organizationType' },
      { name: 'organizationCode' },
      { name: 'organizationDesc' },
      {
        name: 'defaultOrganizationFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: (props) => <EnableRender {...props} />,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: (props) => <EnableRender {...props} />,
      },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    setEditingUserInfo(null)
    setVisible(true);
  }, []);

  const onClose = useCallback(() => {
    setVisible(false);
  }, []);

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MT.MES.USER_ORGANIZATION`,
      title: intl.get('tarzan.model.hmes.userRights.rightsMaintenance.import').d('用户权限导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = tableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return {
      ...queryParams,
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.model.hmes.userRights.rightsMaintenance').d('用户权限维护')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button
          color={ButtonColor.primary}
          icon="daorucanshu"
          onClick={handleImport}
        >
          {intl.get('tarzan.common.button.import').d('导入')}
        </Button>
        <ExcelExport
          method="GET"
          requestUrl={`${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}/mt-user-organization/export/ui`}
          queryParams={getExportQueryParams}
          // otherButtonProps={{disabled: tableDs.selected.length===0}}
          buttonText={intl.get(`tarzan.acquisition.dataItem..export`).d('导出')}
        />
      </Header>
      <Content>
        <UserRightsDrawer
          userInfo={editingUserInfo}
          visible={visible}
          onClose={onClose}
        />
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="MaterialPlan"
          customizedCode="MaterialPlan"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.model.hmes.userRights', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MaterialPlanList),
);
