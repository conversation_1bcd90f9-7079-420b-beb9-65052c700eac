import React, { useMemo, useState } from 'react';
import { Table, DataSet, Button, TextField, Icon } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { observer } from 'mobx-react';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { overrideTableBar } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import LovModal from '@/components/BatchInput/LovModal';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { tableDS } from '../stores';
import { GradeChange } from '../services';

const modelPrompt = 'tarzan.hmes.specialParameterSyncList';
const tenantId = getCurrentOrganizationId();
const endUrl = "";
const SpecialParameterSyncList = props => {
  const { tableDs, history,match: {path}} = props;
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);

  const { run: gradeChange } = useRequest(GradeChange(), { manual: true, needPromise: true });

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
    }
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'identification', minWidth: 240, lock: ColumnLock.left },
      { name: 'materialCode', minWidth: 150 },
      { name: 'materialName' },
      { name: 'qty' },
      { name: 'uomName' },
      {
        name: 'qualityStatus',
        width: 140,
        align: ColumnAlign.center,
        renderer: ({ record, name }) => record!.getField(name)!.getText(),
      },
      { name: 'specifiedLevel' },
      {
        name: 'status',
        width: 140,
        align: ColumnAlign.center,
        renderer: ({ record, name }) => record!.getField(name)!.getText(),
      },
      { name: 'description' },
      { 
        name: 'workOrderNum',
        width: 180,
      },
      { name: 'prodLineCode' },
      { name: 'siteCode' },
    ];
  }, []);


  // 条码相关的跳转按钮禁用逻辑
  const barcodeRelatedBtnDisabled = (() => {
    return !tableDs.selected?.length;
  })();

  // 携带条码信息跳转相关功能的回调
  const jumpToBarcodeRelatedPage = targetUrl => {
    const { tableDs, history,match: {path}} = props;
    debugger;
    const identifications = tableDs.selected?.map(_record => _record?.get('identification'))?.join(',');
    history.push({
      pathname: targetUrl,
      state: { identifications },
    });
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const updateSpecialPara = async () => {
    if (tableDs.selected?.length) {
      const data = tableDs.selected.map(item => item.data);
      setUpdateLoading(true);
      request(`${BASIC.TARZAN_REPORT}${endUrl}/v1/${tenantId}/hme-special-para/update`, {
        method: 'POST',
        body: data,
      }).then(res => {
        setUpdateLoading(false);
        if (res && res.success) {
          notification.success({message: res.message });
        } else{
          notification.error({ message: res.message });
        }
      });
    }
  };

  const handleChangeGrade = () => {
    return new Promise(async (resolve) => {
      const res = await gradeChange({ params: { processBarcodeList:  tableDs.selected.map(_record => _record?.get('identification')) }});
      if (res.success) {
        notification.success({});
        tableDs.query(tableDs.currentPage);
        resolve(true);
      }
      resolve(false);
    })
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('特殊参数同步')}>
        <Button
          disabled={barcodeRelatedBtnDisabled}
          onClick={() => jumpToBarcodeRelatedPage('/hmes/special-parameter-query')}
        >
          {intl.get(`${modelPrompt}.button.productProcessingHistory`).d('特殊参数查询')}
        </Button>
        <PermissionButton
          type="c7n-pro"
          disabled={barcodeRelatedBtnDisabled}
          color={ButtonColor.primary}
          icon="save"
          loading={updateLoading}
          onClick={updateSpecialPara}
          permissionList={[
            {
              code: `${path}.button.update`,
              type: 'button',
              meaning: '列表页-特殊参数更新按钮',
            },
          ]}
        >
          {intl.get(`tarzan.common.button.update`).d('特殊参数更新')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          disabled={!tableDs.selected?.length}
          onClick={handleChangeGrade}
          permissionList={[
            {
              code: `${modelPrompt}.list.button.gradeChange`,
              type: 'button',
              meaning: '列表页-人工分档按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.gradeChange`).d('人工分档')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          queryBar={overrideTableBar}
          queryFields={{
            identificationList: (
              <TextField
                name="identificationList"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(
                          true,
                          'identificationList',
                          intl.get(`${modelPrompt}.identification`).d('条码号'),
                        )
                      }
                    />
                  </div>
                }
              />
            ),
            workOrderNumList: (
              <TextField
                name="workOrderNumList"
                suffix={
                  <div className="c7n-pro-select-suffix">
                    <Icon
                      type="search"
                      onClick={() =>
                        onOpenInputModal(
                          true,
                          'workOrderNumList',
                          intl.get(`${modelPrompt}.workOrderNumList`).d('工单编码'),
                        )
                      }
                    />
                  </div>
                }
              />
            ),
          }}
          dataSet={tableDs}
          columns={columns}
          pagination={{ pageSizeOptions: ['10', '20', '50', '100', '500', '1000'] }}
          customizedCode="barcodeInfoQueryReport_customizedCode"
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(observer(SpecialParameterSyncList)),
);
