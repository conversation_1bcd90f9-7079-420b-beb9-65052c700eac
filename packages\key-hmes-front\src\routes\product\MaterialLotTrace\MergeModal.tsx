import React, { useEffect, useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';

export default ({ ds, mergeList }) => {
  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'materialLotCode',
      },
      {
        name: 'primaryUomQty',
      },
      {
        name: 'lot',
      },
    ];
  }, []);

  useEffect(() => {
    ds.loadData(mergeList);
  }, [mergeList]);

  return (
    <>
      <Table customizedCode="wlpglpt3" dataSet={ds} columns={columns} />
    </>
  );
};
