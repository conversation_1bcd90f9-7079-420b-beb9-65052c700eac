import { getCurrentOrganizationId } from 'utils/utils';
import { Host } from '@/utils/config';
import request from 'utils/request';

// const API = `/yp-mes-38510/v1/${getCurrentOrganizationId()}`;
const API = `${Host}/v1/${getCurrentOrganizationId()}`;

// 绑定
export async function bind(params) {
  return request(`${API}/hme-entry-exit-info/batch/bind`, {
    method: 'POST',
    body: params,
  });
}

// 批量出站
export async function outbound(params) {
  return request(`${API}/hme-entry-exit-info/batch/product/outbound`, {
    method: 'POST',
    body: params,
  });
}
// 批量进站
export async function inbound(params) {
  return request(`${API}/hme-entry-exit-info/batch/product/inbound`, {
    method: 'POST',
    body: params,
  });
}
// 传入
export async function inputData(params) {
  return request(`${API}/hme-entry-exit-info/batch/input`, {
    method: 'POST',
    body: params,
  });
}
// 下载
export async function download() {
  return request(`${API}/hme-entry-exit-info/download/model-attr/ui`, {
    method: 'GET',
    responseType: 'blob',
  });
}
// 蓝胶码校验
export async function blueGlueCodeCheck(params) {
  return request(`${API}/hme-entry-exit-info/batch/blue/glue/code/check`, {
    method: 'POST',
    body: params,
  });
};

// 负极顶盖条码校验补录
export async function codeCheck(params) {
  return request(`${API}/hme-entry-exit-info/top/cover/code/check`, {
    method: 'POST',
    body: params,
  });
}
