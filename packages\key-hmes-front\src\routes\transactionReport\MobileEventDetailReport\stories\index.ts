/**
 * @Description: 移动事件明细报表-主表格-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-07-19 14:40:34
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.mes.event.mobileEventDetailReport';
const tenantId = getCurrentOrganizationId();

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'lastUpdateDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDateFrom`).d('最后更新时间从'),
      max: 'lastUpdateDateTo',
      required: true,
      dynamicProps: {
        defaultValue: () => {
          const d = new Date();
          return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()} 00:00:00`;
        },
      },
    },
    {
      name: 'lastUpdateDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDateTo`).d('最后更新时间至'),
      min: 'lastUpdateDateFrom',
      required: true,
      defaultValue: new Date(),
    },
    {
      name: 'eventId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'dtlFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dtlFlag`).d('状态'),
      lookupCode: 'MT.INTERFACE_STATUS',
    },
    {
      name: 'eventTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eventTypeCodeDesc`).d('事件类型'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.EVENT_TYPE`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'eventTypeId',
      bind: 'eventTypeLov.eventTypeId',
    },
    {
      name: 'businessTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessTypeCode`).d('业务类型'),
      lovCode: 'MT.COMMON.BUSINESS_TYPE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'businessTypeCode',
      bind: 'businessTypeLov.typeCode',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'transAccount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transAccount`).d('事务帐户信息'),
    },
    {
      name: 'transReasonCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transReasonCode`).d('事务原因'),
    },
    {
      name: 'eventTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.eventTimeFrom`).d('事件时间从'),
      max: 'eventTimeTo',
    },
    {
      name: 'eventTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.eventTimeTo`).d('事件时间至'),
      min: 'eventTimeFrom',
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('报错消息'),
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'locatorCode',
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'sourceSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceSiteCode`).d('来源站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'sourceSiteCode',
      bind: 'sourceSiteLov.siteCode',
    },
    {
      name: 'sourceLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceLocatorCode`).d('来源库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'sourceLocatorCode',
      bind: 'sourceLocatorLov.locatorCode',
    },
    {
      name: 'sourceOrderType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOrderType`).d('来源订单类型'),
      lookupCode: 'MT.SOURCE_ORDER_TYPE',
    },
    {
      name: 'sourceOrderLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceOrder`).d('来源订单'),
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode: ({ record }) => {
          const _sourceOrderType = record.get('sourceOrderType');
          if (_sourceOrderType === 'PO') {
            return `${BASIC.LOV_CODE_BEFORE}.PO`;
          }
          if (_sourceOrderType === 'SO') {
            return `${BASIC.LOV_CODE_BEFORE}.SO_NUMBER`;
          }
          return `${BASIC.LOV_CODE_BEFORE}.WORK_ORDER_NUM`;
        },
        disabled: ({ record }) => {
          return !record.get('sourceOrderType');
        },
      },
    },
    {
      name: 'sourceOrder',
      dynamicProps: {
        bind: ({ record }) => {
          const _sourceOrderType = record.get('sourceOrderType');
          if (_sourceOrderType === 'PO') {
            return 'sourceOrderLov.poNumber';
          }
          if (_sourceOrderType === 'SO') {
            return 'sourceOrderLov.soNumber';
          }
          return 'sourceOrderLov.workOrderNum';
        },
      },
    },
    {
      name: 'sourceOrderLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceOrderLine`).d('来源订单行'),
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode: ({ record }) => {
          if (record.get('sourceOrderType') === 'PO') {
            return `${BASIC.LOV_CODE_BEFORE}.MES.PO_LINE`;
          }
          return `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`;
        },
        disabled: ({ record }) => {
          return !record.get('sourceOrderType') ||
            record.get('sourceOrderType') === 'WO' ||
            !record.get('sourceOrder');
        },
        lovPara: ({ record }) => {
          if (record.get('sourceOrderType') === 'PO') {
            return {
              poNumber: record.get('sourceOrder'),
            }
          }
          return {
            soNumber: record.get('sourceOrder'),
          }
        },
      },
    },
    {
      name: 'sourceOrderLineNum',
      dynamicProps: {
        bind: ({ record }) => {
          return record.get('sourceOrderType') === 'PO' ? 'sourceOrderLineLov.poLineNumber' : 'sourceOrderLineLov.soLineNum'
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'createdByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'createdBy',
      bind: 'createdByLov.id',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'lastUpdatedByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最新更新人'),
      lovCode: 'MT.USER.ORG',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'lastUpdatedBy',
      bind: 'lastUpdatedByLov.id',
    },
  ],
  fields: [
    {
      name: 'eventId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
    },
    {
      name: 'eventTypeCodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCodeDesc`).d('事件类型描述'),
    },
    {
      name: 'businessTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessTypeCode`).d('业务类型编码'),
    },
    {
      name: 'businessTypeCodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessTypeCodeDesc`).d('业务类型'),
    },
    {
      name: 'dtlFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dtlFlag`).d('状态'),
      lookupCode: 'MT.INTERFACE_STATUS',
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('报错消息'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
    {
      name: 'sourceSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSiteCode`).d('来源站点'),
    },
    {
      name: 'sourceLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceLocatorCode`).d('来源库位'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('主单位数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('主计量单位'),
    },
    {
      name: 'secondaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.secondaryUomQty`).d('辅助单位数量'),
    },
    {
      name: 'secondaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomCode`).d('辅助计量单位'),
    },
    {
      name: 'eventTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.eventTime`).d('事件时间'),
    },
    {
      name: 'transAccount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transAccount`).d('事务帐户信息'),
    },
    {
      name: 'transReasonCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transReasonCode`).d('事务原因'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批条码'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器条码'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('工作单元'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线'),
    },
    {
      name: 'sourceOrderType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOrderType`).d('来源订单类型'),
      lookupCode: 'MT.SOURCE_ORDER_TYPE',
    },
    {
      name: 'sourceOrder',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOrder`).d('来源订单'),
    },
    {
      name: 'sourceOrderLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOrderLineNum`).d('来源订单行'),
    },
    {
      name: 'sourceOrderCompLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOrderCompLineNum`).d('来源订单组件行'),
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据号'),
    },
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('单据行号'),
    },
    {
      name: 'instructionNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionNum`).d('指令'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商'),
    },
    {
      name: 'supplierSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteCode`).d('供应商地点'),
    },
    {
      name: 'customerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户'),
    },
    {
      name: 'customerSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerSiteCode`).d('客户地点'),
    },
    {
      name: 'operationSequence',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationSequence`).d('组件工序代码'),
    },
    {
      name: 'rsnum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rsnum`).d('预留号'),
    },
    {
      name: 'rspos',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rspos`).d('预留项目行号'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者'),
    },
    {
      name: 'ownerLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerLineCode`).d('目标所有者行编码'),
    },
    {
      name: 'sourceOwnerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOwnerType`).d('来源所有者类型'),
    },
    {
      name: 'sourceOwnerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOwnerCode`).d('来源所有者'),
    },
    {
      name: 'sourceOwnerLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceOwnerLineCode`).d('来源所有者行编码'),
    },
    {
      name: 'reservedObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectType`).d('预留类型'),
    },
    {
      name: 'reservedObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象'),
    },
    {
      name: 'sourceReservedObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceReservedObjectType`).d('来源预留类型'),
    },
    {
      name: 'sourceReservedObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceReservedObjectCode`).d('来源预留对象'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
    {
      name: 'attribute1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute1`).d('扩展字段1'),
    },
    {
      name: 'attribute2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute2`).d('扩展字段2'),
    },
    {
      name: 'attribute3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute3`).d('扩展字段3'),
    },
    {
      name: 'attribute4',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute4`).d('扩展字段4'),
    },
    {
      name: 'attribute5',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute5`).d('扩展字段5'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-material-event-info/query/ui`,
        method: 'GET',
      };
    },
  },
});
