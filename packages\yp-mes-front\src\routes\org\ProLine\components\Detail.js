/**
 * @Description: 站点维护详情页组件
 * @Author: <<EMAIL>>
 * @Date: 2021-02-03 13:32:42
 * @LastEditTime: 2023-05-18 11:32:00
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useEffect, useImperativeHandle, forwardRef, useState } from 'react';
import { DataSet, TextField, Form, Switch, Select, IntlField, Tabs } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { isEmpty } from 'lodash';
import notification from 'utils/notification';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { detailDS, operationTableDS } from '../stores/ProLineDS';
import BasicInfoTab from './BasicInfoTab';
import PlanInfoTab from './PlanInfoTab';
import ProduceInfoTab from './ProduceInfoTab';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const modelPrompt = 'tarzan.model.org.prodLine';

const Detail = (props, ref) => {
  const { canEdit, kid, columns = 1, componentType, customizeForm } = props;
  const [activeKey, setActiveKey] = useState('basic');
  const [defaultMethodvalue, setDefaultMethodvalue] = useState('');
  const operationTableDs = useMemo(() => new DataSet(operationTableDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          operation: operationTableDs,
        },
      }),
    [],
  );

  useEffect(() => {
    if (kid !== 'create') {
      detailQuery(kid);
    }
  }, [kid]);

  const detailQuery = id => {
    detailDs.setQueryParameter('prodLineId', id);
    detailDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.PROLINE_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.PROLINE`)
    detailDs.query().then(res => {
      if (res && res.rows) {
        const {
          rows: { productionLine, prodLineManufacturing },
        } = res;
        setDefaultMethodvalue((prodLineManufacturing || {}).dispatchMethod);
        const { prodLineCode } = productionLine;
        operationTableDs.setQueryParameter('payload', {
          prodLineId: id,
          prodLineCode,
        });
        operationTableDs.query();
      }
    });
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    submit: async () => {
      detailDs.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验
      operationTableDs.forEach(record => {
        // 强制修改DataSet,否则新建的数据不会校验
        record.set({ nowDate: new Date().getTime() });
      });
      const validateDs = await detailDs.validate();
      const validateTableDs = await operationTableDs.validate();
      if (validateDs && validateTableDs) {
        let success = false;
        let newKid = '';
        await detailDs.submit().then(res => {
          const { rows = [] } = res || {};
          if (!isEmpty(rows) && rows[0].success) {
            notification.success({});
            success = true;
            newKid = res.rows[0].rows;
            detailQuery(newKid);
          }
        });
        return { success, newKid };
      } if (!validateTableDs) {
        setActiveKey('produce');
      }
      return { success: false };
    },
    reset: () => {
      detailQuery(kid);
    },
  }));

  const customizeCode = useMemo(() => {
    switch (componentType) {
      case 'PROLINE':
        return `${BASIC.CUSZ_CODE_BEFORE}.PROLINE_DETAIL.BASIC`;
      case 'ORG_RELATION':
        return `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.PROLINE`;
      default:
        console.error('父组件传入类型错误！')
        break;
    }
  }, [componentType]);

  const handleChangeProLineName = value => {
    detailDs.current.set('prodLineType', value);
  };

  const handleChangeTab = value => {
    setActiveKey(value);
  };

  const childProps = {
    canEdit,
    columns,
  };

  return (
    <>
      <Collapse bordered={false} defaultActiveKey={['proLineInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.proLineInfo`).d('生产线信息')}
          key="proLineInfo"
          dataSet={detailDs}
        >
          {customizeForm(
            {
              code: customizeCode,
            },
            <Form
              disabled={!canEdit}
              dataSet={detailDs}
              columns={columns}
              labelLayout="horizontal"
              labelWidth={112}
            >
              <TextField
                name="prodLineCode"
                onInput={e => {
                  const { value } = e.target;
                  operationTableDs.forEach(record => {
                    record.set('prodLineCode', value);
                  });
                }}
              />
              <IntlField
                name="prodLineName"
                modalProps={{
                  title: intl.get(`${modelPrompt}.prodLineName`).d('生产线短描述'),
                }}
              />
              <IntlField
                name="description"
                modalProps={{
                  title: intl.get(`${modelPrompt}.description`).d('生产线长描述'),
                }}
              />
              <Select name="prodLineType" onChange={handleChangeProLineName} />
              <Switch name="enableFlag" />
            </Form>,
          )}
        </Panel>
      </Collapse>
      <Tabs activeKey={activeKey} onChange={handleChangeTab}>
        <TabPane tab={intl.get(`${modelPrompt}.basicInfo`).d('基础属性')} key="basic" forceRender>
          <BasicInfoTab ds={detailDs} focus={activeKey !== 'basic'} {...childProps} />
        </TabPane>
        <TabPane tab={intl.get(`${modelPrompt}.planInfo`).d('计划属性')} key="plan" forceRender>
          <PlanInfoTab ds={detailDs} focus={activeKey !== 'plan'} {...childProps} />
        </TabPane>
        <TabPane
          tab={intl.get(`${modelPrompt}.produceInfo`).d('生产属性')}
          key="produce"
          forceRender
        >
          <ProduceInfoTab
            ds={detailDs}
            tableDs={operationTableDs}
            defaultMethodvalue={defaultMethodvalue}
            focus={activeKey !== 'produce'}
            {...childProps}
          />
        </TabPane>
      </Tabs>
    </>
  );
};

export default withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.PROLINE_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.PROLINE`],
})(forwardRef(Detail));
