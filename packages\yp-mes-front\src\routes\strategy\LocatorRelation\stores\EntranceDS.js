/**
 * @feature 物料库位关系维护-表格列表以及查询的DS
 * @date 2021-12-14
 * <AUTHOR>
 */
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';

const modelPrompt = 'tarzan.strategy.locatorRelation';
const tenantId = getCurrentOrganizationId();

// 所有者类型下拉框数据源
const ownerTypeOptionDs = () =>
  new DataSet({
    autoQuery: true,
    dataKey: 'rows',
    paging: false,
    lang: getCurrentLanguage(),
    noCache: true,
    transport: {
      read: () => {
        return {
          url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/limit-group/type`,
          method: 'POST',
          data: { typeGroup: 'OWNER_TYPE', module: 'GENERAL', tenantId },
          transformResponse: val => {
            const data = JSON.parse(val);
            data.rows.push({
              description: intl.get(`${modelPrompt}.owner`).d('自有'),
              typeCode: 'OWNER',
              typeGroup: 'OWNER_TYPE',
            });
            return {
              ...data,
            };
          },
        };
      },
    },
  });

/**
 * 列表页
 */
const entranceDS = () => ({
  primaryKey: 'locatorMaterialRelId',
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  queryFields: [
    {
      name: 'siteObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteObject.siteId',
    },
    {
      name: 'materialObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialObject.materialId',
    },
    {
      name: 'locatorObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            locatorCategory: ['INVENTORY', 'LOCATION'],
          };
        },
      },
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'locatorObject.locatorId',
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
      options: ownerTypeOptionDs(),
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'ownerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        lovCode({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'MT.MODEL.CUSTOMER';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'MT.MODEL.SUPPLIER';
            case 'OI':
              return `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`;
            default:
              return 'MT.MES.EMPTY';
          }
        },
        textField({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'customerCode';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'supplierCode';
            case 'OI':
              return 'soNumContent';
            default:
              return 'noData';
          }
        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI', 'OD'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerLov.customerId',
      dynamicProps: {
        bind({ record }) {
          switch (record.get('ownerType')) {
            case 'CI':
            case 'IIC':
              return 'ownerLov.customerId';
            case 'SI':
            case 'IIS':
            case 'OD':
              return 'ownerLov.supplierId';
            case 'OI':
              return 'ownerLov.soLineId';
            default:
              return 'ownerLov.customerId';
          }
        },
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      textField: 'meaning',
      valueField: 'value',
      lookupCode: 'MT.ENABLE_FLAG',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorDesc`).d('库位描述'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
    {
      name: 'ownerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerName`).d('所有者描述'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.enable`).d('启用') },
          { value: 'N', key: intl.get(`tarzan.common.label.disable`).d('禁用') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  transport: {
    read: ({ data }) => {
    console.log(BASIC)
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-locator-material-rel/query/ui`,
        method: 'GET',
        data: {
          ...data,
          ownerType: data.ownerType === 'OWNER' ? '' : data.ownerType,
        },
        transformResponse: val => {
          const resData = JSON.parse(val);
          if (resData.rows) {
            (resData.rows.content || []).forEach(element => {
              if (!element.ownerType && !element.ownerTypeDesc) {
                // eslint-disable-next-line no-param-reassign
                element.ownerType = 'OWNER';
                // eslint-disable-next-line no-param-reassign
                element.ownerTypeDesc = intl.get(`${modelPrompt}.owner`).d('自有');
              }
            });
          }
          return {
            ...resData,
          };
        },
      };
    },
  },
});

export { entranceDS };
