/**
 * @feature 入库单查询功能-头行和抽屉的DS
 * @date 2022-01-03
 * <AUTHOR>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import moment from 'moment'

const modelPrompt = 'tarzan.inbound.inboundOrderQuery';
const tenantId = getCurrentOrganizationId();

const headerTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'instructionDocId',
  autoLocateFirst: true,
  queryFields: [
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteObj.siteId',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('入库单号'),
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_PRODUCT_COMPLETE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialObj.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'workOrderObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.WORK_ORDER`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'workOrderId',
      type: FieldType.number,
      bind: 'workOrderObj.workOrderId',
    },
    {
      name: 'productionLineObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.productionLine`).d('生产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'prodLineId',
      type: FieldType.number,
      bind: 'productionLineObj.prodLineId',
    },
    {
      name: 'creationTimeFrom',
      type: FieldType.dateTime,
      max: 'creationTimeTo',
      label: intl.get(`${modelPrompt}.creationTimeFrom`).d('创建时间从'),
      defaultValue: moment(moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'creationTimeTo',
      type: FieldType.dateTime,
      min: 'creationTimeFrom',
      label: intl.get(`${modelPrompt}.creationTimeTo`).d('创建时间至'),
      defaultValue: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
    },
    {
      name: 'materialIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentification`).d('物料批标识'),
    },
    {
      name: 'containerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerIdentification`).d('容器标识'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'fromLocatorIdObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromLocator`).d('来源仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'fromLocatorId',
      type: FieldType.number,
      bind: 'fromLocatorIdObj.locatorId',
    },
    {
      name: 'toLocatorIdObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toLocator`).d('目标仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'toLocatorId',
      type: FieldType.number,
      bind: 'toLocatorIdObj.locatorId',
    },
  ],
  fields: [
    {
      name: 'instructionDocId',
      type: FieldType.number,
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('入库单号'),
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'creationTimes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationTimes`).d('创建时间'),
    },
    {
      name: 'toLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toLocator`).d('目标仓库'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-product-storage-document/head/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.HEAD`,
        method: 'GET',
      };
    },
  },
  record: {
    dynamicProps: {
      // 关闭类型的单据不可选择
      selectable: record => !['CANCEL', 'CLOSED'].includes(record?.get('instructionDocStatusCode')),
    },
  },
});

const lineTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'instructionDocLineId',
  fields: [
    {
      name: 'instructionDocLineId',
      type: FieldType.number,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'identifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
    },
    {
      name: 'lineStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineStatusDesc`).d('行状态'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.quantity`).d('需执行数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'firstExecutedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.actualQty`).d('已接收数量'),
    },
    {
      name: 'secondExecutedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumActualQty`).d('已上架数量'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('目标仓库'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionLine`).d('生产线'),
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNumber`).d('销售订单号'),
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNumber`).d('销售订单行号'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-storage-document/line/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.INBOUND_ORDER_LIST.LINE`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && !datas.success) {
            if (datas.message) {
              notification.error({ message: datas.message });
            }
            return {
              rows: [],
            };
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
});

const drawerTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: '',
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentification`).d('物料批标识'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentificationStatus`).d('物料批状态'),
    },
    {
      name: 'code',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'toLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toLocatorCode`).d('上架库位'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveDate`).d('接收时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveBy`).d('接收人'),
    },
    {
      name: 'shelvesCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shelvesCreationDate`).d('上架时间'),
    },
    {
      name: 'shelvesCreatedBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shelvesCreatedBy`).d('上架人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-storage-document/detail/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { headerTableDS, lineTableDS, drawerTableDS };
