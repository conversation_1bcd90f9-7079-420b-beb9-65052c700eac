/**
 * @Description: 在制品报表ds
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 16:48:18
 * @LastEditTime: 2022-07-25 17:34:03
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.processReport.model.processReport';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  pageSize: 10,
  paging: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: ():any => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-eo-step-wip-workings/wip/ui`,
        method: 'get',
        // data: currentData,
      };
    },
  },
  queryFields: [
    {
      name: 'identificationStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identificationStr`).d('条码号'),
      multiple: ',',
    },
    {
      name: 'workOrderNumStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNumStr`).d('工单编码'),
      multiple: ',',
    },
    {
      name: 'productionLineObj',
      type: FieldType.object,
      lovCode: 'MT.MODEL.PRODLINE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.productionLineObj`).d('生产线编码'),
      multiple: true,
    },
    {
      name: 'productionLineIdStr',
      bind: 'productionLineObj.prodLineId',
    },
    {
      name: 'equipmentObj',
      type: FieldType.object,
      lovCode: 'MT.MODEL.EQUIPMENT.NO.DISCARD',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.equipmentObj`).d('设备编码'),
      multiple: true,
    },
    {
      name: 'equipmentIdStr',
      bind: 'equipmentObj.equipmentId',
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      lovCode: 'MT.MATERIAL.PERMISSION',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
      multiple: true,
    },
    {
      name: 'materialIdStr',
      bind: 'materialObj.materialId',
    },
    {
      name: 'wipStatusStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wipStatusStr`).d('在制状态'),
      lookupCode:'HME.EO_STEP_STATUS',
      multiple: true,
    },
  ],
  fields: [
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.identification`).d('条码号'),
      type: FieldType.string,
    },
    {
      name: 'qty',
      label: intl.get(`${modelPrompt}.qty`).d('在制数量'),
      type: FieldType.string,
    },
    {
      name: 'wipStatusMeaning',
      label: intl.get(`${modelPrompt}.wipStatusMeaning`).d('在制状态'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      type: FieldType.string,
    },
    {
      name: 'operationName',
      label: intl.get(`${modelPrompt}.operationNames`).d('工艺名称'),
      type: FieldType.string,
    },
    {
      name: 'prodLineCode',
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
      type: FieldType.string,
    },
    {
      name: 'prodLineName',
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      type: FieldType.string,
    },
    {
      name: 'equipmentCode',
      label: intl.get(`${modelPrompt}.equipmentCode`).d('设备编码'),
      type: FieldType.string,
    },
    {
      name: 'equipmentName',
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),
      type: FieldType.string,
    },
  ],
});

export { tableDS };
