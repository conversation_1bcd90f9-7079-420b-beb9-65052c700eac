/*
 * @Author: 47844 <EMAIL>
 * @Date: 2024-12-11 09:37:28
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2024-12-11 10:06:51
 * @FilePath: \yp-mes-front\packages\yp-mes-front\src\routes\MaterialMovementTailReport\TransactionReportPlatform\services\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @Description: 事务尾数报表平台-services
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 17:45:04
 * @LastEditTime: 2023-03-23 14:02:33
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 事务尾数报表平台-手工回传按钮
export function TransBack() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-trans-mantissa-ifaces/trans/back/ui`,
    method: 'POST',
  };
}
// 手工废除
export function TransDelete() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-trans-mantissa-ifaces/trans/delete/ui `,
    method: 'POST',
  };
}
