import { DataSet } from 'choerodon-ui/pro';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const numberListDS = () =>
    new DataSet({
        autoCreate: true,
        autoLocateFirst: true,
        dataKey: 'rows',
        fields: [
            { name: 'dataValue' },
            {
                name: 'multipleValue',
                dynamicProps: {
                    range: ({ record }) => {
                        return record.get('valueType') === 'section' ? ['leftValue', 'rightValue'] : false;
                    },
                },
            },
            {
                name: 'leftChar',
                type: FieldType.string,
                defaultValue: '[',
            },
            {
                name: 'leftValue',
                type: FieldType.string,
                dynamicProps: {
                    bind: ({ record }) => {
                        if (record.get('valueType') === 'section') {
                            return 'multipleValue.leftValue';
                        }
                    },
                },
            },
            {
                name: 'rightChar',
                type: FieldType.string,
                defaultValue: ']',
            },
            {
                name: 'rightValue',
                type: FieldType.string,
                dynamicProps: {
                    bind: ({ record }) => {
                        if (record.get('valueType') === 'section') {
                            return 'multipleValue.rightValue';
                        }
                    },
                },
            },
            {
                name: 'valueType',
                type: FieldType.string,
                defaultValue: 'single',
            },
            {
                name: 'valueShow',
                type: FieldType.string,
            },
        ],
        events: {
            load: ({ dataSet }) => {
                if (!dataSet.length) {
                    dataSet.loadData([{ leftChar: '(', rightChar: ')', valueType: 'single' }]);
                    return;
                }
                dataSet.forEach(record => {
                    if (record?.get('valueType') === 'section') {
                        record?.set('multipleValue', {
                            leftValue: record?.get('leftValue'),
                            rightValue: record?.get('rightValue'),
                        });
                    } else {
                        record?.set('multipleValue', record?.get('dataValue'));
                    }
                });
            },
            update: ({ record, name }) => {
                switch (name) {
                    case 'valueType':
                    case 'leftValue':
                    case 'rightValue':
                    case 'leftChar':
                    case 'rightChar':
                    case 'multipleValue':
                        handleUpdateRangeValue(record, name);
                        break;
                    default:
                        break;
                }
            },
        },
    });

const handleUpdateRangeValue = (record, name) => {
    if (record.get('valueType') === 'section') {
        if (!record.get('leftChar')) {
            record.set('leftChar', '(');
        }
        if (!record.get('rightChar')) {
            record.set('rightChar', ')');
        }
        const leftValue = record.get('leftValue') || '';
        const rightValue = record.get('rightValue') || '';
        const leftChar = record.get('leftChar') === '(' ? '<' : '≤';
        const rightChar = record.get('rightChar') === ')' ? '<' : '≤';
        record.set('valueShow', `${leftValue}${leftChar}X${rightChar}${rightValue}`);
    } else if (name === 'valueType') {
        record.set('multipleValue', undefined);
    }
};
export default numberListDS