import { TARZAN_REPORT } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import moment from 'moment';

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-41300`;
const modelPrompt = 'tarzan.receive.rawMaterialBarcodeTraceabilityReport';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'traceRelId',
    paging: true,
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'parentIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.parentIdentification`).d('产品条码'),
      },
      {
        name: 'parentMaterialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.parentMaterialCode`).d('产品物料编码'),
      },
      {
        name: 'parentMaterialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.parentMaterialName`).d('产品物料描述'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('原材料条码'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('原材料物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('原材料物料描述'),
      },
      {
        name: 'materialLotIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotIdentification`).d('原材料批次'),
      },
      {
        name: 'materialLotSupplier',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotSupplier`).d('原材料供应商批次'),
      },
      {
        name: 'assembleQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.assembleQty`).d('装配数量'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('装配工序'),
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('工序描述'),
      },
      {
        name: 'workcellCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellCode`).d('装配工作单元'),
      },
      {
        name: 'workcellName',
        type: 'string',
        label: intl.get(`${modelPrompt}.workcellName`).d('工作单元描述'),
      },
      {
        name: 'loginName',
        type: 'string',
        label: intl.get(`${modelPrompt}.loginName`).d('操作人'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('记录时间'),
      },
    ],
    queryFields: [
      {
        name: 'parentIdentifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.parentIdentifications`).d('产品条码'),
      },
      {
        name: 'identifications',
        type: 'string',
        label: intl.get(`${modelPrompt}.identifications`).d('原材料条码'),
      },
      {
        name: 'parentTraceLevels',
        lookupCode: 'HME.TRACE_LEVEL',
        label: intl.get(`${modelPrompt}.parentTraceLevels`).d('产品类型'),
        type: 'string',
        multiple: true,
        // dynamicProps: {
        //   required: ({ record }) => {
        //     return !!record.get('parentIdentifications')
        //   },
        // },
      },
      {
        name: 'materialLotIdentification',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotIdentification`).d('原材料批次'),
      },
      {
        name: 'materialLotSupplier',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotSupplier`).d('原材料供应商批次'),
      },
      {
        name: 'parentMaterialCodes',
        type: 'string',
        multiple: true,
        label: intl.get(`${modelPrompt}.parentMaterialCodes`).d('产品物料编码'),
      },
      {
        name: 'materialCodes',
        multiple: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCodes`).d('原材料物料编码'),
      },
      {
        name: 'bindDateFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.bindDateFrom`).d('绑定时间从'),
        // required: true,
        max: 'bindDateTo',
        defaultValue: moment().subtract(2, 'days').format('YYYY-MM-DD 00:00:00'),
        validator: (value, _, record) => {
          const bindDateTo = record.get('bindDateTo');
          const threeDaysAgo = moment(bindDateTo).subtract(2, 'days').format('YYYY-MM-DD 00:00:00');
          if (value && bindDateTo) {
            if (moment(value).isBefore(moment(threeDaysAgo))) {
              return intl.get(`${modelPrompt}.bindDateFromError`).d('时间间隔超过3天，请重新选择');
            }
            return true;
          }
          return true;
        },
        dynamicProps: {
          required: ({ record }) => {
            if (record.get('parentIdentifications') || record.get('identifications')) {
              return false
            } else {
              return true
            }
          }
        }
      },
      {
        name: 'bindDateTo',
        type: 'dateTime',
        min: 'bindDateFrom',
        label: intl.get(`${modelPrompt}.bindDateTo`).d('绑定时间至'),
        required: true,
        defaultValue: moment().format('YYYY-MM-DD 23:59:59'),
        validator: (value, _, record) => {
          const bindDateFrom = record.get('bindDateFrom');
          const threeDaysAgo = moment(value).subtract(2, 'days').format('YYYY-MM-DD 00:00:00');
          if (bindDateFrom && value) {
            if (moment(bindDateFrom).isBefore(moment(threeDaysAgo))) {
              return intl.get(`${modelPrompt}.bindDateFromError`).d('时间间隔超过3天，请重新选择');
            }
            return true;
          }
          return true;
        },
        dynamicProps: {
          required: ({ record }) => {
            if (record.get('parentIdentifications') || record.get('identifications')) {
              return false
            } else {
              return true
            }
          }
        }
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${TARZAN_REPORT}/v1/${tenantId}/hme-trace-rels/report/ui`,
          method: 'POST',
        };
      },
    },
  };
};



export { tableDS };
