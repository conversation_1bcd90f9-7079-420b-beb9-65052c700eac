import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  routes: [
    // // 领退料工作台-新
    // {
    //   path: '/public/hmes/receive/receive-return-new-oa',
    //   authorized: true,
    //   routes: [
    //     {
    //       title: '审批流程URL创建',
    //       path: `/public/hmes/receive/receive-return-new-oa/list/`,
    //       component: '@/routes/receive/ReceiveReturn',
    //       authorized: true,
    //     },
    //     {
    //       title: '审批流程URL创建',
    //       path: `/public/hmes/receive/receive-return-new-oa/list/:code`,
    //       component: '@/routes/receive/ReceiveReturn',
    //       authorized: true,
    //     },
    //   ],
    // },
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
        },
      },
    ],
  ],
});
