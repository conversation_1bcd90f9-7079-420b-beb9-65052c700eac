/**
 * @Description: 新区域维护-详情页组件
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 13:19:18
 * @LastEditTime: 2023-07-26 11:07:09
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useEffect, useImperativeHandle, forwardRef, useState } from 'react';
import { DataSet, TextField, Form, Switch, Select, IntlField, Tabs } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { isNull, isEmpty } from 'lodash';
import notification from 'utils/notification';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { DetailDS, BasicsAtttribtesDS, PlanAttributesDS } from '../stores/AreaDetailDS';
import BasicInfoTab from './BasicInfoTab';
import PlanInfoTab from './PlanInfoTab';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const modelPrompt = 'tarzan.model.org.area';

const Detail = (props, ref) => {
  const {
    canEdit,
    kid,
    columns = 1,
    detailType,
    componentType,
    customizeForm,
  } = props;
  const [activeKey, setActiveKey] = useState('basic');
  const [disabledFollow, setFollow] = useState(kid === 'create');
  const basicsAtttribtesDS = useMemo(() => new DataSet(BasicsAtttribtesDS()), []);
  const planAttributesDS = useMemo(() => new DataSet(PlanAttributesDS()), []);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...DetailDS(),
        children: {
          basicsAtttribtesDS,
          mtModAreaScheduleDTOS: planAttributesDS,
        },
      }),
    [],
  );

  useEffect(() => {
    if (kid !== 'create') {
      detailQuery(kid);
    } else if (detailType) {
      if (basicsAtttribtesDS.current) {
        basicsAtttribtesDS.current.init('areaType', detailType);
      } else {
        basicsAtttribtesDS.loadData([{ areaType: detailType }])
      }
      detailDs.current.init('areaType', detailType);
      setFollow(false);
    }
  }, [kid]);

  const detailQuery = id => {
    detailDs.setQueryParameter('areaId', id);
    detailDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.AREA_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.AREA`)
    detailDs.query().then(res => {
      basicsAtttribtesDS.loadData([res.rows]);
      planAttributesDS.loadData([
        {
          ...res.rows.mtModAreaScheduleDTO,
          followAreaId: res.rows.mtModAreaScheduleDTO.followAreaId || null,
        },
      ]);
    });
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    submit: async () => {
      basicsAtttribtesDS.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验
      planAttributesDS.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验

      const validate = await detailDs.validate();

      if (validate) {
        let success = false;
        let newKid = '';
        await detailDs.submit().then(res => {
          const { rows = [] } = res || {};
          if (!isEmpty(rows) && rows[0].success) {
            notification.success({});
            success = true;
            newKid = rows[0].rows.areaId;
            detailQuery(newKid);
          }
        });
        return { success, newKid };
      }
      return { success: false };
    },
    reset: () => {
      detailQuery(kid);
    },
  }));

  const customizeCode = useMemo(() => {
    switch (componentType) {
      case 'AREA':
        return `${BASIC.CUSZ_CODE_BEFORE}.AREA_DETAIL.BASIC`;
      case 'ORG_RELATION':
        return `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.AREA`;
      default:
        console.error('父组件传入类型错误！')
        break;
    }
  }, [componentType])

  // 区域类型下拉框改变时
  const handleChangeSiteName = value => {
    setFollow(isNull(value));
    detailDs.current.set('areaType', value);
    if (planAttributesDS.current) {
      planAttributesDS.current.set('followAreaLov', undefined);
    } else {
      planAttributesDS.loadData([{ followAreaLov: undefined }])
    }
  };

  // Tabs切换
  const handleChangeTab = value => {
    setActiveKey(value);
  };

  const childProps = {
    canEdit,
    columns,
    disabledFollow,
  };

  return (
    <>
      <Collapse bordered={false} defaultActiveKey={['information']}>
        <Panel
          header={intl.get(`${modelPrompt}.information`).d('区域信息')}
          key="information"
          dataSet={basicsAtttribtesDS}
        >
          {customizeForm(
            {
              code: customizeCode,
            },
            <Form
              disabled={!canEdit}
              dataSet={basicsAtttribtesDS}
              columns={columns}
              labelLayout="horizontal"
              labelWidth={112}
            >
              <TextField name="areaCode" />
              <IntlField
                name="areaName"
                modalProps={{
                  title: intl.get(`${modelPrompt}.areaName`).d('区域短描述'),
                }}
              />
              <IntlField
                name="description"
                modalProps={{
                  title: intl.get(`${modelPrompt}.description`).d('区域长描述'),
                }}
              />
              <Select
                name="areaType"
                disabled={kid !== 'create' || detailType}
                onChange={handleChangeSiteName}
              />
              <Select name="areaCategory" />
              <Switch name="enableFlag" />
            </Form>,
          )}
        </Panel>
      </Collapse>
      <Tabs activeKey={activeKey} onChange={handleChangeTab}>
        <TabPane tab={intl.get(`${modelPrompt}.basic`).d('基础属性')} key="basic" forceRender>
          <BasicInfoTab
            dataSet={basicsAtttribtesDS}
            focus={activeKey !== 'basic'}
            {...childProps}
          />
        </TabPane>
        <TabPane tab={intl.get(`${modelPrompt}.plan`).d('计划属性')} key="plan" forceRender>
          <PlanInfoTab dataSet={planAttributesDS} focus={activeKey !== 'plan'} {...childProps} />
        </TabPane>
      </Tabs>
    </>
  );
};

export default withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.AREA_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.AREA`],
})(forwardRef(Detail));
