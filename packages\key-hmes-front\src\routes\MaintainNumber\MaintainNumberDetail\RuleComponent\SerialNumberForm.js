import React, { Component } from 'react';
import { connect } from 'dva';
import { Form, Input, Row, Col, Select, Checkbox, InputNumber, Tooltip, Icon } from 'hzero-ui';
import {
  FORM_COL_3_LAYOUT,
  SEARCH_FORM_ROW_LAYOUT,
  DRAWER_FORM_ITEM_LAYOUT,
} from '@utils/constants';
import intl from 'utils/intl';
import { get as chainGet } from 'lodash';
import notification from 'utils/notification';
import styles from './index.module.less';

const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';

const CheckboxGroup = Checkbox.Group;
const needValidatorArray = [
  'numLowerLimit',
  'numUpperLimit',
  'numAlert',
  'numRadix',
  'numIncrement',
];
let timeout;

@connect(({ maintainNumber }) => ({
  maintainNumber,
}))
@Form.create({ fieldNameProp: null })
export default class SerialNumberForm extends Component {
  constructor(props) {
    super(props);
    props.onRef(this);
    this.state = {
      data: [],
      objectCombination: undefined, //  当前序号类型
      numCurrent:
        props.dataSource.numLevel === 'OVERALL_SERIAL_NUM'
          ? props.dataSource.numCurrent
          : undefined, //  当前序号值
      editingFlag: false, //  是否正在编辑
      defaultOptions: [
        { label: intl.get(`${modelPrompt}.rule1`).d('规则框1'), value: 1 },
        { label: intl.get(`${modelPrompt}.rule2`).d('规则框2'), value: 2 },
        { label: intl.get(`${modelPrompt}.rule3`).d('规则框3'), value: 3 },
        { label: intl.get(`${modelPrompt}.rule4`).d('规则框4'), value: 4 },
        { label: intl.get(`${modelPrompt}.rule5`).d('规则框5'), value: 5 },
        { label: intl.get(`${modelPrompt}.rule6`).d('规则框6'), value: 6 },
        { label: intl.get(`${modelPrompt}.rule7`).d('规则框7'), value: 7 },
        { label: intl.get(`${modelPrompt}.rule8`).d('规则框8'), value: 8 },
      ], //  规则框
    };
  }

  // UNSAFE_componentWillReceiveProps违背了驼峰法
  // eslint-disable-next-line
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.canEdit && !nextProps.canEdit) {
      this.setState({
        editingFlag: false,
      });
    }
    if (
      nextProps.dataSource.numLevel === 'OVERALL_SERIAL_NUM' &&
      this.state.numCurrent !== nextProps.dataSource.numCurrent
    ) {
      this.setState({
        numCurrent: nextProps.dataSource.numCurrent,
      });
    }
  }

  /**
   * 对数据进行保存的通用方法
   * @param {String} key 保存到dataSource对象的key值
   * @param {*} value key对应的值
   * @param {boolean} validatorFlag 是否进行表单校验
   */
  handleChange = (key, value, validatorFlag) => {
    const { dataSource, setUsingRuleDetail, form } = this.props;
    const { _backUpRule = {} } = dataSource;
    let objValue = value;
    if (key === 'numLevel') {
      if (value !== _backUpRule.numLevel) {
        form.setFieldsValue({
          relatedRuleSequence: undefined,
          numInc: 1,
          numLowerLimit: undefined,
          numUpperLimit: undefined,
          numAlertType: undefined,
          numAlert: undefined,
          numResetType: 'NO-RESET',
          numResetPeriod: undefined,
        });
        this.setState({
          data: [],
          objectCombination: undefined,
          numCurrent: undefined,
          editingFlag: false,
        });
      } else {
        form.setFieldsValue({
          relatedRuleSequence: _backUpRule.relatedRuleSequence,
          numInc: _backUpRule.numInc,
          numLowerLimit: _backUpRule.numLowerLimit,
          numUpperLimit: _backUpRule.numUpperLimit,
          numAlertType: _backUpRule.numAlertType,
          numAlert: _backUpRule.numAlert,
          numResetType: _backUpRule.numResetType,
          numResetPeriod: _backUpRule.numResetPeriod,
        });
        this.setState({
          data: [],
          objectCombination: _backUpRule.objectCombination,
          numCurrent: _backUpRule.numCurrent,
          editingFlag: false,
        });
      }
    }

    if (key === 'numIncrement' || key === 'numResetPeriod') {
      objValue = objValue < 1 ? 1 : objValue;
    }
    if (key === 'numResetType') {
      form.setFieldsValue({
        numResetPeriod: undefined,
      });
    }
    if (key === 'numAlertType') {
      form.setFieldsValue({
        numAlert: undefined,
      });
    }

    if (['numLowerLimit', 'numUpperLimit', 'numIncrement'].indexOf('key') > -1) {
      if (objValue > 0) {
        objValue = `${objValue}`.replace(/\b(0+)/gi, '');
      }
      if (objValue && objValue - 0 === 0) {
        objValue = '0';
      }
    }

    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }

    timeout = setTimeout(() => {
      const usingRuleDetail = {
        ...dataSource,
        ...form.getFieldsValue(),
        [key]: objValue,
      };
      setUsingRuleDetail(usingRuleDetail);

      if (validatorFlag) {
        const valiArray = needValidatorArray
          .concat([value])
          .filter(v => needValidatorArray.includes(v) && ![value].includes(v));
        form.validateFields(valiArray, { force: true });
      }
    }, 300);
  };

  //  序列号下限的验证
  lowerValidator = (_, value, callback) => {
    callback();
  };

  //  序列号上限的验证
  upperValidator = (_, value, callback) => {
    const { form } = this.props;
    const numLowerLimit = form.getFieldValue('numLowerLimit');
    if (value && numLowerLimit) {
      if (this.getRadix(value) < this.getRadix(numLowerLimit)) {
        callback(
          intl.get('tarzan.mes.maintainNumber.validation.numUpperLimit').d('序列号上限应大于下限'),
        );
      }
    }
    callback();
  };

  //  号段预警号校验
  alertValidator = (_, value, callback) => {
    const { form } = this.props;
    const numAlertType = form.getFieldValue('numAlertType');
    const numLowerLimit = form.getFieldValue('numLowerLimit');
    const numUpperLimit = form.getFieldValue('numUpperLimit');
    if (numAlertType && value) {
      if (numAlertType === 'NUMBER') {
        if (isNaN(this.getRadix(value))) {
          callback(
            intl
              .get('tarzan.mes.maintainNumber.validation.BaseNecessary')
              .d('请输入符合进制的数字'),
          );
        } else if (numLowerLimit && numUpperLimit) {
          if (this.getRadix(value) <= this.getRadix(numLowerLimit)) {
            callback(
              intl
                .get('tarzan.mes.maintainNumber.validation.numAlertTypeBig')
                .d('预警序列号应大于序列号下限'),
            );
          } else if (this.getRadix(value) > this.getRadix(numUpperLimit)) {
            callback(
              intl
                .get('tarzan.mes.maintainNumber.validation.numAlertTypeSmall')
                .d('预警序列号应小于等于序列号上限'),
            );
          }
        }
      } else if (numAlertType === 'PERCENTAGE') {
        const pattern = new RegExp('[0-9]+');
        const pattern1 = new RegExp('[a-zA-Z]+');
        if (
          pattern.test(value) &&
          !pattern1.test(value) &&
          !isNaN(parseInt(value, 10)) &&
          parseInt(value, 10) > 0 &&
          parseInt(value, 10) <= 100
        ) {
          callback();
        } else {
          callback(
            intl
              .get('tarzan.mes.maintainNumber.validation.lessThanZero')
              .d('号段预警值需大于0%小于等于100%'),
          );
        }
      }
      callback();
    }
    callback();
  };

  //  号段增量校验
  upperThanValidator = (_, value, callback) => {
    const { form } = this.props;
    const numLowerLimit = form.getFieldValue('numLowerLimit');
    const numUpperLimit = form.getFieldValue('numUpperLimit');
    if (value && numLowerLimit && numUpperLimit) {
      if (this.getRadix(value) + this.getRadix(numLowerLimit) >= this.getRadix(numUpperLimit)) {
        callback(
          intl
            .get('tarzan.mes.maintainNumber.validation.numInc')
            .d('序列号下限+号段增量应小于序列号上限'),
        );
      }
    }
    callback();
  };

  //  验证进制，无返回值则不满足进制，有返回值，返回值为十进制
  getRadix = value => {
    const { form } = this.props;
    const numRadix = form.getFieldValue('numRadix');
    let reg;
    switch (numRadix) {
      case 'DECIMAL':
        reg = /[^0-9]/;
        return reg.test(value) ? NaN : Number.parseInt(value, 10);
      case 'BINARY':
        reg = /[^01]/;
        return reg.test(value) ? NaN : Number.parseInt(value, 2);
      case 'OCTAL':
        reg = /[^0-8]/;
        return reg.test(value) ? NaN : Number.parseInt(value, 8);
      case 'HEX':
        reg = /[^0-9a-fA-F]/;
        return reg.test(value) ? NaN : Number.parseInt(value, 16);
      case 'HEXADECIMAL':
        reg = /[^0-9a-zA-Z]/;
        return reg.test(value) ? NaN : Number.parseInt(value, 36);
      default:
        break;
    }
  };

  //  搜索当前序列号类型
  handleSearch = value => {
    const { ruleId, dataSource } = this.props;
    const that = this;
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }

    function fake() {
      that.props
        .dispatch({
          type: 'maintainNumber/fetchNumCurrentInfo',
          payload: {
            ruleId,
            numLevel: dataSource.numLevel,
            objectCombination: value,
          },
        })
        .then(res => {
          if (res) {
            const newArray = res.rows;
            if (value && !newArray.some(item => item.objectCombination === value)) {
              newArray.unshift({
                ruleId,
                numLevel: dataSource.numLevel,
                objectCombination: value,
                numCurrent: undefined,
              });
            }
            that.setState({
              data: newArray,
            });
          }
        });
    }
    timeout = setTimeout(fake, 300);
  };

  //  修改当前序列号类型
  handleChangeObjectCombination = (val, values) => {
    if (val) {
      this.setState({
        editingFlag: false,
        numCurrent: values.props.data.numCurrent,
        objectCombination: val,
      });
    } else {
      this.setState({
        editingFlag: false,
        numCurrent: undefined,
        objectCombination: undefined,
      });
    }
  };

  //  重新获取当前序列号
  onReload = () => {
    const { dispatch, ruleId, focusInputKey, form } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (!err) {
        const { objectCombination } = this.state;
        const { numLowerLimit, numRadix } = fieldsValue;

        this.setState({
          editingFlag: false,
        });

        const keyId = ruleId;
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }

        timeout = setTimeout(() => {
          dispatch({
            type: 'maintainNumber/fetchNumCurrentInfo',
            payload: {
              ruleId: keyId,
              ruleSequence: focusInputKey + 1,
              numLowerLimit,
              objectCombination,
              numRadix,
            },
          }).then(res => {
            if (res) {
              const rows = chainGet(res, 'rows', Number);
              this.setState({
                numCurrent: rows || undefined,
              });
            }
          });
        }, 200);
      }
    });
  };

  /**
   * 修改当前序列号的编辑状态
   */
  handleEditNumCurrent = () => {
    const { editingFlag, numCurrent } = this.state;
    const {
      dataSource: { numLevel, numLowerLimit },
    } = this.props;
    if (numLevel === 'SPECIFIC_OBJECT_NUM' && !numCurrent) {
      this.setState({
        numCurrent: numLowerLimit,
      });
    }
    this.setState({
      editingFlag: !editingFlag,
    });
  };

  //  保存当前序列号
  handleSaveNumCurrent = () => {
    const { numCurrent, objectCombination, data } = this.state;
    const { dispatch, ruleId, dataSource, form, focusInputKey } = this.props;
    const { numLevel } = dataSource;
    const keyId = ruleId;
    const that = this;
    form.validateFields((error, values) => {
      if (!error) {
        if (!numCurrent) {
          notification.error({
            message: intl
              .get('tarzan.mes.maintainNumber.message.numCurrentNeed')
              .d('当前序列号不允许为空'),
          });
          return;
        }
        if (isNaN(this.getRadix(numCurrent))) {
          notification.error({
            message: intl
              .get('tarzan.mes.maintainNumber.message.BaseNecessary')
              .d('当前序列号不满足号段进制'),
          });
          return;
        }
        if (this.getRadix(numCurrent) < this.getRadix(values.numLowerLimit)) {
          notification.error({
            message: intl
              .get('tarzan.mes.maintainNumber.message.bigger')
              .d('当前序列号应大于序列号下限'),
          });
          return;
        }
        if (this.getRadix(numCurrent) > this.getRadix(values.numUpperLimit)) {
          notification.error({
            message: intl
              .get('tarzan.mes.maintainNumber.message.smaller')
              .d('当前序列号应小于序列号上限'),
          });
          return;
        }

        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }

        // eslint会让把这段代码单独剥离出来
        /* eslint-disable */
        function saveNumCurrent() {
          dispatch({
            type: 'maintainNumber/updateNumCurrent',
            payload: {
              ruleId: keyId,
              ruleSequence: focusInputKey + 1,
              objectCombination,
              numLowerLimit: values.numLowerLimit,
              numCurrent,
              numRadix: values.numRadix,
            },
          }).then(async res => {
            if (res) {
              notification.success();
              if (numLevel === 'OVERALL_SERIAL_NUM') {
                //  当规则为全局流水对象时，保存后需要更新DataSource中的numCurrent数据
                await that.handleChange('numCurrent', numCurrent);
              }
              const newData = data.map(item => {
                if (item.objectCombination === objectCombination) {
                  return {
                    ...item,
                    numCurrent,
                  };
                }
                return item;
              });
              that.setState({
                editingFlag: false,
                data: newData,
              });
            }
          });
        }

        timeout = setTimeout(saveNumCurrent, 500);
      }
    });
  };

  //  修改当前序列号
  handleChangeNumCurrent = e => {
    this.setState({
      numCurrent: e.target.value,
    });
  };

  render() {
    const { editingFlag, numCurrent, objectCombination } = this.state;
    const {
      form,
      ruleId,
      canEdit,
      dataSource,
      rulesList,
      maintainNumber: {
        numberLevelList = [],
        numAlertTypeList = [],
        numRadixList = [],
        numResetTypeList = [],
        maintainNumberDetail = {},
        userRole = 'N',
      },
    } = this.props;
    const { initialFlag = 'N' } = maintainNumberDetail;
    const { getFieldDecorator } = form;
    const {
      numLevel,
      relatedRuleSequence,
      numLowerLimit,
      numUpperLimit,
      numLengthLimit,
      numAlertType,
      numAlert,
      numRadix,
      numIncrement,
      numResetType,
      numResetPeriod,
      numrangeRuleId,
      _backUpRule = {},
    } = dataSource;
    const _backUpNumLevel = _backUpRule.numLevel;
    const reloadIconShowFlag = () => {
      if (!numLevel) {
        return false;
      }
      return !(
        (numLevel === 'OVERALL_SERIAL_NUM' && !numrangeRuleId) ||
        (numLevel === 'SPECIFIC_OBJECT_NUM' && !objectCombination)
      );
    };
    //  特定对象关联框
    const plainOptions = this.state.defaultOptions.map((item, index) => {
      const disabled =
        !canEdit ||
        !(rulesList[index] && ['5', '6'].includes(rulesList[index].numRule)) ||
        numLevel !== 'SPECIFIC_OBJECT_NUM';
      return {
        ...item,
        disabled,
      };
    });

    function getFlag() {
      if (ruleId === 'create') {
        return false;
      } else {
        return canEdit && editingFlag;
      }
    }

    //  全局流水下的当前序列号canEdit flag
    const overAllNumCurrent = getFlag();
    const options = this.state.data.map(d => (
      <Select.Option
        key={d.objectCombination}
        data={{
          keyId: d.keyId,
          numCurrent: d.numCurrent,
          numLevel: d.numLevel,
          objectCombination: d.objectCombination,
        }}
      >
        {d.objectCombination}
      </Select.Option>
    ));

    return (
      <>
        <Row {...SEARCH_FORM_ROW_LAYOUT}>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.numLevel`).d('序列号层级')}
            >
              {getFieldDecorator('numLevel', {
                initialValue: numLevel,
                rules: [
                  {
                    required: true,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: intl.get(`${modelPrompt}.numLevel`).d('序列号层级'),
                    }),
                  },
                ],
              })(
                <Select
                  style={{ width: '100%' }}
                  disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                  onSelect={value => {
                    this.handleChange('numLevel', value);
                  }}
                >
                  {numberLevelList instanceof Array &&
                    numberLevelList.length !== 0 &&
                    numberLevelList.map(item => {
                      return (
                        <Select.Option value={item.typeCode} key={item.typeCode}>
                          {item.description}
                        </Select.Option>
                      );
                    })}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.numConnectInputBox`).d('关联对象属性')}
            >
              {getFieldDecorator('relatedRuleSequence', {
                initialValue: numLevel === 'OVERALL_SERIAL_NUM' ? undefined : relatedRuleSequence,
                rules: [
                  {
                    required: numLevel !== 'OVERALL_SERIAL_NUM',
                    message: intl.get('hzero.common.validation.notNull', {
                      name: intl.get(`${modelPrompt}.numConnectInputBox`).d('关联对象属性'),
                    }),
                  },
                ],
              })(
                <CheckboxGroup
                  options={plainOptions}
                  disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                  onChange={value => {
                    this.handleChange('relatedRuleSequence', value);
                  }}
                  style={{ width: '200%' }}
                />,
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row {...SEARCH_FORM_ROW_LAYOUT}>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.segment`).d('号段进制')}
            >
              {getFieldDecorator('numRadix', {
                initialValue: numRadix || 'DECIMAL',
                rules: [
                  {
                    required: true,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: intl.get(`${modelPrompt}.segment`).d('号段进制'),
                    }),
                  },
                ],
              })(
                <Select
                  style={{ width: '100%' }}
                  disabled={
                    !(canEdit && !(_backUpNumLevel && _backUpNumLevel === numLevel)) ||
                    (userRole !== 'Y' && initialFlag === 'Y')
                  }
                  onChange={value => {
                    this.handleChange('numRadix', value, true);
                  }}
                >
                  {numRadixList instanceof Array &&
                    numRadixList.length !== 0 &&
                    numRadixList.map(item => {
                      return (
                        <Select.Option value={item.typeCode} key={item.typeCode}>
                          {item.description}
                        </Select.Option>
                      );
                    })}
                </Select>,
              )}
              <Tooltip
                placement="topLeft"
                title={intl
                  .get('tarzan.mes.maintainNumber.info.onlyFirstTime')
                  .d('首次定义序列号段时可编辑号段进制，保存后无法再次进行修改')}
              >
                <Icon
                  type="question-circle-o"
                  style={{
                    position: 'absolute',
                    theme: 'outlined',
                    top: 2,
                    right: '-20px',
                    fontSize: 14,
                  }}
                />
              </Tooltip>
            </Form.Item>
          </Col>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.numInc`).d('号段增量')}
            >
              {getFieldDecorator('numIncrement', {
                initialValue: numIncrement || 1,
                rules: [
                  {
                    required: true,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: intl.get(`${modelPrompt}.numInc`).d('号段增量'),
                    }),
                  },
                  {
                    validator: this.upperThanValidator,
                  },
                ],
              })(
                <InputNumber
                  precision={0}
                  step={1}
                  min={1}
                  maxLength={20}
                  disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                  onChange={e => {
                    this.handleChange('numIncrement', e, true);
                  }}
                />,
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row {...SEARCH_FORM_ROW_LAYOUT}>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.numLowerLimit`).d('序列号下限')}
            >
              {getFieldDecorator('numLowerLimit', {
                initialValue: numLowerLimit,
                rules: [
                  {
                    required: true,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: intl.get(`${modelPrompt}.numLowerLimit`).d('序列号下限'),
                    }),
                  },
                  {
                    validator: this.lowerValidator,
                  },
                ],
              })(
                <InputNumber
                  precision={0}
                  step={1}
                  min={0}
                  maxLength={20}
                  disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                  onChange={e => {
                    this.handleChange('numLowerLimit', e, true);
                  }}
                />,
              )}
            </Form.Item>
          </Col>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.numUpperLimit`).d('序列号上限')}
            >
              {getFieldDecorator('numUpperLimit', {
                initialValue: numUpperLimit,
                rules: [
                  {
                    required: true,
                    message: intl.get('hzero.common.validation.notNull', {
                      name: intl.get(`${modelPrompt}.numUpperLimit`).d('序列号上限'),
                    }),
                  },
                  {
                    validator: this.upperValidator,
                  },
                ],
              })(
                <InputNumber
                  precision={0}
                  step={1}
                  min={0}
                  maxLength={20}
                  disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                  onChange={e => {
                    this.handleChange('numUpperLimit', e, true);
                  }}
                />,
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row {...SEARCH_FORM_ROW_LAYOUT}>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              style={{ marginBottom: 0 }}
              label={intl.get(`${modelPrompt}.numAlertGroup`).d('号段预警')}
              required={numAlertType}
            >
              <Input.Group compact>
                <Form.Item style={{ width: '65%' }} className="ant-form-item-children-no-flex-fix">
                  {getFieldDecorator('numAlertType', {
                    initialValue: numAlertType || undefined,
                  })(
                    <Select
                      allowClear
                      style={{ width: '100%' }}
                      disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                      onChange={value => {
                        this.handleChange('numAlertType', value);
                      }}
                    >
                      {numAlertTypeList instanceof Array &&
                        numAlertTypeList.length !== 0 &&
                        numAlertTypeList.map(item => {
                          return (
                            <Select.Option value={item.typeCode} key={item.typeCode}>
                              {item.description}
                            </Select.Option>
                          );
                        })}
                    </Select>,
                  )}
                </Form.Item>
                <Form.Item style={{ width: '35%' }}>
                  {getFieldDecorator('numAlert', {
                    initialValue: numAlert,
                    rules: [
                      {
                        required: numAlertType,
                        message: intl.get('hzero.common.validation.notNull', {
                          name: intl.get(`${modelPrompt}.numAlertType`).d('预警序列号'),
                        }),
                      },
                      {
                        validator: this.alertValidator,
                      },
                    ],
                  })(
                    numAlertType === 'NUMBER' ? (
                      <Input
                        dbc2sbc={false}
                        disabled={
                          !canEdit || !numAlertType || (userRole !== 'Y' && initialFlag === 'Y')
                        }
                        onChange={e => {
                          this.handleChange('numAlert', e.target.value, true);
                        }}
                      />
                    ) : (
                      <InputNumber
                        precision={0}
                        step={1}
                        min={1}
                        style={{ width: '100%' }}
                        disabled={
                          !canEdit || !numAlertType || (userRole !== 'Y' && initialFlag === 'Y')
                        }
                        onChange={value => {
                          this.handleChange('numAlert', value, true);
                        }}
                      />
                    ),
                  )}
                </Form.Item>
              </Input.Group>
            </Form.Item>
          </Col>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              style={{ marginBottom: 0 }}
              label={intl.get(`${modelPrompt}.numResetType`).d('重置周期')}
              required
            >
              <Input.Group compact>
                <Form.Item style={{ width: '65%' }} className="ant-form-item-children-no-flex-fix">
                  {getFieldDecorator('numResetType', {
                    initialValue: numResetType || 'NO-RESET',
                    rules: [
                      {
                        required: true,
                        message: intl.get('hzero.common.validation.notNull', {
                          name: intl.get(`${modelPrompt}.numResetType`).d('重置周期'),
                        }),
                      },
                    ],
                  })(
                    <Select
                      style={{ width: '100%' }}
                      disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                      onChange={value => {
                        this.handleChange('numResetType', value);
                      }}
                    >
                      {numResetTypeList instanceof Array &&
                        numResetTypeList.length !== 0 &&
                        numResetTypeList.map(item => {
                          return (
                            <Select.Option value={item.typeCode} key={item.typeCode}>
                              {item.description}
                            </Select.Option>
                          );
                        })}
                    </Select>,
                  )}
                </Form.Item>
                <Form.Item style={{ width: '35%' }}>
                  {getFieldDecorator('numResetPeriod', {
                    initialValue: numResetPeriod,
                    rules: [
                      {
                        required: numResetType !== 'NO-RESET',
                        message: intl.get('hzero.common.validation.notNull', {
                          name: intl.get(`${modelPrompt}.numResetType`).d('重置周期'),
                        }),
                      },
                    ],
                  })(
                    <InputNumber
                      style={{ width: '100%' }}
                      min={1}
                      disabled={
                        !canEdit ||
                        numResetType === 'NO-RESET' ||
                        !numResetType ||
                        (userRole !== 'Y' && initialFlag === 'Y')
                      }
                      onChange={value => {
                        this.handleChange('numResetPeriod', value);
                      }}
                    />,
                  )}
                </Form.Item>
              </Input.Group>
            </Form.Item>
          </Col>
        </Row>
        <Row {...SEARCH_FORM_ROW_LAYOUT}>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              className={styles.unusualFormItem}
              label={intl.get(`${modelPrompt}.numCurrent`).d('当前序列号')}
            >
              {!numLevel && <Input disabled dbc2sbc={false} />}
              {numLevel && (
                <Input
                  dbc2sbc={false}
                  value={numCurrent}
                  disabled={
                    !editingFlag || ruleId === 'create' || numLevel !== 'OVERALL_SERIAL_NUM'
                  }
                  onChange={this.handleChangeNumCurrent}
                  maxLength={20}
                />
              )}
              {ruleId !== 'create' && canEdit && numLevel === 'OVERALL_SERIAL_NUM' && (
                <div className={styles.iconDiv}>
                  {editingFlag && (
                    <Tooltip placement="top" title={intl.get(`tarzan.common.button.save`).d('保存')}>
                      <Icon
                        type="save"
                        onClick={this.handleSaveNumCurrent}
                        className={styles.editIcon}
                      />
                    </Tooltip>
                  )}
                  {!editingFlag && numCurrent >= 0 && (
                    <Tooltip placement="top" title={intl.get(`${modelPrompt}.edit`).d('编辑')}>
                      <Icon
                        type="edit"
                        onClick={this.handleEditNumCurrent}
                        className={styles.editIcon}
                      />
                    </Tooltip>
                  )}
                  {ruleId !== 'create' && numLevel && (
                    <Tooltip placement="top" title={intl.get(`${modelPrompt}.reload`).d('刷新')}>
                      <Icon type="reload" onClick={this.onReload} className={styles.reloadIcon} />
                    </Tooltip>
                  )}
                </div>
              )}
            </Form.Item>
          </Col>
          <Col {...FORM_COL_3_LAYOUT}>
            <Form.Item
              {...DRAWER_FORM_ITEM_LAYOUT}
              label={intl.get(`${modelPrompt}.numLengthLimit`).d('序列号长度')}
            >
              {getFieldDecorator('numLengthLimit', {
                initialValue: numLengthLimit,
              })(
                <InputNumber
                  precision={0}
                  step={1}
                  min={0}
                  max={20}
                  maxLength={2}
                  disabled={!canEdit || (userRole !== 'Y' && initialFlag === 'Y')}
                  onChange={e => {
                    this.handleChange('numLengthLimit', e, true);
                  }}
                />,
              )}
            </Form.Item>
          </Col>
        </Row>
      </>
    );
  }
}
