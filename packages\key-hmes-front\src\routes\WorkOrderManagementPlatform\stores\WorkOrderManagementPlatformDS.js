import { BASIC, Host } from '@/utils/config';
import DataSet from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { searchCopy } from '@utils/utils';
import intl from 'utils/intl';

BASIC.TARZAN_REPORT = '/tznr';
const tenantId = getCurrentOrganizationId();
// const Host = `/mes-38546`;
const modelPrompt = 'tarzan.hmes.WorkOrderManagementPlatform';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'workOrderId',
    paging: true,
    autoQuery: false,
    selection: 'multiple',
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      },
      {
        name: 'statusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.statusDesc`).d('工单状态'),
      },
      {
        name: 'workOrderTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderTypeDesc`).d('工单类型'),
      },
      {
        name: 'supplierName',
        type: 'string',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商信息'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'materialDesignCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialDesignCode`).d('物料型号'),
      },
      {
        name: 'splitFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.splitFlag`).d('是否拆分'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('生产线描述'),
      },
      {
        name: 'prodLineCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
      },
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('生产数量'),
      },
      {
        name: 'completedQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.completedQty`).d('完工数量'),
      },
      {
        name: 'releasedQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.releasedQty`).d('在制数量'),
      },
      {
        name: 'scrappedQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
      },
      {
        name: 'availableQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.availableQty`).d('可用数量'),
      },
      {
        name: 'priority',
        type: 'string',
        label: intl.get(`${modelPrompt}.priority`).d('优先级'),
      },
      {
        name: 'bomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.bomName`).d('装配清单名称'),
      },
      {
        name: 'bomRevision',
        type: 'string',
        label: intl.get(`${modelPrompt}.bomRevision`).d('装配清单版本'),
      },
      {
        name: 'routerName',
        type: 'string',
        label: intl.get(`${modelPrompt}.routerName`).d('工艺路线名称'),
      },
      {
        name: 'routerRevision',
        type: 'string',
        label: intl.get(`${modelPrompt}.routerRevision`).d('工艺路线版本'),
      },
      {
        name: 'planStartTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
      },
      {
        name: 'planEndTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
      },
      {
        name: 'actualEndDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.actualEndDate`).d('实际完成时间'),
      },
      {
        name: 'dispatchTime',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.dispatchTime`).d('派工时间'),
      },
      {
        name: 'attribute1',
        type: 'string',
        label: intl.get(`${modelPrompt}.attribute1`).d('区域编码'),
      },
      {
        name: 'organizationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.organizationName`).d('区域描述'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'attribute2',
        type: 'number',
        label: intl.get(`${modelPrompt}.attribute2`).d('接收数量'),
      },
      {
        name: 'level',
        type: 'string',
        label: intl.get(`${modelPrompt}.level`).d('等级'),
      },
      {
        name: 'parentWorkOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.parentWorkOrderNum`).d('母工单编码'),
      },
      {
        name: 'productionVision',
        type: 'string',
        label: intl.get(`${modelPrompt}.productionVision`).d('生产版本'),
      },
      {
        name: 'packWorkOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.packWorkOrderNum`).d('关联PACK工单'),
      },
    ],
    queryDataSet: new DataSet({
      events: {
        update({ record, name, value }) {
          searchCopy(
            ['workOrderNums'],
            name,
            record,
            value,
          );
        }
      },
      fields: [
        {
          name: 'workOrderNums',
          multiple: true,
          type: 'string',
          label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
        },
        {
          name: 'workOrderType',
          type: 'string',
          label: intl.get(`${modelPrompt}.workOrderType`).d('工单类型'),
          lovPara: { tenantId },
          textField: 'description',
          valueField: 'typeCode',
          lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
          lookupAxiosConfig: {
            transformResponse(data) {
              if (data instanceof Array) {
                return data;
              }
              const { rows } = JSON.parse(data);
              return rows;
            },
          },
        },
        {
          name: 'statusList',
          type: 'string',
          label: intl.get(`${modelPrompt}.status`).d('工单状态'),
          textField: 'description',
          valueField: 'statusCode',
          multiple: true,
          noCache: true,
          lovPara: { tenantId },
          lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=WO_STATUS&type=workOrderStatusOptions`,
          lookupAxiosConfig: {
            transformResponse(data) {
              if (data instanceof Array) {
                return data;
              }
              const { rows } = JSON.parse(data);
              return rows;
            },
          },
        },
        {
          name: 'materialObj',
          type: 'object',
          label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
          lovCode: 'MT.MATERIAL',
          labelWidth: 150,
          textField: 'materialCode',
          dynamicProps: {
            lovPara: () => {
              return {
                tenantId: getCurrentOrganizationId(),
              };
            },
            disabled: ({ record }) => record?.get('materialDesignId'),
          },
        },
        {
          name: 'materialCode',
          type: 'string',
          bind: 'materialObj.materialCode',
        },
        {
          name: 'materialId',
          type: 'number',
          bind: 'materialObj.materialId',
        },
        {
          name: 'bomMaterialCode',
          type: 'object',
          label: intl.get(`${modelPrompt}.bomMaterialCode`).d('工艺组件编码'),
          lovCode: 'MT.MATERIAL',
          labelWidth: 150,
          multiple: true,
          textField: 'materialCode',
          dynamicProps: {
            lovPara: () => {
              return {
                tenantId: getCurrentOrganizationId(),
              };
            },
          },
        },
        {
          name: 'bomMaterialIds',
          type: 'string',
          bind: 'bomMaterialCode.materialId',
        },
        {
          name: 'prodLineObj',
          type: 'object',
          label: intl.get(`${modelPrompt}.prodLineObj`).d('生产线'),
          lovCode: 'HME.PERMISSION_PROD_LINE',
          labelWidth: 150,
          ignore: 'always',
          textField: 'prodLineCode',
          dynamicProps: {
            lovPara: () => {
              return {
                tenantId: getCurrentOrganizationId(),
              };
            },
          },
        },
        {
          name: 'prodLineCode',
          type: 'string',
          bind: 'prodLineObj.prodLineCode',
        },
        {
          name: 'prodLineId',
          type: 'number',
          bind: 'prodLineObj.prodLineId',
        },
        {
          name: 'siteCode',
          type: 'object',
          label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
          lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
          labelWidth: 150,
        },
        {
          name: 'siteId',
          type: 'number',
          bind: 'siteCode.siteId',
          labelWidth: 150,
        },
        {
          type: 'string',
          name: 'existFlag',
          label: intl.get(`${modelPrompt}.existFlag`).d('是否存在在制品'),
          lookupCode: 'HME.ZZP_EXIST',
        },
        {
          name: 'planStartTimeFrom',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.planStartTimeFrom`).d('计划开始时间从'),
          max: 'planStartTimeTo',
        },
        {
          name: 'planStartTimeTo',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.planStartTimeTo`).d('计划开始时间至'),
          min: 'planStartTimeFrom',
        },
        {
          name: 'planEndTimeFrom',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.planEndTimeFrom`).d('计划结束时间从'),
          max: 'planEndTimeTo',
        },
        {
          name: 'planEndTimeTo',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.planEndTimeTo`).d('计划结束时间至'),
          min: 'planEndTimeFrom',
        },
        {
          name: 'actualEndTimeFrom',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.actualEndTimeFrom`).d('实际完成时间从'),
          max: 'actualEndTimeTo',
        },
        {
          name: 'actualEndTimeTo',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.actualEndTimeTo`).d('实际完成时间至'),
          min: 'actualEndTimeFrom',
        },
        {
          name: 'packWorkOrderNum',
          type: 'string',
          label: intl.get(`${modelPrompt}.actualEndTimeTo`).d('PACK生产指令编码'),
        },
        {
          name: 'moduleWorkOrderNum',
          type: 'string',
          label: intl.get(`${modelPrompt}.moduleWorkOrderNum`).d('模组生产指令编码'),
        },
        {
          name: 'materialDesignLov',
          type: 'object',
          label: intl.get(`${modelPrompt}.materialDesignCode`).d('物料型号'),
          lovCode: 'WMS.REQUISITION_TASK_MATERIAL_DESING',
          lovPara: { tenantId },
          ignore: 'always',
          dynamicProps: {
            disabled: ({ record }) => record?.get('materialId'),
          },
        },
        {
          name: 'materialDesignId',
          bind: 'materialDesignLov.materialId',
        },
        {
          name: 'materialDesignCode',
          bind: 'materialDesignLov.materialDesignCode',
        },
        {
          name: 'workDispatchTimeStart',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.workDispatchTimeStart`).d('工单派工时间开始'),
        },
        {
          name: 'workDispatchTimeEnd',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.workDispatchTimeEnd`).d('工单派工时间结束'),
        },
      ],
    }),

    transport: {
      read: ({ data }) => {
        const { materialId, materialDesignId, workOrderNums } = data;
        return {
          url: `${Host}/v1/${tenantId}/hme-work-order-new/list/ui`,
          method: 'POST',
          data: {
            ...data,
            materialId: materialId || materialDesignId,
            workOrderNums: (workOrderNums || []).join(),
          },
        };
      },
    },
  };
};

const dispatchFormDS = () => {
  return {
    name: 'dispatchFormDS',
    paging: true,
    fields: [
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('派工数量'),
        required: true,
        pattern: '^[1-9]*[1-9][0-9]*$',
      },
    ],
  };
};

const levelDS = () => {
  return {
    name: 'levelDS',
    paging: false,
    fields: [
      {
        name: 'level',
        type: 'string',
        lookupCode: 'HME.WO_LEVEL',
        label: intl.get(`${modelPrompt}.level`).d('指定等级'),
      },
    ],
  };
}

const splitFormDS = () => {
  return {
    name: 'splitFormDS',
    paging: true,
    fields: [
      {
        name: 'splitQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
        required: true,
        pattern: '^[1-9]*[1-9][0-9]*$',
      },
    ],
  };
};
const bomAndRouterDS = () => {
  return {
    autoQuery: false,
    autoCreate: true,
    paging: false,
    name: 'bomAndRouterDS',
    fields: [
      {
        name: 'selectType',
        type: 'string',
        defaultValue: 'designChange',
      },
      {
        name: 'designProductionVersion',
        type: 'object',
      },
      {
        name: 'designProductionVersionCode',
        type: 'string',
        bind: 'designProductionVersion.productionVersionCode',
        label: intl.get(`${modelPrompt}.designProductionVersionCode`).d('生产版本'),
        disabled: true,
      },
      {
        name: 'designBom',
        type: 'object',
        label: intl.get(`${modelPrompt}.designBom`).d('装配清单/版本'),
      },
      {
        name: 'designBomName',
        type: 'string',
        bind: 'designBom.bomName',
        disabled: true,
      },
      {
        name: 'designBomRevision',
        type: 'string',
        bind: 'designBom.revision',
        disabled: true,
      },
      {
        name: 'designRouter',
        type: 'object',
        label: intl.get(`${modelPrompt}.designRouter`).d('工艺路线/版本'),
      },
      {
        name: 'designRouterId',
        type: 'number',
        bind: 'designRouter.routerId',
      },
      {
        name: 'designRouterName',
        type: 'string',
        bind: 'designRouter.routerName',
        disabled: true,
      },
      {
        name: 'designRouterRevision',
        type: 'string',
        bind: 'designRouter.revision',
        disabled: true,
      },
      {
        name: 'ownProductionVersion',
        type: 'boolean',
        defaultValue: false,
      },
      {
        name: 'productionVersion',
        type: 'object',
        label: intl.get(`${modelPrompt}.productionVersion`).d('生产版本'),
        lovCode: 'HME.WO_PRODUCTION_VERSION',
        textField: 'productionVersionCode',
        valueField: 'productionVersionId',
        noCache: true,
        required: true,
        dynamicProps: {
          lovPara({ record }) {
            return {
              tenantId,
              siteId: record.get('siteId'),
              materialId: record.get('materialId'),
            };
          },
        },
      },
      {
        name: 'productionVersionCode',
        type: 'string',
        bind: 'productionVersion.productionVersionCode',
      },
      {
        name: 'bom',
        type: 'object',
        label: intl.get(`${modelPrompt}.bom`).d('装配清单/版本'),
        lovCode: 'MT.METHOD.PROD-VERSION.BOM', // 设计变更
        // lovCode: 'MT.BOM_BASIC',
        textField: 'bomName',
        valueField: 'bomId',
        noCache: true,
        ignore: 'always',
        dynamicProps: {
          lovPara({ record }) {
            const queryParams = {
              tenantId,
              siteId: record.get('siteId'),
              materialId: record.get('materialId'),
              revisionCode: record.get('revisionCode'),
              productionVersionCode: record.get('productionVersionCode'),
            };
            return queryParams;
          },
          disabled({ record }) {
            // 有生产版本的话，只能通过选择生产版本带出来
            return record.get('ownProductionVersion');
          },
        },
      },
      {
        name: 'bomName',
        type: 'string',
        bind: 'bom.bomName',
      },
      {
        name: 'bomId',
        type: 'number',
        bind: 'bom.bomId',
      },
      {
        name: 'bomRevision',
        type: 'string',
        disabled: true,
        bind: 'bom.revision',
      },
      {
        name: 'router',
        type: 'object',
        label: intl.get(`${modelPrompt}.router`).d('工艺路线/版本'),
        lovCode: 'MT.METHOD.PROD-VERSION.ROUTER', // 设计变更
        textField: 'routerName',
        valueField: 'routerId',
        noCache: true,
        ignore: 'always',
        dynamicProps: {
          lovPara({ record }) {
            const queryParams = {
              tenantId,
              siteId: record.get('siteId'),
              materialId: record.get('materialId'),
              revisionCode: record.get('revisionCode'),
              productionVersionCode: record.get('productionVersionCode'),
            };
            return queryParams;
          },
          disabled({ record }) {
            // 有生产版本的话，只能通过选择生产版本带出来
            return record.get('ownProductionVersion');
          },
        },
      },
      {
        name: 'routerName',
        type: 'string',
        bind: 'router.routerName',
      },
      {
        name: 'routerId',
        type: 'number',
        bind: 'router.routerId',
      },
      {
        name: 'routerRevision',
        type: 'string',
        disabled: true,
        bind: 'router.revision',
      },
    ],
  }
};

const prodDS = () => {
  return {
    name: 'prodDS',
    paging: false,
    autoCreate: true,
    fields: [
      {
        name: 'prodLineObj',
        type: 'object',
        required: true,
        label: intl.get(`${modelPrompt}.prodLineObj`).d('生产线'),
        lovCode: 'HME.WO_PRODLINE',
        ignore: 'always',
        textField: 'organizationCode',
      },
      {
        name: 'organizationCode',
        type: 'string',
        bind: 'prodLineObj.organizationCode',
        label: intl.get(`${modelPrompt}.organizationCode`).d('生产线编码'),
      },
      {
        name: 'organizationId',
        type: 'number',
        bind: 'prodLineObj.organizationId',
      },
    ],
  };
}

const drawerDS = () => {
  return {
    name: 'drawerDS',
    primaryKey: 'workOrderId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('执行作业标识'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'statusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.statusDesc`).d('质量状态'),
      },
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('执行作业数量'),
      },
      {
        name: 'completedQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.completedQty`).d('完工数量'),
      },
      {
        name: 'scrappedQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${Host}/v1/${tenantId}/hme-work-order-new/list/detail/ui`,
          method: 'GET',
        };
      },
    },
  }
}

const historyDS = () => ({
  selection: false,
  autoQuery: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  fields: [
    {
      name: 'eventId',
      type: 'string',
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'eventTypeCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
    },
    {
      name: 'eventTypeDesc',
      type: 'string',
      label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型'),
    },
    {
      name: 'eventRequestId',
      type: 'number',
      label: intl.get(`${modelPrompt}.eventRequestId`).d('事件请求ID'),
    },
    {
      name: 'eventRequestTypeCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.eventRequestTypeCode`).d('请求类型编码'),
    },
    {
      name: 'eventRequestTypeDesc',
      type: 'string',
      label: intl.get(`${modelPrompt}.eventRequestTypeDesc`).d('请求类型'),
    },
    {
      name: 'creationDate',
      type: 'string',
      label: intl.get(`${modelPrompt}.creationDate`).d('事件时间'),
    },
    {
      name: 'realName',
      type: 'string',
      label: intl.get(`${modelPrompt}.realName`).d('操作人'),
    },
    {
      name: 'workOrderNum',
      type: 'string',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'workOrderTypeDesc',
      type: 'string',
      label: intl.get(`${modelPrompt}.workOrderTypeDesc`).d('工单类型'),
    },
    {
      name: 'prodLineCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'workcellName',
      type: 'string',
      label: intl.get(`${modelPrompt}.workcellName`).d('工作单元'),
    },
    {
      name: 'prodVersion',
      type: 'string',
      label: intl.get(`${modelPrompt}.prodVersion`).d('生产版本'),
    },
    {
      name: 'priority',
      type: 'string',
      label: intl.get(`${modelPrompt}.priority`).d('优先级'),
    },
    {
      name: 'qty',
      type: 'number',
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'statusDesc',
      type: 'string',
      label: intl.get(`${modelPrompt}.statusDesc`).d('工单状态'),
    },
    {
      name: 'lastWoStatusDesc',
      type: 'string',
      label: intl.get(`${modelPrompt}.lastWoStatusDesc`).d('前次工单状态'),
    },
    {
      name: 'planStartTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'bomName',
      type: 'string',
      label: intl.get(`${modelPrompt}.bomName`).d('装配清单'),
    },
    {
      name: 'routerName',
      type: 'string',
      label: intl.get(`${modelPrompt}.routerName`).d('工艺路线'),
    },
    {
      name: 'remark',
      type: 'string',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'trxQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.trxQty`).d('影响数量'),
    },
    {
      name: 'preCompletedQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.preCompletedQty`).d('单件完工数量'),
    },
    {
      name: 'specifiedLevel',
      type: 'string',
      label: intl.get(`${modelPrompt}.specifiedLevel`).d('指定等级'),
    },
    {
      name: 'caAttribute1',
      type: 'string',
      label: intl.get(`${modelPrompt}.caAttribute1`).d('产线组'),
    },
    {
      name: 'caAttribute2',
      type: 'number',
      label: intl.get(`${modelPrompt}.caAttribute2`).d('母工单数量'),
    },
    {
      name: 'caAttribute3',
      type: 'number',
      label: intl.get(`${modelPrompt}.caAttribute3`).d('良率'),
    },
    {
      name: 'caAttribute4',
      type: 'string',
      label: intl.get(`${modelPrompt}.caAttribute4`).d('关联PACK工单编码'),
    },
  ],
  transport: {
    read: config => {
      const { data } = config;
      return {
        ...config,
        data: data?.workOrderIdList,
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-work-order-new/list/his/ui`,
        method: 'POST',
      };
    },
  },
});

export { drawerDS, prodDS, tableDS, dispatchFormDS, splitFormDS, bomAndRouterDS, levelDS, historyDS };
