/**
 * @Description: 数据收集组维护-数据项信息-新建/编辑抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-01-25 15:34:54
 * @LastEditTime: 2022-07-11 17:40:23
 * @LastEditors: <<EMAIL>>
 */

import React, {useEffect, useState} from 'react';
import { Form, TextField, Lov, Select } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import NumberComponent from './components/NumberComponent';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

export default ({ record, edit, trueNumberDs, falseNumberDs}) => {
  const [trueValueDisabled, setTrueDisabled] = useState(true);
  const [falseValueDisabled, setFalseDisabled] = useState(true);
  useEffect(() => {
    setTrueDisabled(false);
    setFalseDisabled(false);
    handleUpdateDisabled()
  })
  const handleUpdateDisabled = () => {
    const { valueType } = record?.toData();
    const trueValue = trueNumberDs?.current?.get('multipleValue');
    const falseValue = falseNumberDs?.current?.get('multipleValue');
    const _isFalseValueEdit = falseNumberDs.length > 1 || verifyHasValue(falseValue);
    if (['VALUE'].includes(valueType)&&_isFalseValueEdit){
      falseNumberDs?.getField('ncCodeLov')?.set('required', true);
    }else{
      falseNumberDs?.getField('ncCodeLov')?.set('required', false);
    }
    if(['VALUE'].includes(valueType)&&verifyHasValue(trueValue)){
      record?.getField('ncCodeLov')?.set('required', true);
    }else if(['DECISION_VALUE'].includes(record?.get('valueType'))
      &&(record?.get('trueValue')||record?.get('falseValue'))){
      record?.getField('ncCodeLov')?.set('required', true);
    }else{
      record?.getField('ncCodeLov')?.set('required', false);
    }
  };
  // 根据值类型不同使用不同的判空方法
  const verifyHasValue = value => {
    if (value instanceof Object) {
      // 数据类型为数值-区间
      return value?.leftValue || value?.rightValue;
    }
    // 数据类型为数值-单值
    return value;
  };

  const handleChange = (value, filedName) => {
    if (value) {
      record?.set(filedName, filedName === 'ncCode' ? value.ncCode : value.uomCode)
    }else{
      record?.set(filedName,undefined)
    }
  }

  return (
    <Form record={record} columns={2} disabled={edit}>
      <Lov name="tagLov" disabled />
      <TextField name="tagDescription" disabled />
      <TextField name="valueTypeMeaning" disabled />
      <TextField name="valueList" />
      <Select name="checkPoint"  colSpan={2}/>
      {record.get('valueType') === 'VALUE' ? (
        <NumberComponent
          showStandard
          title={intl.get(`${modelPrompt}.synchronizeData`).d('符合值')}
          name="trueValue"
          parentDs={record}
          dataSet={trueNumberDs}
          disabled={trueValueDisabled}
          canEdit={!edit}
          handleUpdateDisabled={handleUpdateDisabled}
        />
      ) : (
        <TextField name="trueValue" />
      )}
      {record.get('valueType') === 'VALUE' ? (
        <NumberComponent
          showNcCode
          title={intl.get(`${modelPrompt}.synchronizeData`).d('不符合值')}
          name="falseValue"
          parentDs={record}
          dataSet={falseNumberDs}
          disabled={falseValueDisabled}
          canEdit={!edit}
          handleUpdateDisabled={handleUpdateDisabled}
        />
      ) : (
        <TextField name="falseValue" />
      )}
      <Select name="dateFormat" />
      <Lov name="ncCodeLov" onChange={(value) => { handleChange(value, 'ncCode') }} />
      <Lov name="uomCodeLov" onChange={(value) => { handleChange(value, 'uomCode') }} />
      <Select name="enableFlag" />
    </Form>
  );
};
