/**
 * @Description: 数据收集组DS
 * @Author: <EMAIL>
 * @Date: 2023-03-14 10:19:56
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import uuid from 'uuid/v4';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.materialPreventError';
const tenantId = getCurrentOrganizationId();

// lov配置信息
const objectLovCode = {
  MATERIAL: 'MT.METHOD.MATERIAL',
  OPERATION: 'MT.METHOD.OPERATION',
  WORKCELL: 'MT.MODEL.WORKCELL_SITE',
  AREA: 'MT.MODEL.AREA',
  PROD_LINE: 'MT.MODEL.PRODLINE',
  MATERIAL_CATEGORY: 'MT.METHOD.MATERIAL_CATEGORY_SITES',
  COM_MATERIAL: 'MT.METHOD.MATERIAL',
  COM_MATERIAL_CATEGORY: 'MT.METHOD.MATERIAL_CATEGORY_SITES',
};

const objectIdBind = {
  MATERIAL: 'objectLov.materialId',
  OPERATION: 'objectLov.operationId',
  WORKCELL: 'objectLov.workcellId',
  AREA: 'objectLov.areaId',
  PROD_LINE: 'objectLov.prodLineId',
  MATERIAL_CATEGORY: 'objectLov.materialCategoryId',
  COM_MATERIAL: 'objectLov.materialId',
  COM_MATERIAL_CATEGORY: 'objectLov.materialCategoryId',
};

const objectCodeBind = {
  MATERIAL: 'objectLov.materialCode',
  OPERATION: 'objectLov.operationName',
  WORKCELL: 'objectLov.workcellCode',
  AREA: 'objectLov.areaCode',
  PROD_LINE: 'objectLov.prodLineCode',
  MATERIAL_CATEGORY: 'objectLov.categoryCode',
  COM_MATERIAL: 'objectLov.materialCode',
  COM_MATERIAL_CATEGORY: 'objectLov.categoryCode',
};

const objectDescBind = {
  MATERIAL: 'objectLov.materialName',
  OPERATION: 'objectLov.description',
  WORKCELL: 'objectLov.workcellName',
  AREA: 'objectLov.areaName',
  PROD_LINE: 'objectLov.prodLineName',
  MATERIAL_CATEGORY: 'objectLov.description',
  COM_MATERIAL: 'objectLov.materialName',
  COM_MATERIAL_CATEGORY: 'objectLov.description',
};

const objectRevisionBind = {
  MATERIAL: '',
  OPERATION: 'objectLov.revision',
  WORKCELL: '',
  AREA: '',
  PROD_LINE: '',
  MATERIAL_CATEGORY: '',
  COM_MATERIAL: '',
  COM_MATERIAL_CATEGORY: '',
};

const materialRevisionOptionDs = new DataSet({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
        method: 'GET',
        params: { tenantId },
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          let firstlyQueryData = [];
          const { rows } = JSON.parse(data);
          if (rows instanceof Array) {
            firstlyQueryData = rows.map(item => {
              return {
                kid: uuid(),
                description: item,
              };
            });
          }
          return firstlyQueryData;
        },
      };
    },
  },
});

// 物料防呆防错列表ds
const tableDS = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  queryFields: [
    {
      name: 'strategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyCode`).d('配置编码'),
    },
    {
      name: 'strategyDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyDesc`).d('配置描述'),
    },
    {
      name: 'businessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      valueField: 'typeCode',
      textField: 'description',
      lovPara: { organizationId: tenantId, typeGroup: 'ERR_PROOFING_STRATEGY' },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/list/ui?page=0&size=99999`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows?.content;
        },
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lookupCode: 'MT.FLAG',
    },
    {
      name: 'objectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectType`).d('关联对象类型'),
      valueField: 'typeCode',
      textField: 'description',
      lovPara: { organizationId: tenantId, typeGroup: 'ERR_PROOFING_OBJECT' },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/list/ui?page=0&size=99999`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows?.content;
        },
      },
    },
    {
      name: 'objectLov',
      label: intl.get(`${modelPrompt}.objectCode`).d('关联对象编码'),
      type: FieldType.object,
      lovCode: '',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('objectType');
        },
        lovCode: ({ record }) => {
          const objectType = record.get('objectType');
          return objectLovCode[objectType];
        },
        lovPara() {
          const queryPara = {
            tenantId,
          };
          return queryPara;
        },
      },
    },
    {
      name: 'objectId',
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectIdBind[objectType];
        },
      },
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('关联对象版本'),
    },
  ],
  fields: [
    {
      name: 'strategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyCode`).d('配置编码'),
    },
    {
      name: 'strategyDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyDesc`).d('配置描述'),
    },
    {
      name: 'dimensionList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dimensionList`).d('配置项维度'),
    },
    {
      name: 'businessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-err-proofing-strategys/strategy/info/ui`,
        method: 'post',
      };
    },
  },
});

// 物料防呆防错详情ds
const detailDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'strategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyCode`).d('配置编码'),
      required: true,
    },
    {
      name: 'strategyDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.strategyDesc`).d('配置描述'),
    },
    {
      name: 'siteId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      valueField: 'siteId',
      textField: 'siteCode',
      required: true,
      lovPara: { organizationId: tenantId },
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-component/user/distribution/site/list/ui`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      required: true,
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'businessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      valueField: 'typeCode',
      textField: 'description',
      required: true,
      lovPara: { organizationId: tenantId, typeGroup: 'ERR_PROOFING_STRATEGY' },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/list/ui?page=0&size=99999`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows?.content;
        },
      },
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-err-proofing-strategys/detail/info/ui`,
        method: 'get',
      };
    },
  },
});

// 物料防呆防错详情配置项ds
const dataItemDS = () => ({
  // primaryKey: 'tagGroupAssignId',
  autoCreate: false,
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'detailValue',
      type: FieldType.number,
    },
    {
      name: 'relation',
      type: 'string',
      textField: 'meaning',
      valueField: 'value',
      label: intl.get(`${modelPrompt}.relation`).d('时间范围'),
      lookupCode: 'STRATEGY_RELATION',
      // defaultValue: 'EQUAL',
    },
    {
      name: 'uomId',
      type: FieldType.string,
      textField: 'uomName',
      valueField: 'uomId',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-err-proofing-strategys/uom/info/ui`,
      label: intl.get(`${modelPrompt}.uomId`).d('单位'),
      // required: true,
      // label: intl.get(`${modelPrompt}.collectionMethod`).d('数据收集方式'),
    },
    {
      name: 'pretreatmentStatus',
      type: FieldType.boolean,
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
});

// 物料防呆防错详情关联对象ds
const assObjectsDS = () => ({
  primaryKey: 'tagGroupObjectId',
  autoCreate: false,
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'lineNumber',
      label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
      type: FieldType.number,
    },
    {
      name: 'objectShowList',
      label: intl.get(`${modelPrompt}.assObjArray`).d('关联对象组合'),
      type: FieldType.object,
    },
  ],
});

// 物料防呆防错详情关联对象抽屉ds
const singleAssObjectDS = () => ({
  primaryKey: 'uuid',
  autoCreate: true,
  selection: false,
  autoQuery: false,
  paging: false,
  fields: [
    {
      name: 'parentSiteIds',
      type: FieldType.object,
    },
    {
      name: 'objectType',
      label: intl.get(`${modelPrompt}.objectType`).d('关联对象类型'),
      type: FieldType.string,
    },
    {
      name: 'objectLov',
      label: intl.get(`${modelPrompt}.objectCode`).d('关联对象编码'),
      type: FieldType.object,
      lovCode: '',
      required: true,
      dynamicProps: {
        lovCode: ({ record }) => {
          const objectType = record.get('objectType');
          return objectLovCode[objectType];
        },
        lovPara() {
          const queryPara = {
            tenantId,
          };
          //   const siteIds = record.get('parentSiteIds') || [];
          //   switch (record.get('objectType')) {
          //     case 'MATERIAL_CATEGORY':
          //     case 'MATERIAL':
          //       queryPara.siteIds = siteIds.join(',');
          //       break;
          //     case 'WORKCELL':
          //       queryPara.siteIds = siteIds.join(',');
          //       queryPara.userFlag = 'Y';
          //       break;
          //     case 'AREA':
          //     default:
          //       // OPERATION   PROD_LINE
          //       break;
          //   }
          return queryPara;
        },
      },
    },
    {
      name: 'objectId',
      type: FieldType.number,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectIdBind[objectType];
        },
      },
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectCodeBind[objectType];
        },
      },
    },
    {
      name: 'objectDesc',
      label: intl.get(`${modelPrompt}.objectDesc`).d('关联对象描述'),
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectDescBind[objectType];
        },
      },
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'objectLov.revisionFlag',
    },
    {
      name: 'revisionCode',
      label: intl.get(`${modelPrompt}.revisionCode`).d('关联对象版本'),
      type: FieldType.string,
      required: false,
      textField: 'description',
      valueField: 'description',
      options: materialRevisionOptionDs,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('revisionFlag') === 'Y';
        },
        bind: ({ record }) => {
          const objectType = record.get('objectType');
          return objectRevisionBind[objectType];
        },
      },
    },
  ],
});

// 物料防呆防错复制ds
const copyDS = () => ({
  autoQuery: true,
  autoCreate: false,
  autoQueryAfterSubmit: false,
  fields: [
    {
      name: 'targetStrategyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetStrategyCode`).d('目标配置编码'),
      required: true,
    },
    {
      name: 'targetBusinessType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetBusinessType`).d('目标业务类型'),
      valueField: 'typeCode',
      textField: 'description',
      lovPara: { organizationId: tenantId, typeGroup: 'ERR_PROOFING_STRATEGY' },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/list/ui?page=0&size=99999`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows?.content;
        },
      },
    },
  ],
});

export {
  tableDS,
  detailDS,
  dataItemDS,
  assObjectsDS,
  singleAssObjectDS,
  copyDS,
  materialRevisionOptionDs,
};
