/**
 * @Description: 生产指令管理详情页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2023-07-19 14:53:49
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import uuid from 'uuid/v4';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.productionOrderMgt';
const tenantId = getCurrentOrganizationId();

const formDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && datas.rows) {
            if (datas.rows.bomId === 0) {
              datas.rows.bomId = null;
            }
            if (datas.rows.routerId === 0) {
              datas.rows.routerId = null;
            }
          }
          return {
            ...datas,
          };
        },
      };
    },
  },
  fields: [
    // 基本属性
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderNum`).d('WO编码'),
      disabled: true,
    },
    {
      name: 'soNumberObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.soNumber`).d('销售订单'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`,
      textField: 'soNumberSoLineNum',
      valueField: 'soId',
      noCache: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId') || record.get('workOrderNum');
        },
      },
    },
    {
      name: 'soNumberSoLineNum',
      type: FieldType.string,
      bind: 'soNumberObj.soNumberSoLineNum',
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      bind: 'soNumberObj.soNumber',
    },
    {
      name: 'soId',
      type: FieldType.string,
      bind: 'soNumberObj.soId',
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      bind: 'soNumberObj.soLineNum',
    },
    {
      name: 'soLineId',
      type: FieldType.string,
      bind: 'soNumberObj.soLineId',
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      valueField: 'siteId',
      noCache: true,
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      dynamicProps: {
        disabled({ record }) {
          return record.get('workOrderNum');
        },
      },
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'site.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'site.siteCode',
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.siteName`).d('站点名称'),
      ignore: 'always',
      bind: 'site.siteName',
      disabled: true,
    },
    {
      name: 'workOrderType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.workOrderType`).d('WO类型'),
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      dynamicProps: {
        disabled({ record }) {
          return record.get('workOrderNum');
        },
      },
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.status`).d('WO状态'),
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'NEW',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=WO_STATUS&type=workOrderStatusOptions`,
      disabled: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.remark`).d('备注'),
    },
    // 需求属性
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.BOM_MATERIAL',
      textField: 'materialCode',
      valueField: 'materialId',
      required: true,
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('siteId') || record.get('workOrderNum');
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteIds: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'material.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'material.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.revision`).d('版本'),
      textField: 'description',
      valueField: 'description',
      noCache: true,
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: uuid(),
                  description: item,
                };
              });
            }
            if (record) {
              if (firstlyQueryData.length > 0) {
                if (!record.get('revisionCode')) {
                  record.set('revisionCode', firstlyQueryData[0].description);
                }
              } else {
                record.set('revisionCode', null);
              }
              record.set('revisionCodeUpdate', uuid());
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('siteId') ||
            !record.get('materialId') ||
            record.get('workOrderNum') ||
            !record.get('revisionFlag')
          );
        },
        required({ record }) {
          return record.get('revisionFlag') === 'Y' && record.get('materialId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: record.get('siteId') || undefined,
            materialId: record.get('materialId') || undefined,
            kid: record.get('kid') || undefined,
          };
        },
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialName`).d('物料名称'),
      bind: 'material.materialName',
      disabled: true,
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.qty`).d('数量'),
      required: true,
      min: 0,
      precision: 2,
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.uomCode`).d('单位编码'),
      bind: 'material.uomCode',
      disabled: true,
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.uomName`).d('单位描述'),
      bind: 'material.uomName',
      disabled: true,
    },
    {
      name: 'customer',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.customerCode`).d('客户编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerCode',
      valueField: 'customerId',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'customerCode',
      type: FieldType.string,
      bind: 'customer.customerCode',
    },
    {
      name: 'customerId',
      type: FieldType.number,
      bind: 'customer.customerId',
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.customerName`).d('客户名称'),
      bind: 'customer.customerName',
      disabled: true,
    },
    {
      name: 'makeOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.source`).d('来源制造订单'),
      disabled: true,
    },
    // 生产属性
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planStartTime`).d('开始时间'),
      required: true,
      max: 'planEndTime',
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.planEndTime`).d('结束时间'),
      required: true,
      min: 'planStartTime',
    },
    {
      name: 'locator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.locatorCode`).d('默认完工库位编码'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      textField: 'locatorCode',
      valueField: 'locatorId',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('siteId');
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteIds: record.get('siteId'),
            locatorCategoryList: 'LOCATION,INVENTORY',
          };
        },
      },
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'locator.locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'locator.locatorCode',
    },
    {
      name: 'productionLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionLineCode`).d('生产线编码'),
      lovCode: 'MT.MODEL.PRODLINE',
      textField: 'prodLineCode',
      valueField: 'prodLineId',
      required: true,
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('siteId');
        },
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'productionLineCode',
      type: FieldType.string,
      ignore: 'always',
      bind: 'productionLine.prodLineCode',
    },
    {
      name: 'productionLineId',
      type: FieldType.number,
      bind: 'productionLine.prodLineId',
    },
    {
      name: 'productionLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionLineName`).d('生产线名称'),
      disabled: true,
      ignore: 'always',
      bind: 'productionLine.prodLineName',
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.locatorName`).d('默认完工库位名称'),
      disabled: true,
      bind: 'locator.locatorName',
    },
    {
      name: 'completeControlType',
      type: FieldType.string,
      label: intl
        .get(`${modelPrompt}.model.productionOrderMgt.completeControlType`)
        .d('完工限制类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MATERIAL&typeGroup=CONTROL_TYPE&type=controlTypeOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'completeControlQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.completeControlQty`).d('完工限制值'),
      min: 0,
      precision: 2,
      dynamicProps: {
        disabled({ record }) {
          return !record.get('completeControlType');
        },
        required({ record }) {
          return record.get('completeControlType');
        },
      },
    },
    {
      name: 'bom',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.bomName`).d('装配清单编码'),
      disabled: true,
      textField: 'bomName',
      valueField: 'bomId',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        help({ record }) {
          return record.get('bomType') !== 'EO'
            ? intl.get(`${modelPrompt}.bomMessage`).d('保存时会生成WO类型的装配清单')
            : '';
        },
        // lovCode({ record }) {
        //   return record.get('bomType') ? `${BASIC.LOV_CODE_BEFORE}.MES.BOM_BASIC` : 'MT.METHOD.BOM_BASIC';
        // },
        // disabled({ record }) {
        //   return !record.get('siteId') || !record.get('materialId');
        // },
        // lovPara({ record }) {
        //   return {
        //     tenantId,
        //     siteIds: record.get('siteId'),
        //     materialId: record.get('materialId'),
        //     bomTypes: record.get('bomType') ? 'WO' : undefined,
        //   };
        // },
      },
    },
    {
      name: 'bomName',
      type: FieldType.string,
      bind: 'bom.bomName',
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.bomName`).d('装配清单编码'),
      disabled: true,
    },
    {
      name: 'bomId',
      type: FieldType.number,
      bind: 'bom.bomId',
    },
    {
      name: 'bomRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'bom.revision',
    },
    {
      name: 'router',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.routerName`).d('工艺路线编码'),
      // lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.ROUTER_SITE`,
      disabled: true,
      textField: 'routerName',
      valueField: 'routerId',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        help({ record }) {
          return record.get('routerType') !== 'EO'
            ? intl.get(`${modelPrompt}.routerMessage`).d('保存时会生成WO类型的工艺路线')
            : '';
        },
        //   lovCode({ record }) {
        //     return record.get('routerType') ? `${BASIC.LOV_CODE_BEFORE}.MES.ROUTER_SITE` : 'MT.METHOD.ROUTER_SITE';
        //   },
        //   disabled({ record }) {
        //     return !record.get('siteId') || !record.get('materialId');
        //   },
        //   lovPara({ record }) {
        //     return {
        //       tenantId,
        //       siteIds: record.get('siteId'),
        //       materialId: record.get('materialId'),
        //       routerTypes: record.get('routerType') ? 'WO' : undefined,
        //     };
        //   },
      },
    },
    {
      name: 'routerName',
      type: FieldType.string,
      bind: 'router.routerName',
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.routerName`).d('工艺路线编码'),
      disabled: true,
    },
    {
      name: 'routerId',
      type: FieldType.number,
      bind: 'router.routerId',
    },
    {
      name: 'routerRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'router.revision',
    },
    {
      name: 'productionVersion',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionVersion`).d('生产版本'),
      lovCode: 'MT.METHOD.MATERIAL.SITE.PROD-VERSION',
      textField: 'productionVersionCode',
      valueField: 'productionVersionId',
      noCache: true,
      ignore: 'always',
      disabled: true,
      dynamicProps: {
        // disabled({ record }) {
        //   return record.get('productionVersionDisable') !== 'Y';
        // },
        // required({ record }) {
        //   return record.get('productionVersionRequire') === 'Y';
        // },
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
            revisionCode: record.get('revisionCode'),
          };
        },
      },
    },
    {
      name: 'productionVersionId',
      type: FieldType.number,
      bind: 'productionVersion.productionVersionId',
    },
    {
      name: 'productionVersionLov',
      lovCode: 'HME.WO_PRODUCTION_VERSION',
      textField:'productionVersionCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionVersion`).d('生产版本'),
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
          };
        },
      },
      // bind: 'productionVersion.productionVersionCode',
    },
    {
      name: 'productionVersionCode',
      lovCode: 'HME.WO_PRODUCTION_VERSION',
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionVersion`).d('生产版本'),
      bind: 'productionVersionLov.productionVersionCode',
    },
    {
      name: 'productionVersionRequire',
      type: FieldType.string,
      defaultValue: false,
      ignore: 'always',
    },
    {
      name: 'productionVersionDisable',
      type: FieldType.string,
      defaultValue: false,
      ignore: 'always',
    },
  ],
});

const defaultBomRouterMaterialDS = () => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/default/bom/router`,
        method: 'GET',
      };
    },
  },
});

const bomAndRouterDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  fields: [
    {
      name: 'selectType',
      type: FieldType.string,
      defaultValue: 'designChange',
    },
    {
      name: 'designProductionVersion',
      type: FieldType.object,
    },
    {
      name: 'designProductionVersionCode',
      type: FieldType.string,
      bind: 'designProductionVersion.productionVersionCode',
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionVersion`).d('生产版本'),
      disabled: true,
    },
    {
      name: 'designBom',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.bomName`).d('装配清单/版本'),
    },
    {
      name: 'designBomName',
      type: FieldType.string,
      bind: 'designBom.bomName',
      disabled: true,
    },
    {
      name: 'designBomRevision',
      type: FieldType.string,
      bind: 'designBom.revision',
      disabled: true,
    },
    {
      name: 'designRouter',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.routerName`).d('工艺路线/版本'),
    },
    {
      name: 'designRouterId',
      type: FieldType.number,
      bind: 'designRouter.routerId',
    },
    {
      name: 'designRouterName',
      type: FieldType.string,
      bind: 'designRouter.routerName',
      disabled: true,
    },
    {
      name: 'designRouterRevision',
      type: FieldType.string,
      bind: 'designRouter.revision',
      disabled: true,
    },
    {
      name: 'ownProductionVersion',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'productionVersion',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.productionVersion`).d('生产版本'),
      lovCode: 'MT.METHOD.MATERIAL.SITE.PROD-VERSION',
      textField: 'productionVersionCode',
      valueField: 'productionVersionId',
      noCache: true,
      dynamicProps: {
        lovPara({ record }) {
          return {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
            revisionCode: record.get('revisionCode'),
          };
        },
        required({ record }) {
          return record.get('ownProductionVersion') && record.get('selectType') !== 'designChange';
        },
      },
    },
    {
      name: 'bom',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.bomName`).d('装配清单/版本'),
      lovCode: 'MT.METHOD.PROD-VERSION.BOM', // 设计变更
      textField: 'bomName',
      valueField: 'bomId',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara({ record }) {
          const queryParams = {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
            revisionCode: record.get('revisionCode'),
            productionVersionCode: record.get('productionVersionCode'),
          };
          return queryParams;
        },
        disabled({ record }) {
          // 有生产版本的话，只能通过选择生产版本带出来
          return record.get('ownProductionVersion');
        },
      },
    },
    {
      name: 'bomName',
      type: FieldType.string,
      bind: 'bom.bomName',
    },
    {
      name: 'bomId',
      type: FieldType.number,
      bind: 'bom.bomId',
    },
    {
      name: 'bomRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'bom.revision',
    },
    {
      name: 'router',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.routerName`).d('工艺路线/版本'),
      lovCode: 'MT.METHOD.PROD-VERSION.ROUTER', // 设计变更
      textField: 'routerName',
      valueField: 'routerId',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        lovPara({ record }) {
          const queryParams = {
            tenantId,
            siteId: record.get('siteId'),
            materialId: record.get('materialId'),
            revisionCode: record.get('revisionCode'),
            productionVersionCode: record.get('productionVersionCode'),
          };
          return queryParams;
        },
        disabled({ record }) {
          // 有生产版本的话，只能通过选择生产版本带出来
          return record.get('ownProductionVersion');
        },
      },
    },
    {
      name: 'routerName',
      type: FieldType.string,
      bind: 'router.routerName',
    },
    {
      name: 'routerId',
      type: FieldType.number,
      bind: 'router.routerId',
    },
    {
      name: 'routerRevision',
      type: FieldType.string,
      disabled: true,
      bind: 'router.revision',
    },
  ],
});
export { formDS, defaultBomRouterMaterialDS, bomAndRouterDS };
