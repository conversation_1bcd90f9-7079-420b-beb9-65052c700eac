import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.cassetteMaterial';

const historyFactory = () =>
    new DataSet({
        primaryKey: 'hisKeyId',
        selection: false,
        paging: true,
        autoQuery: false,
        dataKey: 'rows.content',
        totalKey: 'rows.totalElements',
        queryDataSet: new DataSet({
            fields: [
                {
                    name: 'creationFrom',
                    type: FieldType.dateTime,
                    label: intl.get(`${modelPrompt}.form.creationFrom`).d('创建时间从'),
                },
                {
                    name: 'creationTo',
                    type: FieldType.dateTime,
                    label: intl.get(`${modelPrompt}.form.creationTo`).d('创建时间至'),
                },]
        }),
        fields: [
            {
                name: 'assemblePointCode',
                label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
                type: FieldType.string,
            },
            {
                name: 'description',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.description`).d('装配点描述'),
            },
            {
                name: 'materialCode',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
            },
            {
                name: 'materialName',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
            },
            {
                name: 'uomName',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.uomName`).d('单位'),
            },
            {
                name: 'revisionCode',
                label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
            },
            {
                name: 'maxQty',
                type: FieldType.number,
                label: intl.get(`${modelPrompt}.maxQty`).d('最大装载量'),
            },
            {
                name: 'enableFlag',
                lookupCode: 'MT.YES_NO',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.enableFlag`).d('有效性'),
            },
            {
                name: 'creationDate',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.creationDate`).d('创建时间'),
            },
            {
                name: 'createdByRealName',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.createdByRealName`).d('创建人'),
            },
        ],
        transport: {
            read: (config: AxiosRequestConfig): AxiosRequestConfig => {
                return {
                    ...config,
                    url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-his-Assembly-select/object/list/ui`,
                };
            },
        },
    });

export default historyFactory;
