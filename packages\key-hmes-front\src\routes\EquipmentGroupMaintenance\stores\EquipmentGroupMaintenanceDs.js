import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.EquipmentGroupMaintenance';


// const BASIC = `/tarzan-mes-29210`;
// lov配置信息
const objectLovCode = {
  MATERIAL: 'HME.ASSEMBL_EPOINT_SITE_MATERIAL', // 物料
  MATERIAL_CATEGORY: 'MT.MATERIAL_CATEGORY_SITES', // 物料类别
  OPERATION: 'MT.APS.OPERATION', // 工艺
  WORKCELL: 'MT.MODEL.WORKCELL', // 工作单元
  EQUIPMENT: 'MT.MODEL.EQUIPMENT', // 设备
  EQUIPMENT_CATEGORY: 'MT.MODEL.PRODLINE', // 设备类别
};

const objectIdBind = {
  MATERIAL: 'objectLov.materialId', // 物料
  MATERIAL_CATEGORY: 'objectLov.materialCategoryId', // 物料类别
  OPERATION: 'objectLov.operationId', // 工艺
  WORKCELL: 'objectLov.workcellId', // 工作单元
  EQUIPMENT: 'objectLov.equipmentId', // 设备
  EQUIPMENT_CATEGORY: 'objectLov.equipmentCategory', // 设备类别
};

const objectCodeBind = {
  MATERIAL: 'objectLov.materialCode',
  MATERIAL_CATEGORY: 'objectLov.categoryCode',
  OPERATION: 'objectLov.operationName',
  WORKCELL: 'objectLov.workcellCode',
  EQUIPMENT: 'objectLov.equipmentCode',
  EQUIPMENT_CATEGORY: 'objectLov.equipmentCategory',
};

const objectDescBind = {
  MATERIAL: 'objectLov.materialName',
  MATERIAL_CATEGORY: 'objectLov.description',
  OPERATION: 'objectLov.description',
  WORKCELL: 'objectLov.workcellName',
  EQUIPMENT: 'objectLov.equipmentName',
  EQUIPMENT_CATEGORY: 'objectLov.equipmentCategory',
};

const objectVersionBind = {
  MATERIAL: 'objectLov.revisionCode',
  OPERATION: 'objectLov.revision',
};

const tableDS = () => {
  return {
    name: 'tableDS',
    paging: true,
    autoQuery: true,
    selection: 'multiple',
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'assembleGroupCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.assembleGroupCode`).d('装配组编码'),
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('装配组描述'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'groupTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.groupTypeDesc`).d('装配组类型'),
      },
    ],
    queryFields: [
      {
        name: 'siteCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        labelWidth: 150,
      },
      {
        name: 'siteId',
        type: 'number',
        bind: 'siteCode.siteId',
        labelWidth: 150,
      },
      {
        name: 'assembleGroupCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.assembleGroupCode`).d('装配组编码'),
        // pattern: '^[a-zA-Z0-9_]{0,}$',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('装配组描述'),
      },
      {
        name: 'assemblePointObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.assemblePointObj`).d('装配点编码'),
        lovCode: 'HME.HME_ASSEMBLE_POINT',
        labelWidth: 150,
        ignore: 'always',
        textField: 'assemblePointCode',
        dynamicProps: {
          lovPara: ({record}) => {
            return {
              tenantId: getCurrentOrganizationId(),
              siteId:record.get('siteId'),
            };
          },
        },
      },
      {
        name: 'assemblePointId',
        bind: 'assemblePointObj.assemblePointId',
      },
      {
        name: 'assemblePointCode',
        type: 'string',
        bind: 'assemblePointObj.assemblePointCode',
      },
      {
        type: 'string',
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'MT.APS.YES_NO',
        defaultValue: 'Y',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-groups/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const formDS = () => {
  return {
    name: 'formDS',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'siteObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.siteObj`).d('站点'),
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        labelWidth: 150,
        textField: 'siteCode',
        ignore: 'always',
        required: true,
      },
      {
        name: 'siteCode',
        type: 'string',
        bind: 'siteObj.siteCode',
        labelWidth: 150,
      },
      {
        name: 'siteId',
        type: 'number',
        bind: 'siteObj.siteId',
        labelWidth: 150,
      },
      {
        name: 'assembleGroupCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.assembleGroupCode`).d('装配组编码'),
        required: true,
        pattern: '^[a-zA-Z0-9_-]{0,}$',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('装配组描述'),
      },
      {
        name: 'enableFlag',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        type: 'string',
        trueValue: 'Y',
        falseValue: 'N',
        required: true,
      },
      {
        name: 'groupType',
        type: 'string',
        label: intl.get(`${modelPrompt}.groupType`).d('装配组类型'),
        lookupCode: 'HME.ASSEMBLE_GROUP_TYPE',
        textField: 'meaning',
        valueField: 'value',
      },
    ],
  };
};

const detailTableDS = xFormDS => {
  return {
    name: 'detailTableDS',
    primaryKey: 'assembleGroupAssignId',
    paging: false, // 关闭分页
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'serialNumber',
        label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
        type: 'number',
        required: true,
      },
      {
        name: 'assemblePointCode',
        label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
        type: 'string',
      },
      {
        name: 'assemblePointId',
        type: 'number',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${modelPrompt}.description`).d('装配点描述'),
      },
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
        lovCode: 'HME.ASSEMBL_EPOINT_SITE_MATERIAL',
        labelWidth: 150,
        ignore: 'always',
        textField: 'materialCode',
        dynamicProps: {
          lovPara: () => {
            const siteId = xFormDS.current?.data?.siteObj?.siteId;
            return {
              tenantId: getCurrentOrganizationId(),
              siteId,
            };
          },
        },
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        bind: 'materialObj.materialCode',
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'materialObj.materialName',
      },
      {
        name: 'materialSiteId',
        type: 'string',
        bind: 'materialObj.materialSiteId',
      },
      {
        name: 'uomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomName`).d('单位'),
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        bind: 'materialObj.revisionCode',
        textField: 'revisionCode',
        valueField: 'revisionCode',
        lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
        lookupAxiosConfig: ({ record }) => {
          const _params = record?.toData() || {};
          if (_params && _params.materialSiteId) {
            const siteId = xFormDS.current?.data?.siteId;
            return {
              params: {
                materialSiteId: _params.materialSiteId,
                siteId,
              },
              transformResponse(data) {
                // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
                if (data instanceof Array) {
                  return data;
                }
                if (data.failed) {
                  return [];
                }
                const rows = JSON.parse(data);
                return rows;
              },
            };
          }
          return {};
        },
        dynamicProps: {
          required: record => {
            const _params = record?.record.data || {};
            if (_params && _params.revisionFlag && _params.revisionFlag === 'Y') {
              return true;
            }
            return false;
          },
        },
      },
      {
        name: 'maxQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.maxQty`).d('最大装载量'),
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        lookupCode: 'MT.APS.YES_NO',
        trueValue: 'Y',
        falseValue: 'N',
        required: true,
      },
    ],
    // events: {
    //   update({ record, dataSet }) {
    //     if (record.data && record.data.materialSiteId) {
    //       const materialSiteId = record.data.materialObj.materialSiteId
    //         ? record.data.materialObj.materialSiteId
    //         : record.data.materialSiteId;
    //       dataSet.addField('revisionCode', {
    //         name: 'revisionCode',
    //         label: '物料版本',
    //         textField: 'revisionCode',
    //         valueField: 'revisionCode',
    //         computedProps: {
    //           lookupAxiosConfig: () =>
    //             record && {
    //               url: `${BASIC}/v1/${tenantId}/hme-assemble-points/get/material/revision?materialSiteId=${materialSiteId}&siteId=${xFormDS.current?.data.siteId}`,
    //               transformResponse(data) {
    //                 try {
    //                   const jsonData = JSON.parse(data);
    //                   if (data.failed) {
    //                     return [];
    //                   }
    //                   return jsonData;
    //                 } catch (e) {
    //                   return data;
    //                 }
    //               },
    //             },
    //         },
    //       });
    //     }
    //   },
    // },
  };
};

const detailFormDS = xFormDS => {
  return {
    name: 'detailFormDS',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'assemblePointObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.assemblePointObj`).d('装配点编码'),
        lovCode: 'HME.HME_ASSEMBLE_POINT',
        labelWidth: 150,
        ignore: 'always',
        textField: 'assemblePointCode',
        dynamicProps: {
          lovPara: () => {
            const siteId = xFormDS.current?.data?.siteObj?.siteId;
            return {
              tenantId: getCurrentOrganizationId(),
              siteId,
            };
          },
        },
      },
      {
        name: 'assemblePointId',
        type: 'number',
        bind: 'assemblePointObj.assemblePointId',
      },
      {
        name: 'assemblePointCode',
        type: 'string',
        bind: 'assemblePointObj.assemblePointCode',
      },
      {
        name: 'description',
        type: 'string',
        bind: 'assemblePointObj.description',
        label: intl.get(`${modelPrompt}.approvingFlagDesc`).d('是否审批中'),
      },
    ],
  };
};

const singleAssObjectDS = xFormDS => {
  return {
    primaryKey: 'uuid',
    autoCreate: true,
    selection: false,
    autoQuery: false,
    paging: false,
    fields: [
      {
        name: 'parentSiteIds',
        type: 'object',
      },
      {
        name: 'objectType',
        label: intl.get(`${modelPrompt}.objectType`).d('关联对象类型'),
        type: 'string',
      },
      {
        name: 'objectLov',
        label: intl.get(`${modelPrompt}.objectLov`).d('关联对象编码'),
        type: 'object',
        lovCode: '',
        required: true,
        ignore: 'always',
        dynamicProps: {
          lovCode: ({ record }) => {
            const objectType = record.get('objectType');
            return objectLovCode[objectType];
          },
          lovPara() {
            const siteId = xFormDS.current?.data?.siteObj?.siteId;
            const queryPara = {
              tenantId,
              siteId,
            };
            return queryPara;
          },
        },
      },
      {
        name: 'objectId',
        type: 'number',
        dynamicProps: {
          bind: ({ record }) => {
            const objectType = record.get('objectType');
            return objectIdBind[objectType];
          },
        },
      },
      {
        name: 'objectCode',
        type: 'string',
        dynamicProps: {
          bind: ({ record }) => {
            const objectType = record.get('objectType');
            return objectCodeBind[objectType];
          },
        },
      },
      {
        name: 'objectDesc',
        label: intl.get(`${modelPrompt}.objectDesc`).d('关联对象描述'),
        type: 'string',
        dynamicProps: {
          bind: ({ record }) => {
            const objectType = record.get('objectType');
            return objectDescBind[objectType];
          },
        },
      },
      {
        name: 'revisionFlag',
        type: 'string',
        bind: 'objectLov.revisionFlag',
      },
      {
        name: 'materialSiteId',
        type: 'string',
        bind: 'objectLov.materialSiteId',
      },
      {
        name: 'objectRevision',
        type: 'string',
        label: intl.get(`${modelPrompt}.objectRevision`).d('关联对象版本'),
        // bind: 'objectLov.revisionCode',
        textField: 'revisionCode',
        valueField: 'revisionCode',
        lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-points/get/material/revision`,
        lookupAxiosConfig: ({ record }) => {
          const _params = record?.toData() || {};
          if (_params && _params.materialSiteId) {
            const siteId = xFormDS.current?.data?.siteId;
            return {
              params: {
                materialSiteId: _params.materialSiteId,
                siteId,
              },
              transformResponse(data) {
                // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
                if (data instanceof Array) {
                  return data;
                }
                if (data.failed) {
                  return [];
                }
                const rows = JSON.parse(data);
                return rows;
              },
            };
          }
          return {};
        },
        dynamicProps: {
          bind: ({ record }) => {
            const objectType = record.get('objectType');
            return objectVersionBind[objectType];
          },
          required: record => {
            // debugger
            const _params = record?.record.data || {};
            // const revisionFlag = _params.objectLov.revisionFlag?_params.objectLov.revisionFlag:_params.revisionFlag
            if (_params.objectLov?.revisionFlag && _params.objectLov?.revisionFlag === 'Y') {
              return true;
            }
            return false;
          },
        },
      },
    ],
    // events: {
    //   update({ record, dataSet }) {
    //     if (record.data && record.data.materialSiteId) {
    //       const materialSiteId = record.data.objectLov.materialSiteId
    //         ? record.data.objectLov.materialSiteId
    //         : record.data.materialSiteId;
    //       dataSet.addField('objectRevision', {
    //         name: 'objectRevision',
    //         label: '物料版本',
    //         textField: 'revisionCode',
    //         valueField: 'revisionCode',
    //         computedProps: {
    //           lookupAxiosConfig: () =>
    //             record && {
    //               url: `${BASIC}/v1/${tenantId}/hme-assemble-points/get/material/revision?materialSiteId=${materialSiteId}&siteId=${xFormDS.current?.data.siteId}`,
    //               transformResponse(data) {
    //                 try {
    //                   const jsonData = JSON.parse(data);
    //                   if (data.failed) {
    //                     return [];
    //                   }
    //                   return jsonData;
    //                 } catch (e) {
    //                   return data;
    //                 }
    //               },
    //             },
    //         },
    //       });
    //     }
    //   },
    // },
  };
};

const assObjectsDS = () => {
  return {
    name: 'assObjectsDS',
    paging: false,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'lineNumber',
        label: intl.get(`${modelPrompt}.lineNumber`).d('序号'),
        type: 'number',
      },
      {
        name: 'objectShowList',
        label: intl.get(`${modelPrompt}.objectShowList`).d('关联对象组合'),
        type: 'object',
      },
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-assemble-point-materials/get/assemble/point/material`,
          method: 'GET',
        };
      },
    },
  };
};

const enabledDS = () => {
  return {
    name: 'enabledDS',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'enableFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
        trueValue: 'Y',
        falseValue: 'N',
      },
    ],
  };
};

export { tableDS, formDS, detailTableDS, detailFormDS, singleAssObjectDS, assObjectsDS, enabledDS };
