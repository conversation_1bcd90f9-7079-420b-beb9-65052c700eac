import intl from 'utils/intl';
import { Host } from '@/utils/config';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
// import { BASIC } from '@utils/config';
// const Host = '/mes-41300'
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.changePointManagement';

const tableDS: () => DataSetProps = () => ({
  name: 'tableDS',
  primaryKey: 'changePointRecordId',
  paging: true,
  autoQuery: false,
  selection: false,
  queryFields: [
    {
      name: 'changePointRecordCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.changePointRecordCode`).d('记录编号'),
    },
    {
      name: 'man',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.man`).d('人'),

    },
    {
      name: 'machine',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.machine`).d('机'),

    },
    {
      name: 'material',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('料'),
    },
    {
      name: 'method',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.method`).d('法'),
    },
    {
      name: 'environment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.environment`).d('环'),
    },
    {
      name: 'startDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startDate`).d('变化时间从'),
    },
    {
      name: 'endDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endDate`).d('变化时间至'),
    },
    {
      name: 'type',
      type: FieldType.string,
      lookupCode: 'HME.CHANGE_POINT_TYPE',
      label: intl.get(`${modelPrompt}.type`).d('类型'),
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationLov`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      noCache: true,
      textField: 'operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
  ],
  fields: [
    {
      name: 'changePointRecordCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.changePointRecordCode`).d('记录编号'),
    },
    {
      name: 'type',
      type: FieldType.string,
      required: true,
      lookupCode: 'HME.CHANGE_POINT_TYPE',
      label: intl.get(`${modelPrompt}.type`).d('类型'),
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationLov`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      noCache: true,
      required: true,
      textField: 'operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    {
      name: 'man',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.man`).d('人'),
    },
    {
      name: 'machine',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.machine`).d('机'),
    },
    {
      name: 'material',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('料'),
    },
    {
      name: 'method',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.method`).d('法'),
    },
    {
      name: 'environment',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.environment`).d('环'),
    },
    {
      name: 'changeDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.changeDate`).d('变化时点'),
      required: true,
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
    },
    {
      name: 'solution',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.solution`).d('对策'),
    },
    {
      name: 'createdRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdRealName`).d('记录人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('记录时间'),
    },
    {
      name: 'lastUpdatedRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedRealName`).d('更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('更新时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/hme-change-point-record/query`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
