
import React, { useEffect } from 'react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Badge } from 'hzero-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { getCurrentOrganizationId } from 'utils/utils';
import { entranceDS } from './stories/EntranceDs';

const modelPrompt = 'tarzan.mes.cellSortingLevelConfiguration';

const entrance = props => {
  const columns = [
    {
      name: 'siteCode',
      lock: 'left',
      align: 'left',
    },
    {
      name: 'siteName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'levelTypeMeaning',
    },
    {
      name: 'levelCode',
      renderer: ({ record }) => {
        return (
          <a onClick={() => {
            props.history.push(`/hmes/cell-sorting-level-configuration/detail/${record?.get('levelConfigId')}`);
          }}>{record.get('levelCode')}</a>
        );
      },
    },
    { name: 'capacity',
      renderer: ({ record }) => {
        if(record.get('capacity')&&record.get('capacity').length){
          const temp = record.get('capacity')
          return <>
            {
              temp.map(item => {
                return (item?.dataValue&&item?.dataValue)
              })
            }</>
        }
        return ''
      },
    },
    {
      name: 'voltage',
      renderer: ({ record }) => {
        if(record.get('voltage')&&record.get('voltage').length){
          const temp = record.get('voltage')
          return <>
            {
              temp.map(item => {
                return (item?.dataValue&&item?.dataValue)
              })
            }</>
        }
        return ''
      },
    },
    {
      name: 'DCR',
      renderer: ({ record }) => {
        if(record.get('dcr')&&record.get('dcr').length){
          const temp = record.get('dcr')
          return <>
            {
              temp.map(item => {
                return (item?.dataValue&&item?.dataValue)
              })
            }</>
        }
        return ''
      },
    },
    {
      name: 'ACR',
      renderer: ({ record }) => {
        if(record.get('acr')&&record.get('acr').length){
          const temp = record.get('acr')
          return <>
            {
              temp.map(item => {
                return (item?.dataValue&&item?.dataValue)
              })
            }</>
        }
        return ''
      },
    },
    {
      name: 'K1',
      renderer: ({ record }) => {
        if(record.get('k1')&&record.get('k1').length){
          const temp = record.get('k1')
          return <>
            {
              temp.map(item => {
                return (item?.dataValue&&item?.dataValue)
              })
            }</>
        }
        return ''
      },
    },
    {
      name: 'K2',
      renderer: ({ record }) => {
        if(record.get('k2')&&record.get('k2').length){
          const temp = record.get('k2')
          return <>
            {
              temp.map(item => {
                return (item?.dataValue&&item?.dataValue)
              })
            }</>
        }
        return ''
      },
    },
    {
      name: 'voltageFormationEnd',
      width:150,
      renderer: ({ record }) => {
        if(record.get('voltageFormationEnd')&&record.get('voltageFormationEnd').length){
          const temp = record.get('voltageFormationEnd')
          return <>
            {
              temp.map(item => {
                return (item?.dataValue&&item?.dataValue)
              })
            }</>
        }
        return ''
      },
    },
    {
      name: 'capacityFormationEnd',
      width:150,
      renderer: ({ record }) => {
        if(record.get('capacityFormationEnd')&&record.get('capacityFormationEnd').length){
          const temp = record.get('capacityFormationEnd')
          return <>
            {
              temp.map(item => {
                return (item?.dataValue&&item?.dataValue)
              })
            }</>
        }
        return ''
      },
    },
    {
      name: 'chargeCapacitySupplement',
      width:150,
      renderer: ({ record }) => {
        if(record.get('chargeCapacitySupplement')&&record.get('chargeCapacitySupplement').length){
          const temp = record.get('chargeCapacitySupplement')
          return <>
            {
              temp.map(item => {
                return (item?.dataValue&&item?.dataValue)
              })
            }</>
        }
        return ''
      },
    },
    {
      name: 'enableFlag',
      align: 'center',
      width: 100,
      renderer: ({ record }) => (
        <Badge
          status={record.get('enableFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('enableFlag') === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'creationDate',
      width:150
    },
    {
      name: 'lastUpdateDate',
      width:150
    },
    {
      name: 'remark',
    },
    // { name: 'trueValue',
    //   renderer: ({ record }) => {
    //     if(record.get('trueValueList')&&record.get('trueValueList').length){
    //       const temp = record.get('trueValueList')
    //       return <>
    //         {
    //           temp.map(item => {
    //             return (item?.dataValue&&<Tag color="geekblue">{item?.dataValue}</Tag>)
    //           })
    //         }</>
    //     }
    //     return ''
    //   }},
    // { name: 'falseValue',
    //   renderer: ({ record }) => {
    //     if(record.get('falseValueList')&&record.get('falseValueList').length){
    //       const temp = record.get('falseValueList')
    //       return <>
    //         {
    //           temp.map(item => {
    //             return (item?.dataValue&&<Tag color="geekblue">{item?.dataValue}</Tag>)
    //           })
    //         }</>
    //     }
    //     return ''
    //   },
    // },
  ];

  const goDetail = () => {
    props.history.push(`/hmes/cell-sorting-level-configuration/detail/create`);
  };

  useEffect(() => {
    props.dataSet.query(props.dataSet.currentPage);
  }, []);

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/MT.MES.SPECIFIED_LEVEL`,
      title: intl.get(`${modelPrompt}.title.import`).d('电芯分选等级配置导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get(`${modelPrompt}.title.list`).d('电芯分选等级配置')}
        header={
          <>
            <Button
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="add"
              onClick={goDetail}
            >
              {intl.get('tarzan.common.button.create').d('新建')}
            </Button>
            <Button
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="add"
              onClick={handleImport}
            >
              {intl.get('tarzan.common.button.import').d('导入')}
            </Button>
          </>
        }
      >
        <Table
          searchCode="cellSortingLevelConfiguration"
          customizedCode="cellSortingLevelConfiguration"
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={props.dataSet}
          columns={columns}
        />
      </PageHeaderWrapper>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.cellSortingLevelConfiguration', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...entranceDS(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(entrance),
);
