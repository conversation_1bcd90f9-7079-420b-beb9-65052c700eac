import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import { BASIC } from '@utils/config';
import moment from 'moment'

const modelPrompt = 'tarzan.ass.cassetteMaterial';
const tenantId = getCurrentOrganizationId()
const historyFactory = () =>
    new DataSet({
        primaryKey: 'hisId',
        selection: false,
        paging: true,
        autoQuery: true,
        autoCreate: true,
        dataKey: 'rows.content',
        totalKey: 'rows.totalElements',
        queryDataSet: new DataSet({
            forceValidate: true,
            validateBeforeQuery: true,
            autoCreate: true,
            // 限制设备编码、浆料条码、入罐时间、出罐时间必输其一（时间成对使用，不超过3个月）。
            fields: [
                {
                    name: 'equipmentLov',
                    type: FieldType.object,
                    ignore: FieldIgnore.always,
                    lovCode: 'HME.FINISHED_PRODUCT_TANK',
                    label: intl.get(`${modelPrompt}.form.equipmentCode`).d('设备编码'),
                    required: true,
                    lovPara: {
                        tenantId,
                    },
                },
                {
                    name: 'equipmentCode',
                    bind: 'equipmentLov.value'
                },
                {
                    name: 'materialCode',
                    type: FieldType.string,
                    label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
                },
                {
                    name: 'materialLotCode',
                    type: FieldType.string,
                    label: intl.get(`${modelPrompt}.form.materialLotCode`).d('浆料条码'),
                },
                {
                    name: 'inTransferTimeFrom',
                    type: FieldType.dateTime,
                    label: intl.get(`${modelPrompt}.form.inTransferTimeFrom`).d('开始时间'),
                    max: 'inTransferTimeTo',
                    required:true,
                    dynamicProps: {
                        min: ({ record }) => {
                            if (record?.get('inTransferTimeTo')) {
                                return moment(record?.get('inTransferTimeTo')).subtract(6, 'months')
                            }
                        },
                    }
                },
                {
                    name: 'inTransferTimeTo',
                    type: FieldType.dateTime,
                    min: 'inTransferTimeFrom',
                    label: intl.get(`${modelPrompt}.form.inTransferTimeTo`).d('结束时间'),
                    required:true,
                    dynamicProps: {
                        max: ({ record }) => {
                            if (record?.get('inTransferTimeFrom')) {
                                return moment(record?.get('inTransferTimeFrom')).add(6, 'months')
                            }
                        },
                    }
                },
            ]
        }),
        fields: [
            {
                name: 'equipmentCode',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.eventId`).d('成品罐设备编码'),
            },
            {
                name: 'equipmentName',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.equipmentName`).d('成品罐设备名称'),
            },
            {
                name: 'materialLotCode',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.materialLotCode`).d('浆料条码'),
            },
            {
                name: 'materialCode',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
            },
            {
                name: 'materialName',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
            },
            {
                name: 'inTransferTime',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.inTransferTime`).d('入罐时间'),
            },
            {
                name: 'outTransferTime',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.outTransferTime`).d('出罐时间'),
            },
            {
                name: 'inTransferBy',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.inTransferBy`).d('入罐人'),
            },
            {
                name: 'outTransferBy',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.form.outTransferBy`).d('出罐人'),
            },
        ],
        transport: {
            read: (config: AxiosRequestConfig): AxiosRequestConfig => {
                return {
                    ...config,
                    method: 'POST',
                    url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-slurry-sequence-adjust-new/his-detail/query/for/ui`,
                };
            },
        },
    });

export default historyFactory;
