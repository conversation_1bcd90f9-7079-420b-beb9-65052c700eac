import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
// @ts-ignore
import { Host } from '@/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();
const modelPrompt = 'aori.andonRcAssesment';

const tableDS = (setIsEdit): DataSetProps => ({
  autoQuery: true,
  selection: false,
  primaryKey: 'lampFailureId',
  queryFields: [
    {
      label: getLang('EVALITEM_CODE'),
      type: FieldType.string,
      name: 'evalItemCode',
      maxLength: 120,
    },
    {
      label: getLang('EVALITEM_DESC'),
      type: FieldType.string,
      name: 'evalItemDesc',
      maxLength: 120,
    },
    {
      label: getLang('RELATE_ASSET'),
      type: FieldType.object,
      name: 'assetLov',
      lovCode: 'MT.MODEL.EQUIPMENT',
      ignore: FieldIgnore.always,
      textField: 'equipmentCode',
      valueField: 'equipmentId',
    },
    {
      name: 'assetId',
      type: FieldType.number,
      bind: 'assetLov.equipmentId',
    },
    {
      name: 'equipmentId',
      type: FieldType.number,
      bind: 'assetLov.equipmentId',
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      bind: 'assetLov.equipmentCode',
    },
    {
      label: intl.get(`${modelPrompt}.confirmLov`).d('创建人'),
      type: FieldType.object,
      name: 'confirmLov',
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      lovPara: {
        tenantId: organizationId,
      },
    },
    {
      type: FieldType.number,
      name: 'createdBy',
      bind: 'confirmLov.id',
    },
    {
      name: 'enabledFlag',
      type: FieldType.number,
      lookupCode: 'HPFM.FLAG',
      trueValue: 1,
      falseValue: 0,
      defaultValue: 1,
      label: getLang('ENABLED_FLAG'),
    },
  ],
  fields: [
    {
      label: getLang('EVALITEM_CODE'),
      type: FieldType.string,
      name: 'evalItemCode',
      maxLength: 120,
      required: true,
    },
    {
      label: getLang('EVALITEM_DESC'),
      type: FieldType.string,
      name: 'evalItemDesc',
      maxLength: 120,
      required: true,
    },
    {
      label: intl.get(`${modelPrompt}.assetLov`).d('关联设备编码'),
      type: FieldType.object,
      name: 'assetLov',
      lovCode: 'MT.MODEL.EQUIPMENT',
      ignore: FieldIgnore.always,
      lovPara: {
        aclFlag: 1,
        organizationId,
      },
      textField: 'equipmentCode',
      valueField: 'equipmentId',
    },
    {
      name: 'equipmentId',
      type: FieldType.number,
      bind: 'assetLov.equipmentId',
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      bind: 'assetLov.equipmentCode',
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      bind: 'assetLov.equipmentName',
      label: intl.get(`${modelPrompt}.equipmentName`).d('设备名称'),

    },
    {
      label: getLang('DESC'),
      type: FieldType.string,
      name: 'description',
      maxLength: 240,
    },
    {
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),

      type: FieldType.string,
      name: 'createdByName',
    },
    {
      label: getLang('CREATION_DATE'),
      type: FieldType.dateTime,
      name: 'creationDate',
    },
    {
      label: getLang('LAST_UPDATE_DATE'),
      type: FieldType.dateTime,
      name: 'lastUpdateDate',
    },
    {
      label: getLang('UPDATE_EMPLOYEE'),
      type: FieldType.string,
      name: 'lastUpdatedByName',
    },
    {
      name: 'enabledFlag',
      type: FieldType.number,
      trueValue: 1,
      falseValue: 0,
      defaultValue: 1,
      label: getLang('ENABLED_FLAG'),
    },
  ],
  events: {
    load: () => {
      setIsEdit(false)
    },
  },
  transport: {
    read: ({ data, params }) => {
      const url = `${Host}/v1/${organizationId}/aaccm-lamp-failures/query`;
      return {
        url,
        params: {
          ...params,
          ...data,
        },
        method: 'GET',
      };
    },
    submit: ({ data }) => {
      return {
        url: `${Host}/v1/${organizationId}/aaccm-lamp-failures/create-update`,
        data: {
          ...data[0],
        },
        method: 'POST',
      };
    },
  },
});

export { tableDS };
