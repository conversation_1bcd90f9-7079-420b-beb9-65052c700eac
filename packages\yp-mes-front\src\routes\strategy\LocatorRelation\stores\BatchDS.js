/**
 * @feature 物料库位关系维护-批量导入页面的DS
 * @date 2021-12-15
 * <AUTHOR>
 */
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.strategy.locatorRelation';
const tenantId = getCurrentOrganizationId();

const batchDS = () => ({
  autoQuery: true,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: 'multiple',
  autoLocateFirst: false,
  lang: getCurrentLanguage(),
  queryFields: [
    {
      name: 'siteObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObject.siteCode',
    },
    {
      name: 'materialObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: { tenantId },
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialObject.materialCode',
    },
    {
      name: 'locatorObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      noCache: true,
      ignore: 'always',
      lovPara: { tenantId },
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'locatorObject.locatorCode',
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('是否成功'),
      textField: 'meaning',
      valueField: 'typecode',
      options: new DataSet({
        data: [
          {
            meaning: intl.get(`${modelPrompt}.yes`).d('是'),
            typecode: 'Y',
          },
          {
            meaning: intl.get(`${modelPrompt}.no`).d('否'),
            typecode: 'N',
          },
        ],
        selection: 'single',
        autoQuery: true,
      }),
    },
  ],
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('序号'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('是否成功'),
      textField: 'meaning',
      valueField: 'typecode',
      options: new DataSet({
        data: [
          {
            meaning: intl.get(`${modelPrompt}.yes`).d('是'),
            typecode: 'Y',
          },
          {
            meaning: intl.get(`${modelPrompt}.no`).d('否'),
            typecode: 'N',
          },
        ],
        selection: 'single',
        autoQuery: true,
      }),
    },
    {
      name: 'importMsg',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.importMsg`).d('导入信息'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerType`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-locator-material-rel/import/data/query/ui`,
        method: 'GET',
      };
    },
    destroy: config => {
      const lineNumber = [];
      const { data } = config;
      data.forEach(item => {
        lineNumber.push(item.lineNumber);
      });
      return {
        ...config,
        url: `${
          BASIC.HMES_BASIC
        }/v1/${getCurrentOrganizationId()}/mt-locator-material-rel/import/data/delete/ui`,
        data: lineNumber,
      };
    },
  },
});

export { batchDS };
