/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2022-06-13 11:16:14
 * @LastEditTime: 2022-06-13 11:19:55
 * @LastEditors: <<EMAIL>>
 */
// import React, { useEffect } from 'react';
// import { Table, Switch, InputNumber } from 'choerodon-ui';
// import intl from 'utils/intl';
// // import { Size } from 'choerodon-ui/lib/_util/enum';
//
// const modelPrompt = 'tarzan.aps.common';
//
// const ExpandTable = (props) => {
//   const { dataSource } = props;
//
//   useEffect(() => {
//     console.log(dataSource);
//   }, [dataSource]);
//
//   const renderSwitch = (val, type, index, record = {}) => (
//     <Switch
//       defaultChecked={val}
//       disabled={record.disabled}
//       // onChange={() => changeSwitchValue(type, index)}
//     />
//   );
//
//   const columns = [
//     {
//       title: intl.get(`${modelPrompt}.table.fieldName`).d('字段名'),
//       dataIndex: 'fieldName',
//       width: 150,
//     },
//     {
//       title: intl.get(`${modelPrompt}.table.hidden`).d('是否显示'),
//       dataIndex: 'hidden',
//       align: 'center',
//       width: 150,
//       render: (val, record, index) => renderSwitch(val, 'hidden', index, record),
//     },
//     {
//       title: intl.get(`${modelPrompt}.table.orderSeq`).d('显示顺序'),
//       dataIndex: 'orderSeq',
//       width: 150,
//       render: (val, record) => (
//         <InputNumber
//           disabled={record.disabled}
//           defaultValue={val}
//           // onChange={(value) => changeSeqValue(value, index)}
//         />
//       ),
//     },
//     {
//       title: intl.get(`${modelPrompt}.table.fixedLeft`).d('是否冻结'),
//       dataIndex: 'fixedLeft',
//       align: 'center',
//       width: 150,
//       render: (val, record, index) => renderSwitch(val, 'fixedLeft', index, record),
//     },
//   ];
//
//   return (
//     <div className="hmes-style">
//       <Table
//         columns={columns}
//         bordered={false}
//         // rowKey="cid"
//         // size={Size.large}
//         dataSource={dataSource}
//         pagination={false}
//         filterBar={false}
//       />
//     </div>
//   );
// };
//
// export default ExpandTable;
