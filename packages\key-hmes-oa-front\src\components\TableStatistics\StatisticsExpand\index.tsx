/**
 * 处理搜索统计汇总字段配置，配置哪些字段可以汇总，汇总维度字段，比如 对编码字段汇总，汇总的维度字段是数量
 */
import React, { CSSProperties, FC, useRef, useMemo } from 'react';
import { Modal, Button } from 'choerodon-ui/pro';
import { Icon } from 'choerodon-ui';
import intl from 'utils/intl';
import myInstance from '@/utils/myAxios';
import notification from 'utils/notification';
import { cloneDeep } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import ExpandTable from './ExpandTable';

export interface ConfigProps {
  fieldValue: any;
  fieldMeaning: any;
  type?: string;
}
export interface ExpandProps {
  keyStr: string; // 表格唯一标识
  baseServer: string; // BASE_SERVER
  style?: CSSProperties;  // 自定义样式
  queryData: () => void; // 汇总统计查询
  initConfigData: Array<ConfigProps>; // 汇总列初始配置
  options?: Array<ConfigProps>; // 汇总字段选项
}

const modelPrompt = 'tarzan.aps.statistics';

const commonStyle: CSSProperties = {
  textAlign: 'right',
  marginBottom: '8px',
  marginTop: '-1px',
};

const StatisticsExpand: FC<ExpandProps> = ({
  keyStr,
  baseServer,
  style,
  queryData,
  initConfigData,
  options,
}) => {
  const modal = useRef<any>(null); // 当前模态框
  const cache = useRef<any>(null); // 缓存初始数据
  const columnsRef = useRef<any>(null);
  const optionsRef = useRef<any>(null);

  const sortFields = fields =>
    fields
      .sort((a, b) => Number(a.showOrder) - Number(b.showOrder))
      .map((item, index) => {
        return {
          ...item,
          showOrder: (index + 1) * 10,
        };
      });

  // 根据传入配置字段数据生成初始配置数据
  const generateInitConfigData = (initData, originData = []) => {
    const originDataMap = originData.reduce((pre, next) => {
      pre[(next as any).fieldValue] = next;
      return pre;
    }, {});

    let tableData = initData
      .filter(item => item.type !== 'option')
      .map((item, index) => {
        const originCurrentField = originDataMap[item.fieldValue];
        return {
          fieldValue: item.fieldValue,
          fieldMeaning: item.fieldMeaning,
          showOrder: originCurrentField ? originCurrentField.showOrder : (index + 1) * 10,
          isCollect: originCurrentField ? originCurrentField.isCollect : 'N',
          collectFieldValue: originCurrentField ? originCurrentField.collectFieldValue : '',
          collectFieldMeaning: originCurrentField ? originCurrentField.collectFieldMeaning : '',
        };
      });
    tableData = sortFields(tableData);
    const optionsData = options
      ? options
      : initData.filter(item => ['option','all'].includes(item.type));

    // 重新设置表格配置数据及汇总字段选项
    optionsRef.current = optionsData;
    columnsRef.current = tableData;
    // 缓存值
    cache.current = cloneDeep(tableData);
    // 重新渲染配置弹框
    modal.current.update({
      children: _renderContent(false, tableData),
    });
  };

  // 获取汇总字段配置数据
  const queryBasicData = async () => {
    const url = `${baseServer}/v1/${getCurrentOrganizationId()}/hps-collect-column-configs/list?moduleCode=${keyStr}`;
    const res = await myInstance.get(url);
    if (res && res.data && res.data.rows) {
      const originData = res.data.rows;
      // 根据初始字段配置与数据库数据生成当前配置数据
      generateInitConfigData(initConfigData, originData);
    } else {
      notification.error({
        description: null,
        message: res.data.message,
      });
    }
  };

  const cancelModal = () => {
    resetData();
    modal.current = null;
  };

  const closeModal = () => {
    modal.current.close();
    cancelModal();
  };

  // 保存汇总配置
  const submitData = async () => {
    const configFieldsData = columnsRef.current || [];
    const nullConfigFields = configFieldsData
      .filter(
        fields => fields.isCollect === 'Y' && (!fields.fieldValue || !fields.collectFieldValue),
      )
      .map(fields => fields.fieldMeaning);

    if (nullConfigFields && nullConfigFields.length) {
      notification.error({
        description: null,
        message: intl
          .get(`${modelPrompt}.error.nullConfigFields`)
          .d(`${nullConfigFields.join('、')} 的汇总字段不能为空`),
      });
      return;
    }

    const url = `${baseServer}/v1/${getCurrentOrganizationId()}/hps-collect-column-configs/${keyStr}/save`;

    await myInstance.post(url, sortFields(configFieldsData)).then(res => {
      if (res.data && res.data.failed && res.data.message) {
        notification.error({
          description: null,
          message: res.data.message,
        });
      } else if (res.data && res.status === 200) {
        modal.current.close();
        modal.current = null;
        cache.current = cloneDeep(res.data);
        // 重新执行一次查询
        queryData();
      }
    });
  };

  const resetData = () => {
    const _middle = cloneDeep(cache.current);
    columnsRef.current = _middle;
  };
  const changeSwitchValue = (value, types, index) => {
    const middle = columnsRef.current;
    const switchValue = value ? 'Y' : 'N';
    middle[index][types] = switchValue;
    columnsRef.current = middle;
  };

  const changeSeqValue = (value, index) => {
    const prev = columnsRef.current;
    prev[index].showOrder = value;
    columnsRef.current = prev;
  };

  const changeSelectValue = (value, index) => {
    const prev = columnsRef.current;
    const selectedOption = optionsRef.current.find(option => option.fieldValue === value);
    const name = selectedOption ? selectedOption.fieldMeaning : '';
    prev[index].collectFieldValue = value;
    prev[index].collectFieldMeaning = name;
    columnsRef.current = prev;
  };

  const _renderContent = (loading, datas: Array<any> = []) => (
    <ExpandTable
      isLoading={loading}
      dataSource={datas}
      changeSeqValue={changeSeqValue}
      changeSwitchValue={changeSwitchValue}
      changeSelectValue={changeSelectValue}
      numberColumns={optionsRef.current}
    />
  );
  /**
   * 打开模态框扩展列模态框
   * @return {void} 无
   * <AUTHOR> 2021-06-04 10:11:39 星期五
   */
  const openModal = () => {
    queryBasicData();
    modal.current = Modal.open({
      key: `${keyStr}ModalKey`,
      title: intl.get(`${modelPrompt}.title.expand.modal`).d('汇总字段配置'),
      style: { width: '720px' },
      className: 'hcmp-modal',
      destroyOnClose: false,
      drawer: true,
      closable: true,
      onClose: cancelModal,
      footer: (
        <div id="hcmpModal">
          <Button onClick={closeModal}>
            {intl.get(`tarzan.aps.common.button.cancel`).d('取消')}
          </Button>
          <Button color={ButtonColor.primary} onClick={submitData}>
            {intl.get(`tarzan.aps.common.button.sure`).d('确定')}
          </Button>
        </div>
      ),
      children: _renderContent(true),
    });
  };

  return (
    <>
      <div className="expand-btn" style={{ ...commonStyle, ...style }}>
        <Icon
          style={{ cursor: 'pointer', fontSize: '16px' }}
          onClick={openModal}
          type="format_list_bulleted"
        />
      </div>
    </>
  );
};

export default StatisticsExpand;
