/*
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2024-03-07 14:53:59
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-19 13:52:02
 */
import React, { forwardRef } from 'react';
import intl from 'utils/intl';
import './index.module.less';

const modelPrompt = 'tarzan.hmes.deliveryReportPrint';

const PrintReportItem = ({ dataSource }) => {
  return (
    <div className="print-pdf-report">
      <table
        border="1"
        className={
          dataSource.result === '合格'
            ? 'print-pdf-report-table print-pdf-report-table-ok'
            : 'print-pdf-report-table'
        }
      >
        <tr className="print-pdf-report-table-header">
          <td colSpan={9}>
            {intl.get(`${modelPrompt}.report.title`).d('PACK出货报告')}
            <div className="print-pdf-report-table-header-icon" />
          </td>
        </tr>
        <tr>
          <td colSpan={2}>{intl.get(`${modelPrompt}.report.project`).d('项目')}</td>
          <td colSpan={2}>{dataSource?.project}</td>
          <td>{intl.get(`${modelPrompt}.report.custom`).d('客户')}</td>
          <td colSpan={4}>{dataSource?.custom}</td>
        </tr>
        <tr>
          <td colSpan={2}>{intl.get(`${modelPrompt}.report.stage`).d('阶段')}</td>
          <td colSpan={2}>{dataSource?.stage}</td>
          <td>{intl.get(`${modelPrompt}.report.materialCode`).d('客户零件号')}</td>
          <td colSpan={4}>{dataSource?.materialCode}</td>
        </tr>
        <tr>
          <td colSpan={2}>{intl.get(`${modelPrompt}.report.productShortName`).d('产品简称')}</td>
          <td colSpan={2}>{dataSource?.productShortName}</td>
          <td>{intl.get(`${modelPrompt}.report.identification`).d('PACK条码')}</td>
          <td colSpan={4}>{dataSource?.identification}</td>
        </tr>
        <tr>
          <td colSpan={2}>{intl.get(`${modelPrompt}.report.model`).d('产品型号')}</td>
          <td colSpan={2}>{dataSource?.model}</td>
          <td>{intl.get(`${modelPrompt}.report.actualEndTime`).d('下线时间')}</td>
          <td colSpan={4}>{dataSource?.actualEndTime}</td>
        </tr>
        <tr>
          <td>{intl.get(`${modelPrompt}.report.category`).d('类别')}</td>
          <td>{intl.get(`${modelPrompt}.report.sequence`).d('序号')}</td>
          <td>{intl.get(`${modelPrompt}.report.tagDescription`).d('项目')}</td>
          <td>{intl.get(`${modelPrompt}.report.trueValue`).d('标准')}</td>
          <td>{intl.get(`${modelPrompt}.report.equipmentName`).d('测量仪器')}</td>
          <td className="print-pdf-report-table-line-cell-15">
            {intl.get(`${modelPrompt}.report.frequency`).d('测量频率')}
          </td>
          <td className="print-pdf-report-table-line-cell-15">
            {intl.get(`${modelPrompt}.report.tagCalculateResult`).d('判定')}
          </td>
          <td className="print-pdf-report-table-line-cell-15">
            {intl.get(`${modelPrompt}.report.loginName`).d('检验人')}
          </td>
          <td className="print-pdf-report-table-line-cell-15">
            {intl.get(`${modelPrompt}.report.remark`).d('备注')}
          </td>
        </tr>
        {(dataSource?.lineList || []).map((lineItem, index) => (
          <tr>
            {index === 0 && (
              <td rowSpan={dataSource?.lineList?.length || 1}>
                {intl.get(`${modelPrompt}.report.performance`).d('性能类')}
              </td>
            )}
            <td>{lineItem?.sequence || index + 1}</td>
            <td>{lineItem?.tagDescription}</td>
            <td>{(lineItem?.trueValueList || []).map(i => i.dataValue).join(',')}</td>
            <td>{lineItem?.equipmentName}</td>
            <td className="print-pdf-report-table-line-cell-15">{lineItem?.frequency}</td>
            <td className="print-pdf-report-table-line-cell-15">{lineItem?.tagCalculateResult}</td>
            <td className="print-pdf-report-table-line-cell-15">{lineItem?.loginName}</td>
            <td className="print-pdf-report-table-line-cell-15" />
          </tr>
        ))}
        <tr>
          <td>{intl.get(`${modelPrompt}.report.appearance`).d('外观')}</td>
          <td />
          <td />
          <td />
          <td />
          <td />
          <td />
          <td />
          <td />
        </tr>
        <tr>
          <td colSpan={2}>{intl.get(`${modelPrompt}.report.remark`).d('备注')}</td>
          <td colSpan={7} />
        </tr>
        <tr>
          <td colSpan={2}>{intl.get(`${modelPrompt}.report.decide`).d('判定')}</td>
          <td colSpan={7}>
            <div className="check-box-group-cell">
              <div className="check-box-group">
                <div
                  className={
                    dataSource.result === '合格' ? 'check-box check-box-select' : 'check-box'
                  }
                />
                <div className="check-box-label">
                  {intl.get(`${modelPrompt}.report.ok`).d('合格')}
                </div>
              </div>
              <div className="check-box-group">
                <div
                  className={
                    dataSource.result === '不合格' ? 'check-box check-box-select' : 'check-box'
                  }
                />
                <div className="check-box-label">
                  {intl.get(`${modelPrompt}.report.ng`).d('不合格')}
                </div>
              </div>
            </div>
          </td>
        </tr>
      </table>
    </div>
  );
};

const PerviewComponent = props => {
  const { dataSource } = props;
  return (
    <div id="print-pdf">
      {(dataSource || []).map(dataItem => (
        <PrintReportItem dataSource={dataItem} />
      ))}
    </div>
  );
};

export default forwardRef(PerviewComponent);
