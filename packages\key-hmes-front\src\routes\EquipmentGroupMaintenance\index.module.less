.card-select-wrapper {
  display: flex;
  flex-wrap: wrap;
 // justify-content: space-between;
  padding: 0 20px;

  .card-select {
    //width: 15%;
    width: 20%; // 注释掉了工艺路线，装配清单，装配清单组件,故样式有所改变
    height: 28px;
    line-height: 28px;
    text-align: center;
    border: 1px solid#d9d9d9;
    border-radius: 4px;
    color: #333;
    cursor: pointer;
    margin-bottom: 24px;
    margin-left: 40px;
    margin-right: 40px;

    &:hover {
      color: #4ad0df;
      border-color: #4ad0df;
    }

    &[data-selected] {
      color: #29bece;
      border-color: #29bece;
    }

    &[disabled] {
      pointer-events: none;
      color: rgba(0, 0, 0, 0.25);
      border-color: #d9d9d9;
    }

    &[data-selected][disabled] {
      color: #333;
      border-color: rgba(0, 0, 0, 0.2);
    }
  }
}


.hmes-style {
  :global {
    .c7n-pro-btn-primary.c7n-pro-btn-raised:enabled:focus {
      background-color: rgb(30, 50, 85) !important;
      border-color: rgb(30, 50, 85) !important;
      color: rgb(255, 255, 255) !important;
    }
  }
}

.addon-after-compare-icon {
  color: #29bece;
  cursor: pointer;
}

.query-header-wrapper {
  :global {
    .icon {
      cursor: pointer;
    }

    .c7n-pro-select-group-before,
    .c7n-pro-select-group-after,
    .c7n-pro-select-group-help,
    .c7n-pro-input-group-before,
    .c7n-pro-input-group-after,
    .c7n-pro-input-group-help,
    .c7n-pro-calendar-picker-group-before,
    .c7n-pro-calendar-picker-group-after,
    .c7n-pro-calendar-picker-group-help {
      background-color: #fff !important;
      border: none !important;
    }

    .c7n-pro-select-group-input:not(:last-child) .c7n-pro-select,
    .c7n-pro-input-group-input:not(:last-child) .c7n-pro-input {
      border-radius: 4px !important;
    }

    .c7n-pro-calendar-picker-group,
    .c7n-pro-select-group,
    .c7n-pro-input-group {
      display: block !important;
      position: relative;
    }
    .c7n-pro-calendar-picker-group-after,
    .c7n-pro-select-group-after,
    .c7n-pro-input-group-after {
      position: absolute;
      top: 3px;
      right: -25px;
    }

    .c7n-pro-form-wrapper {
      padding-right: 20px;
    }

    .c7n-pro-calendar-picker-group-wrapper,
    .c7n-pro-select-group-wrapper,
    .c7n-pro-input-group-wrapper {
      display: block !important;
    }
  }

  .query-header-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .query-header-title-dimension {
      font-size: 14px;
    }
  }

  .right-data-compare {
    height: 20px;
    display: flex;
    align-items: center;
    color: #29bece;

    .compare-icon {
      margin-right: 10px;
    }
    .right-data-compare-text {
      margin-bottom: 2px;
    }
  }

  .customer-divider {
    width: 100%;
    height: 1px;
    opacity: 1;
    background-color: #e4e4e4;
    margin: 15px 0;
  }

  .add-icon {
    margin-left: 12px;
    margin-bottom: 3px;
    color: #666;
  }

  .add-icon:hover {
    color: #29bece;
  }

  .query-header-row-header {
    vertical-align: middle;
    display: flex;
    margin: 10px 0;

    .remove-icon {
      margin-right: 12px;
      color: #666;
    }
  }

  .query-header-row-form{
    margin-left: 18px;
  }
}

.hcm-hpro-table-wrapper {
  margin-bottom: 24px;
  :global {
    .c7n-pro-table-empty-row td {
      border-bottom: 1px solid #e8e8e8 !important;
    }

    .c7n-pro-table-body {
      overflow-x: hidden !important;
    }
  }

  .hcm-icon-wrapper {
    display: flex;
    flex-flow: row-reverse;
    color: #29bece;
    cursor: pointer;
  }
}

.hcm-date-picker {
  :global {
    .c7n-pro-calendar-header {
      display: none;
    }
    .c7n-pro-calendar-old .c7n-pro-calendar-cell-inner {
      color: inherit;
    }
  }
}

.hcm-year-picker {
  :global {
    .c7n-pro-calendar-view-select {
      pointer-events: none;
      color: rgba(0, 0, 0, 0.25);
      cursor: not-allowed;
      opacity: 1;
    }
  }
}

.hpro-picker-wrapper {
  width: 2.8rem;
  line-height: 1.5;
  background-clip: padding-box;
  padding: 0.08rem 0.12rem;

  table {
    width: 100%;
    font-size: 12.03px;
  }

  .hpro-calendar-cell {
    height: 0.3rem;
    padding: 0.14rem 0;
    text-align: center;

    .hpro-calendar-cell-inner {
      display: block;
      height: 0.24rem;
      margin: 0 auto;
      padding: 0;
      line-height: 0.22rem;
      text-align: center;
      background-color: transparent;
      border: 0.01rem solid transparent;
      border-radius: 0.02rem;
      cursor: pointer;
      -webkit-transition: background 0.3s ease;
      transition: background 0.3s ease;
    }

    .hpro-calendar-cell-inner:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
    .hpro-calendar-cell-inner:active {
      background-color: #29bece;
      color: #fff;
    }
  }
}

:global {
  #hpro-menu-container {
    .c7n-menu-submenu-vertical > .c7n-menu-submenu-title .c7n-menu-submenu-arrow {
      right: 0.12rem !important;
    }

    .c7n-pro-dropdown-popup .c7n-menu-vertical > .c7n-menu-submenu > .c7n-menu-submenu-title {
      padding-right: 0.36rem !important;
      padding-left: 0.12rem !important;
    }
  }

  .hpro-menu-wrapper {
    .c7n-menu-horizontal.c7n-menu-sub,
    .c7n-menu-vertical-left.c7n-menu-sub,
    .c7n-menu-vertical-right.c7n-menu-sub,
    .c7n-menu-vertical.c7n-menu-sub {
      min-width: 1rem !important;
    }
  }
}

