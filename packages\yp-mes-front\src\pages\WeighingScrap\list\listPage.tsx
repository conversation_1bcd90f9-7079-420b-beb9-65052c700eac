import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import { useDataSetEvent } from 'utils/hooks';
import { Button as PermissionButton } from 'components/Permission';
import { getCurrentOrganizationId } from 'utils/utils';
import ExcelExportPro from 'components/ExcelExportPro';
import notification from 'utils/notification';
import listPageFactory from '../stores/listPageDs';
import axios from 'axios';

const tenantId = getCurrentOrganizationId();
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.hmes.weighingScrap';

const ListPageComponent: FC<ListPageProps> = ({ listDs, history, match: { path } }) => {
  const [closeFlag, setCloseFlag] = useState(true);
  const [weighingFlag, setWeighingFlag] = useState(true);

  useEffect(() => {
    listDs.query();
  }, []);

  useDataSetEvent(listDs, 'load', () => {
    setCloseFlag(false);
    setWeighingFlag(false);
  });

  useDataSetEvent(listDs, 'select', () => {
    eventStatus();
  });

  useDataSetEvent(listDs, 'selectAll', () => {
    eventStatus();
  });

  useDataSetEvent(listDs, 'unSelectAll', () => {
    eventStatus();
  });

  useDataSetEvent(listDs, 'unselect', () => {
    eventStatus();
  });

  const eventStatus = () => {
    if (listDs.selected.length > 0) {
      const flag1 = listDs.selected.every(item => item.get('docStatus') === 'NEW');
      const flag2 = listDs.selected.every(item => item.get('docStatus') === 'COMPLETED');
      setCloseFlag(!flag1);
      setWeighingFlag(!flag2);
    } else {
      setCloseFlag(false);
      setWeighingFlag(false);
    }
  };

  const columns: ColumnProps[] = [
    {
      name: 'docNum',
      width: 150,
      renderer: ({ record }) => {
        return (
          <a
            onClick={() => {
              onHandleOrder(record?.get('docId'));
            }}
          >
            {record?.get('docNum')}
          </a>
        );
      },
    },
    {
      name: 'docType',
    },
    {
      name: 'docStatus',
    },
    {
      name: 'siteCode',
    },
    {
      name: 'workOrderNum',
      width: 150,
    },
    {
      name: 'equipmentCode',
      width: 150,
    },
    {
      name: 'weightFactor',
      width: 120,
    },
    {
      name: 'meterFactor',
      width: 120,
    },
    {
      name: 'scarpWeight',
    },
    {
      name: 'creationDate',
      width: 150,
    },
    {
      name: 'createdByRealName',
    },
    {
      name: 'lastUpdateDate',
      width: 150,
    },
    {
      name: 'scrapTime',
    },
    {
      name: 'lastUpdatedByRealName',
    },
    {
      name: 'remark',
    },
  ];

  const onHandleOrder = id => {
    history.push(`/hmes/weighingScrap/detail/${id}`);
  };
  const handleCreate = () => {
    history.push(`/hmes/weighingScrap/detail/create`);
  };

  const handleClose = async () => {
    const idList = listDs.selected.map(item => item.get('docId'));
    const url = `${
      BASIC.HMES_BASIC
    }/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/close`;
    const res: any = await axios.post(url, idList);
    if (res && res.success) {
      await listDs.query();
    } else {
      notification.error({ message: res.message });
    }
  };

  const handleWeighing = async () => {
    const idList = listDs.selected.map(item => item.get('docId'));
    const url = `${
      BASIC.HMES_BASIC
    }/v1/${getCurrentOrganizationId()}/hme-weighing-scrap-platform/scrap/cancel`;
    const res: any = await axios.post(url, idList);
    if (res && res.success) {
      await listDs.query();
    } else {
      notification.error({ message: res.message });
    }
  };

  const getExportQueryParams = () => {
    return {
      ...listDs?.queryDataSet?.toJSONData()[0],
      docIds: listDs.selected.map(item => item.get('docId')),
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.weighingScrap`).d('称重报废平台')}>
        <Button icon="add" color={ButtonColor.primary} onClick={handleCreate}>
          {intl.get('tarzan.common.button.create').d('新增')}
        </Button>
        <Button disabled={closeFlag} onClick={() => handleClose()}>
          {intl.get('tarzan.common.button.close').d('关闭')}
        </Button>
        <ExcelExportPro
          method="POST"
          exportAsync={false}
          allBody
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weighing-scrap-platform/export`}
          queryParams={getExportQueryParams}
          buttonText="导出"
        />
        <PermissionButton
          type="c7n-pro"
          disabled={weighingFlag}
          onClick={() => handleWeighing()}
          permissionList={[
            {
              code: `${path}.button.weighing`,
              type: 'button',
              meaning: '称重报废平台-报废撤销',
            },
          ]}
        >
          {intl.get('tarzan.common.button.weighing').d('报废撤销')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="weighingScrap"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={4} // 头部显示的查询字段的数量
          searchCode="weighingScrap" // 动态筛选条后端接口唯一编码
          customizedCode="weighingScrap" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.hmes.weighingScrap'],
})(ListPage);
