/**
 * @Description: 站点维护-列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-02-02 09:54:28
 * @LastEditTime: 2023-05-18 11:14:35
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores/SiteDS';

const modelPrompt = 'tarzan.model.org.site';

const SiteList = props => {
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;
  useEffect(() => {
    tableDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.SITE_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.SITE_LIST.LIST`)
    tableDs.query(props.tableDs.currentPage);
  }, []);
  const columns = [
    {
      name: 'siteCode',
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(`/hmes/organization-modeling/site/detail/${record.data.siteId}`);
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'siteName',
    },
    {
      name: 'siteTypeDesc',
      align: 'center',
    },
    {
      name: 'enableFlag',
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
  ];

  const handleCreate = () => {
    props.history.push('/hmes/organization-modeling/site/detail/create');
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.siteMaintenance`).d('站点维护')}>
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.SITE_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.SITE_LIST.LIST`,
          },
          <Table
            queryBar='filterBar'
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="SiteList"
            customizedCode="SiteList"
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.model.org.site', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.SITE_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.SITE_LIST.LIST`] }),
)(SiteList);
