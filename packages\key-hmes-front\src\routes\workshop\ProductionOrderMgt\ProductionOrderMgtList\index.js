/**
 * @Description: 生产指令管理列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:40:22
 * @LastEditTime: 2023-05-18 14:41:23
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { DataSet, Table } from 'choerodon-ui/pro';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Button as PermissionButton } from 'components/Permission';
import { BASIC } from '@utils/config';
import { tableDS } from '../stores/ProductionOrderMgtListDS';

const modelPrompt = 'tarzan.workshop.productionOrderMgt';

const ProductionOrderMgtList = props => {
  const {
    dataSet,
    match: { path },
    customizeTable,
  } = props;

  useEffect(() => {
    dataSet.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.LIST.TABLE`,
    );
    dataSet.query(props.dataSet.currentPage);
  }, []);

  const orderDetail = id => {
    props.history.push(`/hmes/workshop/production-order-mgt/detail/${id}`);
  };

  const columns = [
    {
      name: 'workOrderNum',
      lock: 'left',
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              orderDetail(record.data.workOrderId);
            }}
          >
            {value}
          </a>
        );
      },
      width: 180,
    },
    {
      name: 'status',
      width: 150,
    },
    // {
    //   name: 'siteName',
    //   width: 170,
    // },
    {
      name: 'materialCode',
      width: 180,
    },
    {
      name: 'revisionCode',
      width: 120,
    },
    {
      name: 'materialName',
      width: 170,
    },
    {
      name: 'qty',
      width: 100,
      align: 'right',
      renderer: ({ value }) => value || 0,
    },
    {
      name: 'kitQty',
      width: 100,
      align: 'right',
      renderer: ({ value }) => value || 0,
    },
    {
      name: 'releasedQty',
      width: 100,
      align: 'right',
      renderer: ({ value }) => value || 0,
    },
    {
      name: 'completedQty',
      width: 100,
      align: 'right',
      renderer: ({ value }) => value || 0,
    },
    {
      name: 'scrappedQty',
      width: 100,
      align: 'right',
      renderer: ({ value }) => value || 0,
    },
    {
      name: 'planStartTime',
      width: 170,
      align: 'center',
    },
    {
      name: 'planEndTime',
      width: 170,
      align: 'center',
    },
    {
      name: 'creationDate',
      width: 170,
      align: 'center',
    },
    {
      name: 'workOrderType',
      width: 150,
    },
    {
      name: 'productionLineCode',
      width: 180,
    },
    // {
    //   name: 'productionLineName',
    //   width: 140,
    // },
    {
      name: 'siteCode',
      width: 120,
    },
  ];

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get(`${modelPrompt}.model.productionOrderMgt.productionOrderMgt`)
          .d('生产指令管理')}
      >
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="edit-o"
          onClick={() => orderDetail('create')}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.LIST.TABLE`,
          },
          <Table
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={dataSet}
            columns={columns}
            searchCode="ProductionOrderMgtList"
            customizedCode="ProductionOrderMgtList"
          />,
        )}
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.workshop.productionOrderMgt', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...tableDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.LIST.TABLE`],
    })(ProductionOrderMgtList),
  ),
);
