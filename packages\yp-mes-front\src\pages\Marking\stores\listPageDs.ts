import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
const modelPrompt = 'tarzan.inventory.marking';

const listPageFactory = () =>
    new DataSet({
        autoQuery: false,
        primaryKey: 'lineNumber',
        selection: false,
        dataKey: 'rows.content',
        totalKey: 'rows.totalElements',
        queryFields: [
            {
                name: 'processBarcodeListStr',
                type: FieldType.string,
                label: intl.get(`${modelPrompt}.processBarcodeListStr`).d('产品条码'),
                dynamicProps: {
                    required: ({ record }) => {
                        if (!record.get('markingCodeListStr')) {
                            return true
                        }
                    }
                }
            },
            {
                name: 'markingCodeListStr',
                label: intl.get(`${modelPrompt}.markingCodeListStr`).d('标记数组'),
                type: FieldType.string,
                dynamicProps: {
                    required: ({ record }) => {
                        if (!record.get('processBarcodeListStr')) {
                            return true
                        }
                    }
                }
            },
            {
                name: 'level',
                type: FieldType.string,
                disabled: true,
                label: intl.get(`${modelPrompt}.level`).d('层级'),
            },
        ],
        fields: [
            // {
            //   name: 'seq',
            //   type: FieldType.string,
            //   label: intl.get(`${modelPrompt}.seq`).d('电池'),
            // },
            // {
            //   name: 'status',
            //   type: FieldType.string,
            //   label: intl.get(`${modelPrompt}.status`).d('电芯'),
            // },
            // {
            //   name: 'LDX',
            //   type: FieldType.string,
            //   label: intl.get(`${modelPrompt}.LDX`).d('裸电芯'),
            // },
            // {
            //   name: 'GF',
            //   type: FieldType.string,
            //   label: intl.get(`${modelPrompt}.GF`).d('辑分卷'),
            // },
            // {
            //   name: 'TB',
            //   type: FieldType.string,
            //   label: intl.get(`${modelPrompt}.TB`).d('涂布卷'),
            // },
            // {
            //   name: 'JL',
            //   type: FieldType.string,
            //   label: intl.get(`${modelPrompt}.JL`).d('浆料'),
            // },
        ],
        transport: {
            read: ({ data }) => {
                return {
                    method: 'POST',
                    url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/hme-marking-report/query/ui`,
                    data: {
                        ...data,
                    }
                };
            },
        },
    });

export default listPageFactory;
