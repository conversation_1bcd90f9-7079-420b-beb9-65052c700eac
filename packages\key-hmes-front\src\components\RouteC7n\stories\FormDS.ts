/**
 * 详情页-表单/表格 ds
 * @date 2022-8-23
 * <AUTHOR> <<EMAIL>>
 */
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { BASIC } from '@/utils/config';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import notification from 'utils/notification';
import { routeStepOptionDS, stepOptionDS } from './CommonDS';

const modelPrompt = 'tarzan.process.routes.model.routes';
const tenantId = getCurrentOrganizationId();

// 获取工艺路线状态
const routeStatusOptionDS = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ROUTER&statusGroup=ROUTER_STATUS`,
        method: 'GET',
        transformResponse: data => {
          if (Array.isArray(data)) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      };
    },
  },
});

// 站点列表DS数据源
const siteListDS: (serveCode: string) => DataSetProps = serveCode => ({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${serveCode}/v1/${tenantId}/mt-component/user/distribution/site/list/ui`,
        method: 'GET',
        transformResponse: data => {
          if (data instanceof Array) {
            return data;
          }
          const res = JSON.parse(data);
          // 处理接口报错
          if (res && !res.success) {
            if (res.message) {
              notification.error({ message: res.message });
            }
            return {
              rows: [],
            };
          }
          res.rows.forEach(i => {
            i.displayName = `${i.typeDesc}-${i.siteCode}-${i.siteName}`;
          });
          return res.rows;
        },
      };
    },
  },
});

const formDS: (typeGroup, assemblyLov, serveCode, siteListDs) => DataSetProps = (
  typeGroup,
  assemblyLov,
  serveCode,
  siteListDs,
) => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  autoCreate: true,
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'routerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerName`).d('编码'),
      required: true,
    },
    {
      name: 'revision',
      label: intl.get(`${modelPrompt}.revision`).d('版本'),
      required: true,
      type: FieldType.string,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('autoRevisionFlag') === 'Y';
        },
        required: ({ record }) => {
          return record.get('autoRevisionFlag') !== 'Y';
        },
      },
    },
    {
      name: 'currentFlag',
      label: intl.get(`${modelPrompt}.currentFlag`).d('当前版本'),
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'site',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      required: true,
      multiple: true,
      options: siteListDs,
      textField: 'displayName',
      valueField: 'siteId',
    },
    {
      name: 'relaxedFlowFlag',
      label: intl.get(`${modelPrompt}.relaxedFlowFlag`).d('松散标识'),
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'routerStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerStatus`).d('状态'),
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'NEW',
      required: true,
      dynamicProps: {
        options: ({ record }) => {
          const data = routeStatusOptionDS.toData();
          let routerStatusList: any = [];
          // 新建状态可更改为：冻结、可用、废弃
          // 可用状态可更改为：冻结、废弃
          // 冻结状态可更改为：新建、废弃、可用
          // 废弃状态下不可修改
          // 保留状态：不可手动修改状态为保留，也不可将保留状态的工艺路线改为其他状态
          if (record.get('routerId') === 'create') {
            // 新建的装配清单
            routerStatusList = data.filter((item: any) => item.statusCode === 'NEW');
          } else if (
            record?.getState('originRouterStatus') === 'NEW' ||
            record?.getState('originRouterStatus') === 'FREEZE'
          ) {
            routerStatusList = data.filter((item: any) => item.statusCode !== 'HOLD');
          } else if (record?.getState('originRouterStatus') === 'USABLE') {
            routerStatusList = data.filter(
              (item: any) => !['NEW', 'HOLD'].includes(item.statusCode),
            );
          } else if (record?.getState('originRouterStatus') === 'HOLD') {
            routerStatusList = data.filter((item: any) => item.statusCode === 'HOLD');
          } else {
            routerStatusList = data;
          }
          return new DataSet({
            data: [...routerStatusList],
          });
        },
      },
    },
    {
      name: 'routerId', // 装配清单入参会用到
      type: FieldType.string,
    },
    {
      name: 'usageFlag',
      label: intl.get(`${modelPrompt}.usageFlag`).d('使用状态标识'),
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      disabled: true,
    },
    {
      name: 'routerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.routerType`).d('类型'),
      options: new DataSet({ ...routeStepOptionDS(typeGroup) }),
      textField: 'description',
      valueField: 'typeCode',
      required: true,
    },
    {
      name: 'bomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.bomName`).d('装配清单'),
      lovCode: assemblyLov,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            bomType: record.get('routerType') || record.get('selectedRouterType'),
            siteIds: (record.get('site') || []).join(','),
          };
        },
        disabled: ({ record }) => !record.get('site') || !record.get('routerType'),
      },
    },
    {
      name: 'selectedRouterType', // 装配清单传参需要使用
    },
    {
      name: 'bomId',
      bind: 'bomLov.bomId',
    },
    {
      name: 'bomType',
      bind: 'bomLov.bomType',
    },
    {
      name: 'bomTypeDesc',
      bind: 'bomLov.typeDesc',
    },
    {
      name: 'bomRevision',
      bind: 'bomLov.revision',
    },
    {
      name: 'bomName',
      bind: 'bomLov.bomName',
    },
    {
      name: 'autoRevisionFlag',
      label: intl.get(`${modelPrompt}.autoRevisionFlag`).d('自动升版本标识'),
      type: FieldType.string,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('生效时间'),
      max: 'dateTo',
      required: true,
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('失效时间'),
      min: 'dateFrom',
    },
    {
      name: 'copiedFromRouterName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.copiedFromRouterName`).d('来源工艺路线'),
    },
    {
      name: 'copiedFromRouterRevision',
      type: FieldType.string,
      disabled: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${serveCode}/v1/${tenantId}/mt-router/detail/ui`,
        method: 'get',
      };
    },
  },
});

const detailTableDS = (serveCode: string) => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  autoLocateFirst: true,
  primaryKey: 'routerStepId',
  childrenField: 'mtRouterStepGroupStepDTO',
  expandField: 'expand',
  dataKey: 'rows',
  fields: [
    {
      name: 'sequence',
      type: FieldType.number,
      width: 120,
      label: intl.get(`${modelPrompt}.sequence`).d('步骤顺序'),
    },
    {
      name: 'description',
      type: FieldType.string,
      width: 150,
      label: intl.get(`${modelPrompt}.descriptionStep`).d('步骤描述'),
    },
    {
      name: 'routerDoneStepFlag',
      type: FieldType.string,
      width: 160,
      label: intl.get(`${modelPrompt}.stepNameAndRevision`).d('编码/版本'),
    },
    {
      name: 'routerStepType',
      type: FieldType.string,
      width: 160,
      label: intl.get(`${modelPrompt}.routerStepType`).d('步骤类型'),
      options: new DataSet({ ...stepOptionDS() }),
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'entryStepFlag',
      type: FieldType.string,
      width: 200,
      label: intl.get(`${modelPrompt}.entryStepFlag`).d('步骤属性'),
    },
    {
      name: 'requiredTimeInProcess',
      type: FieldType.string,
      width: 130,
      label: intl.get(`${modelPrompt}.requiredTimeInProcess`).d('标准工时（分钟）'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${serveCode}/v1/${tenantId}/mt-router-step/list/ui`,
        method: 'get',
        transformResponse: data => {
          if (Array.isArray(data)) {
            return data;
          }
          const res = JSON.parse(data); // 处理接口报错
          if (res && !res.success) {
            if (res.message) {
              notification.error({
                message: res.message,
              });
            }
            return {
              rows: [],
            };
          }
          res.rows.forEach((i: any) => {
            if (i.mtRouterStepGroupDTO?.routerStepGroupId) {
              i.mtRouterStepGroupStepDTO = i.mtRouterStepGroupDTO.mtRouterStepGroupStepDTO;
            }
          });
          return res.rows;
        },
      };
    },
  },
});

export { formDS, detailTableDS, siteListDS };
