/**
 * @Description: 覆盖C7N表格的搜索表单
 * @Author: <<EMAIL>>
 * @Date: 2021-01-12 17:08:54
 * @LastEditTime: 2022-02-16 16:45:04
 * @LastEditors: <<EMAIL>>
 *
 * @example
 *  import { overrideTableBar } from '@/components/tarzan-ui'
 *  <Table dataSet={tableDs} columns={columns} queryBar={overrideTableBar} />
 */

import React, { useMemo, useState, CSSProperties } from 'react';
import intl from 'utils/intl';
import { Button, Form, Row, Col } from 'choerodon-ui/pro';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import { TableQueryBarHookProps } from 'choerodon-ui/pro/lib/table/Table';
import ExpandTable, { ExpandProps } from '@components/tarzan-ui/ExpandTable';

interface RenderTableSearchProps extends TableQueryBarHookProps {
  expand: ExpandProps;
}

const buttonColumn: CSSProperties = {
  lineHeight: '40px',
  paddingLeft: '16px',
  display: 'flex',
  'justifyContent': 'flex-end',
};

const rowStyle: CSSProperties = {
  marginBottom: '11px',
  position: 'relative',
};

const RenderTableSearchBar: React.FC<RenderTableSearchProps> = ({
  queryFields,
  queryDataSet,
  dataSet,
  expand,
}) => {
  const [hidden, setVisible] = useState(true);
  const firstFlowFidlds = useMemo(() => {
    return queryFields.slice(0, 2);
  }, [queryFields]);
  const secondFlowFidlds = useMemo(() => {
    return queryFields.slice(2, 4);
  }, [queryFields]);
  const otherFidlds = useMemo(() => {
    return queryFields.slice(4);
  }, [queryFields, hidden]);

  const keyDownClick = async e => {
    if (e.keyCode === 13) {
      await dataSet.query();
    }
  };

  const clickHandler = async () => {
    await dataSet.query();
  };

  const loadEmptyData = () => {
    queryDataSet!.loadData([{}]);
  };

  return (
    <>
      <Row style={rowStyle}>
        <Col span={18}>
          <Form columns={2} labelWidth={121} onKeyDown={keyDownClick} dataSet={queryDataSet}>
            {firstFlowFidlds}
            {!hidden && secondFlowFidlds}
          </Form>
          <Form columns={1} labelWidth={121} onKeyDown={keyDownClick} dataSet={queryDataSet}>
            {!hidden && otherFidlds}
          </Form>
        </Col>
        <Col span={6} style={buttonColumn}>
          {queryFields.length > 3 && (
            <Button
              onClick={() => {
                setVisible(prev => !prev);
              }}
            >
              {hidden
                ? intl.get('tarzan.common.button.moreQueries').d('更多查询')
                : intl.get('tarzan.common.button.lessQueries').d('收起查询')}
            </Button>
          )}
          <Button onClick={loadEmptyData}>
            {intl.get('tarzan.common.button.reset').d('重置')}
          </Button>
          <Button onClick={clickHandler} color={ButtonColor.primary} type={ButtonType.submit}>
            {intl.get('tarzan.common.button.search').d('查询')}
          </Button>
        </Col>
        <ExpandTable {...expand} />
      </Row>
    </>
  );
};

const overrideTableBar = (expand: ExpandProps) => (props: TableQueryBarHookProps) => (
  <RenderTableSearchBar {...props} expand={expand} />
);

export default overrideTableBar;
