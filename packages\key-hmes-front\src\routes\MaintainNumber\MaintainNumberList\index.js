/**
 * maintainNumberList - 编码规则维护
 * @date: 2020-5-21
 * @author: jrq <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2019, Hand
 */
import React from 'react';
import { connect } from 'dva';
import { Form, Table, Badge, Tag } from 'hzero-ui';
import { Header, Content } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { LocaleProvider } from '@components/tarzan-ui';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import styles from './index.module.less';
import FilterForm from './FilterForm';

const modelPrompt = 'tarzan.mes.maintainNumber.model.maintainNumber';

@connect(({ maintainNumber, loading }) => ({
  maintainNumber,
  currentTenantId: getCurrentOrganizationId(),
  loading: loading.effects['maintainNumber/fetchMaintainNumberList'],
}))
@formatterCollections({ code: ['tarzan.mes.maintainNumber', 'tarzan.common'] })
@Form.create({ fieldNameProp: null })
export default class MaintainNumberList extends React.Component {
  queryForm;

  componentDidMount() {
    const {
      maintainNumber: { maintainNumberPagination = {}, maintainNumberParams = {} },
    } = this.props;
    this.queryForm.props.form.setFieldsValue(maintainNumberParams);
    this.queryForm.setState({
      objectCode: maintainNumberParams.objectCode,
    });

    setTimeout(() => {
      this.queryForm.fetchQueryList(maintainNumberPagination);
    }, 0);
  }

  /**
   * 页面跳转到号码段明细维护页面
   * @param {object} record 行数据
   */
  showNumberRangeDist = (record = {}) => {
    const { history } = this.props;
    history.push({
      pathname: `/hmes/mes/maintain-number-new/detail/${record.ruleId}`,
    });
  };

  /**
   *新建号码段页面
   * @param {object} record 行数据
   */
  createNumberRange = () => {
    const { history, dispatch } = this.props;
    dispatch({
      type: 'maintainNumber/updateState',
      payload: {
        displayList: {},
      },
    });
    history.push({
      pathname: `/hmes/mes/maintain-number-new/detail/create`,
      state: {
        ...this.queryForm.props.form.getFieldsValue(),
        objectCode: this.queryForm.state.objectCode,
      },
    });
  };

  /**
   * 分页变化后触发方法
   * @param {object} pagination 分页信息
   */
  handleTableChange = pagination => {
    this.queryForm.fetchQueryList(pagination);
  };

  /**
   *
   * @param {object} ref - FilterForm子组件对象
   */
  handleBindQueryRef = (ref = {}) => {
    this.queryForm = ref;
  };

  /**
   * 渲染方法
   * @returns
   */
  render() {
    const {
      maintainNumber: { maintainNumberList = [], maintainNumberPagination = {} },
      loading,
      match: { path },
    } = this.props;
    const columns = [
      {
        title: intl.get(`${modelPrompt}.objectCode`).d('编码对象编码'),
        width: 180,
        dataIndex: 'objectCode',
        render: (val, record) => (
          <span className="action-link">
            <a onClick={() => this.showNumberRangeDist(record)}>{val}</a>
          </span>
        ),
      },
      {
        title: intl.get(`${modelPrompt}.objectName`).d('编码对象描述'),
        dataIndex: 'ruleDescription',
        width: 200,
      },
      {
        title: intl.get(`${modelPrompt}.numExample`).d('编码示例'),
        dataIndex: 'numExample',
        width: 200,
        align: 'left',
      },
      {
        title: intl.get(`${modelPrompt}.objectTypeList`).d('对象类型'),
        dataIndex: 'objectTypeCodeList',
        width: 200,
        align: 'left',
        render: val => (
          <div className="hcm-hzero-table-tag">
            {(val || []).map(item => (
              <Tag color="blue" key={item}>
                {item}
              </Tag>
            ))}
          </div>
        ),
      },
      {
        title: intl.get(`${modelPrompt}.siteCodeList`).d('站点编码'),
        dataIndex: 'siteCodeList',
        width: 200,
        align: 'left',
        render: val => (
          <div className="hcm-hzero-table-tag">
            {(val || []).map(item => (
              <Tag color="green" key={item}>
                {item}
              </Tag>
            ))}
          </div>
        ),
      },
      {
        title: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
        dataIndex: 'enableFlag',
        width: 90,
        align: 'center',
        render: (_, record) => (
          <Badge
            status={record.enableFlag === 'Y' ? 'success' : 'error'}
            text={
              record.enableFlag === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      {
        title: intl.get(`tarzan.common.label.initialFlag`).d('初始化'),
        dataIndex: 'initialFlag',
        width: 90,
        align: 'center',
        render: (_, record) => (
          <Badge
            status={record.initialFlag === 'Y' ? 'success' : 'error'}
            text={
              record.initialFlag === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
    ];
    return (
      <div className="hmes-style">
        <Header
          title={intl.get('tarzan.mes.maintainNumber.title.maintainNumber').d('编码规则维护')}
        >
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
            onClick={() => {
              this.createNumberRange();
            }}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
        </Header>
        <LocaleProvider>
          <Content className={styles.MaintainNumContent}>
            <FilterForm onRef={this.handleBindQueryRef} path={path} />
            <Table
              loading={loading}
              rowKey="numrangeId"
              dataSource={maintainNumberList}
              columns={columns}
              pagination={maintainNumberPagination || {}}
              onChange={this.handleTableChange}
              bordered={false}
            />
          </Content>
        </LocaleProvider>
      </div>
    );
  }
}
