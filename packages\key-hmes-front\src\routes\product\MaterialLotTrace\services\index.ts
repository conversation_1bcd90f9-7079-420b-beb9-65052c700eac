/**
 * @Description: 物料批管理平台-services
 * @Author: <<EMAIL>>
 * @Date: 2022-01-18 20:19:41
 * @LastEditTime: 2023-05-18 16:06:33
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 物料批管理平台-获取物料批数据
export function FetchMaterialLotDetail() {
  return {
    url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-material-lot-trace/material-lot/detail/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_DETAIL.BASIC`,
    method: 'GET',
  };
}

// 物料批管理平台-获取到期日期
export function FetchExpirationDate() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/expiration-data/calculate/ui`,
    method: 'GET',
  };
}

// 物料批管理平台-保存
export function SaveMaterialLotDetail() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/material-lot/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_DETAIL.BASIC`,
    method: 'POST',
  };
}

// 物料批管理平台-获取动态列
export function FetchDynamicColumn(tableName) {
  return {
    url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-extend-setting/limit-table/ui?mainTable=${tableName}`,
    method: 'GET',
  };
}

// 物料批管理平台-合并物料批
export function MergeMaterial() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/ibtMaterialLotMerge`,
    method: 'GET',
  };
}