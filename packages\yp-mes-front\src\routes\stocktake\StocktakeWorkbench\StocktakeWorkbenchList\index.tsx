/**
 * @Description: 盘点工作台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-02-08 16:50:04
 * @LastEditTime: 2023-05-18 16:09:44
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import {
  DataSet,
  Table,
  Dropdown,
  Menu,
  Modal,
  Lov,
  Form,
  Button,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExport';
// import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import notification from 'utils/notification';
// import request from 'utils/request';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST, BASIC } from '@utils/config';
import { queryIdpValue } from '@/services/api';
import { observer } from 'mobx-react';
import {
  tableDS,
  batchCreateDS,
  batchCreateTableDS,
  costCenterDS,
} from '../stores/StocktakeWorkbenchListDS';
import { ChangeStatus, BatchAdd, Approve } from '../services';
import BatchCreateDrawer from './BatchCreateDrawer';

import { infoDS } from '../stores/historicalQueryDS';

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';
const tenantId = getCurrentOrganizationId();

// function isAllEqualWithKeyWord(array: string[], keyWord: string) {
//   if (array.length > 0) {
//     return !array.some(value => {
//       return value !== keyWord;
//     });
//   }
//   return false;

// }

const ControlChartList = observer((props: any) => {
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;

  const [selectedTableLineIds, setSelectedTableLineIds] = useState<number[]>([]);
  const [statusList, setStatusList] = useState([]);
  // const [selectedTalbeLineStatus, setSelectedTalbeLineStatus] = useState<string[]>([]);
  const changeStatus = useRequest(ChangeStatus(), { manual: true });
  const approve = useRequest(Approve(), { manual: true, needPromise: true });
  const batchAdd = useRequest(BatchAdd(), {
    manual: true,
    needPromise: true,
  });
  const batchCreateDs = useMemo(() => new DataSet(batchCreateDS()), []);
  const batchCreateTableDs = useMemo(() => new DataSet(batchCreateTableDS()), []);
  const costCenterDs = useMemo(() => new DataSet(costCenterDS()), []);

  useEffect(() => {
    tableDs.query(props.tableDs.currentPage);
    queryStatusList();
  }, []);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // useDataSetEvent(tableDs, 'load', () => {
  //   if (tableDs.toData().length > 0) {
  //     tableDs.setQueryParameter('materialCodes', tableDs.toData()[0].materialCodes);
  //     tableDs.query();
  //   } else {
  //     tableDs.loadData([]);
  //   }
  // });

  const queryStatusList = () => {
    queryIdpValue('WMS_STOCKTAKE_STATUS_LIMIT').then(res => {
      if (res) {
        setStatusList(res);
      }
    });
  };

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (tableDs.queryDataSet) {
      const handler = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      handler.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (tableDs) {
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(tableDs, 'batchSelect', handleLineTableChange);
      handler.call(tableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 切换站点时清空站点相关Lov
  const handleQueryDataSetUpdate = event => {
    if (event && event.name === 'siteLov') {
      event.record.init('areaLocatorLov', undefined);
      event.record.init('locatorRangeLov', undefined);
      event.record.init('materialRangeLov', undefined);
    }
    if (event && event.name === 'areaLocatorLov') {
      event.record.init('locatorRangeLov', undefined);
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    const _selectedTableLineIds: number[] = [];
    const _selectedTableLineStatus: string[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLineIds.push(item.toData().stocktakeId);
      _selectedTableLineStatus.push(item.toData().stocktakeStatus);
    });
    setSelectedTableLineIds(_selectedTableLineIds);
    // setSelectedTalbeLineStatus(_selectedTableLineStatus);
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'stocktakeNum',
        width: 240,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return <a onClick={() => handleClickToDetailPage(record)}>{value}</a>;
        },
      },
      {
        name: 'identification',
        width: 240,
      },
      {
        name: 'remark',
        width: 240,
      },
      {
        name: 'stocktakeStatusDesc',
        width: 100,
        align: ColumnAlign.center,
      },
      {
        name: 'openFlag',
        width: 130,
        align: ColumnAlign.center,
        renderer: ({ value, record }) => {
          return (
            <Tag color={value === 'Y' ? 'orange' : 'blue'}>
              {record?.addField('openFlag').getText(value)}
            </Tag>
          );
        },
      },
      {
        name: 'siteCode',
        width: 200,
      },
      {
        name: 'areaLocatorCode',
        width: 200,
      },
      {
        name: 'createByName',
        width: 180,
      },
      {
        name: 'createDate',
        align: ColumnAlign.center,
        width: 180,
      },
      {
        name: 'lastUpdateByName',
        width: 180,
      },
      {
        name: 'lastUpdateDate',
        align: ColumnAlign.center,
        width: 180,
      },
    ],
    [],
  );

  const handleClickToDetailPage = record => {
    props.history.push(`/hmes/inventory/inventory-workbench/detail/${record.get('stocktakeId')}`);
  };

  const handleDrawerConfirm = async () => {
    const {
      openFlag,
      remark,
      siteId,
      areaLocatorIds,
      locatorIds,
    } = batchCreateDs.current!.toData();
    if (!(await batchCreateDs.validate())) {
      return false;
    }
    if (!batchCreateTableDs.toData().length) {
      notification.warning({
        message: intl.get(`${modelPrompt}.selectItemOperation`).d('请选择仓库和库位后再进行新建'),
      });
      return false;
    }
    return batchAdd
      .run({
        params: {
          openFlag,
          remark,
          siteId,
          areaLocatorIds,
          locatorIds,
          stocktakeStatus: 'NEW',
        },
      })
      .then(res => {
        if (res && res.success) {
          notification.success({});
          tableDs.query();
        } else {
          return Promise.resolve(false);
        }
      });
  };

  const handleBatchCreate = () => {
    batchCreateTableDs.loadData([]);
    Modal.open({
      ...drawerPropsC7n({ ds: batchCreateDs }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.batchCreate`).d('批量新建'),
      style: {
        width: 1080,
      },
      children: <BatchCreateDrawer formDs={batchCreateDs} tableDs={batchCreateTableDs} />,
      onOk: handleDrawerConfirm,
      okText: intl.get('tarzan.common.button.create').d('新增'),
    });
  };

  const handleCreate = () => {
    props.history.push('/hmes/inventory/inventory-workbench/detail/create');
  };

  const handleChangeStatus = (sourceStatus: string | null, targetStatus: string) => {
    changeStatus.run({
      params: {
        sourceStatus,
        stocktakeIds: selectedTableLineIds,
        targetStatus,
      },
      onSuccess: () => {
        notification.success({});
        tableDs.batchUnSelect(tableDs.selected);
        setSelectedTableLineIds([]);
        // setSelectedTalbeLineStatus([]);
        tableDs.query();
      },
    });
  };

  const handleApprove = async () => {
    await approve
      .run({
        params: {
          stocktakeId: tableDs.selected[0]?.data.stocktakeId,
          costCenterId: costCenterDs?.current?.toJSONData()?.costCenterId,
        },
      })
      .then(res => {
        if (res) {
          if (res && res.success) {
            if (res.rows === 'Y') {
              // 如果返回成功弹出成本中心弹框
              costCenterDs.loadData([]);
              const modalKey = Modal.key();
              Modal.open({
                key: modalKey,
                width: 320,
                title: intl.get(`${modelPrompt}.costCenter`).d('成本中心'),
                closable: true,
                destroyOnClose: true,
                children: (
                  <Form columns={1} dataSet={costCenterDs}>
                    <Lov name="costCenterLov" />
                  </Form>
                ),
                onOk: async () => {
                  if (costCenterDs?.current?.toJSONData()?.costCenterId) {
                    await handleApprove();
                    return true;
                  }
                  return false;
                },
                onClose: () => {
                  costCenterDs.loadData([]);
                },
              });
            } else {
              notification.success({});
              tableDs.batchUnSelect(tableDs.selected);
              setSelectedTableLineIds([]);
              tableDs.query();
            }
          }
        }
      });
  };

  const menu = (
    <Menu style={{ width: '100px' }}>
      <Menu.Item disabled={tableDs.selected.some(item => item.get('stocktakeStatus') !== 'NEW')}>
        <a onClick={() => handleChangeStatus('NEW', 'RELEASED')}>
          {intl.get(`${modelPrompt}.released`).d('下达')}
        </a>
      </Menu.Item>
      <Menu.Item
        disabled={tableDs.selected.some(item => item.get('stocktakeStatus') !== 'STCOUNTING')}
      >
        <a onClick={() => handleChangeStatus('STCOUNTING', 'COMPLETED')}>
          {intl.get(`${modelPrompt}.completed`).d('完成')}
        </a>
      </Menu.Item>
      <Menu.Item
        disabled={!['RELEASED', 'NEW'].includes(tableDs.selected[0]?.data?.stocktakeStatus)}
      >
        <a onClick={() => handleChangeStatus(null, 'CLOSED')}>
          {intl.get(`${modelPrompt}.closed`).d('关闭')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const getExportQueryParams = () => {
    const queryParams = tableDs.queryDataSet.current.toData();
    delete queryParams.locatorLov;
    delete queryParams.locatorRangeLov;
    delete queryParams.materialRangeLov;
    delete queryParams.siteLov;
    queryParams.stocktakeIds = selectedTableLineIds;
    return queryParams;
  };

  const getExportQueryGenParams = () => {
    return {
      stocktakeIds: tableDs.selected.map(item => item.get('stocktakeId')),
    };
  };

  // const handleExport = async () => {
  //   // 请求后台
  //   const params = {
  //     stocktakeIds: tableDs.selected.map(item=>item.get("stocktakeId")),
  //   };
  //   const res = await request(
  //     `/yp-mes-31093/v1/${tenantId}/mt-stocktake-doc/inventory/report/generation`,
  //     {
  //       method: 'GET',
  //       query: params,
  //       responseType: 'blob',
  //     },
  //   );
  //   if (res) {
  //     if (res.type === 'application/json') {
  //       const fileReader = new FileReader();
  //       fileReader.onloadend = () => {
  //         const jsonData = JSON.parse(fileReader.result);
  //         // 普通对象，读取信息
  //         getResponse(jsonData);
  //         notification.error({ message: jsonData.message });
  //       };
  //       fileReader.readAsText(res);
  //     } else {
  //       const file = new Blob([res], {
  //         type: 'vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //       });
  //       const url = window.URL.createObjectURL(file);
  //       const a = document.createElement('a');
  //       a.href = url;
  //       a.download = decodeURIComponent('盘点工作台盘点报告.xlsx');
  //       document.body.appendChild(a);
  //       a.click();
  //       setTimeout(() => {
  //         document.body.removeChild(a);
  //         window.URL.revokeObjectURL(url);
  //       }, 1000);
  //     }
  //   }
  // };

  // 历史查询
  async function handleHistory() {
    const { selected } = tableDs;
    const ds = new DataSet(infoDS());

    ds.setQueryParameter(
      'stocktakeIds',
      selected.map(i => i.get('stocktakeId')),
    );
    ds.query();

    Modal.open({
      title: intl.get(`${modelPrompt}.historicalQuery`).d('历史查询'),
      drawer: true,
      style: {
        width: '60%',
      },
      okButton: false,
      closable: true,
      children: (
        <Table
          dataSet={ds}
          columns={infoDS().fields}
          queryFields={{
            dateObj: <DateTimePicker colSpan={2} />,
          }}
        />
      ),
    });
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.inventoryWorkbench`).d('盘点工作台')}>
        {/* <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton> */}
        {/* <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleBatchCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.batchCreate`).d('批量新建')}
        </PermissionButton> */}
        <Button color={ButtonColor.primary} icon="add" onClick={handleCreate}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </Button>
        <Button icon="add" onClick={handleBatchCreate}>
          {intl.get(`${modelPrompt}.batchCreate`).d('批量新建')}
        </Button>
        <Dropdown
          overlay={menu}
          placement={Placements.bottomRight}
          disabled={!selectedTableLineIds.length}
        >
          {/* <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={selectedTableLineIds.length!==1||
              tableDs.selected.some(item => item.get('stocktakeStatus')==='COMPLETED'
            ||item.get('stocktakeStatus')==='DISALLOWED')}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.statusChange`).d('状态变更')}
          </PermissionButton> */}
          <Button
            icon="cached"
            disabled={
              selectedTableLineIds.length !== 1 ||
              tableDs.selected.some(
                item =>
                  item.get('stocktakeStatus') === 'COMPLETED' ||
                  item.get('stocktakeStatus') === 'DISALLOWED',
              )
            }
          >
            {intl.get(`${modelPrompt}.statusChange`).d('状态变更')}
          </Button>
        </Dropdown>
        {/* <PermissionButton
          type="c7n-pro"
          icon="cached"
          disabled={tableDs.selected.length!==1
            ||!['COMPLETED', 'DISALLOWED'].includes(tableDs.selected[0]?.data?.stocktakeStatus)}
          onClick={handleApprove}
        >
          {intl.get(`${modelPrompt}.approve`).d('审批')}
        </PermissionButton> */}
        <Button
          icon="cached"
          disabled={
            tableDs.selected.length !== 1 ||
            !['COMPLETED', 'DISALLOWED'].includes(tableDs.selected[0]?.data?.stocktakeStatus)
          }
          onClick={handleApprove}
        >
          {intl.get(`${modelPrompt}.approve`).d('审批')}
        </Button>
        {/* <Button
          onClick={handleExport}
          disabled={selectedTableLineIds.length===0||
            tableDs.selected.some(item => !statusList.map(item=>item.value).includes(item.get('stocktakeStatusDesc')))}
        >
          {intl.get(`${modelPrompt}.approve`).d('盘点报告生成')}
        </Button> */}
        <ExcelExport
          method="GET"
          otherButtonProps={{
            disabled:
              selectedTableLineIds.length === 0 ||
              tableDs.selected.some(
                item =>
                  !statusList.map(item => item.value).includes(item.get('stocktakeStatusDesc')),
              ),
          }}
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/inventory/report/generation`}
          queryParams={getExportQueryGenParams}
          buttonText={intl.get(`${modelPrompt}.exportGen`).d('盘点报告生成')}
        />
        <ExcelExport
          method="GET"
          otherButtonProps={{
            disabled: selectedTableLineIds.length === 0,
          }}
          // exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <Button disabled={!selectedTableLineIds.length} onClick={handleHistory}>
          {intl.get(`${modelPrompt}.historicalQuery`).d('历史查询')}
        </Button>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="pdgzt1"
            customizedCode="pdgzt1"
          />,
        )}
      </Content>
    </div>
  );
});

export default flow(
  formatterCollections({ code: ['tarzan.hmes.stocktake.stocktakeWorkbench', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.LIST`,
    ],
  }),
)(ControlChartList);
