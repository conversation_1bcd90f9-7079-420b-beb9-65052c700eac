/**
 * @feature 新数据收集项维护
 * @date 2021-4-13
 * <AUTHOR> <<EMAIL>>
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState,useMemo } from 'react';
import { Form, IntlField, Lov, Select, Switch, TextField, DataSet } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import myInstance from '@utils/myAxios';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { isUndefined, isNumber } from 'lodash';
import NumberComponent from '../components/NumberComponent';
import { baseInfoDS, numberListDS } from '../stories/BaseInfoDs';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.acquisition.dataItem.model.dataItem';

const Detail = (props, ref) => {
  const { canEdit, id, columns = 1, type, baseInfoDs, customizeForm, valueType } = props;
  const [selectType, setSelectType] = useState('')
  const [trueValueDisabled, setTrueDisabled] = useState(true);
  const [falseValueDisabled, setFalseDisabled] = useState(true);
  useEffect(() => {
    if (id !== 'create') {
      detailQuery(id);
    }

  }, [id, type]);

  const trueNumberDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  const falseNumberDs = useMemo(
    () =>
      new DataSet({
        ...numberListDS(),
      }),
    [id],
  );
  useEffect(() => {
    baseInfoDs.addEventListener('load', handleLoadData);
    baseInfoDs.addEventListener('update', handleUpdate);
    return () => {
      baseInfoDs.removeEventListener('load', handleLoadData);
      baseInfoDs.removeEventListener('update', handleUpdate);
    };
  }, []);

  const handleUpdate = ({name}) => {
    if(name === 'valueType'||name === 'trueValue' || name==="falseValue"){
      if(['DECISION_VALUE'].includes(baseInfoDs?.current?.get('valueType'))
      &&(baseInfoDs?.current?.get('trueValue')||baseInfoDs?.current?.get('falseValue'))){
        baseInfoDs?.getField('defaultNcCodeLov')?.set('required', true);
      }else{
        baseInfoDs?.getField('defaultNcCodeLov')?.set('required', false);
      }
      baseInfoDs?.current?.set('defaultNcCodeLov', null);
    }
  }

  const handleLoadData = () => {
  }

  useEffect(() => {
    setSelectType(valueType)
  }, [valueType])

  useEffect(() => {
    setTrueDisabled(false);
    setFalseDisabled(false);
    handleUpdateDisabled();
    if(['DECISION_VALUE'].includes(baseInfoDs?.current?.get('valueType'))
      &&(baseInfoDs?.current?.get('trueValue')||baseInfoDs?.current?.get('falseValue'))){
      baseInfoDs?.getField('defaultNcCodeLov')?.set('required', true);
    }else{
      baseInfoDs?.getField('defaultNcCodeLov')?.set('required', false);
    }
    // 原来的逻辑
    // if((verifyHasValue(trueValue)||verifyHasValue(falseValue))&&['DECISION_VALUE'].includes(valueType)){
    //   baseInfoDs?.getField('defaultNcCodeLov')?.set('required', true);
    // }else{
    //   baseInfoDs?.getField('defaultNcCodeLov')?.set('required', false);
    // }
    // const trueValue = trueNumberDs.current?.get('multipleValue');
    // const falseValue = falseNumberDs.current?.get('multipleValue');
    // const _isTrueValueEdit = trueNumberDs.length > 1 || verifyHasValue(trueValue);
    // const _isFalseValueEdit = falseNumberDs.length > 1 || verifyHasValue(falseValue);
    // // 数据类型为“数值”： 符合值与不符合值可输入，且二者选其一，预警值只有在有符合值时才可输入
    // if (['VALUE'].includes(valueType)) {
    //   setTrueDisabled(_isFalseValueEdit);
    //   setFalseDisabled(_isTrueValueEdit);
    // } else {
    //   setTrueDisabled(true);
    //   setFalseDisabled(true);
    // }
  }, [canEdit])

  const detailQuery = paramsId => {
    baseInfoDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.TAG_DETAIL.BASIC`);
    baseInfoDs.setQueryParameter('tagId', paramsId);
    baseInfoDs.query().then(res => {
      if (res.success) {
        const data = res.rows;
        if (data.valueList) {
          baseInfoDs.current.set('valueList', data.valueList.split(','));
        } else {
          baseInfoDs.current.set('valueList', []);
        }
        baseInfoDs.current.set('uomCodeObject', {
          uomCode: data.uomCode,
          uomName: data.uomDesc,
          uomId: data.uomId,
        });
        if(data.defaultNcCodeId){
          baseInfoDs.current.set('defaultNcCodeLov', {
            defaultNcCode: data.defaultNcCode,
            defaultNcCodeId: data.defaultNcCodeId,
          });
          baseInfoDs.current.set('defaultNcCode', data.defaultNcCode)
          baseInfoDs.current.set('defaultNcCodeId', data.defaultNcCodeId)
        }else{
          baseInfoDs.current.set('defaultNcCodeLov', null);
        }

        if(baseInfoDs.current.toJSONData().valueType === 'VALUE'){
          trueNumberDs.loadData(baseInfoDs.current.toJSONData()?.trueValueList?baseInfoDs.current.toJSONData()?.trueValueList:[])
          falseNumberDs.loadData(baseInfoDs.current.toJSONData()?.falseValueList?baseInfoDs.current.toJSONData()?.falseValueList:[])
        }
      } else {
        notification.error({
          message: res.message,
        });
      }
    });
  };

  const handleIntersection = () => {
    let flag = false
    const trueData = trueNumberDs.toData()
    const falseData = falseNumberDs.toData()
    for(let i=0;i<falseData.length;i++){
      const itemF = falseData[i]
      if(itemF.valueType === 'single'){
        if(trueData.some(x =>x.dataValue&&x.valueType==='single'&& Number(x.dataValue)=== Number(itemF.dataValue))){
          flag = true;
          break;
        }
        const arr = trueData.filter(m => m.valueType === 'section')
        if(arr.length){
          const leftTemp = arr.map(m =>isUndefined(m.multipleValue.leftValue)?undefined:Number(m.multipleValue.leftValue))
          const rightTemp = arr.map(m => isUndefined(m.multipleValue.rightValue)?undefined:Number(m.multipleValue.rightValue))
          const leftMin = leftTemp.reduce((x, y) => x < y ? x : y)
          const rightMax = rightTemp.reduce((x, y) => x > y ? x : y)
          // 不存在负无穷
          if(leftTemp.every(m => isNumber(m))&&rightTemp.every(m => isNumber(m))){
            if(itemF.dataValue>leftMin&&itemF.dataValue<rightMax){
              flag = true;
              break;
            }
          }
          // 负无穷
          if(leftTemp.some(m => isUndefined(m))){
            if(itemF.dataValue<rightMax){
              flag = true;
              break;
            }
          }
          // 正无穷
          if(rightTemp.some(m => isUndefined(m))){
            if(itemF.dataValue>leftMin){
              flag = true;
              break;
            }
          }
        }
      }else if(itemF.valueType === 'section'){
        const leftValue = isUndefined(itemF.multipleValue.leftValue)?undefined:Number(itemF.multipleValue.leftValue)
        const rightValue = isUndefined(itemF.multipleValue.rightValue)?undefined:Number(itemF.multipleValue.rightValue)
        const arrSingle = trueData.filter(m => m.valueType === 'single'&&m.dataValue)
        const arrSection = trueData.filter(m => m.valueType === 'section')
        // 符合值中的左侧值与右侧值
        if(arrSection.length){
          const leftTemp = arrSection.map(m => isUndefined(m.multipleValue.leftValue)?undefined:Number(m.multipleValue.leftValue))
          const rightTemp = arrSection.map(m => isUndefined(m.multipleValue.rightValue)?undefined:Number(m.multipleValue.rightValue))
          const leftMin = leftTemp.reduce((x, y) => x < y ? x : y)
          const rightMax = rightTemp.reduce((x, y) => x > y ? x : y)
          if(rightValue&&leftValue){
            if(arrSingle.length){
              if(arrSingle.some(x =>x.dataValue&& x.dataValue>leftValue&&x.dataValue<rightValue)){
                flag = true;
                break;
              }
            }
            if(leftTemp.every(m => isNumber(m))&&rightTemp.every(m => isNumber(m))){
              if(leftValue>leftMin&&leftValue<rightMax||rightValue>leftMin&&rightValue<rightMax||(leftValue<leftMin)&&(rightValue>rightMax)){
                flag = true;
                break;
              }
            }
            // 负无穷
            if(leftTemp.some(m => isUndefined(m))){
              if(leftValue<rightMax){
                flag = true;
                break;
              }
            }
            // 正无穷
            if(rightTemp.some(m => isUndefined(m))){
              if(rightValue>leftMin){
                flag = true;
                break;
              }
            }
          }else if(isUndefined(rightValue)||isUndefined(leftValue)){
            if(arrSingle.length){
              if(arrSingle.some(x =>x.dataValue&&Number(x.dataValue)<rightValue)||arrSingle.some(x =>x.dataValue&&Number(x.dataValue)>leftValue)){
                flag = true;
                break;
              }
            }
            if(leftTemp.every(m => isNumber(m))&&rightTemp.every(m => isNumber(m))){
              if(isUndefined(rightValue)&&leftValue<rightMax){
                flag = true;
                break;
              }
              if(isUndefined(leftValue)&&rightValue>leftMin){
                flag = true;
                break;
              }
            }
            // 负无穷
            if(leftTemp.some(m => isUndefined(m))){
              if(isUndefined(rightValue)&&leftValue<rightMax){
                flag = true;
                break;
              }
              if(isUndefined(leftValue)){
                flag = true;
                break;
              }
            }
            // 正无穷
            if(rightTemp.some(m => isUndefined(m))){
              if(isUndefined(rightValue)){
                flag = true;
                break;
              }
              if(isUndefined(leftValue)&&rightValue>leftMin){
                flag = true;
                break;
              }
            }
          }
        }else if(arrSingle.length){
          if(rightValue&&leftValue){
            if(arrSingle.some(x =>x.dataValue&& Number(x.dataValue)>leftValue&&Number(x.dataValue)<rightValue)){
              flag = true;
              break;
            }
          }else if(isUndefined(rightValue)||isUndefined(leftValue)){
            if(arrSingle.length){
              if(arrSingle.some(x =>x.dataValue&&Number(x.dataValue)>leftValue)||arrSingle.some(x =>x.dataValue&&Number(x.dataValue)<rightValue)){
                flag = true;
                break;
              }
            }
          }
        }
      }
    }
    return flag;
  }

  useImperativeHandle(ref, () => ({
    detailQuery,
    // 暴露给父组件的方法
    submit: async () => {
      if(selectType === 'VALUE'){
        if(handleIntersection()){
          return notification.error({
            message: intl
              .get(`${modelPrompt}.error.trueValueList.falseValueList`)
              .d(`符合值与不符合值存在交集，请检查！`),
          });
        }
        if(!await falseNumberDs?.validate())return
      }

      const validate = await getResult();
      // 根据ds生成数据
      const baseDetailData = baseInfoDs.toData();
      let success = false;
      if (validate) {
        let resultId = '';
        // ${BASIC.HMES_BASIC}
        const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-tag/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.TAG_DETAIL.BASIC`;
        await myInstance
          .post(url, {
            ...baseDetailData[0],
            valueList: baseDetailData[0].valueList ? baseDetailData[0].valueList.toString() : null,
            trueValueList: selectType === 'VALUE'?trueNumberDs.toData():null,
            falseValueList: selectType === 'VALUE'?falseNumberDs.toData():null,
          })
          .then(res => {
            if (res.data.success) {
              notification.success();
              success = true;
              resultId = res.data.rows;
            } else {
              notification.error({
                message: res.data.message,
              });
            }
          });
        return { success, resultId };
      }
      return { success };
    },
    reset: () => {
      detailQuery(id);
    },
  }));
  const getResult = async () => {
    // 基本信息校验
    // eslint-disable-next-line no-return-await
    return await baseInfoDs.validate(false, true);
  };

  const valueTypeChange = () => {
    setSelectType(baseInfoDs?.current?.get('valueType'))
    if(baseInfoDs?.current?.get('valueType') === 'VALUE'){
      setTrueDisabled(false)
      setFalseDisabled(false)
    }
    baseInfoDs?.getField('defaultNcCodeLov')?.set('required', false);
    handleUpdateDisabled()
    // baseInfoDs.current.init('minimumValue');
    // baseInfoDs.current.init('maximalValue');
    baseInfoDs?.current?.init('uomCodeObject');
    baseInfoDs?.current?.init('trueValue');
    baseInfoDs?.current?.init('falseValue');
    baseInfoDs?.current.init('valueList');
    baseInfoDs?.current?.init('dateFormat');
    baseInfoDs?.current?.init('specialRecordFlag');
  };
  // 根据值类型不同使用不同的判空方法
  const verifyHasValue = value => {
    if (value instanceof Object) {
      // 数据类型为数值-区间
      return value?.leftValue || value?.rightValue;
    }
    // 数据类型为数值-单值
    return value;
  };

  const handleUpdateDisabled = () => {
    const { valueType } = baseInfoDs.current?.toData();
    const trueValue = trueNumberDs.current?.get('multipleValue');
    const falseValue = falseNumberDs.current?.get('multipleValue');
    const _isFalseValueEdit = falseNumberDs.length > 1 || verifyHasValue(falseValue);
    if (['VALUE'].includes(valueType)){
      if(_isFalseValueEdit){
        falseNumberDs.getField('ncCodeLov').set('required', true);
      }else{
        falseNumberDs.getField('ncCodeLov').set('required', false);
      }
    }
    if(['VALUE'].includes(valueType)&&verifyHasValue(trueValue)){
      baseInfoDs?.getField('defaultNcCodeLov')?.set('required', true);
    }else{
      baseInfoDs?.getField('defaultNcCodeLov')?.set('required', false);
    }

  };

  return (
    <>
      <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
        <Panel
          header={intl.get(`${modelPrompt}.baseInfo`).d('基础信息')}
          key="basicInfo"
          dataSet={baseInfoDs}
        >
          {customizeForm(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.TAG_DETAIL.BASIC`,
            },
            <Form
              disabled={!canEdit}
              dataSet={baseInfoDs}
              columns={columns}
              labelLayout="horizontal"
              labelWidth={112}
            >
              <TextField name="tagCode" />
              <IntlField
                name="tagDescription"
                modalProps={{
                  title: intl.get(`${modelPrompt}.description`).d('描述'),
                }}
              />
              <Select name="collectionMethod" />
              <Select name="valueType" onChange={valueTypeChange} />
              <Switch name="displayValueFlag" />
              <Switch name="enableFlag" />
              <Switch name="valueAllowMissing" />
              <Switch name="allowUpdateFlag" />
              <Switch name="specialRecordFlag" />
              <TextField name="remark" />
              <Switch name="cpFlag" />
              <Switch name="warningFlag" />
              <Switch name="formulaFlag" />
              <Lov name="higherRangeLov" />
              <Lov name="lowerRangeLov" />
            </Form>,
          )}
        </Panel>

        <Panel
          header={intl.get(`${modelPrompt}.collectSpecification`).d('采集规范')}
          key="location"
          dataSet={baseInfoDs}
        >
          <Form
            disabled={!canEdit}
            dataSet={baseInfoDs}
            columns={columns}
            labelLayout="horizontal"
            labelWidth={112}
          >
            {/* <NumberField name="minimumValue" />
            <NumberField name="maximalValue" /> */}
            <Lov name="uomCodeObject" placeholder=" " />
            {selectType === 'VALUE'?
              <NumberComponent
                showStandard
                name="trueValue"
                parentDs={baseInfoDs}
                dataSet={trueNumberDs}
                disabled={trueValueDisabled}
                canEdit={canEdit}
                handleUpdateDisabled={handleUpdateDisabled}
              />:<TextField name="trueValue" />}
            {selectType === 'VALUE'?
              <NumberComponent
                showNcCode
                name="falseValue"
                parentDs={baseInfoDs}
                dataSet={falseNumberDs}
                disabled={falseValueDisabled}
                canEdit={canEdit}
                handleUpdateDisabled={handleUpdateDisabled}
              />:<TextField name="falseValue" />}
            <TextField name="valueList" multiple clearButton />
            <Select disabled={selectType!=='DATE'} name="dateFormat" />
            <Lov name="defaultNcCodeLov" />
            {/* <NumberField name="mandatoryNum" min={0} step={1} />
            <NumberField name="optionalNum" min={0} step={1} /> */}
          </Form>
        </Panel>
      </Collapse>
    </>
  );
};

export default forwardRef(Detail);
