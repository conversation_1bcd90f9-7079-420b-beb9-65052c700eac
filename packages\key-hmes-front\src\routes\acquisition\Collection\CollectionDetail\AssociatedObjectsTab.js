/**
 * @Description: 数据收集组维护-关联对象
 * @Author: <<EMAIL>>
 * @Date: 2021-04-16 15:28:25
 * @LastEditTime: 2023-05-23 15:22:43
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { Table, Button, Modal, DataSet } from 'choerodon-ui/pro';
import { Tag, Popconfirm } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { SortColumnC7n } from './TableSortColumnC7n';
import AssociatedObjectsDrawer from './AssociatedObjectsDrawer';
import { singleAssObjectDS } from '../stores/CollectionDS';

const modelPrompt = 'tarzan.hmes.acquisition.collection';

const AssociatedObjectsTab = observer(props => {
  const { canEdit, assObjectsDs, detailDs, tableHeight } = props;
  const singleAssObjectDs = useMemo(() => new DataSet(singleAssObjectDS()), []);
  const typeName = {
    MATERIAL: intl.get(`${modelPrompt}.MATERIAL`).d('物料'),
    OPERATION: intl.get(`${modelPrompt}.OPERATION`).d('工艺'),
    ROUTER: intl.get(`${modelPrompt}.ROUTER`).d('工艺路线'),
    ROUTER_STEP: intl.get(`${modelPrompt}.ROUTER_STEP`).d('工艺路线步骤'),
    WORKCELL: intl.get(`${modelPrompt}.WORKCELL`).d('工作单元'),
    NC_CODE: intl.get(`${modelPrompt}.NC_CODE`).d('NC代码'),
    BOM: intl.get(`${modelPrompt}.BOM`).d('装配清单'),
    BOM_COMPONENT: intl.get(`${modelPrompt}.BOM_COMPONENT`).d('装配清单组件'),
    SUBSTEP: intl.get(`${modelPrompt}.SUBSTEP`).d('子步骤'),
    WORK_ORDER: intl.get(`${modelPrompt}.WORK_ORDER`).d('生产指令'),
    EO: intl.get(`${modelPrompt}.EO`).d('执行作业'),
    MATERIAL_CATEGORY: intl.get(`${modelPrompt}.MATERIAL_CATEGORY`).d('物料类别'),
    MATERIAL_LOT: intl.get(`${modelPrompt}.MATERIAL_LOT`).d('物料批'),
    'MT.WO_ROUTER': intl.get(`${modelPrompt}.MT.WO_ROUTER`).d('工艺路线'),
    'MT.ROUTER_STEP': intl.get(`${modelPrompt}.MT.ROUTER_STEP`).d('工艺路线步骤'),
    'MT.MODEL.EQUIPMENT': intl.get(`${modelPrompt}.MT.MODEL.EQUIPMENT`).d('设备'),
  };

  let _dataItemDrawer;

  const associatedObjectsDrawerClose = record => {
    if (!record) {
      assObjectsDs.remove(assObjectsDs.current);
    }
    _dataItemDrawer.close();
  };

  const associatedObjectsDrawerSubmit = async record => {
    const validate = await singleAssObjectDs.validate(false, true);
    if (!validate) {
      return;
    }
    if (singleAssObjectDs.toData().length === 0) {
      _dataItemDrawer.close();
      assObjectsDs.remove(assObjectsDs.current);
      return;
    }
    if (!record.get('lineNumber')) {
      const newLineNumber =
        (assObjectsDs.toData().sort((a, b) => b.lineNumber - a.lineNumber)[0].lineNumber || 0) + 1;
      record.set('lineNumber', newLineNumber);
    }
    record.set('objectList', singleAssObjectDs.toData());
    _dataItemDrawer.close();
  };

  const associatedObjectsModify = record => {
    if (!record) {
      assObjectsDs.create({ tagGroupObjectId: -Date.parse(new Date()) });
    }
    _dataItemDrawer = Modal.open({
      key: Modal.key(),
      title: record
        ? intl.get(`${modelPrompt}.associatedObjectsEdit`).d('关联对象组合编辑')
        : intl.get(`${modelPrompt}.associatedObjectsCreate`).d('新建关联对象组合'),
      drawer: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <AssociatedObjectsDrawer
          singleAssObjectDs={singleAssObjectDs}
          canEdit={canEdit}
          record={record || assObjectsDs.current}
          // 用get方法取，他是ObservableArrayAdministration，还需要spile
          parentSiteIds={detailDs.current.toData().tagGroupInfo.siteIds || []}
        />
      ),
      footer: !canEdit ? (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              associatedObjectsDrawerClose(record);
            }}
          >
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        </div>
      ) : (
        <div style={{ float: 'right' }}>
          <Button
            onClick={() => {
              associatedObjectsDrawerClose(record);
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            type="submit"
            onClick={() => {
              associatedObjectsDrawerSubmit(record || assObjectsDs.current);
            }}
            color={ButtonColor.primary}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  const columns = [
    {
      header: (
        <Button
          icon="add"
          disabled={!canEdit||assObjectsDs?.toData().length === 1}
          onClick={() => associatedObjectsModify()}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      name: 'editColumn',
      align: 'center',
      width: 70,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => {
            assObjectsDs.remove(record);
          }}
        >
          <Button disabled={!canEdit} funcType="flat" icon="remove" shape="circle" size="small" />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      header: SortColumnC7n({
        name: 'lineNumber',
        title: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
        ds: assObjectsDs,
      }),
      name: 'lineNumber',
      align: 'left',
      width: 120,
      renderer: ({ value, record }) => (
        <a
          style={{ minWidth: '100%', display: 'inline-block' }}
          onClick={() => {
            associatedObjectsModify(record);
          }}
        >
          {value}
        </a>
      ),
    },
    {
      name: 'objectList',
      renderer: ({ record }) => {
        return (record.get('objectList') || []).length && (record.get('objectList') || []).map(item => (
          <Tag key={item.objectId} color="blue">
            {`${typeName[item.objectType]}：${item.objectDesc ? item.objectDesc : item.objectCode}`}
          </Tag>
        ));
      },
    },
  ];

  return <Table dataSet={assObjectsDs} columns={columns} virtual style={{ height: tableHeight }} />;
});

export default AssociatedObjectsTab;
