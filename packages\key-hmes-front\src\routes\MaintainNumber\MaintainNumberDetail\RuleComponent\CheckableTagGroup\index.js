import React, { Component } from 'react';
import { Tag, Tooltip, Input, Icon } from 'hzero-ui';
import styles from './index.module.less';

const { CheckableTag } = Tag;
export default class TagGroupForLov extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: undefined,
      initialValue: undefined,
      inputVisible: false,
      inputValue: '',
    };
  }

  //  不使用constructor来给tags赋予初始值，是因为可能页面加载完成后，才得到服务器返回数据
  static getDerivedStateFromProps(nextProps, prevState) {
    if (JSON.stringify(nextProps.value) !== JSON.stringify(prevState.initialValue)) {
      return {
        value: nextProps.value || undefined,
        initialValue: nextProps.value,
      };
    }
    return null;
  }

  handleChange = item => {
    const { onChange = e => e, changeUsedTag = e => e } = this.props;
    this.setState({ value: item.objectColumnCode });
    onChange(item.objectColumnCode);
    changeUsedTag(item.objectColumnCode);
  };

  showInput = () => {
    this.setState({ inputVisible: true }, () => this.input.focus());
  };

  handleInputChange = e => {
    this.setState({ inputValue: e.target.value.trim() });
  };

  handleInputOnBlur = () => {
    const { inputValue } = this.state;
    this.props.saveObjTypeCodeList(inputValue);
    this.setState({
      inputVisible: false,
      inputValue: '',
    });
  };

  handleInputEnter = () => {
    const { inputValue } = this.state;
    this.props.saveObjTypeCodeList(inputValue);
    this.setState({
      inputValue: '',
    });
  };

  handleDelete = (item, e) => {
    const { value } = this.state;
    const { onChange = a => a, deleteTag = a => a } = this.props;
    if (item.objectColumnCode === value) {
      this.setState({ value: undefined });
      onChange(undefined);
    }
    deleteTag(item, e);
  };

  saveInputRef = input => {
    this.input = input;
  };

  render() {
    const { value, inputVisible, inputValue } = this.state;
    const { style, className, dataSource = [], canEdit, usingTagArray, tagName } = this.props;
    const classNames = canEdit
      ? [className, 'CheckableTagGroup']
      : [className, 'CheckableTagGroup', 'CannotEdit'];
    return (
      <div style={style} className={classNames.join(' ')}>
        {!canEdit && <div className="mask" />}
        {dataSource.map(tag => {
          const isLongTag = tag.objectColumnCode.length > 20;
          const usedFlag = usingTagArray.includes(tag.objectColumnCode)
            ? tag.objectColumnCode !== value
            : false;
          const tagElem = (
            <CheckableTag
              className={usedFlag ? 'usedTag' : undefined}
              key={tag.objectColumnCode}
              checked={value === tag.objectColumnCode}
              onChange={() => {
                this.handleChange(tag);
              }}
            >
              <div className="content">
                {isLongTag ? `${tag.objectColumnCode.slice(0, 20)}...` : tag.objectColumnCode}
              </div>
              {canEdit && tag.initialFlag === 'N' && (
                <Icon type="close" onClick={this.handleDelete.bind(this, tag)} />
              )}
            </CheckableTag>
          );
          return isLongTag ? (
            <Tooltip title={tag.objectColumnCode} key={tag.objectColumnCode}>
              {tagElem}
            </Tooltip>
          ) : (
            tagElem
          );
        })}
        {inputVisible && (
          <Input
            dbc2sbc={false}
            ref={this.saveInputRef}
            type="text"
            size="small"
            style={{ width: 140 }}
            value={inputValue}
            onChange={this.handleInputChange}
            onBlur={this.handleInputOnBlur}
            onPressEnter={this.handleInputEnter}
          />
        )}
        {!inputVisible && canEdit && (
          <Tag onClick={this.showInput} className={styles.inputTag}>
            <Icon type="plus" /> {tagName}
          </Tag>
        )}
      </div>
    );
  }
}
