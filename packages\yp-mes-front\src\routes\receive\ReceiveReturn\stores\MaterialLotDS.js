/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2021-12-14 16:12:24
 * @LastEditTime: 2023-05-18 15:33:27
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.receive.receiveReturn';
// const endUrl = '-30711';
const endUrl = '';
const HMES_BASIC = BASIC.HMES_BASIC;

const tenantId = getCurrentOrganizationId();

const returnMaterialLotDS = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  selection: false,
  paging: false,
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}${endUrl}/v1/${tenantId}/receive-return-bill/material-lot/get/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_MATERIAL_LOT.QUERY`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'materialIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentification`).d('物料批标识'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatusDesc`).d('物料批状态'),
    },
    {
      name: 'containerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerIdentification`).d('容器'),
    },
    {
      name: 'sumActualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumActualQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'returnLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.returnLocatorCode`).d('退料接收库位'),
    },
    {
      name: 'returnDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.returnDate`).d('退料时间'),
    },
    {
      name: 'returnName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.returnName`).d('退料人'),
    },
  ],
});

const receiveMaterialLotDS = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  selection: false,
  paging: true,
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}${endUrl}/v1/${tenantId}/receive-return-bill/material-lot/get/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_MATERIAL_LOT.QUERY`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'materialIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentification`).d('物料批标识'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.codeStatusDesc`).d('条码状态'),
    },
    {
      name: 'containerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerIdentification`).d('容器'),
    },
    {
      name: 'sumActualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumActualQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'receiveLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveLocatorCode`).d('领取库位'),
    },
    {
      name: 'receiveDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveDate`).d('领取时间'),
    },
    {
      name: 'receiveBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveBy`).d('领料人'),
    },
    {
      name: 'signedLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.signedWarehouse`).d('签收仓库'),
    },
    {
      name: 'signedDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.signedDate`).d('签收时间'),
    },
    {
      name: 'signedBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.signedBy`).d('签收人'),
    },
  ],
});

export { returnMaterialLotDS, receiveMaterialLotDS };
