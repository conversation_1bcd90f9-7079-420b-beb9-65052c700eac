import React, { useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import { DataSet, Form, Switch, TextField } from 'choerodon-ui/pro';
import { isEmpty } from 'lodash';
import { EnterpriseDS } from './stories/EnterpriseDS';

const ControlDetail = (props, ref) => {
  const { canEdit, kid, columns = 1 } = props;
  const formDs = useMemo(() => new DataSet(EnterpriseDS()), []);

  useEffect(() => {
    queryTable();
  }, [kid]);

  useImperativeHandle(ref, () => ({
    submit: async () => {
      formDs.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验
      const validate = await formDs.validate();
      if (validate) {
        let success = false;
        await formDs.submit().then(res => {
          const { rows = [] } = res || {};
          if (!isEmpty(rows) && rows[0].success) {
            formDs.query();
            success = true;
          }
        });
        return { success };
      }
      return { success: false };
    },
    reset: () => {
      queryTable();
    },
  }));

  // 查询上方表单,下方表格
  const queryTable = () => {
    formDs.setQueryParameter('enterpriseId', kid);
    formDs.query();
  };

  return (
    <div className="hmes-style">
      <Form dataSet={formDs} columns={columns} disabled={!canEdit} style={{ marginTop: '16px' }}>
        <TextField name="enterpriseCode" />
        <TextField name="enterpriseName" />
        <TextField name="enterpriseShortName" />
        <Switch name="enableFlag" />
      </Form>
    </div>
  );
};

export default forwardRef(ControlDetail);
