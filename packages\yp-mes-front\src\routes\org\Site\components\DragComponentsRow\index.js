/**
 * @Description: 列拖拽
 * @Author: <<EMAIL>>
 * @Date: 2021-09-14 15:41:22
 * @LastEditTime: 2022-05-17 15:15:55
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import DragContainer from './dragContainer';

const DragComponents = ({ list, canEdit }, ref) => {
  useEffect(() => {
    setPreviewList([...list]);
  }, [list]);

  const [previewList, setPreviewList] = useState(list);

  useImperativeHandle(ref, () => ({
    getCoordinateList: () => {
      return previewList;
    },
  }));

  const handlePreviewList = _list => {
    setPreviewList([..._list]);
  };

  return (
    <DragContainer
      handlePreviewList={handlePreviewList}
      previewList={previewList}
      canEdit={canEdit}
    />
  );
};

export default forwardRef(DragComponents);
