/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-07-22 13:41:47
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-07-31 17:14:07
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\TraceabilityDataTemplateMaintenance\HistoryDrawer.tsx
 * @Description: 追溯数据模板维护历史查询抽屉
 */
import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Badge } from 'hzero-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import intl from 'utils/intl';

interface HistoryDrawerProps {
  headHistoryDs: DataSet;
  lineHistoryDs: DataSet;
}

const HistoryDrawer: React.FC<HistoryDrawerProps> = ({ headHistoryDs, lineHistoryDs }) => {
  // 头历史表格列配置
  const headHistoryColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'siteObj' },
      { name: 'materialObj' },
      { name: 'materialName' },
      { name: 'revisionCode' },
      { name: 'customerObj' },
      { name: 'customerName' },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('enableFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('enableFlag') === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      { name: 'createdByObj' },
      { name: 'creationDate' },
    ],
    [],
  );

  // 行历史表格列配置
  const lineHistoryColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'paraTypeObj' },
      { name: 'paraName' },
      { name: 'paraValue' },
      { name: 'operationObj' },
      { name: 'operationDescription' },
      { name: 'tagObj' },
      { name: 'tagDescription' },
      { name: 'upperLimitValue' },
      { name: 'lowerLimitValue' },
      {
        name: 'enableFlag',
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('enableFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('enableFlag') === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          />
        ),
      },
      { name: 'createdByObj' },
      { name: 'creationDate' },
    ],
    [],
  );

  return (
    <>
      <Table
        dataSet={headHistoryDs}
        columns={headHistoryColumns}
        queryBar={TableQueryBarType.none}
      />
      <Table
        dataSet={lineHistoryDs}
        columns={lineHistoryColumns}
        queryBar={TableQueryBarType.none}
      />
    </>
  );
};

export default HistoryDrawer;
